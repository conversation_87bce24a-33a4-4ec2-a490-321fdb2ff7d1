2025-07-20T12:07:48.117+08:00 DEBUG 14207 --- [RMI TCP Connection(1)-127.0.0.1] sun.rmi.loader                           : RMI TCP Connection(1)-127.0.0.1: name = "javax.management.ObjectName", codebase = ""
2025-07-20T12:07:48.175+08:00 DEBUG 14207 --- [RMI TCP Connection(1)-127.0.0.1] javax.management.remote.rmi              : connectionId=rmi://127.0.0.1  1, name=java.lang:type=Memory, attribute=HeapMemoryUsage
2025-07-20T12:07:48.176+08:00 DEBUG 14207 --- [RMI TCP Connection(1)-127.0.0.1] sun.rmi.transport.tcp                    : RMI TCP Connection(1)-127.0.0.1: (port 51779) op = 80
2025-07-20T12:07:48.176+08:00 DEBUG 14207 --- [RMI TCP Connection(1)-127.0.0.1] sun.rmi.loader                           : RMI TCP Connection(1)-127.0.0.1: name = "javax.management.ObjectName", codebase = ""
2025-07-20T12:07:48.176+08:00 DEBUG 14207 --- [RMI TCP Connection(1)-127.0.0.1] javax.management.remote.rmi              : connectionId=rmi://127.0.0.1  1, name=java.lang:type=Memory, attribute=HeapMemoryUsage
2025-07-20T12:07:49.180+08:00 DEBUG 14207 --- [RMI TCP Connection(1)-127.0.0.1] sun.rmi.transport.tcp                    : RMI TCP Connection(1)-127.0.0.1: (port 51779) op = 82
2025-07-20T12:07:49.181+08:00 DEBUG 14207 --- [RMI TCP Connection(1)-127.0.0.1] sun.rmi.transport.tcp                    : RMI TCP Connection(1)-127.0.0.1: (port 51779) op = 80
2025-07-20T12:07:49.181+08:00 DEBUG 14207 --- [RMI TCP Connection(1)-127.0.0.1] sun.rmi.loader                           : RMI TCP Connection(1)-127.0.0.1: name = "javax.management.ObjectName", codebase = ""
2025-07-20T12:07:49.181+08:00 DEBUG 14207 --- [RMI TCP Connection(1)-127.0.0.1] javax.management.remote.rmi              : connectionId=rmi://127.0.0.1  1, name=java.lang:type=OperatingSystem, attribute=ProcessCpuLoad
2025-07-20T12:07:49.188+08:00 DEBUG 14207 --- [RMI TCP Connection(1)-127.0.0.1] sun.rmi.transport.tcp                    : RMI TCP Connection(1)-127.0.0.1: (port 51779) op = 82
2025-07-20T12:07:49.188+08:00 DEBUG 14207 --- [RMI TCP Connection(1)-127.0.0.1] sun.rmi.transport.tcp                    : RMI TCP Connection(1)-127.0.0.1: (port 51779) op = 80
2025-07-20T12:07:49.188+08:00 DEBUG 14207 --- [RMI TCP Connection(1)-127.0.0.1] sun.rmi.loader                           : RMI TCP Connection(1)-127.0.0.1: name = "javax.management.ObjectName", codebase = ""
2025-07-20T12:07:49.188+08:00 DEBUG 14207 --- [RMI TCP Connection(1)-127.0.0.1] javax.management.remote.rmi              : connectionId=rmi://127.0.0.1  1, name=java.lang:type=Memory, attribute=HeapMemoryUsage
2025-07-20T12:07:49.189+08:00 DEBUG 14207 --- [RMI TCP Connection(1)-127.0.0.1] sun.rmi.transport.tcp                    : RMI TCP Connection(1)-127.0.0.1: (port 51779) op = 80
2025-07-20T12:07:49.189+08:00 DEBUG 14207 --- [RMI TCP Connection(1)-127.0.0.1] sun.rmi.loader                           : RMI TCP Connection(1)-127.0.0.1: name = "javax.management.ObjectName", codebase = ""
2025-07-20T12:07:49.189+08:00 DEBUG 14207 --- [RMI TCP Connection(1)-127.0.0.1] javax.management.remote.rmi              : connectionId=rmi://127.0.0.1  1, name=java.lang:type=Memory, attribute=HeapMemoryUsage
2025-07-20T12:07:50.191+08:00 DEBUG 14207 --- [RMI TCP Connection(1)-127.0.0.1] sun.rmi.transport.tcp                    : RMI TCP Connection(1)-127.0.0.1: (port 51779) op = 82
2025-07-20T12:07:50.191+08:00 DEBUG 14207 --- [RMI TCP Connection(1)-127.0.0.1] sun.rmi.transport.tcp                    : RMI TCP Connection(1)-127.0.0.1: (port 51779) op = 80
2025-07-20T12:07:50.192+08:00 DEBUG 14207 --- [RMI TCP Connection(1)-127.0.0.1] sun.rmi.loader                           : RMI TCP Connection(1)-127.0.0.1: name = "javax.management.ObjectName", codebase = ""
2025-07-20T12:07:50.192+08:00 DEBUG 14207 --- [RMI TCP Connection(1)-127.0.0.1] javax.management.remote.rmi              : connectionId=rmi://127.0.0.1  1, name=java.lang:type=OperatingSystem, attribute=ProcessCpuLoad
2025-07-20T12:07:50.193+08:00 DEBUG 14207 --- [RMI TCP Connection(1)-127.0.0.1] sun.rmi.transport.tcp                    : RMI TCP Connection(1)-127.0.0.1: (port 51779) op = 82
2025-07-20T12:07:50.194+08:00 DEBUG 14207 --- [RMI TCP Connection(1)-127.0.0.1] sun.rmi.transport.tcp                    : RMI TCP Connection(1)-127.0.0.1: (port 51779) op = 80
2025-07-20T12:07:50.194+08:00 DEBUG 14207 --- [RMI TCP Connection(1)-127.0.0.1] sun.rmi.loader                           : RMI TCP Connection(1)-127.0.0.1: name = "javax.management.ObjectName", codebase = ""
2025-07-20T12:07:50.194+08:00 DEBUG 14207 --- [RMI TCP Connection(1)-127.0.0.1] javax.management.remote.rmi              : connectionId=rmi://127.0.0.1  1, name=java.lang:type=Memory, attribute=HeapMemoryUsage
2025-07-20T12:07:50.195+08:00 DEBUG 14207 --- [RMI TCP Connection(1)-127.0.0.1] sun.rmi.transport.tcp                    : RMI TCP Connection(1)-127.0.0.1: (port 51779) op = 80
2025-07-20T12:07:50.195+08:00 DEBUG 14207 --- [RMI TCP Connection(1)-127.0.0.1] sun.rmi.loader                           : RMI TCP Connection(1)-127.0.0.1: name = "javax.management.ObjectName", codebase = ""
2025-07-20T12:07:50.196+08:00 DEBUG 14207 --- [RMI TCP Connection(1)-127.0.0.1] javax.management.remote.rmi              : connectionId=rmi://127.0.0.1  1, name=java.lang:type=Memory, attribute=HeapMemoryUsage
2025-07-20T12:07:51.197+08:00 DEBUG 14207 --- [RMI TCP Connection(1)-127.0.0.1] sun.rmi.transport.tcp                    : RMI TCP Connection(1)-127.0.0.1: (port 51779) op = 82
2025-07-20T12:07:51.198+08:00 DEBUG 14207 --- [RMI TCP Connection(1)-127.0.0.1] sun.rmi.transport.tcp                    : RMI TCP Connection(1)-127.0.0.1: (port 51779) op = 80
2025-07-20T12:07:51.198+08:00 DEBUG 14207 --- [RMI TCP Connection(1)-127.0.0.1] sun.rmi.loader                           : RMI TCP Connection(1)-127.0.0.1: name = "javax.management.ObjectName", codebase = ""
2025-07-20T12:07:51.198+08:00 DEBUG 14207 --- [RMI TCP Connection(1)-127.0.0.1] javax.management.remote.rmi              : connectionId=rmi://127.0.0.1  1, name=java.lang:type=OperatingSystem, attribute=ProcessCpuLoad
2025-07-20T12:07:51.199+08:00 DEBUG 14207 --- [RMI TCP Connection(1)-127.0.0.1] sun.rmi.transport.tcp                    : RMI TCP Connection(1)-127.0.0.1: (port 51779) op = 80
2025-07-20T12:07:51.199+08:00 DEBUG 14207 --- [RMI TCP Connection(1)-127.0.0.1] sun.rmi.loader                           : RMI TCP Connection(1)-127.0.0.1: name = "javax.management.ObjectName", codebase = ""
2025-07-20T12:07:51.199+08:00 DEBUG 14207 --- [RMI TCP Connection(1)-127.0.0.1] javax.management.remote.rmi              : connectionId=rmi://127.0.0.1  1, name=java.lang:type=Memory, attribute=HeapMemoryUsage
2025-07-20T12:07:51.199+08:00 DEBUG 14207 --- [RMI TCP Connection(1)-127.0.0.1] sun.rmi.transport.tcp                    : RMI TCP Connection(1)-127.0.0.1: (port 51779) op = 80
2025-07-20T12:07:51.200+08:00 DEBUG 14207 --- [RMI TCP Connection(1)-127.0.0.1] sun.rmi.loader                           : RMI TCP Connection(1)-127.0.0.1: name = "javax.management.ObjectName", codebase = ""
2025-07-20T12:07:51.200+08:00 DEBUG 14207 --- [RMI TCP Connection(1)-127.0.0.1] javax.management.remote.rmi              : connectionId=rmi://127.0.0.1  1, name=java.lang:type=Memory, attribute=HeapMemoryUsage
