2025-07-20T13:15:03.333+08:00 DEBUG 14207 --- [http-nio-0.0.0.0-8080-exec-2] org.hibernate.orm.sql.exec               : Skipping reading Query result cache data: cache-enabled = false, cache-mode = IGNORE
2025-07-20T13:15:03.346+08:00 DEBUG 14207 --- [http-nio-0.0.0.0-8080-exec-2] org.hibernate.SQL                        : 
    select
        r1_0.user_id,
        r1_0.role 
    from
        user_roles r1_0 
    where
        r1_0.user_id=?
2025-07-20T13:15:03.348+08:00 DEBUG 14207 --- [http-nio-0.0.0.0-8080-exec-2] o.h.sql.results.internal.ResultsHelper   : Collection fully initialized: [com.example.gymbooking.model.User.roles#17]
2025-07-20T13:15:03.349+08:00  INFO 14207 --- [http-nio-0.0.0.0-8080-exec-2] c.e.g.s.services.UserDetailsServiceImpl  : 成功加载用户: 13402838501, 角色: [ROLE_USER]
2025-07-20T13:15:03.349+08:00 DEBUG 14207 --- [http-nio-0.0.0.0-8080-exec-2] o.s.orm.jpa.JpaTransactionManager        : Initiating transaction commit
2025-07-20T13:15:03.349+08:00 DEBUG 14207 --- [http-nio-0.0.0.0-8080-exec-2] o.s.orm.jpa.JpaTransactionManager        : Committing JPA transaction on EntityManager [SessionImpl(845485690<open>)]
2025-07-20T13:15:03.349+08:00 DEBUG 14207 --- [http-nio-0.0.0.0-8080-exec-2] o.h.e.t.internal.TransactionImpl         : committing
2025-07-20T13:15:03.349+08:00 DEBUG 14207 --- [http-nio-0.0.0.0-8080-exec-2] o.h.e.i.AbstractFlushingEventListener    : Processing flush-time cascades
2025-07-20T13:15:03.349+08:00 DEBUG 14207 --- [http-nio-0.0.0.0-8080-exec-2] o.h.e.i.AbstractFlushingEventListener    : Dirty checking collections
2025-07-20T13:15:03.350+08:00 DEBUG 14207 --- [http-nio-0.0.0.0-8080-exec-2] o.hibernate.engine.internal.Collections  : Collection found: [com.example.gymbooking.model.User.roles#17], was: [com.example.gymbooking.model.User.roles#17] (initialized)
2025-07-20T13:15:03.350+08:00 DEBUG 14207 --- [http-nio-0.0.0.0-8080-exec-2] o.h.e.i.AbstractFlushingEventListener    : Flushed: 0 insertions, 0 updates, 0 deletions to 1 objects
2025-07-20T13:15:03.350+08:00 DEBUG 14207 --- [http-nio-0.0.0.0-8080-exec-2] o.h.e.i.AbstractFlushingEventListener    : Flushed: 0 (re)creations, 0 updates, 0 removals to 1 collections
2025-07-20T13:15:03.350+08:00 DEBUG 14207 --- [http-nio-0.0.0.0-8080-exec-2] o.hibernate.internal.util.EntityPrinter  : Listing entities:
2025-07-20T13:15:03.350+08:00 DEBUG 14207 --- [http-nio-0.0.0.0-8080-exec-2] o.hibernate.internal.util.EntityPrinter  : com.example.gymbooking.model.User{createdAt=2025-06-22T12:46:54.618255, password=$2a$10$v6ApAukPPgUlLAhqPLPGT.Jm.5u6/ib2tlQEpMpb0Wn8m0aFGxfZ6, phone=13402838501, roles=[ROLE_USER], nickname=test1, active=true, id=17, avatar=null, email=null, updatedAt=2025-06-22T12:46:54.618293, username=13402838501}
2025-07-20T13:15:03.353+08:00 DEBUG 14207 --- [http-nio-0.0.0.0-8080-exec-2] o.s.orm.jpa.JpaTransactionManager        : Closing JPA EntityManager [SessionImpl(845485690<open>)] after transaction
2025-07-20T13:15:03.353+08:00 DEBUG 14207 --- [http-nio-0.0.0.0-8080-exec-2] c.e.g.security.jwt.AuthTokenFilter       : 用户 13402838501 认证成功
2025-07-20T13:15:03.354+08:00 DEBUG 14207 --- [http-nio-0.0.0.0-8080-exec-2] o.s.security.web.FilterChainProxy        : Secured GET /api/bookings/412
2025-07-20T13:15:03.354+08:00 DEBUG 14207 --- [http-nio-0.0.0.0-8080-exec-2] o.s.web.servlet.DispatcherServlet        : GET "/api/bookings/412", parameters={}
2025-07-20T13:15:03.355+08:00 DEBUG 14207 --- [http-nio-0.0.0.0-8080-exec-2] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.gymbooking.controller.BookingController#getBookingDetail(Long)
2025-07-20T13:15:03.356+08:00  INFO 14207 --- [http-nio-0.0.0.0-8080-exec-2] c.e.g.controller.BookingController       : 🔍 收到获取订单详情请求, ID: 412
2025-07-20T13:15:03.357+08:00 DEBUG 14207 --- [http-nio-0.0.0.0-8080-exec-2] o.s.orm.jpa.JpaTransactionManager        : Creating new transaction with name [org.springframework.data.jpa.repository.support.SimpleJpaRepository.findById]: PROPAGATION_REQUIRED,ISOLATION_DEFAULT,readOnly
2025-07-20T13:15:03.357+08:00 DEBUG 14207 --- [http-nio-0.0.0.0-8080-exec-2] o.s.orm.jpa.JpaTransactionManager        : Opened new EntityManager [SessionImpl(1715116458<open>)] for JPA transaction
2025-07-20T13:15:03.358+08:00 DEBUG 14207 --- [http-nio-0.0.0.0-8080-exec-2] o.s.jdbc.datasource.DataSourceUtils      : Setting JDBC Connection [HikariProxyConnection@43902890 wrapping com.mysql.cj.jdbc.ConnectionImpl@2475f97d] read-only
2025-07-20T13:15:03.358+08:00 DEBUG 14207 --- [http-nio-0.0.0.0-8080-exec-2] o.h.e.t.internal.TransactionImpl         : On TransactionImpl creation, JpaCompliance#isJpaTransactionComplianceEnabled == false
2025-07-20T13:15:03.358+08:00 DEBUG 14207 --- [http-nio-0.0.0.0-8080-exec-2] o.h.e.t.internal.TransactionImpl         : begin
2025-07-20T13:15:03.359+08:00 DEBUG 14207 --- [http-nio-0.0.0.0-8080-exec-2] o.s.orm.jpa.JpaTransactionManager        : Exposing JPA transaction as JDBC [org.springframework.orm.jpa.vendor.HibernateJpaDialect$HibernateConnectionHandle@c5ce899]
2025-07-20T13:15:03.360+08:00 DEBUG 14207 --- [http-nio-0.0.0.0-8080-exec-2] org.hibernate.orm.sql.exec               : Skipping reading Query result cache data: cache-enabled = false, cache-mode = IGNORE
2025-07-20T13:15:03.360+08:00 DEBUG 14207 --- [http-nio-0.0.0.0-8080-exec-2] org.hibernate.SQL                        : 
    select
        o1_0.id,
        o1_0.allow_sharing,
        o1_0.booking_time,
        o1_0.booking_type,
        o1_0.contact_info,
        o1_0.created_at,
        o1_0.current_participants,
        o1_0.description,
        o1_0.end_time,
        o1_0.field_id,
        o1_0.field_name,
        o1_0.max_participants,
        o1_0.order_no,
        o1_0.original_end_time,
        o1_0.original_start_time,
        o1_0.status,
        o1_0.team_name,
        o1_0.total_price,
        o1_0.updated_at,
        o1_0.username,
        o1_0.venue_id,
        o1_0.venue_name 
    from
        `order` o1_0 
    where
        o1_0.id=?
2025-07-20T13:15:03.363+08:00 DEBUG 14207 --- [http-nio-0.0.0.0-8080-exec-2] o.s.orm.jpa.JpaTransactionManager        : Initiating transaction commit
2025-07-20T13:15:03.363+08:00 DEBUG 14207 --- [http-nio-0.0.0.0-8080-exec-2] o.s.orm.jpa.JpaTransactionManager        : Committing JPA transaction on EntityManager [SessionImpl(1715116458<open>)]
2025-07-20T13:15:03.363+08:00 DEBUG 14207 --- [http-nio-0.0.0.0-8080-exec-2] o.h.e.t.internal.TransactionImpl         : committing
2025-07-20T13:15:03.364+08:00 DEBUG 14207 --- [http-nio-0.0.0.0-8080-exec-2] o.s.jdbc.datasource.DataSourceUtils      : Resetting read-only flag of JDBC Connection [HikariProxyConnection@43902890 wrapping com.mysql.cj.jdbc.ConnectionImpl@2475f97d]
2025-07-20T13:15:03.364+08:00 DEBUG 14207 --- [http-nio-0.0.0.0-8080-exec-2] o.s.orm.jpa.JpaTransactionManager        : Closing JPA EntityManager [SessionImpl(1715116458<open>)] after transaction
2025-07-20T13:15:03.365+08:00  INFO 14207 --- [http-nio-0.0.0.0-8080-exec-2] c.e.g.controller.BookingController       : 📦 找到订单: ORD1752988499463998
2025-07-20T13:15:03.365+08:00  INFO 14207 --- [http-nio-0.0.0.0-8080-exec-2] c.e.g.controller.BookingController       : ⏰ 从TimeSlot表获取时间信息
2025-07-20T13:15:03.365+08:00 DEBUG 14207 --- [http-nio-0.0.0.0-8080-exec-2] tor$SharedEntityManagerInvocationHandler : Creating new EntityManager for shared EntityManager invocation
2025-07-20T13:15:03.365+08:00 DEBUG 14207 --- [http-nio-0.0.0.0-8080-exec-2] org.hibernate.orm.sql.ast.create         : Created new SQL alias : ts1_0
2025-07-20T13:15:03.366+08:00 DEBUG 14207 --- [http-nio-0.0.0.0-8080-exec-2] org.hibernate.orm.sql.ast.create         : Registration of TableGroup [StandardTableGroup(com.example.gymbooking.model.TimeSlot(52))] with identifierForTableGroup [com.example.gymbooking.model.TimeSlot] for NavigablePath [com.example.gymbooking.model.TimeSlot] 
2025-07-20T13:15:03.366+08:00 DEBUG 14207 --- [http-nio-0.0.0.0-8080-exec-2] o.h.q.sqm.sql.BaseSqmToSqlAstConverter   : Determining mapping-model type for SqmParameter : org.hibernate.query.sqm.tree.expression.SqmJpaCriteriaParameterWrapper@3e8beac9
2025-07-20T13:15:03.366+08:00 DEBUG 14207 --- [http-nio-0.0.0.0-8080-exec-2] o.h.q.sqm.sql.BaseSqmToSqlAstConverter   : Determining mapping-model type for SqmPath : SqmBasicValuedSimplePath(com.example.gymbooking.model.TimeSlot(52).orderId) 
2025-07-20T13:15:03.366+08:00 DEBUG 14207 --- [http-nio-0.0.0.0-8080-exec-2] org.hibernate.orm.results.graph.AST      : DomainResult Graph:
 \-EntityResultImpl [com.example.gymbooking.model.TimeSlot(52)]
 |  +-BasicFetch [com.example.gymbooking.model.TimeSlot(52).createdAt]
 |  +-BasicFetch [com.example.gymbooking.model.TimeSlot(52).date]
 |  +-BasicFetch [com.example.gymbooking.model.TimeSlot(52).endTime]
 |  +-BasicFetch [com.example.gymbooking.model.TimeSlot(52).orderId]
 |  +-BasicFetch [com.example.gymbooking.model.TimeSlot(52).price]
 |  +-BasicFetch [com.example.gymbooking.model.TimeSlot(52).startTime]
 |  +-BasicFetch [com.example.gymbooking.model.TimeSlot(52).status]
 |  +-BasicFetch [com.example.gymbooking.model.TimeSlot(52).updatedAt]
 |  \-BasicFetch [com.example.gymbooking.model.TimeSlot(52).venueId]

2025-07-20T13:15:03.366+08:00 DEBUG 14207 --- [http-nio-0.0.0.0-8080-exec-2] org.hibernate.orm.sql.ast.tree           : SQL AST Tree:
    SelectStatement {
      FromClause {
        StandardTableGroup (ts1 : com.example.gymbooking.model.TimeSlot(52)) {
          primaryTableReference : time_slots as ts1_0
        }
      }
    }

2025-07-20T13:15:03.367+08:00 DEBUG 14207 --- [http-nio-0.0.0.0-8080-exec-2] org.hibernate.orm.sql.exec               : Skipping reading Query result cache data: cache-enabled = false, cache-mode = NORMAL
2025-07-20T13:15:03.367+08:00 DEBUG 14207 --- [http-nio-0.0.0.0-8080-exec-2] org.hibernate.SQL                        : 
    select
        ts1_0.id,
        ts1_0.created_at,
        ts1_0.date,
        ts1_0.end_time,
        ts1_0.order_id,
        ts1_0.price,
        ts1_0.start_time,
        ts1_0.status,
        ts1_0.updated_at,
        ts1_0.venue_id 
    from
        time_slots ts1_0 
    where
        ts1_0.order_id=?
2025-07-20T13:15:03.376+08:00 DEBUG 14207 --- [http-nio-0.0.0.0-8080-exec-2] org.hibernate.orm.results                : Initializer list:
	  com.example.gymbooking.model.TimeSlot(52) -> EntityJoinedFetchInitializer(com.example.gymbooking.model.TimeSlot(52))@1923531706 (SingleTableEntityPersister(com.example.gymbooking.model.TimeSlot))

2025-07-20T13:15:03.377+08:00  INFO 14207 --- [http-nio-0.0.0.0-8080-exec-2] c.e.g.controller.BookingController       : 找到 0 个关联的时间段
2025-07-20T13:15:03.377+08:00  WARN 14207 --- [http-nio-0.0.0.0-8080-exec-2] c.e.g.controller.BookingController       : ⚠️ 订单 412 没有关联的时间段，使用订单信息
2025-07-20T13:15:03.378+08:00  INFO 14207 --- [http-nio-0.0.0.0-8080-exec-2] c.e.g.controller.BookingController       : 🏟️ 查找场馆信息, venueId: 39
2025-07-20T13:15:03.378+08:00 DEBUG 14207 --- [http-nio-0.0.0.0-8080-exec-2] o.s.orm.jpa.JpaTransactionManager        : Creating new transaction with name [org.springframework.data.jpa.repository.support.SimpleJpaRepository.findById]: PROPAGATION_REQUIRED,ISOLATION_DEFAULT,readOnly
2025-07-20T13:15:03.378+08:00 DEBUG 14207 --- [http-nio-0.0.0.0-8080-exec-2] o.s.orm.jpa.JpaTransactionManager        : Opened new EntityManager [SessionImpl(1523452260<open>)] for JPA transaction
2025-07-20T13:15:03.378+08:00 DEBUG 14207 --- [http-nio-0.0.0.0-8080-exec-2] o.s.jdbc.datasource.DataSourceUtils      : Setting JDBC Connection [HikariProxyConnection@1488276015 wrapping com.mysql.cj.jdbc.ConnectionImpl@2475f97d] read-only
2025-07-20T13:15:03.379+08:00 DEBUG 14207 --- [http-nio-0.0.0.0-8080-exec-2] o.h.e.t.internal.TransactionImpl         : On TransactionImpl creation, JpaCompliance#isJpaTransactionComplianceEnabled == false
2025-07-20T13:15:03.380+08:00 DEBUG 14207 --- [http-nio-0.0.0.0-8080-exec-2] o.h.e.t.internal.TransactionImpl         : begin
2025-07-20T13:15:03.380+08:00 DEBUG 14207 --- [http-nio-0.0.0.0-8080-exec-2] o.s.orm.jpa.JpaTransactionManager        : Exposing JPA transaction as JDBC [org.springframework.orm.jpa.vendor.HibernateJpaDialect$HibernateConnectionHandle@1c4af990]
2025-07-20T13:15:03.381+08:00 DEBUG 14207 --- [http-nio-0.0.0.0-8080-exec-2] org.hibernate.orm.sql.exec               : Skipping reading Query result cache data: cache-enabled = false, cache-mode = IGNORE
2025-07-20T13:15:03.381+08:00 DEBUG 14207 --- [http-nio-0.0.0.0-8080-exec-2] org.hibernate.SQL                        : 
    select
        v1_0.id,
        v1_0.close_time,
        v1_0.created_at,
        v1_0.description,
        v1_0.facilities,
        v1_0.image,
        v1_0.location,
        v1_0.manager_id,
        v1_0.name,
        v1_0.open_time,
        v1_0.price,
        v1_0.status,
        v1_0.support_sharing,
        v1_0.type,
        v1_0.updated_at 
    from
        venues v1_0 
    where
        v1_0.id=?
2025-07-20T13:15:03.383+08:00 DEBUG 14207 --- [http-nio-0.0.0.0-8080-exec-2] o.s.orm.jpa.JpaTransactionManager        : Initiating transaction commit
2025-07-20T13:15:03.383+08:00 DEBUG 14207 --- [http-nio-0.0.0.0-8080-exec-2] o.s.orm.jpa.JpaTransactionManager        : Committing JPA transaction on EntityManager [SessionImpl(1523452260<open>)]
2025-07-20T13:15:03.383+08:00 DEBUG 14207 --- [http-nio-0.0.0.0-8080-exec-2] o.h.e.t.internal.TransactionImpl         : committing
2025-07-20T13:15:03.384+08:00 DEBUG 14207 --- [http-nio-0.0.0.0-8080-exec-2] o.s.jdbc.datasource.DataSourceUtils      : Resetting read-only flag of JDBC Connection [HikariProxyConnection@1488276015 wrapping com.mysql.cj.jdbc.ConnectionImpl@2475f97d]
2025-07-20T13:15:03.385+08:00 DEBUG 14207 --- [http-nio-0.0.0.0-8080-exec-2] o.s.orm.jpa.JpaTransactionManager        : Closing JPA EntityManager [SessionImpl(1523452260<open>)] after transaction
2025-07-20T13:15:03.385+08:00  INFO 14207 --- [http-nio-0.0.0.0-8080-exec-2] c.e.g.controller.BookingController       : 🏟️ 找到场馆位置: 北京市昌平区篮球大道7号
2025-07-20T13:15:03.385+08:00  INFO 14207 --- [http-nio-0.0.0.0-8080-exec-2] c.e.g.controller.BookingController       : ✅ 订单详情获取成功: ORD1752988499463998
2025-07-20T13:15:03.385+08:00 DEBUG 14207 --- [http-nio-0.0.0.0-8080-exec-2] o.s.w.s.m.m.a.HttpEntityMethodProcessor  : Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/yaml]
2025-07-20T13:15:03.386+08:00 DEBUG 14207 --- [http-nio-0.0.0.0-8080-exec-2] o.s.w.s.m.m.a.HttpEntityMethodProcessor  : Writing [{code=200, data={orderNo=ORD1752988499463998, totalPrice=0.0, venueName=星光篮球场, createdAt=2025-07-20T (truncated)...]
2025-07-20T13:15:03.387+08:00 DEBUG 14207 --- [http-nio-0.0.0.0-8080-exec-2] o.s.web.servlet.DispatcherServlet        : Completed 200 OK
2025-07-20T13:15:03.477+08:00 DEBUG 14207 --- [RMI TCP Connection(7)-127.0.0.1] sun.rmi.transport.tcp                    : RMI TCP Connection(7)-127.0.0.1: (port 51779) op = 82
2025-07-20T13:15:03.478+08:00 DEBUG 14207 --- [RMI TCP Connection(7)-127.0.0.1] sun.rmi.transport.tcp                    : RMI TCP Connection(7)-127.0.0.1: (port 51779) op = 80
2025-07-20T13:15:03.478+08:00 DEBUG 14207 --- [RMI TCP Connection(7)-127.0.0.1] sun.rmi.loader                           : RMI TCP Connection(7)-127.0.0.1: name = "javax.management.ObjectName", codebase = ""
2025-07-20T13:15:03.479+08:00 DEBUG 14207 --- [RMI TCP Connection(7)-127.0.0.1] javax.management.remote.rmi              : connectionId=rmi://127.0.0.1  1, name=java.lang:type=OperatingSystem, attribute=ProcessCpuLoad
2025-07-20T13:15:03.480+08:00 DEBUG 14207 --- [RMI TCP Connection(7)-127.0.0.1] sun.rmi.transport.tcp                    : RMI TCP Connection(7)-127.0.0.1: (port 51779) op = 80
2025-07-20T13:15:03.480+08:00 DEBUG 14207 --- [RMI TCP Connection(7)-127.0.0.1] sun.rmi.loader                           : RMI TCP Connection(7)-127.0.0.1: name = "javax.management.ObjectName", codebase = ""
2025-07-20T13:15:03.480+08:00 DEBUG 14207 --- [RMI TCP Connection(7)-127.0.0.1] javax.management.remote.rmi              : connectionId=rmi://127.0.0.1  1, name=java.lang:type=Memory, attribute=HeapMemoryUsage
2025-07-20T13:15:03.481+08:00 DEBUG 14207 --- [RMI TCP Connection(7)-127.0.0.1] sun.rmi.transport.tcp                    : RMI TCP Connection(7)-127.0.0.1: (port 51779) op = 80
2025-07-20T13:15:03.482+08:00 DEBUG 14207 --- [RMI TCP Connection(7)-127.0.0.1] sun.rmi.loader                           : RMI TCP Connection(7)-127.0.0.1: name = "javax.management.ObjectName", codebase = ""
2025-07-20T13:15:03.482+08:00 DEBUG 14207 --- [RMI TCP Connection(7)-127.0.0.1] javax.management.remote.rmi              : connectionId=rmi://127.0.0.1  1, name=java.lang:type=Memory, attribute=HeapMemoryUsage
2025-07-20T13:15:04.486+08:00 DEBUG 14207 --- [RMI TCP Connection(7)-127.0.0.1] sun.rmi.transport.tcp                    : RMI TCP Connection(7)-127.0.0.1: (port 51779) op = 82
2025-07-20T13:15:04.487+08:00 DEBUG 14207 --- [RMI TCP Connection(7)-127.0.0.1] sun.rmi.transport.tcp                    : RMI TCP Connection(7)-127.0.0.1: (port 51779) op = 80
2025-07-20T13:15:04.487+08:00 DEBUG 14207 --- [RMI TCP Connection(7)-127.0.0.1] sun.rmi.loader                           : RMI TCP Connection(7)-127.0.0.1: name = "javax.management.ObjectName", codebase = ""
2025-07-20T13:15:04.487+08:00 DEBUG 14207 --- [RMI TCP Connection(7)-127.0.0.1] javax.management.remote.rmi              : connectionId=rmi://127.0.0.1  1, name=java.lang:type=OperatingSystem, attribute=ProcessCpuLoad
2025-07-20T13:15:04.489+08:00 DEBUG 14207 --- [RMI TCP Connection(7)-127.0.0.1] sun.rmi.transport.tcp                    : RMI TCP Connection(7)-127.0.0.1: (port 51779) op = 80
2025-07-20T13:15:04.491+08:00 DEBUG 14207 --- [RMI TCP Connection(7)-127.0.0.1] sun.rmi.loader                           : RMI TCP Connection(7)-127.0.0.1: name = "javax.management.ObjectName", codebase = ""
2025-07-20T13:15:04.491+08:00 DEBUG 14207 --- [RMI TCP Connection(7)-127.0.0.1] javax.management.remote.rmi              : connectionId=rmi://127.0.0.1  1, name=java.lang:type=Memory, attribute=HeapMemoryUsage
2025-07-20T13:15:04.494+08:00 DEBUG 14207 --- [RMI TCP Connection(7)-127.0.0.1] sun.rmi.transport.tcp                    : RMI TCP Connection(7)-127.0.0.1: (port 51779) op = 80
2025-07-20T13:15:04.495+08:00 DEBUG 14207 --- [RMI TCP Connection(7)-127.0.0.1] sun.rmi.loader                           : RMI TCP Connection(7)-127.0.0.1: name = "javax.management.ObjectName", codebase = ""
2025-07-20T13:15:04.495+08:00 DEBUG 14207 --- [RMI TCP Connection(7)-127.0.0.1] javax.management.remote.rmi              : connectionId=rmi://127.0.0.1  1, name=java.lang:type=Memory, attribute=HeapMemoryUsage
