/**
 * 数据结构检查工具
 * 用于检查时间段和场馆数据的完整性
 */

// 检查时间段数据结构
export function checkTimeSlotStructure(timeSlots) {
  console.log('🔍 检查时间段数据结构')
  
  const analysis = {
    totalSlots: timeSlots.length,
    slotsWithPrice: 0,
    slotsWithoutPrice: 0,
    priceValues: [],
    priceTypes: [],
    sampleSlots: [],
    issues: []
  }
  
  if (timeSlots.length === 0) {
    analysis.issues.push('没有时间段数据')
    return analysis
  }
  
  timeSlots.forEach((slot, index) => {
    // 检查基本字段
    const slotInfo = {
      index: index,
      id: slot.id,
      startTime: slot.startTime,
      endTime: slot.endTime,
      status: slot.status,
      hasPrice: slot.price !== undefined,
      priceValue: slot.price,
      priceType: typeof slot.price,
      hasPricePerHour: slot.pricePerHour !== undefined,
      pricePerHourValue: slot.pricePerHour
    }
    
    if (slot.price !== undefined) {
      analysis.slotsWithPrice++
      analysis.priceValues.push(slot.price)
      analysis.priceTypes.push(typeof slot.price)
    } else {
      analysis.slotsWithoutPrice++
    }
    
    // 保存前5个时间段作为样本
    if (index < 5) {
      analysis.sampleSlots.push(slotInfo)
    }
    
    // 检查问题
    if (slot.price === undefined && slot.pricePerHour === undefined) {
      analysis.issues.push(`时间段${index + 1}(${slot.startTime}-${slot.endTime})缺少价格信息`)
    }
    
    if (slot.price === 0) {
      analysis.issues.push(`时间段${index + 1}(${slot.startTime}-${slot.endTime})价格为0`)
    }
  })
  
  // 统计价格类型
  const priceTypeStats = {}
  analysis.priceTypes.forEach(type => {
    priceTypeStats[type] = (priceTypeStats[type] || 0) + 1
  })
  analysis.priceTypeStats = priceTypeStats
  
  // 计算价格范围
  if (analysis.priceValues.length > 0) {
    const numericPrices = analysis.priceValues.filter(p => !isNaN(p)).map(p => parseFloat(p))
    if (numericPrices.length > 0) {
      analysis.priceRange = {
        min: Math.min(...numericPrices),
        max: Math.max(...numericPrices),
        avg: numericPrices.reduce((a, b) => a + b, 0) / numericPrices.length
      }
    }
  }
  
  console.log('📊 时间段数据结构分析:', analysis)
  return analysis
}

// 检查场馆数据结构
export function checkVenueStructure(venue) {
  console.log('🔍 检查场馆数据结构')
  
  const analysis = {
    hasVenue: venue !== null && venue !== undefined,
    venueId: venue?.id,
    venueName: venue?.name,
    hasPrice: venue?.price !== undefined,
    priceValue: venue?.price,
    priceType: typeof venue?.price,
    priceIsZero: venue?.price === 0,
    priceIsNull: venue?.price === null,
    issues: []
  }
  
  if (!analysis.hasVenue) {
    analysis.issues.push('场馆数据缺失')
    return analysis
  }
  
  if (!analysis.hasPrice) {
    analysis.issues.push('场馆价格字段缺失')
  } else if (analysis.priceIsZero) {
    analysis.issues.push('场馆价格为0')
  } else if (analysis.priceIsNull) {
    analysis.issues.push('场馆价格为null')
  } else if (isNaN(venue.price)) {
    analysis.issues.push('场馆价格不是有效数字')
  }
  
  console.log('📊 场馆数据结构分析:', analysis)
  return analysis
}

// 模拟价格计算过程
export function simulatePriceCalculation(timeSlots, venue) {
  console.log('🔍 模拟价格计算过程')
  
  const simulation = {
    selectedSlots: timeSlots,
    venue: venue,
    calculations: [],
    totalPrice: 0,
    issues: []
  }
  
  timeSlots.forEach((slot, index) => {
    const calc = {
      slotIndex: index + 1,
      timeRange: `${slot.startTime}-${slot.endTime}`,
      method: '',
      price: 0,
      success: false
    }
    
    // 模拟getSlotPrice逻辑
    if (slot.price && slot.price > 0) {
      calc.price = parseFloat(slot.price)
      calc.method = 'slot.price'
      calc.success = true
    } else if (slot.pricePerHour && slot.pricePerHour > 0) {
      calc.price = parseFloat(slot.pricePerHour)
      calc.method = 'slot.pricePerHour'
      calc.success = true
    } else if (venue?.price && venue.price > 0) {
      calc.price = parseFloat(venue.price) / 2
      calc.method = 'venue.price/2'
      calc.success = true
    } else {
      calc.price = 60
      calc.method = 'default'
      calc.success = false
      simulation.issues.push(`时间段${index + 1}使用默认价格`)
    }
    
    simulation.calculations.push(calc)
    simulation.totalPrice += calc.price
  })
  
  if (simulation.totalPrice === 0) {
    simulation.issues.push('总价格为0')
  }
  
  console.log('📊 价格计算模拟结果:', simulation)
  return simulation
}

// 检查API响应数据
export function checkApiResponseData(response, apiName) {
  console.log(`🔍 检查${apiName}API响应数据`)
  
  const analysis = {
    apiName: apiName,
    hasResponse: response !== null && response !== undefined,
    statusCode: response?.statusCode,
    hasData: response?.data !== undefined,
    dataType: typeof response?.data,
    dataIsArray: Array.isArray(response?.data),
    dataLength: Array.isArray(response?.data) ? response?.data.length : 0,
    sampleData: null,
    issues: []
  }
  
  if (!analysis.hasResponse) {
    analysis.issues.push('API响应为空')
    return analysis
  }
  
  if (analysis.statusCode !== 200) {
    analysis.issues.push(`API响应状态码异常: ${analysis.statusCode}`)
  }
  
  if (!analysis.hasData) {
    analysis.issues.push('API响应没有data字段')
  } else if (analysis.dataIsArray && analysis.dataLength === 0) {
    analysis.issues.push('API响应data为空数组')
  } else if (analysis.dataIsArray && analysis.dataLength > 0) {
    // 检查数组第一个元素的结构
    analysis.sampleData = response.data[0]
    
    if (apiName.includes('时间段') || apiName.includes('timeslot')) {
      // 检查时间段数据必需字段
      const requiredFields = ['id', 'startTime', 'endTime', 'status']
      const missingFields = requiredFields.filter(field => 
        analysis.sampleData[field] === undefined
      )
      
      if (missingFields.length > 0) {
        analysis.issues.push(`时间段数据缺少字段: ${missingFields.join(', ')}`)
      }
      
      if (analysis.sampleData.price === undefined) {
        analysis.issues.push('时间段数据缺少price字段')
      }
    }
  }
  
  console.log(`📊 ${apiName}API响应数据分析:`, analysis)
  return analysis
}

// 综合数据完整性检查
export async function comprehensiveDataCheck(venueId, date, venueStore) {
  console.log('🚀 开始综合数据完整性检查')
  
  const checkResult = {
    timestamp: new Date().toISOString(),
    venueId: venueId,
    date: date,
    venueCheck: null,
    timeSlotsCheck: null,
    priceCalculationCheck: null,
    overallIssues: [],
    recommendations: []
  }
  
  try {
    // 1. 检查场馆数据
    const venue = venueStore.venueDetail
    checkResult.venueCheck = checkVenueStructure(venue)
    
    // 2. 检查时间段数据
    const timeSlots = venueStore.timeSlots || []
    checkResult.timeSlotsCheck = checkTimeSlotStructure(timeSlots)
    
    // 3. 模拟价格计算
    if (timeSlots.length > 0) {
      checkResult.priceCalculationCheck = simulatePriceCalculation(timeSlots.slice(0, 4), venue)
    }
    
    // 4. 收集所有问题
    if (checkResult.venueCheck.issues.length > 0) {
      checkResult.overallIssues.push(...checkResult.venueCheck.issues)
    }
    if (checkResult.timeSlotsCheck.issues.length > 0) {
      checkResult.overallIssues.push(...checkResult.timeSlotsCheck.issues)
    }
    if (checkResult.priceCalculationCheck?.issues.length > 0) {
      checkResult.overallIssues.push(...checkResult.priceCalculationCheck.issues)
    }
    
    // 5. 生成建议
    if (checkResult.venueCheck.priceIsZero || !checkResult.venueCheck.hasPrice) {
      checkResult.recommendations.push('需要设置场馆价格')
    }
    if (checkResult.timeSlotsCheck.slotsWithoutPrice > 0) {
      checkResult.recommendations.push('需要为时间段设置价格')
    }
    if (checkResult.timeSlotsCheck.totalSlots === 0) {
      checkResult.recommendations.push('需要生成时间段数据')
    }
    
    console.log('📋 综合数据完整性检查结果:')
    console.log('🏢 场馆检查:', checkResult.venueCheck)
    console.log('⏰ 时间段检查:', checkResult.timeSlotsCheck)
    console.log('💰 价格计算检查:', checkResult.priceCalculationCheck)
    console.log('❌ 总体问题:', checkResult.overallIssues)
    console.log('💡 建议:', checkResult.recommendations)
    
  } catch (error) {
    console.error('❌ 综合数据检查失败:', error)
    checkResult.error = error.message
  }
  
  return checkResult
}

// 实时数据监控
export function startDataMonitoring(venueStore) {
  console.log('🔍 启动实时数据监控')
  
  const monitor = {
    isActive: false,
    intervalId: null,
    lastVenueData: null,
    lastTimeSlotsData: null,
    changeLog: [],
    
    start() {
      if (this.isActive) {
        console.log('⚠️ 数据监控已在运行')
        return
      }
      
      this.isActive = true
      this.lastVenueData = JSON.stringify(venueStore.venueDetail)
      this.lastTimeSlotsData = JSON.stringify(venueStore.timeSlots)
      
      this.intervalId = setInterval(() => {
        const currentVenueData = JSON.stringify(venueStore.venueDetail)
        const currentTimeSlotsData = JSON.stringify(venueStore.timeSlots)
        
        if (currentVenueData !== this.lastVenueData) {
          const change = {
            timestamp: new Date().toISOString(),
            type: 'VENUE_DATA_CHANGE',
            before: this.lastVenueData,
            after: currentVenueData
          }
          this.changeLog.push(change)
          console.log('🔄 检测到场馆数据变化:', change)
          this.lastVenueData = currentVenueData
        }
        
        if (currentTimeSlotsData !== this.lastTimeSlotsData) {
          const change = {
            timestamp: new Date().toISOString(),
            type: 'TIMESLOTS_DATA_CHANGE',
            before: this.lastTimeSlotsData,
            after: currentTimeSlotsData
          }
          this.changeLog.push(change)
          console.log('🔄 检测到时间段数据变化:', change)
          this.lastTimeSlotsData = currentTimeSlotsData
        }
      }, 2000)
      
      console.log('✅ 实时数据监控已启动')
    },
    
    stop() {
      if (!this.isActive) {
        console.log('⚠️ 数据监控未在运行')
        return
      }
      
      this.isActive = false
      if (this.intervalId) {
        clearInterval(this.intervalId)
        this.intervalId = null
      }
      
      console.log('🛑 实时数据监控已停止')
      return this.changeLog
    }
  }
  
  monitor.start()
  return monitor
}
