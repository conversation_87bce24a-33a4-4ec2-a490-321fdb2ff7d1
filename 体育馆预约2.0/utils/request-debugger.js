/**
 * 请求调试工具
 * 专门用于调试HTTP请求的数据传递问题
 */

// 测试POST请求数据传递
export async function testPostDataTransmission(testData) {
  console.log('🔍 测试POST请求数据传递')
  
  const results = {
    originalData: testData,
    serializedData: null,
    requestOptions: null,
    actualSent: null,
    issues: []
  }
  
  try {
    // 1. 测试数据序列化
    console.log('📊 原始数据:', testData)
    results.serializedData = JSON.stringify(testData)
    console.log('📊 序列化后:', results.serializedData)
    
    // 2. 模拟请求选项构建
    const requestOptions = {
      url: 'http://localhost:8080/api/bookings',
      method: 'POST',
      data: testData,
      header: {
        'Content-Type': 'application/json'
      }
    }
    results.requestOptions = requestOptions
    console.log('📊 请求选项:', requestOptions)
    
    // 3. 检查uni.request的实际行为
    return new Promise((resolve) => {
      // 保存原始的uni.request
      const originalRequest = uni.request
      
      // 临时替换uni.request来捕获实际发送的数据
      uni.request = function(options) {
        console.log('🚀 [请求拦截] 实际发送的选项:', options)
        console.log('🚀 [请求拦截] 实际发送的数据:', options.data)
        console.log('🚀 [请求拦截] 数据类型:', typeof options.data)
        
        results.actualSent = {
          data: options.data,
          dataType: typeof options.data,
          isString: typeof options.data === 'string',
          isObject: typeof options.data === 'object',
          stringified: JSON.stringify(options.data)
        }
        
        // 恢复原始方法
        uni.request = originalRequest
        
        // 不实际发送请求，直接返回模拟响应
        if (options.success) {
          setTimeout(() => {
            options.success({
              statusCode: 200,
              data: { message: '测试成功', receivedData: options.data }
            })
          }, 100)
        }
        
        resolve(results)
      }
      
      // 发起测试请求
      uni.request(requestOptions)
    })
    
  } catch (error) {
    console.error('❌ 测试POST数据传递失败:', error)
    results.issues.push(error.message)
    return results
  }
}

// 检查价格数据的各种格式
export function testPriceFormats(price) {
  console.log('🔍 测试价格格式')
  
  const tests = {
    original: {
      value: price,
      type: typeof price
    },
    asNumber: {
      value: Number(price),
      type: typeof Number(price),
      isNaN: isNaN(Number(price))
    },
    asString: {
      value: String(price),
      type: typeof String(price)
    },
    asFloat: {
      value: parseFloat(price),
      type: typeof parseFloat(price),
      isNaN: isNaN(parseFloat(price))
    },
    inObject: {
      value: { price: price },
      serialized: JSON.stringify({ price: price })
    },
    afterSerialization: null
  }
  
  // 测试序列化和反序列化
  try {
    const serialized = JSON.stringify({ price: price })
    const deserialized = JSON.parse(serialized)
    tests.afterSerialization = {
      serialized: serialized,
      deserializedPrice: deserialized.price,
      typeAfterDeserialization: typeof deserialized.price,
      valueChanged: deserialized.price !== price
    }
  } catch (error) {
    tests.afterSerialization = { error: error.message }
  }
  
  console.log('📊 价格格式测试结果:', tests)
  return tests
}

// 模拟后端接收数据
export function simulateBackendReceive(frontendData) {
  console.log('🔍 模拟后端接收数据')
  
  const simulation = {
    received: frontendData,
    priceExtraction: null,
    issues: []
  }
  
  try {
    // 模拟后端的价格提取逻辑
    let extractedPrice = null
    
    if (frontendData.hasOwnProperty('price')) {
      console.log('📊 发现price字段:', frontendData.price)
      
      try {
        extractedPrice = Double.valueOf ? Double.valueOf(frontendData.price.toString()) : parseFloat(frontendData.price.toString())
        console.log('📊 提取的价格:', extractedPrice)
      } catch (error) {
        console.error('❌ 价格格式错误:', error)
        simulation.issues.push('价格格式错误')
      }
    } else {
      console.log('❌ 未发现price字段')
      simulation.issues.push('price字段缺失')
    }
    
    simulation.priceExtraction = {
      found: frontendData.hasOwnProperty('price'),
      originalValue: frontendData.price,
      originalType: typeof frontendData.price,
      extractedValue: extractedPrice,
      extractedType: typeof extractedPrice,
      isZero: extractedPrice === 0,
      isNull: extractedPrice === null,
      isUndefined: extractedPrice === undefined
    }
    
  } catch (error) {
    console.error('❌ 模拟后端接收失败:', error)
    simulation.issues.push(error.message)
  }
  
  console.log('📊 后端接收模拟结果:', simulation)
  return simulation
}

// 完整的请求-响应调试
export async function debugCompleteRequestResponse(bookingData) {
  console.log('🚀 开始完整的请求-响应调试')
  
  const debug = {
    step1_dataPreparation: null,
    step2_requestTransmission: null,
    step3_backendSimulation: null,
    overallIssues: []
  }
  
  try {
    // 步骤1: 数据准备
    console.log('\n=== 步骤1: 数据准备 ===')
    debug.step1_dataPreparation = testPriceFormats(bookingData.price)
    
    // 步骤2: 请求传输
    console.log('\n=== 步骤2: 请求传输 ===')
    debug.step2_requestTransmission = await testPostDataTransmission(bookingData)
    
    // 步骤3: 后端模拟
    console.log('\n=== 步骤3: 后端接收模拟 ===')
    const actualSentData = debug.step2_requestTransmission.actualSent?.data || bookingData
    debug.step3_backendSimulation = simulateBackendReceive(actualSentData)
    
    // 收集所有问题
    if (debug.step2_requestTransmission.issues.length > 0) {
      debug.overallIssues.push(...debug.step2_requestTransmission.issues)
    }
    if (debug.step3_backendSimulation.issues.length > 0) {
      debug.overallIssues.push(...debug.step3_backendSimulation.issues)
    }
    
    // 检查价格是否会变成0
    const finalPrice = debug.step3_backendSimulation.priceExtraction?.extractedValue
    if (finalPrice === 0 || finalPrice === null || finalPrice === undefined) {
      debug.overallIssues.push('最终价格为0或无效')
    }
    
    console.log('\n📋 完整调试结果:')
    console.log('📊 数据准备:', debug.step1_dataPreparation.original)
    console.log('📊 请求传输:', debug.step2_requestTransmission.actualSent)
    console.log('📊 后端接收:', debug.step3_backendSimulation.priceExtraction)
    console.log('📊 总体问题:', debug.overallIssues)
    
  } catch (error) {
    console.error('❌ 完整调试失败:', error)
    debug.overallIssues.push(error.message)
  }
  
  return debug
}

// 快速价格传递测试
export async function quickPriceTest(price) {
  console.log('⚡ 快速价格传递测试')
  
  const testData = {
    venueId: 25,
    date: '2025-07-19',
    startTime: '18:00',
    endTime: '20:00',
    price: price,
    bookingType: 'EXCLUSIVE'
  }
  
  const result = await debugCompleteRequestResponse(testData)
  
  const summary = {
    originalPrice: price,
    finalPrice: result.step3_backendSimulation?.priceExtraction?.extractedValue,
    pricePreserved: result.step3_backendSimulation?.priceExtraction?.extractedValue === price,
    issues: result.overallIssues,
    success: result.overallIssues.length === 0 && result.step3_backendSimulation?.priceExtraction?.extractedValue > 0
  }
  
  console.log('⚡ 快速测试结果:', summary)
  return summary
}

// 实时请求监控
export function startRequestMonitoring() {
  console.log('🔍 启动实时请求监控')
  
  const originalRequest = uni.request
  const requestLog = []
  
  uni.request = function(options) {
    const timestamp = new Date().toISOString()
    const logEntry = {
      timestamp,
      url: options.url,
      method: options.method,
      data: options.data,
      dataType: typeof options.data,
      hasPrice: options.data && options.data.price !== undefined,
      priceValue: options.data?.price,
      priceType: typeof options.data?.price
    }
    
    requestLog.push(logEntry)
    console.log('📡 [请求监控]', logEntry)
    
    // 特别关注预约请求
    if (options.url && options.url.includes('/bookings')) {
      console.log('🎯 [预约请求] 详细信息:', {
        完整数据: options.data,
        价格字段: options.data?.price,
        价格类型: typeof options.data?.price,
        序列化测试: JSON.stringify(options.data)
      })
    }
    
    return originalRequest.call(this, options)
  }
  
  return {
    stop() {
      uni.request = originalRequest
      console.log('🛑 请求监控已停止')
      return requestLog
    },
    getLog() {
      return requestLog
    }
  }
}
