/**
 * 支付页面调试工具
 * 用于诊断和修复订单金额显示问题
 */

// 订单金额计算调试
export function debugOrderAmount(orderInfo) {
  console.log('🔍 [支付调试] 开始分析订单金额问题')
  console.log('📋 订单信息:', orderInfo)
  
  if (!orderInfo) {
    console.log('❌ 订单信息为空')
    return { success: false, error: '订单信息为空' }
  }

  // 检查各种价格字段
  const priceFields = {
    totalPrice: orderInfo.totalPrice,
    paymentAmount: orderInfo.paymentAmount,
    price: orderInfo.price,
    amount: orderInfo.amount
  }
  
  console.log('💰 价格字段检查:', priceFields)
  
  // 检查时间字段
  const timeFields = {
    startTime: orderInfo.startTime,
    endTime: orderInfo.endTime,
    bookingTime: orderInfo.bookingTime,
    bookingDate: orderInfo.bookingDate
  }
  
  console.log('⏰ 时间字段检查:', timeFields)
  
  // 检查订单类型
  const typeFields = {
    bookingType: orderInfo.bookingType,
    isVirtualOrder: orderInfo.isVirtualOrder,
    orderType: orderInfo.orderType
  }
  
  console.log('🏷️ 类型字段检查:', typeFields)
  
  // 尝试计算正确的价格
  let calculatedPrice = 0
  let calculationMethod = ''
  
  // 方法1：使用现有价格字段
  if (orderInfo.totalPrice && orderInfo.totalPrice > 0) {
    calculatedPrice = orderInfo.totalPrice
    calculationMethod = 'totalPrice字段'
  } else if (orderInfo.paymentAmount && orderInfo.paymentAmount > 0) {
    calculatedPrice = orderInfo.paymentAmount
    calculationMethod = 'paymentAmount字段'
  } else if (orderInfo.price && orderInfo.price > 0) {
    calculatedPrice = orderInfo.price
    calculationMethod = 'price字段'
  }
  
  // 方法2：根据时间段计算
  if (calculatedPrice === 0 && orderInfo.startTime && orderInfo.endTime) {
    try {
      const startHour = parseInt(orderInfo.startTime.split(':')[0])
      const startMinute = parseInt(orderInfo.startTime.split(':')[1])
      const endHour = parseInt(orderInfo.endTime.split(':')[0])
      const endMinute = parseInt(orderInfo.endTime.split(':')[1])
      
      const duration = (endHour + endMinute / 60) - (startHour + startMinute / 60)
      const hourlyRate = 120 // 标准费率
      
      calculatedPrice = duration * hourlyRate
      calculationMethod = `时间段计算 (${duration}小时 × ${hourlyRate}元/小时)`
      
      console.log(`⏱️ 时间段计算: ${orderInfo.startTime}-${orderInfo.endTime} = ${duration}小时`)
    } catch (error) {
      console.error('时间段计算失败:', error)
    }
  }
  
  // 方法3：根据订单类型使用默认价格
  if (calculatedPrice === 0) {
    if (orderInfo.bookingType === 'SHARED' || orderInfo.isVirtualOrder) {
      calculatedPrice = 120 // 拼场默认价格
      calculationMethod = '拼场默认价格'
    } else {
      calculatedPrice = 240 // 独享默认价格（2小时）
      calculationMethod = '独享默认价格'
    }
  }
  
  const result = {
    success: true,
    originalPrice: orderInfo.totalPrice || 0,
    calculatedPrice: calculatedPrice,
    calculationMethod: calculationMethod,
    recommendation: calculatedPrice > 0 ? '使用计算价格' : '需要检查后端数据'
  }
  
  console.log('✅ [支付调试] 分析结果:', result)
  return result
}

// 时间段刷新调试
export function debugTimeSlotRefresh(venueId, date, venueStore) {
  console.log('🔄 [时间段调试] 开始分析时间段刷新问题')
  console.log('📍 参数:', { venueId, date })
  
  if (!venueStore) {
    console.log('❌ VenueStore未提供')
    return { success: false, error: 'VenueStore未提供' }
  }
  
  // 检查当前时间段状态
  const currentTimeSlots = venueStore.timeSlots || []
  console.log('📊 当前时间段数量:', currentTimeSlots.length)
  console.log('📊 当前时间段状态:', currentTimeSlots.map(slot => ({
    id: slot.id,
    time: `${slot.startTime}-${slot.endTime}`,
    status: slot.status
  })))
  
  return {
    success: true,
    currentSlotsCount: currentTimeSlots.length,
    currentSlots: currentTimeSlots,
    recommendation: '执行强制刷新'
  }
}

// 执行时间段强制刷新
export async function forceRefreshTimeSlots(venueId, date, venueStore) {
  console.log('🚀 [时间段调试] 执行强制刷新')
  
  try {
    // 步骤1：清除缓存
    console.log('🧹 步骤1: 清除缓存')
    venueStore.clearTimeSlots()
    
    // 步骤2：等待缓存清除
    console.log('⏳ 步骤2: 等待缓存清除')
    await new Promise(resolve => setTimeout(resolve, 200))
    
    // 步骤3：强制获取新数据
    console.log('📡 步骤3: 强制获取新数据')
    await venueStore.getTimeSlots(venueId, date, true)
    
    // 步骤4：验证结果
    console.log('✅ 步骤4: 验证结果')
    const newTimeSlots = venueStore.timeSlots || []
    console.log('📊 刷新后时间段数量:', newTimeSlots.length)
    console.log('📊 刷新后时间段状态:', newTimeSlots.map(slot => ({
      id: slot.id,
      time: `${slot.startTime}-${slot.endTime}`,
      status: slot.status
    })))
    
    return {
      success: true,
      newSlotsCount: newTimeSlots.length,
      newSlots: newTimeSlots
    }
  } catch (error) {
    console.error('❌ 强制刷新失败:', error)
    return {
      success: false,
      error: error.message
    }
  }
}

// 综合调试工具
export function runPaymentDiagnostics(orderInfo, venueId, date, venueStore) {
  console.log('🔍 [综合调试] 开始支付相关问题诊断')
  
  const results = {
    orderAmount: debugOrderAmount(orderInfo),
    timeSlotRefresh: debugTimeSlotRefresh(venueId, date, venueStore)
  }
  
  console.log('📋 [综合调试] 诊断结果:', results)
  return results
}
