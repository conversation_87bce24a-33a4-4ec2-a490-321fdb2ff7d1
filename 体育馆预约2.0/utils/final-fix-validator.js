/**
 * 最终修复验证工具
 * 用于验证所有修复是否真正生效
 */

// 验证价格传递是否修复
export async function validatePriceFix(bookingData) {
  console.log('🔍 验证价格传递修复')
  
  const validation = {
    timestamp: new Date().toISOString(),
    bookingData: bookingData,
    priceChecks: {
      hasPrice: false,
      priceIsNumber: false,
      priceIsPositive: false,
      priceIsReasonable: false
    },
    issues: [],
    success: false
  }
  
  // 检查价格字段存在性
  if (bookingData.price !== undefined && bookingData.price !== null) {
    validation.priceChecks.hasPrice = true
  } else {
    validation.issues.push('价格字段缺失')
  }
  
  // 检查价格是否为有效数字
  if (validation.priceChecks.hasPrice) {
    const price = parseFloat(bookingData.price)
    if (!isNaN(price)) {
      validation.priceChecks.priceIsNumber = true
      
      // 检查价格是否为正数
      if (price > 0) {
        validation.priceChecks.priceIsPositive = true
        
        // 检查价格是否合理（30-1000元之间）
        if (price >= 30 && price <= 1000) {
          validation.priceChecks.priceIsReasonable = true
        } else {
          validation.issues.push(`价格不在合理范围内: ¥${price}`)
        }
      } else {
        validation.issues.push('价格不是正数')
      }
    } else {
      validation.issues.push('价格不是有效数字')
    }
  }
  
  // 综合评估
  validation.success = Object.values(validation.priceChecks).every(check => check)
  
  console.log('📊 价格传递修复验证结果:', validation)
  return validation
}

// 验证时间段刷新是否修复
export async function validateTimeSlotRefresh(venueId, date, venueStore, expectedReservedSlots = []) {
  console.log('🔍 验证时间段刷新修复')
  
  const validation = {
    timestamp: new Date().toISOString(),
    venueId: venueId,
    date: date,
    beforeRefresh: null,
    afterRefresh: null,
    refreshSuccess: false,
    dataChanged: false,
    expectedReservedFound: false,
    issues: []
  }
  
  try {
    // 记录刷新前状态
    const beforeSlots = venueStore.timeSlots || []
    validation.beforeRefresh = {
      totalSlots: beforeSlots.length,
      reservedSlots: beforeSlots.filter(slot => slot.status === 'RESERVED').length,
      availableSlots: beforeSlots.filter(slot => slot.status === 'AVAILABLE').length
    }
    
    console.log('📊 刷新前状态:', validation.beforeRefresh)
    
    // 执行强制刷新
    console.log('🔄 执行强制刷新...')
    const refreshResult = await venueStore.forceRefreshTimeSlots(venueId, date)
    validation.refreshSuccess = refreshResult.success
    
    if (!refreshResult.success) {
      validation.issues.push('刷新操作失败')
    }
    
    // 等待数据更新
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 记录刷新后状态
    const afterSlots = venueStore.timeSlots || []
    validation.afterRefresh = {
      totalSlots: afterSlots.length,
      reservedSlots: afterSlots.filter(slot => slot.status === 'RESERVED').length,
      availableSlots: afterSlots.filter(slot => slot.status === 'AVAILABLE').length
    }
    
    console.log('📊 刷新后状态:', validation.afterRefresh)
    
    // 检查数据是否真的改变了
    const beforeStr = JSON.stringify(validation.beforeRefresh)
    const afterStr = JSON.stringify(validation.afterRefresh)
    validation.dataChanged = beforeStr !== afterStr
    
    if (!validation.dataChanged && validation.beforeRefresh.totalSlots > 0) {
      validation.issues.push('数据没有发生变化')
    }
    
    // 检查预期的已预约时间段是否存在
    if (expectedReservedSlots.length > 0) {
      const foundReservedSlots = afterSlots.filter(slot => 
        slot.status === 'RESERVED' && 
        expectedReservedSlots.some(expected => 
          expected.startTime === slot.startTime && expected.endTime === slot.endTime
        )
      )
      
      validation.expectedReservedFound = foundReservedSlots.length === expectedReservedSlots.length
      
      if (!validation.expectedReservedFound) {
        validation.issues.push(`预期的已预约时间段未找到: 期望${expectedReservedSlots.length}个，实际找到${foundReservedSlots.length}个`)
      }
    }
    
    // 检查基本数据完整性
    if (validation.afterRefresh.totalSlots === 0) {
      validation.issues.push('刷新后没有获取到时间段数据')
    }
    
  } catch (error) {
    console.error('❌ 验证时间段刷新失败:', error)
    validation.issues.push(`验证过程出错: ${error.message}`)
  }
  
  console.log('📊 时间段刷新修复验证结果:', validation)
  return validation
}

// 端到端验证
export async function endToEndValidation(bookingData, venueId, date, venueStore, selectedSlots) {
  console.log('🚀 开始端到端验证')
  
  const validation = {
    timestamp: new Date().toISOString(),
    priceValidation: null,
    timeSlotValidation: null,
    overallSuccess: false,
    summary: ''
  }
  
  try {
    // 1. 验证价格传递修复
    console.log('\n=== 验证1: 价格传递修复 ===')
    validation.priceValidation = await validatePriceFix(bookingData)
    
    // 2. 验证时间段刷新修复
    console.log('\n=== 验证2: 时间段刷新修复 ===')
    validation.timeSlotValidation = await validateTimeSlotRefresh(
      venueId, 
      date, 
      venueStore, 
      selectedSlots
    )
    
    // 3. 综合评估
    const priceOk = validation.priceValidation.success
    const timeSlotOk = validation.timeSlotValidation.refreshSuccess && 
                      validation.timeSlotValidation.issues.length === 0
    
    validation.overallSuccess = priceOk && timeSlotOk
    
    // 4. 生成总结
    const priceStatus = priceOk ? '✅ 修复成功' : '❌ 仍有问题'
    const timeSlotStatus = timeSlotOk ? '✅ 修复成功' : '❌ 仍有问题'
    
    validation.summary = `价格传递: ${priceStatus}, 时间段刷新: ${timeSlotStatus}`
    
    console.log('\n📋 端到端验证结果:')
    console.log(`💰 价格传递: ${priceStatus}`)
    if (!priceOk) {
      console.log(`   问题: ${validation.priceValidation.issues.join(', ')}`)
    }
    
    console.log(`⏰ 时间段刷新: ${timeSlotStatus}`)
    if (!timeSlotOk) {
      console.log(`   问题: ${validation.timeSlotValidation.issues.join(', ')}`)
    }
    
    console.log(`🎯 总体评估: ${validation.overallSuccess ? '🎉 全部修复成功' : '⚠️ 仍有问题需要解决'}`)
    
  } catch (error) {
    console.error('❌ 端到端验证失败:', error)
    validation.error = error.message
  }
  
  return validation
}

// 生成修复报告
export function generateFixReport(validationResult) {
  console.log('📋 生成修复报告')
  
  const report = {
    timestamp: new Date().toISOString(),
    title: '预约系统问题修复报告',
    summary: validationResult.summary,
    overallSuccess: validationResult.overallSuccess,
    details: {
      priceTransmission: {
        status: validationResult.priceValidation?.success ? 'FIXED' : 'FAILED',
        issues: validationResult.priceValidation?.issues || [],
        priceValue: validationResult.priceValidation?.bookingData?.price
      },
      timeSlotRefresh: {
        status: validationResult.timeSlotValidation?.refreshSuccess ? 'FIXED' : 'FAILED',
        issues: validationResult.timeSlotValidation?.issues || [],
        beforeRefresh: validationResult.timeSlotValidation?.beforeRefresh,
        afterRefresh: validationResult.timeSlotValidation?.afterRefresh
      }
    },
    recommendations: []
  }
  
  // 生成建议
  if (!validationResult.overallSuccess) {
    if (report.details.priceTransmission.status === 'FAILED') {
      report.recommendations.push('需要检查时间段价格数据和场馆价格设置')
      report.recommendations.push('确保前端价格计算逻辑正确')
    }
    
    if (report.details.timeSlotRefresh.status === 'FAILED') {
      report.recommendations.push('需要检查后端时间段状态更新逻辑')
      report.recommendations.push('确保前端缓存清除机制有效')
    }
  } else {
    report.recommendations.push('所有问题已修复，系统运行正常')
  }
  
  console.log('📊 修复报告:', report)
  return report
}

// 快速验证工具
export async function quickValidation(bookingData, venueId, date, venueStore) {
  console.log('⚡ 快速验证修复效果')
  
  const result = {
    timestamp: new Date().toISOString(),
    priceOk: false,
    timeSlotOk: false,
    overallOk: false,
    issues: []
  }
  
  try {
    // 快速检查价格
    const price = parseFloat(bookingData.price)
    if (isNaN(price) || price <= 0) {
      result.issues.push('价格仍然无效')
    } else {
      result.priceOk = true
    }
    
    // 快速检查时间段数据
    const timeSlots = venueStore.timeSlots || []
    if (timeSlots.length === 0) {
      result.issues.push('没有时间段数据')
    } else {
      const reservedSlots = timeSlots.filter(slot => slot.status === 'RESERVED')
      if (reservedSlots.length > 0) {
        result.timeSlotOk = true
      } else {
        result.issues.push('没有已预约的时间段')
      }
    }
    
    result.overallOk = result.priceOk && result.timeSlotOk
    
    console.log('⚡ 快速验证结果:', {
      价格: result.priceOk ? '✅' : '❌',
      时间段: result.timeSlotOk ? '✅' : '❌',
      总体: result.overallOk ? '✅' : '❌',
      问题: result.issues
    })
    
  } catch (error) {
    console.error('❌ 快速验证失败:', error)
    result.issues.push(error.message)
  }
  
  return result
}

// 持续监控修复效果
export function startFixValidationMonitoring(bookingData, venueId, date, venueStore) {
  console.log('🔍 启动修复效果持续监控')
  
  const monitor = {
    isActive: false,
    intervalId: null,
    validationLog: [],
    
    start() {
      if (this.isActive) return
      
      this.isActive = true
      this.intervalId = setInterval(async () => {
        const validation = await quickValidation(bookingData, venueId, date, venueStore)
        this.validationLog.push(validation)
        
        if (!validation.overallOk) {
          console.log('🚨 检测到修复效果异常:', validation.issues)
        }
        
        // 只保留最近10次验证记录
        if (this.validationLog.length > 10) {
          this.validationLog.shift()
        }
      }, 10000) // 每10秒验证一次
      
      console.log('✅ 修复效果持续监控已启动')
    },
    
    stop() {
      if (!this.isActive) return
      
      this.isActive = false
      if (this.intervalId) {
        clearInterval(this.intervalId)
        this.intervalId = null
      }
      
      console.log('🛑 修复效果持续监控已停止')
      return this.validationLog
    }
  }
  
  monitor.start()
  return monitor
}
