/**
 * 深度修复验证工具
 * 用于验证后端价格传递和时间段刷新的深度修复效果
 */

// 验证后端价格接收
export async function validateBackendPriceReceiving(bookingData) {
  console.log('🔍 开始验证后端价格接收...')
  
  const validation = {
    frontendPrice: null,
    backendReceived: null,
    priceTransmitted: false,
    issues: []
  }
  
  try {
    // 检查前端发送的价格
    validation.frontendPrice = parseFloat(bookingData.price) || 0
    console.log('💰 前端发送的价格:', validation.frontendPrice)
    
    if (validation.frontendPrice <= 0) {
      validation.issues.push('前端发送的价格为0或无效')
    }
    
    // 模拟检查后端接收情况
    // 这里需要实际的API调用来验证
    console.log('📡 检查后端是否正确接收价格...')
    
    // 检查价格传递的关键点
    const priceChecks = {
      hasPrice: bookingData.price !== undefined && bookingData.price !== null,
      priceIsNumber: !isNaN(parseFloat(bookingData.price)),
      priceIsPositive: parseFloat(bookingData.price) > 0,
      priceIsReasonable: parseFloat(bookingData.price) >= 50 && parseFloat(bookingData.price) <= 1000
    }
    
    console.log('🔍 价格检查结果:', priceChecks)
    
    validation.priceTransmitted = Object.values(priceChecks).every(check => check)
    
    if (!priceChecks.hasPrice) {
      validation.issues.push('价格字段缺失')
    }
    if (!priceChecks.priceIsNumber) {
      validation.issues.push('价格不是有效数字')
    }
    if (!priceChecks.priceIsPositive) {
      validation.issues.push('价格不是正数')
    }
    if (!priceChecks.priceIsReasonable) {
      validation.issues.push('价格不在合理范围内')
    }
    
  } catch (error) {
    console.error('❌ 验证后端价格接收失败:', error)
    validation.issues.push(`验证失败: ${error.message}`)
  }
  
  console.log('📊 后端价格接收验证结果:', validation)
  return validation
}

// 验证时间段刷新的深度问题
export async function validateTimeSlotRefreshDeep(venueId, date, venueStore) {
  console.log('🔍 开始深度验证时间段刷新...')
  
  const validation = {
    beforeRefresh: null,
    afterRefresh: null,
    cacheCleared: false,
    dataChanged: false,
    refreshSuccess: false,
    issues: []
  }
  
  try {
    // 步骤1：记录刷新前状态
    validation.beforeRefresh = {
      slotsCount: venueStore.timeSlots ? venueStore.timeSlots.length : 0,
      slotsData: venueStore.timeSlots ? [...venueStore.timeSlots] : [],
      timestamp: Date.now()
    }
    
    console.log('📊 刷新前状态:', validation.beforeRefresh)
    
    // 步骤2：检查缓存清除
    const cacheKeys = []
    if (typeof window !== 'undefined' && window.cacheManager) {
      for (const key of window.cacheManager.cache.keys()) {
        if (key.includes('timeslots') || key.includes(venueId)) {
          cacheKeys.push(key)
        }
      }
    }
    
    console.log('🔍 刷新前缓存键:', cacheKeys)
    
    // 步骤3：执行强力刷新
    console.log('🚀 执行强力刷新...')
    const refreshResult = await venueStore.forceRefreshTimeSlots(venueId, date)
    
    // 步骤4：检查缓存是否被清除
    const cacheKeysAfter = []
    if (typeof window !== 'undefined' && window.cacheManager) {
      for (const key of window.cacheManager.cache.keys()) {
        if (key.includes('timeslots') || key.includes(venueId)) {
          cacheKeysAfter.push(key)
        }
      }
    }
    
    validation.cacheCleared = cacheKeysAfter.length < cacheKeys.length
    console.log('🧹 缓存清除检查:', {
      before: cacheKeys.length,
      after: cacheKeysAfter.length,
      cleared: validation.cacheCleared
    })
    
    // 步骤5：记录刷新后状态
    validation.afterRefresh = {
      slotsCount: venueStore.timeSlots ? venueStore.timeSlots.length : 0,
      slotsData: venueStore.timeSlots ? [...venueStore.timeSlots] : [],
      timestamp: Date.now()
    }
    
    console.log('📊 刷新后状态:', validation.afterRefresh)
    
    // 步骤6：检查数据是否真的改变了
    const beforeDataStr = JSON.stringify(validation.beforeRefresh.slotsData)
    const afterDataStr = JSON.stringify(validation.afterRefresh.slotsData)
    validation.dataChanged = beforeDataStr !== afterDataStr
    
    console.log('🔄 数据变化检查:', {
      dataChanged: validation.dataChanged,
      beforeHash: beforeDataStr.substring(0, 50) + '...',
      afterHash: afterDataStr.substring(0, 50) + '...'
    })
    
    // 步骤7：综合评估
    validation.refreshSuccess = refreshResult.success && validation.afterRefresh.slotsCount > 0
    
    // 收集问题
    if (!refreshResult.success) {
      validation.issues.push('刷新操作失败')
    }
    if (!validation.cacheCleared) {
      validation.issues.push('缓存未被正确清除')
    }
    if (validation.afterRefresh.slotsCount === 0) {
      validation.issues.push('刷新后没有获取到时间段数据')
    }
    if (!validation.dataChanged && validation.beforeRefresh.slotsCount > 0) {
      validation.issues.push('数据没有发生变化，可能仍在使用缓存')
    }
    
  } catch (error) {
    console.error('❌ 深度验证时间段刷新失败:', error)
    validation.issues.push(`验证失败: ${error.message}`)
  }
  
  console.log('📊 时间段刷新深度验证结果:', validation)
  return validation
}

// 综合深度验证
export async function runDeepValidation(bookingData, venueId, date, venueStore) {
  console.log('🚀 开始综合深度验证...')
  
  const results = {
    priceValidation: null,
    timeSlotValidation: null,
    overallSuccess: false,
    summary: ''
  }
  
  try {
    // 验证1：后端价格接收
    console.log('\n=== 验证1: 后端价格接收 ===')
    results.priceValidation = await validateBackendPriceReceiving(bookingData)
    
    // 验证2：时间段刷新
    console.log('\n=== 验证2: 时间段刷新 ===')
    results.timeSlotValidation = await validateTimeSlotRefreshDeep(venueId, date, venueStore)
    
    // 综合评估
    const priceOk = results.priceValidation.priceTransmitted && results.priceValidation.issues.length === 0
    const timeSlotOk = results.timeSlotValidation.refreshSuccess && results.timeSlotValidation.issues.length === 0
    
    results.overallSuccess = priceOk && timeSlotOk
    
    // 生成总结
    const priceStatus = priceOk ? '✅ 通过' : '❌ 失败'
    const timeSlotStatus = timeSlotOk ? '✅ 通过' : '❌ 失败'
    
    results.summary = `价格传递: ${priceStatus}, 时间段刷新: ${timeSlotStatus}`
    
    console.log('\n📋 综合深度验证结果:')
    console.log(`${priceOk ? '✅' : '❌'} 价格传递验证: ${results.priceValidation.issues.length === 0 ? '无问题' : results.priceValidation.issues.join(', ')}`)
    console.log(`${timeSlotOk ? '✅' : '❌'} 时间段刷新验证: ${results.timeSlotValidation.issues.length === 0 ? '无问题' : results.timeSlotValidation.issues.join(', ')}`)
    console.log(`${results.overallSuccess ? '🎉' : '⚠️'} 总体评估: ${results.overallSuccess ? '全部修复成功' : '仍有问题需要解决'}`)
    
  } catch (error) {
    console.error('❌ 综合深度验证失败:', error)
    results.error = error.message
  }
  
  return results
}

// 实时监控工具
export function createRealTimeMonitor(venueId, date, venueStore) {
  console.log('📡 创建实时监控器...')
  
  const monitor = {
    isMonitoring: false,
    intervalId: null,
    lastState: null,
    changeLog: [],
    
    start() {
      if (this.isMonitoring) {
        console.log('⚠️ 监控器已在运行')
        return
      }
      
      console.log('🚀 启动实时监控')
      this.isMonitoring = true
      this.lastState = this.getCurrentState()
      
      this.intervalId = setInterval(() => {
        const currentState = this.getCurrentState()
        
        if (JSON.stringify(currentState) !== JSON.stringify(this.lastState)) {
          const change = {
            timestamp: new Date().toLocaleTimeString(),
            before: this.lastState,
            after: currentState,
            type: this.detectChangeType(this.lastState, currentState)
          }
          
          this.changeLog.push(change)
          console.log('🔄 检测到时间段状态变化:', change)
          
          this.lastState = currentState
        }
      }, 1000) // 每秒检查一次
    },
    
    stop() {
      if (!this.isMonitoring) {
        console.log('⚠️ 监控器未在运行')
        return
      }
      
      console.log('🛑 停止实时监控')
      this.isMonitoring = false
      
      if (this.intervalId) {
        clearInterval(this.intervalId)
        this.intervalId = null
      }
      
      console.log('📊 监控日志:', this.changeLog)
      return this.changeLog
    },
    
    getCurrentState() {
      const slots = venueStore.timeSlots || []
      return {
        count: slots.length,
        statuses: slots.map(slot => ({
          id: slot.id,
          status: slot.status,
          time: `${slot.startTime}-${slot.endTime}`
        }))
      }
    },
    
    detectChangeType(before, after) {
      if (before.count !== after.count) {
        return 'COUNT_CHANGE'
      }
      
      // 检查状态变化
      for (let i = 0; i < Math.min(before.statuses.length, after.statuses.length); i++) {
        if (before.statuses[i].status !== after.statuses[i].status) {
          return 'STATUS_CHANGE'
        }
      }
      
      return 'UNKNOWN_CHANGE'
    }
  }
  
  return monitor
}
