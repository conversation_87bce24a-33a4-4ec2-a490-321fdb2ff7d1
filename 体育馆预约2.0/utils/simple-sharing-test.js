/**
 * 简化版拼场测试工具
 * 用于快速验证拼场功能是否正常工作
 */

// 简单的拼场价格计算测试
export function testSharingPriceCalculation(selectedSlots, venue) {
  console.log('🧮 测试拼场价格计算')
  
  const test = {
    selectedSlots: selectedSlots.length,
    calculations: [],
    totalPrice: 0,
    pricePerTeam: 0,
    method: 'unknown'
  }
  
  selectedSlots.forEach((slot, index) => {
    let slotPrice = 0
    let method = ''
    
    if (slot.price && slot.price > 0) {
      slotPrice = parseFloat(slot.price)
      method = 'slot.price'
    } else if (venue && venue.price && venue.price > 0) {
      slotPrice = parseFloat(venue.price) / 2 // 半小时价格
      method = 'venue.price/2'
    } else {
      slotPrice = 60 // 默认价格
      method = 'default'
    }
    
    test.calculations.push({
      index: index + 1,
      timeRange: `${slot.startTime}-${slot.endTime}`,
      price: slotPrice,
      method: method
    })
    
    test.totalPrice += slotPrice
  })
  
  // 计算每队价格（拼场特有）
  test.pricePerTeam = Math.round((test.totalPrice / 2) * 100) / 100
  
  // 确定主要使用的方法
  const methods = test.calculations.map(calc => calc.method)
  const methodCounts = {}
  methods.forEach(method => {
    methodCounts[method] = (methodCounts[method] || 0) + 1
  })
  
  test.method = Object.keys(methodCounts).reduce((a, b) => 
    methodCounts[a] > methodCounts[b] ? a : b
  )
  
  console.log('🧮 拼场价格计算测试结果:', test)
  
  return test
}

// 验证拼场预约数据
export function validateSharingBookingData(bookingData) {
  console.log('📋 验证拼场预约数据:', bookingData)
  
  const validation = {
    isValid: true,
    issues: []
  }
  
  // 检查拼场特有字段
  const requiredFields = ['venueId', 'date', 'startTime', 'endTime', 'teamName', 'contactInfo', 'price']
  
  requiredFields.forEach(field => {
    if (!bookingData[field]) {
      validation.issues.push(`缺少字段: ${field}`)
      validation.isValid = false
    }
  })
  
  // 检查拼场特有逻辑
  if (bookingData.maxParticipants !== 2) {
    validation.issues.push('拼场maxParticipants应该为2')
    validation.isValid = false
  }
  
  if (!bookingData.teamName || bookingData.teamName.trim() === '') {
    validation.issues.push('拼场必须提供队伍名称')
    validation.isValid = false
  }
  
  if (!bookingData.contactInfo || bookingData.contactInfo.trim() === '') {
    validation.issues.push('拼场必须提供联系方式')
    validation.isValid = false
  }
  
  // 验证价格（应该是每队价格，不是总价）
  if (bookingData.price && bookingData.price > 0) {
    // 拼场价格通常应该是总价的一半，所以不会太高
    if (bookingData.price > 500) {
      validation.issues.push('每队价格过高，可能传递了总价而非每队价格')
    }
  }
  
  console.log('📋 拼场预约数据验证结果:', validation.isValid ? '✅ 有效' : '❌ 无效', validation.issues)
  
  return validation
}

// 快速拼场功能测试
export async function quickSharingTest(selectedSlots, venue, bookingForm, bookingStore) {
  console.log('⚡ 快速拼场功能测试')
  
  const test = {
    timestamp: new Date().toISOString(),
    priceTest: null,
    dataValidation: null,
    mockCreation: null,
    overallSuccess: false,
    issues: []
  }
  
  try {
    // 1. 测试价格计算
    test.priceTest = testSharingPriceCalculation(selectedSlots, venue)
    
    // 2. 构建测试数据
    const mockBookingData = {
      venueId: 29, // 测试场馆ID
      date: '2025-07-20',
      startTime: selectedSlots[0]?.startTime || '12:00',
      endTime: selectedSlots[selectedSlots.length - 1]?.endTime || '13:00',
      teamName: bookingForm.teamName || '测试队伍',
      contactInfo: bookingForm.contactInfo || '13800138000',
      maxParticipants: 2,
      description: bookingForm.description || '测试拼场',
      price: test.priceTest.pricePerTeam,
      slotIds: selectedSlots.map(slot => slot.id)
    }
    
    // 3. 验证数据
    test.dataValidation = validateSharingBookingData(mockBookingData)
    
    // 4. 模拟创建（可选）
    if (bookingStore && test.dataValidation.isValid) {
      try {
        console.log('🧪 模拟创建拼场预约...')
        // 这里可以调用真实的API，但为了测试安全，我们只是验证数据
        test.mockCreation = {
          success: true,
          data: mockBookingData,
          message: '数据验证通过，可以创建'
        }
      } catch (error) {
        test.mockCreation = {
          success: false,
          error: error.message
        }
      }
    }
    
    // 5. 综合评估
    if (test.priceTest.pricePerTeam > 0 && test.dataValidation.isValid) {
      test.overallSuccess = true
    } else {
      if (test.priceTest.pricePerTeam <= 0) {
        test.issues.push('价格计算结果为0')
      }
      if (!test.dataValidation.isValid) {
        test.issues.push(...test.dataValidation.issues)
      }
    }
    
    console.log('⚡ 快速拼场测试结果:', test.overallSuccess ? '✅ 成功' : '❌ 失败', test.issues)
    
  } catch (error) {
    console.error('❌ 快速拼场测试失败:', error)
    test.issues.push(`测试过程出错: ${error.message}`)
  }
  
  return test
}

// 拼场问题诊断
export function diagnoseSharingIssues(selectedSlots, venue, bookingForm) {
  console.log('🔍 拼场问题诊断')
  
  const diagnosis = {
    timestamp: new Date().toISOString(),
    issues: [],
    warnings: [],
    recommendations: []
  }
  
  // 检查基本数据
  if (!selectedSlots || selectedSlots.length === 0) {
    diagnosis.issues.push('没有选中的时间段')
  }
  
  if (!venue) {
    diagnosis.issues.push('场馆数据缺失')
  } else if (!venue.price || venue.price <= 0) {
    diagnosis.warnings.push('场馆价格缺失或为0')
  }
  
  // 检查拼场表单
  if (!bookingForm.teamName || bookingForm.teamName.trim() === '') {
    diagnosis.issues.push('拼场必须提供队伍名称')
  }
  
  if (!bookingForm.contactInfo || bookingForm.contactInfo.trim() === '') {
    diagnosis.issues.push('拼场必须提供联系方式')
  }
  
  // 检查时间段价格
  if (selectedSlots && selectedSlots.length > 0) {
    const slotsWithoutPrice = selectedSlots.filter(slot => !slot.price || slot.price <= 0)
    if (slotsWithoutPrice.length > 0) {
      diagnosis.warnings.push(`${slotsWithoutPrice.length}个时间段缺少价格`)
    }
  }
  
  // 生成建议
  if (diagnosis.issues.length === 0 && diagnosis.warnings.length === 0) {
    diagnosis.recommendations.push('拼场数据完整，可以创建')
  } else {
    if (diagnosis.issues.length > 0) {
      diagnosis.recommendations.push('需要解决必需字段缺失问题')
    }
    if (diagnosis.warnings.length > 0) {
      diagnosis.recommendations.push('建议检查价格数据完整性')
    }
  }
  
  console.log('🔍 拼场问题诊断结果:', diagnosis)
  
  return diagnosis
}

// 生成拼场测试报告
export function generateSharingTestReport(selectedSlots, venue, bookingForm) {
  console.log('📊 生成拼场测试报告')
  
  const priceTest = testSharingPriceCalculation(selectedSlots, venue)
  const diagnosis = diagnoseSharingIssues(selectedSlots, venue, bookingForm)
  
  const report = {
    timestamp: new Date().toISOString(),
    summary: '',
    priceCalculation: {
      totalPrice: priceTest.totalPrice,
      pricePerTeam: priceTest.pricePerTeam,
      method: priceTest.method,
      slotsCount: priceTest.selectedSlots
    },
    dataValidation: {
      issues: diagnosis.issues.length,
      warnings: diagnosis.warnings.length,
      recommendations: diagnosis.recommendations
    },
    status: 'UNKNOWN'
  }
  
  // 确定状态
  if (diagnosis.issues.length === 0 && priceTest.pricePerTeam > 0) {
    report.status = 'READY'
    report.summary = `✅ 拼场准备就绪 - 每队¥${priceTest.pricePerTeam}`
  } else if (diagnosis.issues.length > 0) {
    report.status = 'ERROR'
    report.summary = `❌ 发现${diagnosis.issues.length}个问题`
  } else {
    report.status = 'WARNING'
    report.summary = `⚠️ 有${diagnosis.warnings.length}个警告`
  }
  
  console.log('📊 拼场测试报告:', report)
  
  return report
}

// 导出所有功能
export default {
  testSharingPriceCalculation,
  validateSharingBookingData,
  quickSharingTest,
  diagnoseSharingIssues,
  generateSharingTestReport
}
