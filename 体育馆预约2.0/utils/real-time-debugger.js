/**
 * 实时调试工具
 * 用于监控和调试实际的数据传递和API调用
 */

// 拦截并监控所有API请求
export function interceptApiCalls() {
  console.log('🔍 启动API调用拦截器')
  
  // 保存原始的uni.request方法
  const originalRequest = uni.request
  
  // 重写uni.request方法
  uni.request = function(options) {
    console.log('🚀 [API拦截] 发起请求:', {
      url: options.url,
      method: options.method,
      data: options.data,
      header: options.header
    })
    
    // 特别关注预约相关的请求
    if (options.url && (options.url.includes('/bookings') || options.url.includes('/timeslots'))) {
      console.log('📋 [预约API] 详细数据:', JSON.stringify(options.data, null, 2))
      
      // 检查价格字段
      if (options.data && options.data.price !== undefined) {
        console.log('💰 [价格检查] 发送的价格:', {
          value: options.data.price,
          type: typeof options.data.price,
          isNumber: !isNaN(options.data.price),
          isPositive: options.data.price > 0
        })
      }
    }
    
    // 包装success回调
    const originalSuccess = options.success
    options.success = function(response) {
      console.log('✅ [API拦截] 请求成功:', {
        url: options.url,
        statusCode: response.statusCode,
        data: response.data
      })
      
      // 特别关注预约相关的响应
      if (options.url && (options.url.includes('/bookings') || options.url.includes('/timeslots'))) {
        console.log('📋 [预约API] 响应详情:', JSON.stringify(response.data, null, 2))
      }
      
      if (originalSuccess) {
        originalSuccess(response)
      }
    }
    
    // 包装fail回调
    const originalFail = options.fail
    options.fail = function(error) {
      console.error('❌ [API拦截] 请求失败:', {
        url: options.url,
        error: error
      })
      
      if (originalFail) {
        originalFail(error)
      }
    }
    
    // 调用原始方法
    return originalRequest.call(this, options)
  }
  
  console.log('✅ API调用拦截器已启动')
}

// 监控时间段数据变化
export function monitorTimeSlotChanges(venueStore) {
  console.log('🔍 启动时间段变化监控')
  
  let lastTimeSlots = null
  let changeCount = 0
  
  const monitor = setInterval(() => {
    const currentTimeSlots = venueStore.timeSlots || []
    const currentSlotsStr = JSON.stringify(currentTimeSlots.map(slot => ({
      id: slot.id,
      status: slot.status,
      time: `${slot.startTime}-${slot.endTime}`
    })))
    
    if (lastTimeSlots !== null && lastTimeSlots !== currentSlotsStr) {
      changeCount++
      console.log(`🔄 [时间段监控] 检测到变化 #${changeCount}:`)
      
      const lastSlots = JSON.parse(lastTimeSlots)
      const currentSlots = JSON.parse(currentSlotsStr)
      
      // 找出变化的时间段
      const changes = []
      for (let i = 0; i < Math.max(lastSlots.length, currentSlots.length); i++) {
        const lastSlot = lastSlots[i]
        const currentSlot = currentSlots[i]
        
        if (!lastSlot && currentSlot) {
          changes.push({ type: 'ADDED', slot: currentSlot })
        } else if (lastSlot && !currentSlot) {
          changes.push({ type: 'REMOVED', slot: lastSlot })
        } else if (lastSlot && currentSlot && lastSlot.status !== currentSlot.status) {
          changes.push({ 
            type: 'STATUS_CHANGED', 
            slot: currentSlot, 
            from: lastSlot.status, 
            to: currentSlot.status 
          })
        }
      }
      
      console.log('📊 [时间段监控] 变化详情:', changes)
    }
    
    lastTimeSlots = currentSlotsStr
  }, 1000)
  
  // 返回停止监控的函数
  return () => {
    clearInterval(monitor)
    console.log('🛑 时间段变化监控已停止')
  }
}

// 检查后端数据库状态
export async function checkBackendDatabase(orderId) {
  console.log('🔍 检查后端数据库状态...')
  
  try {
    // 这里需要调用一个专门的API来检查数据库状态
    // 由于没有直接的数据库查询API，我们通过订单详情API来检查
    const response = await uni.request({
      url: `/api/bookings/${orderId}`,
      method: 'GET'
    })
    
    if (response.statusCode === 200 && response.data) {
      console.log('📊 [数据库检查] 订单详情:', response.data)
      
      const order = response.data.data || response.data
      console.log('💰 [数据库检查] 订单价格:', {
        totalPrice: order.totalPrice,
        priceType: typeof order.totalPrice,
        isZero: order.totalPrice === 0
      })
      
      return {
        success: true,
        order: order,
        priceIsZero: order.totalPrice === 0
      }
    } else {
      console.error('❌ [数据库检查] 获取订单详情失败')
      return {
        success: false,
        error: '获取订单详情失败'
      }
    }
  } catch (error) {
    console.error('❌ [数据库检查] 检查失败:', error)
    return {
      success: false,
      error: error.message
    }
  }
}

// 实时价格传递验证
export function validatePriceTransmission(bookingData) {
  console.log('🔍 实时价格传递验证')
  
  const validation = {
    frontendData: {
      hasPrice: bookingData.price !== undefined,
      priceValue: bookingData.price,
      priceType: typeof bookingData.price,
      isNumber: !isNaN(bookingData.price),
      isPositive: bookingData.price > 0
    },
    issues: []
  }
  
  console.log('📊 [价格验证] 前端数据:', validation.frontendData)
  
  // 检查各种可能的问题
  if (!validation.frontendData.hasPrice) {
    validation.issues.push('价格字段缺失')
  }
  
  if (validation.frontendData.hasPrice && !validation.frontendData.isNumber) {
    validation.issues.push('价格不是有效数字')
  }
  
  if (validation.frontendData.isNumber && !validation.frontendData.isPositive) {
    validation.issues.push('价格不是正数')
  }
  
  // 检查数据序列化
  try {
    const serialized = JSON.stringify(bookingData)
    const deserialized = JSON.parse(serialized)
    
    if (deserialized.price !== bookingData.price) {
      validation.issues.push('价格在序列化过程中发生变化')
    }
    
    console.log('📊 [价格验证] 序列化测试:', {
      original: bookingData.price,
      serialized: serialized.includes('"price"'),
      deserialized: deserialized.price
    })
  } catch (error) {
    validation.issues.push('数据序列化失败')
  }
  
  validation.isValid = validation.issues.length === 0
  
  console.log('📊 [价格验证] 结果:', {
    isValid: validation.isValid,
    issues: validation.issues
  })
  
  return validation
}

// 综合实时调试
export function startRealTimeDebugging(venueStore) {
  console.log('🚀 启动综合实时调试')
  
  const debugSession = {
    apiInterceptor: null,
    timeSlotMonitor: null,
    isActive: false,
    logs: []
  }
  
  // 启动API拦截
  interceptApiCalls()
  debugSession.apiInterceptor = true
  
  // 启动时间段监控
  debugSession.timeSlotMonitor = monitorTimeSlotChanges(venueStore)
  
  debugSession.isActive = true
  
  console.log('✅ 综合实时调试已启动')
  
  // 返回调试会话控制器
  return {
    stop() {
      if (debugSession.timeSlotMonitor) {
        debugSession.timeSlotMonitor()
      }
      debugSession.isActive = false
      console.log('🛑 综合实时调试已停止')
    },
    
    isActive() {
      return debugSession.isActive
    },
    
    getLogs() {
      return debugSession.logs
    }
  }
}

// 快速问题诊断
export async function quickDiagnosis(bookingData, venueStore) {
  console.log('🔍 开始快速问题诊断...')
  
  const diagnosis = {
    timestamp: new Date().toISOString(),
    priceIssues: [],
    timeSlotIssues: [],
    overallStatus: 'UNKNOWN'
  }
  
  // 1. 价格问题诊断
  console.log('💰 诊断价格问题...')
  const priceValidation = validatePriceTransmission(bookingData)
  if (!priceValidation.isValid) {
    diagnosis.priceIssues = priceValidation.issues
  }
  
  // 2. 时间段问题诊断
  console.log('⏰ 诊断时间段问题...')
  const timeSlots = venueStore.timeSlots || []
  
  if (timeSlots.length === 0) {
    diagnosis.timeSlotIssues.push('没有时间段数据')
  }
  
  const reservedSlots = timeSlots.filter(slot => slot.status === 'RESERVED')
  if (reservedSlots.length === 0) {
    diagnosis.timeSlotIssues.push('没有已预约的时间段')
  }
  
  // 3. 综合评估
  const hasIssues = diagnosis.priceIssues.length > 0 || diagnosis.timeSlotIssues.length > 0
  diagnosis.overallStatus = hasIssues ? 'ISSUES_FOUND' : 'OK'
  
  console.log('📊 快速诊断结果:', diagnosis)
  
  return diagnosis
}
