/**
 * 时间段同步修复工具
 * 恢复前端生成时间段并同步到后端的功能
 */

import { generateTimeSlots } from '@/api/timeslot.js'

// 修复时间段生成和同步逻辑
export async function fixTimeSlotGeneration(venueId, date, venueStore) {
  console.log('🔧 修复时间段生成和同步逻辑')
  
  const fix = {
    venueId: venueId,
    date: date,
    steps: [],
    success: false,
    generatedSlots: null,
    syncedToBackend: false,
    error: null
  }
  
  try {
    // 步骤1: 检查当前时间段状态
    fix.steps.push('检查当前时间段状态')
    const currentSlots = venueStore.timeSlots || []
    console.log('📊 当前时间段数量:', currentSlots.length)
    
    // 步骤2: 尝试后端生成
    fix.steps.push('尝试后端生成时间段')
    try {
      console.log('📡 调用后端生成API...')
      const generateResponse = await generateTimeSlots(venueId, date)
      console.log('✅ 后端生成API调用成功:', generateResponse)
      
      // 步骤3: 重新获取后端生成的时间段
      fix.steps.push('获取后端生成的时间段')
      const { getVenueTimeSlots } = await import('@/api/timeslot.js')
      const fetchResponse = await getVenueTimeSlots(venueId, date, true)
      
      if (fetchResponse && fetchResponse.data && fetchResponse.data.length > 0) {
        fix.generatedSlots = fetchResponse.data
        fix.syncedToBackend = true
        fix.success = true
        
        // 更新VenueStore
        venueStore.setTimeSlots(fetchResponse.data)
        console.log('✅ 后端生成并同步成功:', fetchResponse.data.length, '个时间段')
        
        fix.steps.push(`成功生成${fetchResponse.data.length}个时间段`)
      } else {
        console.warn('⚠️ 后端生成API成功但返回空数据')
        throw new Error('后端生成返回空数据')
      }
      
    } catch (backendError) {
      console.warn('⚠️ 后端生成失败，使用前端生成:', backendError)
      fix.steps.push('后端生成失败，使用前端生成')
      
      // 步骤4: 前端生成时间段
      const frontendSlots = generateFrontendTimeSlots(venueId, date, venueStore.venueDetail)
      fix.generatedSlots = frontendSlots
      
      // 更新VenueStore
      venueStore.setTimeSlots(frontendSlots)
      console.log('✅ 前端生成成功:', frontendSlots.length, '个时间段')
      
      // 步骤5: 尝试同步到后端（可选）
      fix.steps.push('尝试同步前端生成的时间段到后端')
      try {
        await syncFrontendSlotsToBackend(frontendSlots, venueId, date)
        fix.syncedToBackend = true
        console.log('✅ 前端时间段同步到后端成功')
        fix.steps.push('同步到后端成功')
      } catch (syncError) {
        console.warn('⚠️ 同步到后端失败:', syncError)
        fix.steps.push('同步到后端失败，但前端可用')
      }
      
      fix.success = true
    }
    
  } catch (error) {
    console.error('❌ 时间段生成修复失败:', error)
    fix.error = error.message
    fix.steps.push(`修复失败: ${error.message}`)
  }
  
  console.log('🔧 时间段生成修复结果:', fix)
  return fix
}

// 前端生成时间段
function generateFrontendTimeSlots(venueId, date, venue) {
  console.log('🏗️ 前端生成时间段')
  
  // 获取场馆价格信息
  const venueHourPrice = venue?.price || 120 // 默认120元/小时
  const venueHalfHourPrice = Math.round(venueHourPrice / 2) // 半小时价格
  
  console.log('💰 场馆小时价格:', venueHourPrice)
  console.log('💰 场馆半小时价格:', venueHalfHourPrice)
  
  const slots = []
  const startHour = 9
  const endHour = 22
  
  // 生成半小时间隔的时间段
  for (let hour = startHour; hour < endHour; hour++) {
    for (let minute = 0; minute < 60; minute += 30) {
      const startTime = `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`
      const endMinute = minute + 30
      const endHour = endMinute >= 60 ? hour + 1 : hour
      const actualEndMinute = endMinute >= 60 ? 0 : endMinute
      const endTime = `${endHour.toString().padStart(2, '0')}:${actualEndMinute.toString().padStart(2, '0')}`
      
      // 如果结束时间超过营业时间，跳出循环
      if (endHour >= endHour && actualEndMinute > 0 && endHour >= endHour) {
        break
      }
      
      const slot = {
        id: `default_${venueId}_${date}_${hour}_${minute}`,
        venueId: parseInt(venueId),
        date: date,
        startTime: startTime,
        endTime: endTime,
        status: 'AVAILABLE',
        price: venueHalfHourPrice,
        isDefault: true // 标记为前端生成的默认时间段
      }
      
      slots.push(slot)
    }
  }
  
  console.log('🏗️ 前端生成完成:', slots.length, '个时间段')
  return slots
}

// 同步前端生成的时间段到后端
async function syncFrontendSlotsToBackend(slots, venueId, date) {
  console.log('🔄 同步前端时间段到后端')
  
  // 这里可以实现批量创建时间段的API调用
  // 由于当前后端只有生成API，我们先尝试调用生成API
  try {
    await generateTimeSlots(venueId, date)
    console.log('✅ 通过后端生成API同步成功')
  } catch (error) {
    console.warn('⚠️ 后端同步失败:', error)
    throw error
  }
}

// 验证时间段数据完整性
export function validateTimeSlots(timeSlots) {
  console.log('🔍 验证时间段数据完整性')
  
  const validation = {
    totalSlots: timeSlots.length,
    validSlots: 0,
    invalidSlots: 0,
    issues: [],
    priceIssues: 0,
    timeIssues: 0
  }
  
  timeSlots.forEach((slot, index) => {
    let isValid = true
    
    // 检查必需字段
    if (!slot.id || !slot.startTime || !slot.endTime || !slot.status) {
      validation.issues.push(`时间段${index + 1}缺少必需字段`)
      isValid = false
    }
    
    // 检查价格
    if (!slot.price || slot.price <= 0) {
      validation.priceIssues++
      validation.issues.push(`时间段${index + 1}价格无效`)
      isValid = false
    }
    
    // 检查时间格式
    const timeRegex = /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/
    if (!timeRegex.test(slot.startTime) || !timeRegex.test(slot.endTime)) {
      validation.timeIssues++
      validation.issues.push(`时间段${index + 1}时间格式错误`)
      isValid = false
    }
    
    if (isValid) {
      validation.validSlots++
    } else {
      validation.invalidSlots++
    }
  })
  
  console.log('🔍 时间段验证结果:', validation)
  return validation
}

// 强制重新生成时间段
export async function forceRegenerateTimeSlots(venueId, date, venueStore) {
  console.log('🔄 强制重新生成时间段')
  
  const regenerate = {
    success: false,
    method: 'unknown',
    slotsCount: 0,
    error: null
  }
  
  try {
    // 清除现有时间段
    venueStore.clearTimeSlots()
    
    // 等待清除完成
    await new Promise(resolve => setTimeout(resolve, 200))
    
    // 执行修复
    const fixResult = await fixTimeSlotGeneration(venueId, date, venueStore)
    
    if (fixResult.success) {
      regenerate.success = true
      regenerate.method = fixResult.syncedToBackend ? 'backend' : 'frontend'
      regenerate.slotsCount = fixResult.generatedSlots?.length || 0
      console.log('✅ 强制重新生成成功')
    } else {
      regenerate.error = fixResult.error
      console.error('❌ 强制重新生成失败')
    }
    
  } catch (error) {
    console.error('❌ 强制重新生成过程出错:', error)
    regenerate.error = error.message
  }
  
  return regenerate
}

// 检查时间段同步状态
export async function checkTimeSlotSyncStatus(venueId, date, venueStore) {
  console.log('🔍 检查时间段同步状态')
  
  const status = {
    frontendSlots: 0,
    backendSlots: 0,
    synced: false,
    needsSync: false,
    issues: []
  }
  
  try {
    // 检查前端时间段
    const frontendSlots = venueStore.timeSlots || []
    status.frontendSlots = frontendSlots.length
    
    // 检查后端时间段
    const { getVenueTimeSlots } = await import('@/api/timeslot.js')
    const backendResponse = await getVenueTimeSlots(venueId, date, true)
    const backendSlots = backendResponse?.data || []
    status.backendSlots = backendSlots.length
    
    // 判断同步状态
    if (status.frontendSlots > 0 && status.backendSlots > 0) {
      status.synced = true
    } else if (status.frontendSlots > 0 && status.backendSlots === 0) {
      status.needsSync = true
      status.issues.push('前端有时间段但后端没有')
    } else if (status.frontendSlots === 0 && status.backendSlots === 0) {
      status.needsSync = true
      status.issues.push('前后端都没有时间段数据')
    }
    
  } catch (error) {
    console.error('❌ 检查同步状态失败:', error)
    status.issues.push(`检查失败: ${error.message}`)
  }
  
  console.log('🔍 时间段同步状态:', status)
  return status
}

// 自动修复时间段问题
export async function autoFixTimeSlotIssues(venueId, date, venueStore) {
  console.log('🤖 自动修复时间段问题')
  
  const autoFix = {
    steps: [],
    success: false,
    finalSlotsCount: 0
  }
  
  try {
    // 步骤1: 检查同步状态
    autoFix.steps.push('检查同步状态')
    const syncStatus = await checkTimeSlotSyncStatus(venueId, date, venueStore)
    
    // 步骤2: 根据状态决定修复策略
    if (syncStatus.needsSync) {
      autoFix.steps.push('需要同步，执行时间段生成修复')
      const fixResult = await fixTimeSlotGeneration(venueId, date, venueStore)
      
      if (fixResult.success) {
        autoFix.success = true
        autoFix.finalSlotsCount = fixResult.generatedSlots?.length || 0
        autoFix.steps.push(`修复成功，生成${autoFix.finalSlotsCount}个时间段`)
      } else {
        autoFix.steps.push('修复失败')
      }
    } else if (syncStatus.synced) {
      autoFix.success = true
      autoFix.finalSlotsCount = syncStatus.frontendSlots
      autoFix.steps.push('时间段已同步，无需修复')
    }
    
  } catch (error) {
    console.error('❌ 自动修复失败:', error)
    autoFix.steps.push(`自动修复失败: ${error.message}`)
  }
  
  console.log('🤖 自动修复结果:', autoFix)
  return autoFix
}
