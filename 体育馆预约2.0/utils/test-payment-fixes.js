/**
 * 支付问题修复验证脚本
 * 用于快速验证订单金额显示和时间段刷新问题的修复效果
 */

import { debugOrderAmount, forceRefreshTimeSlots } from './payment-debug.js'

// 测试订单金额计算修复
export async function testOrderAmountFix() {
  console.log('🧪 开始测试订单金额计算修复...')
  
  const testCases = [
    // 测试用例1: 正常订单（有startTime和endTime）
    {
      name: '正常订单（18:00-20:00）',
      orderInfo: {
        startTime: '18:00',
        endTime: '20:00',
        bookingType: 'EXCLUSIVE',
        totalPrice: 0,
        venueName: '奥体中心篮球馆'
      },
      expectedPrice: 240 // 2小时 × 120元/小时
    },
    
    // 测试用例2: 拼场订单
    {
      name: '拼场订单',
      orderInfo: {
        startTime: '18:00',
        endTime: '19:00',
        bookingType: 'SHARED',
        totalPrice: 0,
        venueName: '奥体中心篮球馆'
      },
      expectedPrice: 120 // 1小时 × 120元/小时
    },
    
    // 测试用例3: 虚拟订单
    {
      name: '虚拟订单',
      orderInfo: {
        isVirtualOrder: true,
        paymentAmount: 0,
        bookingType: 'SHARED'
      },
      expectedPrice: 120 // 拼场默认价格
    },
    
    // 测试用例4: 缺少时间信息的订单
    {
      name: '缺少时间信息的独享订单',
      orderInfo: {
        bookingType: 'EXCLUSIVE',
        totalPrice: 0,
        venueName: '奥体中心篮球馆'
      },
      expectedPrice: 240 // 独享默认价格
    }
  ]
  
  const results = []
  
  for (const testCase of testCases) {
    console.log(`\n🔍 测试: ${testCase.name}`)
    
    try {
      const result = debugOrderAmount(testCase.orderInfo)
      
      const success = result.success && result.calculatedPrice === testCase.expectedPrice
      
      results.push({
        name: testCase.name,
        success: success,
        expected: testCase.expectedPrice,
        actual: result.calculatedPrice,
        method: result.calculationMethod,
        details: result
      })
      
      console.log(`${success ? '✅' : '❌'} 期望: ¥${testCase.expectedPrice}, 实际: ¥${result.calculatedPrice}`)
      console.log(`📝 计算方法: ${result.calculationMethod}`)
      
    } catch (error) {
      console.error(`❌ 测试失败: ${error.message}`)
      results.push({
        name: testCase.name,
        success: false,
        error: error.message
      })
    }
  }
  
  // 汇总结果
  const successCount = results.filter(r => r.success).length
  const totalCount = results.length
  
  console.log(`\n📊 订单金额计算测试结果: ${successCount}/${totalCount} 通过`)
  
  return {
    success: successCount === totalCount,
    results: results,
    summary: `${successCount}/${totalCount} 测试通过`
  }
}

// 测试时间段刷新修复
export async function testTimeSlotRefreshFix(venueStore, venueId = '25', date = '2025-07-19') {
  console.log('🧪 开始测试时间段刷新修复...')
  
  try {
    // 步骤1: 记录刷新前状态
    const beforeSlots = venueStore.timeSlots ? [...venueStore.timeSlots] : []
    console.log(`📊 刷新前时间段数量: ${beforeSlots.length}`)
    
    // 步骤2: 执行强制刷新
    console.log('🔄 执行强制刷新...')
    const refreshResult = await forceRefreshTimeSlots(venueId, date, venueStore)
    
    // 步骤3: 检查刷新结果
    const afterSlots = venueStore.timeSlots ? [...venueStore.timeSlots] : []
    console.log(`📊 刷新后时间段数量: ${afterSlots.length}`)
    
    // 步骤4: 验证刷新效果
    const refreshSuccess = refreshResult.success
    const dataChanged = JSON.stringify(beforeSlots) !== JSON.stringify(afterSlots)
    
    console.log(`${refreshSuccess ? '✅' : '❌'} 刷新执行: ${refreshSuccess ? '成功' : '失败'}`)
    console.log(`${dataChanged ? '✅' : '⚠️'} 数据变化: ${dataChanged ? '是' : '否'}`)
    
    return {
      success: refreshSuccess,
      dataChanged: dataChanged,
      beforeCount: beforeSlots.length,
      afterCount: afterSlots.length,
      details: refreshResult
    }
    
  } catch (error) {
    console.error(`❌ 时间段刷新测试失败: ${error.message}`)
    return {
      success: false,
      error: error.message
    }
  }
}

// 综合测试
export async function runComprehensivePaymentTest(venueStore) {
  console.log('🚀 开始综合支付问题修复测试...')
  
  const results = {
    orderAmount: null,
    timeSlotRefresh: null,
    overall: false
  }
  
  try {
    // 测试1: 订单金额计算
    console.log('\n=== 测试1: 订单金额计算 ===')
    results.orderAmount = await testOrderAmountFix()
    
    // 测试2: 时间段刷新
    console.log('\n=== 测试2: 时间段刷新 ===')
    results.timeSlotRefresh = await testTimeSlotRefreshFix(venueStore)
    
    // 综合评估
    results.overall = results.orderAmount.success && results.timeSlotRefresh.success
    
    console.log('\n📋 综合测试结果:')
    console.log(`${results.orderAmount.success ? '✅' : '❌'} 订单金额计算: ${results.orderAmount.summary}`)
    console.log(`${results.timeSlotRefresh.success ? '✅' : '❌'} 时间段刷新: ${results.timeSlotRefresh.success ? '成功' : '失败'}`)
    console.log(`${results.overall ? '🎉' : '⚠️'} 总体评估: ${results.overall ? '全部修复成功' : '仍有问题需要解决'}`)
    
  } catch (error) {
    console.error(`❌ 综合测试失败: ${error.message}`)
    results.error = error.message
  }
  
  return results
}

// 快速验证函数（用于页面调用）
export function quickValidatePaymentFixes() {
  console.log('⚡ 快速验证支付修复...')
  
  // 快速测试订单金额计算
  const testOrder = {
    startTime: '18:00',
    endTime: '20:00',
    bookingType: 'EXCLUSIVE',
    totalPrice: 0
  }
  
  const amountResult = debugOrderAmount(testOrder)
  const amountFixed = amountResult.success && amountResult.calculatedPrice > 0
  
  console.log(`${amountFixed ? '✅' : '❌'} 订单金额计算: ${amountFixed ? '已修复' : '仍有问题'}`)
  
  return {
    orderAmountFixed: amountFixed,
    calculatedPrice: amountResult.calculatedPrice,
    method: amountResult.calculationMethod
  }
}
