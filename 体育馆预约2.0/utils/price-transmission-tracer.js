/**
 * 价格传递追踪器
 * 专门用于追踪价格从前端计算到后端接收的完整过程
 */

// 全局价格追踪日志
let priceTraceLog = []

// 清除追踪日志
export function clearPriceTrace() {
  priceTraceLog = []
  console.log('🧹 价格追踪日志已清除')
}

// 获取追踪日志
export function getPriceTrace() {
  return priceTraceLog
}

// 添加追踪记录
function addTrace(step, data) {
  const trace = {
    timestamp: new Date().toISOString(),
    step: step,
    data: data
  }
  priceTraceLog.push(trace)
  console.log(`📊 [价格追踪] ${step}:`, data)
}

// 追踪时间段价格计算
export function traceSlotPriceCalculation(slots, venue) {
  addTrace('时间段价格计算开始', {
    slotsCount: slots.length,
    venuePrice: venue?.price
  })
  
  let totalPrice = 0
  const slotDetails = []
  
  slots.forEach((slot, index) => {
    const slotData = {
      index: index + 1,
      timeRange: `${slot.startTime}-${slot.endTime}`,
      slotPrice: slot.price,
      slotPriceType: typeof slot.price,
      venuePrice: venue?.price,
      venuePriceType: typeof venue?.price
    }
    
    // 模拟getSlotPrice逻辑
    let calculatedPrice = 0
    let method = ''
    
    if (slot.price && slot.price > 0) {
      calculatedPrice = parseFloat(slot.price)
      method = 'slot.price'
    } else if (slot.pricePerHour && slot.pricePerHour > 0) {
      calculatedPrice = parseFloat(slot.pricePerHour)
      method = 'slot.pricePerHour'
    } else if (venue?.price && venue.price > 0) {
      calculatedPrice = parseFloat(venue.price) / 2
      method = 'venue.price/2'
    } else {
      calculatedPrice = 60
      method = 'default'
    }
    
    slotData.calculatedPrice = calculatedPrice
    slotData.method = method
    totalPrice += calculatedPrice
    
    slotDetails.push(slotData)
  })
  
  addTrace('时间段价格计算完成', {
    slotDetails: slotDetails,
    totalPrice: totalPrice
  })
  
  return totalPrice
}

// 追踪预约数据构建
export function traceBookingDataConstruction(bookingData) {
  addTrace('预约数据构建', {
    hasPrice: bookingData.price !== undefined,
    priceValue: bookingData.price,
    priceType: typeof bookingData.price,
    isNumber: !isNaN(bookingData.price),
    isPositive: bookingData.price > 0,
    bookingType: bookingData.bookingType,
    venueId: bookingData.venueId,
    date: bookingData.date,
    startTime: bookingData.startTime,
    endTime: bookingData.endTime
  })
  
  // 检查数据序列化
  try {
    const serialized = JSON.stringify(bookingData)
    const deserialized = JSON.parse(serialized)
    
    addTrace('数据序列化测试', {
      originalPrice: bookingData.price,
      serializedContainsPrice: serialized.includes('"price"'),
      deserializedPrice: deserialized.price,
      pricePreserved: deserialized.price === bookingData.price
    })
  } catch (error) {
    addTrace('数据序列化失败', { error: error.message })
  }
}

// 追踪API请求
export function traceApiRequest(url, method, data) {
  addTrace('API请求发送', {
    url: url,
    method: method,
    hasData: data !== undefined,
    dataType: typeof data,
    hasPrice: data && data.price !== undefined,
    priceValue: data?.price,
    priceType: typeof data?.price
  })
  
  // 检查uni.request的实际行为
  const originalRequest = uni.request
  
  uni.request = function(options) {
    addTrace('uni.request实际调用', {
      url: options.url,
      method: options.method,
      dataType: typeof options.data,
      hasPrice: options.data && options.data.price !== undefined,
      priceValue: options.data?.price,
      actualDataSent: options.data
    })
    
    // 恢复原始方法
    uni.request = originalRequest
    
    // 调用原始方法
    return originalRequest.call(this, options)
  }
}

// 追踪API响应
export function traceApiResponse(response) {
  addTrace('API响应接收', {
    statusCode: response.statusCode,
    hasData: response.data !== undefined,
    dataType: typeof response.data,
    responseData: response.data
  })
}

// 完整的价格传递追踪
export async function traceCompletePriceTransmission(selectedSlots, venue, bookingForm) {
  console.log('🚀 开始完整价格传递追踪')
  clearPriceTrace()
  
  try {
    // 步骤1: 追踪价格计算
    const calculatedPrice = traceSlotPriceCalculation(selectedSlots, venue)
    
    // 步骤2: 构建预约数据
    const bookingData = {
      venueId: venue?.id || 25,
      date: '2025-07-19',
      startTime: selectedSlots[0]?.startTime || '18:00',
      endTime: selectedSlots[selectedSlots.length - 1]?.endTime || '20:00',
      slotIds: selectedSlots.map(slot => slot.id),
      bookingType: bookingForm?.bookingType || 'EXCLUSIVE',
      description: bookingForm?.description || '',
      price: calculatedPrice
    }
    
    // 步骤3: 追踪数据构建
    traceBookingDataConstruction(bookingData)
    
    // 步骤4: 模拟API调用
    traceApiRequest('/bookings', 'POST', bookingData)
    
    // 返回追踪结果
    const trace = getPriceTrace()
    
    console.log('📋 完整价格传递追踪结果:')
    trace.forEach((entry, index) => {
      console.log(`${index + 1}. ${entry.step}:`, entry.data)
    })
    
    return {
      success: true,
      calculatedPrice: calculatedPrice,
      finalBookingData: bookingData,
      trace: trace,
      issues: trace.filter(entry => 
        entry.data.error || 
        (entry.step.includes('价格') && entry.data.calculatedPrice === 0) ||
        (entry.step.includes('数据') && !entry.data.pricePreserved)
      )
    }
    
  } catch (error) {
    addTrace('追踪过程出错', { error: error.message })
    return {
      success: false,
      error: error.message,
      trace: getPriceTrace()
    }
  }
}

// 实时价格监控
export function startPriceMonitoring() {
  console.log('🔍 启动实时价格监控')
  
  const monitor = {
    isActive: false,
    interceptors: [],
    
    start() {
      if (this.isActive) {
        console.log('⚠️ 价格监控已在运行')
        return
      }
      
      this.isActive = true
      
      // 拦截uni.request
      const originalRequest = uni.request
      uni.request = function(options) {
        if (options.url && options.url.includes('/bookings')) {
          addTrace('监控到预约请求', {
            url: options.url,
            method: options.method,
            hasPrice: options.data && options.data.price !== undefined,
            priceValue: options.data?.price,
            priceType: typeof options.data?.price,
            fullData: options.data
          })
        }
        
        return originalRequest.call(this, options)
      }
      
      this.interceptors.push(() => {
        uni.request = originalRequest
      })
      
      console.log('✅ 实时价格监控已启动')
    },
    
    stop() {
      if (!this.isActive) {
        console.log('⚠️ 价格监控未在运行')
        return
      }
      
      this.isActive = false
      
      // 恢复所有拦截器
      this.interceptors.forEach(restore => restore())
      this.interceptors = []
      
      console.log('🛑 实时价格监控已停止')
      
      // 返回监控日志
      return getPriceTrace()
    }
  }
  
  monitor.start()
  return monitor
}

// 快速价格问题诊断
export function quickPriceDiagnosis(selectedSlots, venue) {
  console.log('⚡ 快速价格问题诊断')
  
  const diagnosis = {
    timestamp: new Date().toISOString(),
    issues: [],
    warnings: [],
    summary: ''
  }
  
  // 检查时间段数据
  if (!selectedSlots || selectedSlots.length === 0) {
    diagnosis.issues.push('没有选中的时间段')
  } else {
    selectedSlots.forEach((slot, index) => {
      if (!slot.price && !slot.pricePerHour) {
        diagnosis.warnings.push(`时间段${index + 1}缺少价格信息`)
      }
      if (slot.price === 0) {
        diagnosis.issues.push(`时间段${index + 1}价格为0`)
      }
    })
  }
  
  // 检查场馆数据
  if (!venue) {
    diagnosis.issues.push('场馆数据缺失')
  } else if (!venue.price || venue.price === 0) {
    diagnosis.warnings.push('场馆价格缺失或为0')
  }
  
  // 模拟价格计算
  let totalPrice = 0
  if (selectedSlots && selectedSlots.length > 0) {
    selectedSlots.forEach(slot => {
      if (slot.price && slot.price > 0) {
        totalPrice += parseFloat(slot.price)
      } else if (venue?.price && venue.price > 0) {
        totalPrice += parseFloat(venue.price) / 2
      } else {
        totalPrice += 60 // 默认价格
      }
    })
  }
  
  if (totalPrice === 0) {
    diagnosis.issues.push('计算的总价格为0')
  }
  
  // 生成总结
  const issueCount = diagnosis.issues.length
  const warningCount = diagnosis.warnings.length
  
  if (issueCount === 0 && warningCount === 0) {
    diagnosis.summary = '✅ 价格计算正常'
  } else if (issueCount === 0) {
    diagnosis.summary = `⚠️ 有${warningCount}个警告`
  } else {
    diagnosis.summary = `❌ 有${issueCount}个问题，${warningCount}个警告`
  }
  
  diagnosis.calculatedPrice = totalPrice
  
  console.log('⚡ 快速诊断结果:', diagnosis)
  return diagnosis
}
