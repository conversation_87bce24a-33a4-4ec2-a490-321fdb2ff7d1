/**
 * 简化版修复测试工具
 * 用于快速验证价格传递和基本功能
 */

// 简单的价格验证
export function validatePrice(price) {
  console.log('💰 验证价格:', price)
  
  const validation = {
    original: price,
    isValid: false,
    issues: []
  }
  
  if (price === undefined || price === null) {
    validation.issues.push('价格为空')
    return validation
  }
  
  const numPrice = parseFloat(price)
  if (isNaN(numPrice)) {
    validation.issues.push('价格不是有效数字')
    return validation
  }
  
  if (numPrice <= 0) {
    validation.issues.push('价格必须大于0')
    return validation
  }
  
  if (numPrice > 1000) {
    validation.issues.push('价格过高，可能有误')
  }
  
  validation.isValid = numPrice > 0
  console.log('💰 价格验证结果:', validation.isValid ? '✅ 有效' : '❌ 无效', validation.issues)
  
  return validation
}

// 验证预约数据
export function validateBookingData(bookingData) {
  console.log('📋 验证预约数据:', bookingData)
  
  const validation = {
    isValid: true,
    issues: []
  }
  
  // 检查必需字段
  const requiredFields = ['venueId', 'date', 'startTime', 'endTime', 'price']
  
  requiredFields.forEach(field => {
    if (!bookingData[field]) {
      validation.issues.push(`缺少字段: ${field}`)
      validation.isValid = false
    }
  })
  
  // 验证价格
  const priceValidation = validatePrice(bookingData.price)
  if (!priceValidation.isValid) {
    validation.issues.push(...priceValidation.issues)
    validation.isValid = false
  }
  
  console.log('📋 预约数据验证结果:', validation.isValid ? '✅ 有效' : '❌ 无效', validation.issues)
  
  return validation
}

// 检查时间段数据质量
export function checkTimeSlotQuality(timeSlots) {
  console.log('⏰ 检查时间段数据质量')
  
  const check = {
    totalSlots: timeSlots.length,
    slotsWithPrice: 0,
    slotsWithoutPrice: 0,
    averagePrice: 0,
    issues: []
  }
  
  if (timeSlots.length === 0) {
    check.issues.push('没有时间段数据')
    return check
  }
  
  let totalPrice = 0
  let priceCount = 0
  
  timeSlots.forEach((slot, index) => {
    if (slot.price && slot.price > 0) {
      check.slotsWithPrice++
      totalPrice += parseFloat(slot.price)
      priceCount++
    } else {
      check.slotsWithoutPrice++
      check.issues.push(`时间段${index + 1}(${slot.startTime}-${slot.endTime})缺少价格`)
    }
  })
  
  if (priceCount > 0) {
    check.averagePrice = Math.round((totalPrice / priceCount) * 100) / 100
  }
  
  console.log('⏰ 时间段质量检查结果:', check)
  
  return check
}

// 快速诊断
export function quickDiagnose(selectedSlots, venue, bookingData) {
  console.log('🔍 快速诊断开始')
  
  const diagnosis = {
    timestamp: new Date().toISOString(),
    timeSlotCheck: null,
    venueCheck: null,
    bookingDataCheck: null,
    overallStatus: 'UNKNOWN',
    recommendations: []
  }
  
  // 1. 检查时间段
  diagnosis.timeSlotCheck = checkTimeSlotQuality(selectedSlots)
  
  // 2. 检查场馆
  diagnosis.venueCheck = {
    hasVenue: !!venue,
    hasPrice: !!(venue && venue.price),
    priceValue: venue?.price,
    isValidPrice: !!(venue && venue.price && venue.price > 0)
  }
  
  // 3. 检查预约数据
  if (bookingData) {
    diagnosis.bookingDataCheck = validateBookingData(bookingData)
  }
  
  // 4. 生成建议
  if (diagnosis.timeSlotCheck.slotsWithoutPrice > 0) {
    diagnosis.recommendations.push('部分时间段缺少价格信息')
  }
  
  if (!diagnosis.venueCheck.isValidPrice) {
    diagnosis.recommendations.push('场馆价格信息有问题')
  }
  
  if (diagnosis.bookingDataCheck && !diagnosis.bookingDataCheck.isValid) {
    diagnosis.recommendations.push('预约数据验证失败')
  }
  
  // 5. 综合评估
  const hasIssues = diagnosis.timeSlotCheck.issues.length > 0 ||
                   !diagnosis.venueCheck.isValidPrice ||
                   (diagnosis.bookingDataCheck && !diagnosis.bookingDataCheck.isValid)
  
  diagnosis.overallStatus = hasIssues ? 'ISSUES_FOUND' : 'OK'
  
  console.log('🔍 快速诊断结果:', diagnosis)
  
  return diagnosis
}

// 实时价格计算测试
export function testPriceCalculation(selectedSlots, venue) {
  console.log('🧮 测试价格计算')
  
  const test = {
    selectedSlots: selectedSlots.length,
    calculations: [],
    totalPrice: 0,
    method: 'unknown'
  }
  
  selectedSlots.forEach((slot, index) => {
    let slotPrice = 0
    let method = ''
    
    if (slot.price && slot.price > 0) {
      slotPrice = parseFloat(slot.price)
      method = 'slot.price'
    } else if (venue && venue.price && venue.price > 0) {
      slotPrice = parseFloat(venue.price) / 2 // 半小时价格
      method = 'venue.price/2'
    } else {
      slotPrice = 60 // 默认价格
      method = 'default'
    }
    
    test.calculations.push({
      index: index + 1,
      timeRange: `${slot.startTime}-${slot.endTime}`,
      price: slotPrice,
      method: method
    })
    
    test.totalPrice += slotPrice
  })
  
  // 确定主要使用的方法
  const methods = test.calculations.map(calc => calc.method)
  const methodCounts = {}
  methods.forEach(method => {
    methodCounts[method] = (methodCounts[method] || 0) + 1
  })
  
  test.method = Object.keys(methodCounts).reduce((a, b) => 
    methodCounts[a] > methodCounts[b] ? a : b
  )
  
  console.log('🧮 价格计算测试结果:', test)
  
  return test
}

// 生成测试报告
export function generateTestReport(selectedSlots, venue, bookingData) {
  console.log('📊 生成测试报告')
  
  const report = {
    timestamp: new Date().toISOString(),
    summary: '',
    details: {
      priceCalculation: testPriceCalculation(selectedSlots, venue),
      diagnosis: quickDiagnose(selectedSlots, venue, bookingData)
    },
    status: 'UNKNOWN',
    issues: [],
    recommendations: []
  }
  
  // 收集问题
  if (report.details.diagnosis.timeSlotCheck.issues.length > 0) {
    report.issues.push(...report.details.diagnosis.timeSlotCheck.issues)
  }
  
  if (!report.details.diagnosis.venueCheck.isValidPrice) {
    report.issues.push('场馆价格无效')
  }
  
  if (report.details.diagnosis.bookingDataCheck && !report.details.diagnosis.bookingDataCheck.isValid) {
    report.issues.push(...report.details.diagnosis.bookingDataCheck.issues)
  }
  
  // 收集建议
  report.recommendations.push(...report.details.diagnosis.recommendations)
  
  // 确定状态
  if (report.issues.length === 0) {
    report.status = 'PASS'
    report.summary = `✅ 测试通过 - 总价格¥${report.details.priceCalculation.totalPrice}`
  } else {
    report.status = 'FAIL'
    report.summary = `❌ 测试失败 - ${report.issues.length}个问题`
  }
  
  console.log('📊 测试报告:', report)
  
  return report
}

// 简单的修复尝试
export function attemptSimpleFix(selectedSlots, venue) {
  console.log('🔧 尝试简单修复')
  
  const fix = {
    originalSlots: selectedSlots,
    fixedSlots: [],
    fixesApplied: [],
    success: false
  }
  
  // 修复时间段价格
  selectedSlots.forEach((slot, index) => {
    const fixedSlot = { ...slot }
    
    if (!slot.price || slot.price <= 0) {
      if (venue && venue.price && venue.price > 0) {
        fixedSlot.price = parseFloat(venue.price) / 2
        fix.fixesApplied.push(`时间段${index + 1}使用场馆价格的一半`)
      } else {
        fixedSlot.price = 60
        fix.fixesApplied.push(`时间段${index + 1}使用默认价格60元`)
      }
    }
    
    fix.fixedSlots.push(fixedSlot)
  })
  
  fix.success = fix.fixedSlots.every(slot => slot.price > 0)
  
  console.log('🔧 简单修复结果:', fix)
  
  return fix
}

// 导出所有功能
export default {
  validatePrice,
  validateBookingData,
  checkTimeSlotQuality,
  quickDiagnose,
  testPriceCalculation,
  generateTestReport,
  attemptSimpleFix
}
