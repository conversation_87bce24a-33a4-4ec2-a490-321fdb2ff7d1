import { get, post, patch } from '@/utils/request.js'

// 获取场馆指定日期的所有时间段
export function getVenueTimeSlots(venueId, date, forceRefresh = false) {
  const options = {
    cache: !forceRefresh, // 如果forceRefresh为true，则禁用缓存
    cacheTTL: forceRefresh ? 0 : 60000 // 强制刷新时设置缓存时间为0
  }

  // 🔧 增强强制刷新功能
  if (forceRefresh) {
    // 添加时间戳和随机数避免所有层级的缓存
    const timestamp = Date.now()
    const randomId = Math.random().toString(36).substring(7)
    const params = {
      _t: timestamp,
      _r: randomId,
      _nocache: 1
    }

    // 设置更强的缓存控制选项
    options.cache = false
    options.cacheTTL = 0
    options.forceRefresh = true
    options.headers = {
      'Cache-Control': 'no-cache, no-store, must-revalidate',
      'Pragma': 'no-cache',
      'Expires': '0'
    }

    console.log(`🚫 [API] 强制无缓存获取时间段 - venueId: ${venueId}, date: ${date}, params:`, params)
    return get(`/timeslots/venue/${venueId}/date/${date}`, params, options)
  }

  console.log(`[API] getVenueTimeSlots - venueId: ${venueId}, date: ${date}, forceRefresh: ${forceRefresh}, options:`, options)
  return get(`/timeslots/venue/${venueId}/date/${date}`, {}, options)
}

// 获取场馆指定日期的可用时间段
export function getAvailableTimeSlots(venueId, date) {
  return get(`/timeslots/venue/${venueId}/date/${date}/available`)
}

// 检查时间段是否可预约
export function checkTimeSlotAvailability(params) {
  return get('/timeslots/check', params)
}

// 为场馆生成指定日期的时间段（仅限管理员）
export function generateTimeSlots(venueId, date) {
  return post(`/timeslots/venue/${venueId}/date/${date}/generate`)
}

// 批量生成未来一周的时间段（仅限管理员）
export function generateWeekTimeSlots(venueId) {
  return post(`/timeslots/venue/${venueId}/generate-week`)
}

// 更新时间段状态（仅限管理员）
export function updateTimeSlotStatus(id, data) {
  return patch(`/timeslots/${id}/status`, data)
}