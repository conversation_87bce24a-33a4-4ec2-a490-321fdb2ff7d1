import { defineStore } from 'pinia'
import * as venueApi from '@/api/venue.js'
import * as timeslotApi from '@/api/timeslot.js'
import { showError } from '@/utils/ui.js'

export const useVenueStore = defineStore('venue', {
  state: () => ({
    venueList: [],
    popularVenues: [],
    venueDetail: null,
    venueTypes: [],
    timeSlots: [],
    searchResults: [],
    loading: false,
    pagination: {
      current: 1,
      pageSize: 10,
      total: 0,
      totalPages: 1
    }
  }),

  getters: {
    // 场馆列表相关 - 这些应该是getter，返回状态值
    venueListGetter: (state) => state.venueList,
    popularVenuesGetter: (state) => state.popularVenues,
    venueDetailGetter: (state) => state.venueDetail,
    venueTypesGetter: (state) => state.venueTypes,
    timeSlotsGetter: (state) => state.timeSlots,
    searchResultsGetter: (state) => state.searchResults,

    // 状态相关
    isLoading: (state) => state.loading,
    getPagination: (state) => state.pagination,

    // 计算属性
    totalVenues: (state) => state.venueList.length,
    hasMoreVenues: (state) => state.pagination.current < state.pagination.totalPages,

    // 按类型筛选场馆
    getVenuesByType: (state) => (typeId) => {
      if (!typeId) return state.venueList
      return state.venueList.filter(venue => venue.typeId === typeId)
    },

    // 获取可用时间段
    getAvailableTimeSlots: (state) => {
      return state.timeSlots.filter(slot => slot.status === 'AVAILABLE')
    }
  },

  actions: {
    // 设置加载状态
    setLoading(loading) {
      this.loading = loading
    },
    
    // 设置场馆列表
    setVenueList({ list, pagination }) {
      this.venueList = list
      if (pagination) {
        this.pagination = { ...this.pagination, ...pagination }
      }
    },
    
    // 追加场馆列表（分页加载）
    appendVenueList(list) {
      this.venueList = [...this.venueList, ...list]
    },
    
    // 设置热门场馆
    setPopularVenues(venues) {
      this.popularVenues = venues
    },
    
    // 设置场馆详情
    setVenueDetail(venue) {
      this.venueDetail = venue
    },
    
    // 设置场馆类型
    setVenueTypes(types) {
      this.venueTypes = types
    },
    
    // 设置时间段
    setTimeSlots(slots) {
      console.log('[VenueStore] setTimeSlots 被调用，参数:', slots)
      console.log('[VenueStore] setTimeSlots 参数类型:', typeof slots)
      console.log('[VenueStore] setTimeSlots 是否为数组:', Array.isArray(slots))

      // 确保设置的是数组
      if (Array.isArray(slots)) {
        this.timeSlots = slots
      } else {
        console.warn('[VenueStore] setTimeSlots 收到非数组参数，强制设置为空数组')
        this.timeSlots = []
      }

      console.log('[VenueStore] setTimeSlots 设置后的值:', this.timeSlots)
    },
    
    // 设置搜索结果
    setSearchResults(results) {
      this.searchResults = results
    },
    
    // 设置分页信息
    setPagination(pagination) {
      this.pagination = { ...this.pagination, ...pagination }
    },

    // 获取场馆列表
    async getVenueList(params = {}) {
      try {
        console.log('[VenueStore] 开始获取场馆列表，参数:', params)
        this.setLoading(true)
        
        // 添加超时处理
        const timeoutPromise = new Promise((_, reject) => {
          setTimeout(() => reject(new Error('请求超时')), 10000) // 10秒超时
        })
        
        const apiPromise = venueApi.getVenueList(params)
        const response = await Promise.race([apiPromise, timeoutPromise])
        
        console.log('[VenueStore] 场馆API响应:', response)
        
        // 处理响应数据
        let list = []
        let pagination = {
          current: 1,
          pageSize: 10,
          total: 0,
          totalPages: 1
        }
        
        if (response && response.data) {
          if (Array.isArray(response.data)) {
            list = response.data
            pagination = {
              current: response.page || params.page || 1,
              pageSize: response.pageSize || params.pageSize || 10,
              total: response.total || response.data.length,
              totalPages: response.totalPages || 1
            }
          } else {
            console.warn('[VenueStore] API响应数据格式异常，使用空数组:', response)
          }
        } else if (response && Array.isArray(response)) {
          // 直接返回数组的情况
          list = response
          pagination.total = response.length
        } else {
          console.warn('[VenueStore] API响应为空或格式错误，使用空数组:', response)
        }
        
        console.log('[VenueStore] 解析的场馆列表:', list)
        console.log('[VenueStore] 分页信息:', pagination)
        
        if (params.page === 1 || params.refresh) {
          this.setVenueList({ list, pagination })
        } else {
          this.appendVenueList(list)
          this.setVenueList({ list: this.venueList, pagination })
        }
        
        return response
      } catch (error) {
        console.error('[VenueStore] 获取场馆列表失败:', error)
        showError(error.message || '获取场馆列表失败')
        throw error
      } finally {
        this.setLoading(false)
      }
    },

    // 获取热门场馆
    async getPopularVenues() {
      try {
        console.log('[VenueStore] 开始获取热门场馆')
        const response = await venueApi.getPopularVenues()
        
        if (response && response.data) {
          this.setPopularVenues(response.data)
          console.log('[VenueStore] 热门场馆获取成功:', response.data)
        }
        
        return response
      } catch (error) {
        console.error('[VenueStore] 获取热门场馆失败:', error)
        showError(error.message || '获取热门场馆失败')
        throw error
      }
    },

    // 获取场馆详情
    async getVenueDetail(venueId) {
      try {
        console.log('[VenueStore] 开始获取场馆详情:', venueId)
        this.setLoading(true)
        
        const response = await venueApi.getVenueDetail(venueId)
        console.log('[VenueStore] 完整API响应:', response)
        console.log('[VenueStore] 响应数据类型:', typeof response)
        console.log('[VenueStore] 响应数据结构:', Object.keys(response || {}))

        if (response && response.data) {
          this.setVenueDetail(response.data)
          console.log('[VenueStore] 场馆详情获取成功:', response.data)
        } else if (response) {
          // 如果没有data字段，可能数据直接在response中
          this.setVenueDetail(response)
          console.log('[VenueStore] 场馆详情获取成功（直接响应）:', response)
        }
        
        return response
      } catch (error) {
        console.error('[VenueStore] 获取场馆详情失败:', error)
        showError(error.message || '获取场馆详情失败')
        throw error
      } finally {
        this.setLoading(false)
      }
    },

    // 获取场馆类型
    async getVenueTypes() {
      try {
        console.log('[VenueStore] 开始获取场馆类型')
        const response = await venueApi.getVenueTypes()
        
        if (response && response.data) {
          this.setVenueTypes(response.data)
          console.log('[VenueStore] 场馆类型获取成功:', response.data)
        }
        
        return response
      } catch (error) {
        console.error('[VenueStore] 获取场馆类型失败:', error)
        showError(error.message || '获取场馆类型失败')
        throw error
      }
    },

    // 获取时间段
    async getTimeSlots(venueId, date, forceRefresh = false) {
      try {
        console.log('[VenueStore] 开始获取时间段:', { venueId, date, forceRefresh })
        this.setLoading(true)

        const response = await timeslotApi.getVenueTimeSlots(venueId, date, forceRefresh)

        if (response && response.data && response.data.length > 0) {
          this.setTimeSlots(response.data)
          console.log('[VenueStore] 时间段获取成功:', response.data)
        } else {
          // 🔧 如果没有时间段数据，使用修复工具生成（恢复并增强原有逻辑）
          console.log('[VenueStore] 没有时间段数据，使用修复工具生成...')

          try {
            // 使用时间段同步修复工具
            const { fixTimeSlotGeneration } = await import('@/utils/timeslot-sync-fix.js')
            const fixResult = await fixTimeSlotGeneration(venueId, date, this)

            if (fixResult.success && fixResult.generatedSlots && fixResult.generatedSlots.length > 0) {
              console.log('[VenueStore] 时间段修复成功:', fixResult.generatedSlots.length, '个时间段')
              console.log('[VenueStore] 同步到后端:', fixResult.syncedToBackend ? '成功' : '失败')
            } else {
              console.warn('[VenueStore] 时间段修复失败，使用前端生成作为备用')
              this.generateDefaultTimeSlots(venueId, date)
            }
          } catch (fixError) {
            console.warn('[VenueStore] 时间段修复工具失败，使用前端生成:', fixError)
            // 如果修复工具失败，回退到原有逻辑
            this.generateDefaultTimeSlots(venueId, date)
          }
        }

        return response
      } catch (error) {
        console.error('[VenueStore] 获取时间段失败:', error)
        // 如果API完全失败，生成默认时间段作为备用
        console.log('[VenueStore] API失败，生成默认时间段作为备用')
        this.generateDefaultTimeSlots(venueId, date)
        // 不抛出错误，让用户可以继续使用默认时间段
      } finally {
        this.setLoading(false)
      }
    },

    // 生成默认时间段（半小时间隔）
    generateDefaultTimeSlots(venueId, date) {
      console.log('[VenueStore] 开始生成默认时间段（半小时间隔）')

      // 获取场馆详情中的价格（半小时价格 = 小时价格 / 2）
      const venueHourPrice = this.venueDetail?.price || 100
      const venueHalfHourPrice = Math.round(venueHourPrice / 2) // 半小时价格
      console.log('[VenueStore] 场馆小时价格:', venueHourPrice)
      console.log('[VenueStore] 场馆半小时价格:', venueHalfHourPrice)

      const defaultSlots = []
      const startHour = 9
      const endHour = 22

      // 生成半小时间隔的时间段
      for (let hour = startHour; hour < endHour; hour++) {
        for (let minute = 0; minute < 60; minute += 30) {
          const startTime = `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`
          const endMinute = minute + 30
          const nextHour = endMinute >= 60 ? hour + 1 : hour
          const actualEndMinute = endMinute >= 60 ? 0 : endMinute
          const endTime = `${nextHour.toString().padStart(2, '0')}:${actualEndMinute.toString().padStart(2, '0')}`

          // 避免超过营业时间
          if (nextHour > endHour) break

          defaultSlots.push({
            id: `default_${venueId}_${date}_${hour}_${minute}`,
            venueId: parseInt(venueId),
            date: date,
            startTime: startTime,
            endTime: endTime,
            price: venueHalfHourPrice, // 使用场馆的半小时价格
            status: 'AVAILABLE'
          })
        }
      }

      console.log('[VenueStore] 生成的默认时间段数量:', defaultSlots.length)
      console.log('[VenueStore] 生成的默认时间段详情:', defaultSlots)

      this.setTimeSlots(defaultSlots)
      console.log('[VenueStore] 默认时间段设置完成')
    },

    // 获取场馆时间段（别名方法，用于兼容性）
    async getVenueTimeSlots(params) {
      if (typeof params === 'object' && params.venueId && params.date) {
        return await this.getTimeSlots(params.venueId, params.date)
      }
      // 如果参数格式不对，尝试直接调用
      return await this.getTimeSlots(params)
    },

    // 搜索场馆
    async searchVenues(params) {
      try {
        console.log('[VenueStore] 开始搜索场馆:', params)
        this.setLoading(true)

        const response = await venueApi.searchVenues(params)

        if (response && response.data) {
          this.setSearchResults(response.data)
          console.log('[VenueStore] 场馆搜索成功:', response.data)
        } else {
          // 如果搜索API返回空结果，尝试使用本地过滤作为备用方案
          console.log('[VenueStore] 搜索API返回空结果，尝试本地过滤...')
          await this.searchVenuesLocally(params)
        }

        return response
      } catch (error) {
        console.error('[VenueStore] 搜索场馆失败:', error)
        // 如果搜索API失败，尝试使用本地过滤作为备用方案
        console.log('[VenueStore] 搜索API失败，尝试本地过滤...')
        try {
          await this.searchVenuesLocally(params)
        } catch (localError) {
          console.error('[VenueStore] 本地搜索也失败:', localError)
          showError('搜索功能暂时不可用')
        }
        throw error
      } finally {
        this.setLoading(false)
      }
    },

    // 本地搜索备用方案
    async searchVenuesLocally(params) {
      try {
        // 获取所有场馆数据
        const allVenuesResponse = await venueApi.getVenueList({ page: 1, pageSize: 100 })

        if (allVenuesResponse && allVenuesResponse.data) {
          const allVenues = allVenuesResponse.data
          const keyword = params.keyword?.toLowerCase() || ''

          // 本地过滤
          const filteredVenues = allVenues.filter(venue => {
            const nameMatch = venue.name?.toLowerCase().includes(keyword)
            const typeMatch = venue.type?.toLowerCase().includes(keyword)
            const locationMatch = venue.location?.toLowerCase().includes(keyword)
            const descriptionMatch = venue.description?.toLowerCase().includes(keyword)

            return nameMatch || typeMatch || locationMatch || descriptionMatch
          })

          this.setSearchResults(filteredVenues)
          console.log('[VenueStore] 本地搜索完成，结果:', filteredVenues)
        }
      } catch (error) {
        console.error('[VenueStore] 本地搜索失败:', error)
        throw error
      }
    },

    // 清空场馆详情
    clearVenueDetail() {
      this.venueDetail = null
    },
    
    // 清空搜索结果
    clearSearchResults() {
      this.searchResults = []
    },
    
    // 重置分页
    resetPagination() {
      this.pagination = {
        current: 1,
        pageSize: 10,
        total: 0,
        totalPages: 1
      }
    },

    // 清除时间段缓存
    clearTimeSlots() {
      console.log('[VenueStore] 清除时间段缓存')
      this.timeSlots = []
      this.selectedTimeSlots = []

      // 清除旧的请求缓存（兼容性）
      if (typeof window !== 'undefined' && window.requestCache) {
        // 清除所有时间段相关的缓存
        Object.keys(window.requestCache).forEach(key => {
          if (key.includes('/api/timeslots/') || key.includes('/timeslots/')) {
            delete window.requestCache[key]
            console.log('[VenueStore] 清除旧缓存键:', key)
          }
        })
      }

      // 清除新的缓存管理器中的缓存
      if (typeof window !== 'undefined' && window.cacheManager) {
        // 使用cacheManager的clearUrl方法清除时间段相关缓存
        window.cacheManager.clearUrl('/timeslots/')
        console.log('[VenueStore] 使用cacheManager.clearUrl清除时间段缓存')

        // 额外清除可能的其他格式
        window.cacheManager.clearUrl('/api/timeslots/')
        console.log('[VenueStore] 清除/api/timeslots/相关缓存')

        // 彻底清除所有缓存（作为最后手段）
        let clearedCount = 0
        for (const [key] of window.cacheManager.cache.entries()) {
          if (key.includes('timeslots') || key.includes('TimeSlot')) {
            window.cacheManager.cache.delete(key)
            clearedCount++
            console.log('[VenueStore] 强制清除缓存键:', key)
          }
        }
        console.log(`[VenueStore] 强制清除了 ${clearedCount} 个时间段相关缓存`)
      }
    },

    // 强力刷新时间段（清除所有缓存并重新获取）
    async forceRefreshTimeSlots(venueId, date) {
      console.log('🚀 [VenueStore] 执行强力刷新时间段')

      try {
        // 步骤1：清除Store中的时间段数据和所有缓存
        this.clearTimeSlots()

        // 步骤2：等待缓存清除
        await new Promise(resolve => setTimeout(resolve, 300))

        // 步骤3：强制获取新数据（禁用缓存）
        console.log('📡 [VenueStore] 强制获取新数据')
        const response = await timeslotApi.getVenueTimeSlots(venueId, date, true)

        if (response && response.data && response.data.length > 0) {
          this.setTimeSlots(response.data)
          console.log('✅ [VenueStore] 强力刷新成功，获取到时间段:', response.data.length)

          // 验证数据是否真的更新了
          const updatedSlots = this.timeSlots
          console.log('📊 [VenueStore] 刷新后的时间段状态:', updatedSlots.map(slot => ({
            id: slot.id,
            time: `${slot.startTime}-${slot.endTime}`,
            status: slot.status
          })))

          return {
            success: true,
            count: response.data.length,
            data: response.data
          }
        } else {
          console.warn('⚠️ [VenueStore] 强力刷新获取到空数据')
          return {
            success: false,
            error: '获取到空数据'
          }
        }
      } catch (error) {
        console.error('❌ [VenueStore] 强力刷新失败:', error)
        return {
          success: false,
          error: error.message
        }
      }
    }
  }
})
