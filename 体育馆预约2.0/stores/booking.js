import { defineStore } from 'pinia'
import * as bookingApi from '@/api/booking.js'
import * as userApi from '@/api/user.js'
import * as sharingApi from '@/api/sharing.js'
import { showSuccess, showError } from '@/utils/ui.js'

export const useBookingStore = defineStore('booking', {
  state: () => ({
    bookingList: [],
    bookingDetail: null,
    sharingOrders: [],
    userSharingOrders: [],
    joinedSharingOrders: [],
    sharingDetail: null,
    loading: false,
    pagination: {
      current: 1,
      pageSize: 10,
      total: 0,
      totalPages: 1,
      currentPage: 1
    }
  }),

  getters: {
    // 基础getters
    getBookingList: (state) => state.bookingList,
    getBookingDetail: (state) => state.bookingDetail,
    getSharingOrders: (state) => state.sharingOrders,
    getUserSharingOrders: (state) => state.userSharingOrders,
    getJoinedSharingOrders: (state) => state.joinedSharingOrders,
    getSharingDetail: (state) => state.sharingDetail,
    isLoading: (state) => state.loading,
    getPagination: (state) => state.pagination,
    
    // 计算属性
    totalBookings: (state) => state.bookingList.length,
    totalSharingOrders: (state) => state.sharingOrders.length,
    totalUserSharingOrders: (state) => state.userSharingOrders.length,
    totalJoinedSharingOrders: (state) => state.joinedSharingOrders.length,
    
    // 按状态筛选预订
    getBookingsByStatus: (state) => (status) => {
      return state.bookingList.filter(booking => booking.status === status)
    },
    
    // 待确认的预订
    getPendingBookings: (state) => {
      return state.bookingList.filter(booking => booking.status === 'PENDING')
    },
    
    // 已确认的预订
    getConfirmedBookings: (state) => {
      return state.bookingList.filter(booking => booking.status === 'CONFIRMED')
    },
    
    // 是否有更多数据
    hasMoreData: (state) => {
      return state.pagination.current < state.pagination.totalPages
    }
  },

  actions: {
    // 设置加载状态
    setLoading(loading) {
      this.loading = loading
    },
    
    // 设置预订列表
    setBookingList({ list, pagination }) {
      this.bookingList = Array.isArray(list) ? list : []
      if (pagination) {
        this.pagination = { ...this.pagination, ...pagination }
      }
    },
    
    // 追加预订列表
    appendBookingList(list) {
      const newList = Array.isArray(list) ? list : []
      this.bookingList = [...this.bookingList, ...newList]
    },
    
    // 设置预订详情
    setBookingDetail(detail) {
      this.bookingDetail = detail
    },
    
    // 设置分享订单列表
    setSharingOrders(orders) {
      this.sharingOrders = Array.isArray(orders) ? orders : []
    },
    
    // 设置用户分享订单
    setUserSharingOrders(orders) {
      this.userSharingOrders = Array.isArray(orders) ? orders : []
    },
    
    // 设置加入的分享订单
    setJoinedSharingOrders(orders) {
      this.joinedSharingOrders = Array.isArray(orders) ? orders : []
    },
    
    // 设置分享详情
    setSharingDetail(detail) {
      this.sharingDetail = detail
    },
    
    // 设置分页信息
    setPagination(pagination) {
      this.pagination = { ...this.pagination, ...pagination }
    },
    
    // 更新预订状态
    updateBookingStatus({ bookingId, status }) {
      const booking = this.bookingList.find(b => b.id === bookingId)
      if (booking) {
        booking.status = status
      }
    },
    
    // 创建预订
    async createBooking(bookingData) {
      try {
        console.log('[BookingStore] 发起预约创建请求，数据:', bookingData)
        this.setLoading(true)
        
        const response = await bookingApi.createBooking(bookingData)
        console.log('[BookingStore] 预约创建API响应:', response)
        console.log('[BookingStore] response.data:', response.data)
        console.log('[BookingStore] response.data?.id:', response.data?.id)

        showSuccess('预约成功')

        // 返回响应数据，确保包含订单ID
        const result = response.data || response
        console.log('[BookingStore] 最终返回结果:', result)
        return result
      } catch (error) {
        console.error('[BookingStore] 创建预约失败:', error)
        showError(error.message || '预约失败')
        throw error
      } finally {
        this.setLoading(false)
      }
    },

    // 获取我的预订列表（别名方法，用于测试兼容性）
    async getMyBookings(params = {}) {
      return await this.getBookingList(params)
    },

    // 获取预订详情
    async getBookingDetails(bookingId) {
      try {
        console.log('[BookingStore] 获取预订详情，ID:', bookingId)
        this.setLoading(true)

        const response = await bookingApi.getBookingDetails(bookingId)
        console.log('[BookingStore] 预订详情获取成功:', response)

        return response
      } catch (error) {
        console.error('[BookingStore] 获取预订详情失败:', error)
        showError(error.message || '获取预订详情失败')
        throw error
      } finally {
        this.setLoading(false)
      }
    },

    // 创建拼场预约
    async createSharedBooking(bookingData) {
      try {
        console.log('[BookingStore] 开始创建拼场预约')
        this.setLoading(true)
        
        const response = await bookingApi.createSharedBooking(bookingData)
        
        showSuccess('拼场预约成功')
        console.log('[BookingStore] 拼场预约创建成功')
        
        return response
      } catch (error) {
        console.error('[BookingStore] 创建拼场预约失败:', error)
        showError(error.message || '拼场预约失败')
        throw error
      } finally {
        this.setLoading(false)
      }
    },
    
    // 获取用户预约列表
    async getUserBookings(params = {}) {
      try {
        console.log('[BookingStore] 开始获取用户预约列表，参数:', params)
        this.setLoading(true)
        
        const response = await userApi.getUserBookings(params)
        
        console.log('[BookingStore] API响应原始数据:', response)
        console.log('[BookingStore] response.data:', response.data)
        console.log('[BookingStore] response.data类型:', typeof response.data)
        
        const { data, total, page, pageSize, totalPages } = response
        
        console.log('[BookingStore] 解构后的数据:')
        console.log('data:', data)
        console.log('data类型:', typeof data)
        console.log('data是否为数组:', Array.isArray(data))
        console.log('total:', total)
        console.log('page:', page)
        console.log('pageSize:', pageSize)
        console.log('totalPages:', totalPages)
        
        const pagination = {
          current: page,
          pageSize: pageSize,
          total: total,
          totalPages: totalPages,
          currentPage: page
        }
        
        if (params.page === 1 || params.refresh) {
          console.log('[BookingStore] 设置新的预约列表，数据长度:', (data || []).length)
          this.setBookingList({ list: data || [], pagination: pagination })
        } else {
          console.log('[BookingStore] 追加预约列表，新增数据长度:', (data || []).length)
          this.appendBookingList(data || [])
          this.setPagination(pagination)
        }
        
        return response
      } catch (error) {
        console.error('[BookingStore] 获取用户预约列表失败:', error)
        // 清空列表并重置分页
        this.setBookingList({ 
          list: [], 
          pagination: { current: 1, pageSize: 10, total: 0, totalPages: 1, currentPage: 1 } 
        })
        showError(error.message || '获取预约列表失败')
        throw error
      } finally {
        this.setLoading(false)
      }
    },
    
    // 获取预约详情
    async getBookingDetail(bookingId) {
      try {
        console.log('[BookingStore] 🌐 发起API请求获取订单详情, ID:', bookingId)
        console.log('[BookingStore] 🌐 ID类型:', typeof bookingId)
        this.setLoading(true)
        
        if (!bookingId) {
          throw new Error('订单ID不能为空')
        }
        
        const response = await bookingApi.getBookingDetail(bookingId)
        console.log('[BookingStore] 📡 完整API响应:', response)
        console.log('[BookingStore] 📡 响应类型:', typeof response)
        console.log('[BookingStore] 📡 响应是否为空:', !response)
        
        // 处理不同的响应数据结构
        let bookingData = null
        if (response && typeof response === 'object') {
          // 如果response直接是数据对象
          if (response.id || response.orderNo) {
            bookingData = response
            console.log('[BookingStore] 📡 使用response作为数据')
          }
          // 如果response有data属性
          else if (response.data) {
            bookingData = response.data
            console.log('[BookingStore] 📡 使用response.data作为数据')
          }
          // 如果response有result属性
          else if (response.result) {
            bookingData = response.result
            console.log('[BookingStore] 📡 使用response.result作为数据')
          }
          else {
            console.warn('[BookingStore] 📡 响应数据结构未知:', Object.keys(response))
            // 尝试直接使用response
            bookingData = response
          }
        } else {
          console.error('[BookingStore] 📡 响应数据无效:', response)
          throw new Error('服务器返回的数据格式不正确')
        }
        
        console.log('[BookingStore] 📡 处理后的订单数据:', bookingData)
        console.log('[BookingStore] 📡 数据类型:', typeof bookingData)
        console.log('[BookingStore] 📡 数据键:', bookingData ? Object.keys(bookingData) : 'null')
        console.log('[BookingStore] ⏰ API返回的开始时间:', bookingData?.startTime)
        console.log('[BookingStore] ⏰ API返回的结束时间:', bookingData?.endTime)
        console.log('[BookingStore] 💰 API返回的总价格:', bookingData?.totalPrice)
        console.log('[BookingStore] 🏷️ API返回的订单号(orderNo):', bookingData?.orderNo)
        console.log('[BookingStore] 🏷️ API返回的订单号(orderNumber):', bookingData?.orderNumber)
        console.log('[BookingStore] 🆔 API返回的ID:', bookingData?.id)
        
        if (!bookingData) {
          throw new Error('未能获取到有效的订单数据')
        }
        
        // 字段映射：如果后端返回的是orderNumber，映射为orderNo
        if (bookingData.orderNumber && !bookingData.orderNo) {
          bookingData.orderNo = bookingData.orderNumber
          console.log('[BookingStore] 🔄 字段映射: orderNumber -> orderNo:', bookingData.orderNo)
        }
        
        this.setBookingDetail(bookingData)
        console.log('[BookingStore] ✅ 数据已存储到store')
        return response
      } catch (error) {
        console.error('[BookingStore] ❌ API请求失败:', error)
        console.error('[BookingStore] ❌ 错误类型:', error.constructor.name)
        console.error('[BookingStore] ❌ 错误消息:', error.message)
        console.error('[BookingStore] ❌ 错误堆栈:', error.stack)
        
        // 清空详情数据
        this.setBookingDetail(null)
        
        showError(error.message || '获取预约详情失败')
        throw error
      } finally {
        this.setLoading(false)
      }
    },
    
    // 取消预约
    async cancelBooking(bookingId) {
      try {
        console.log('[BookingStore] 开始取消预约:', bookingId)

        const response = await bookingApi.cancelBooking(bookingId)

        // 立即更新本地状态
        this.updateBookingStatus({ bookingId, status: 'CANCELLED' })

        // 发送全局事件通知拼场大厅刷新（如果是拼场订单）
        console.log('[BookingStore] 发送订单取消事件通知')
        uni.$emit('orderCancelled', {
          orderId: bookingId,
          type: 'booking'
        })

        // 延迟重新获取数据以确保服务器状态同步
        setTimeout(() => {
          this.getUserBookings({ page: 1, pageSize: 10, refresh: true })
        }, 1000)

        showSuccess('预约已取消')
        console.log('[BookingStore] 预约取消成功')
        return response
      } catch (error) {
        console.error('[BookingStore] 取消预约失败:', error)
        showError(error.message || '取消预约失败')
        throw error
      }
    },
    
    // 清空预订详情
    clearBookingDetail() {
      this.bookingDetail = null
    },
    
    // 重置分页
    resetPagination() {
      this.pagination = {
        current: 1,
        pageSize: 10,
        total: 0,
        totalPages: 1,
        currentPage: 1
      }
    },

    // === 分享相关功能 ===

    // 申请拼场
    async createSharingOrder({ orderId, data }) {
      try {
        console.log('[BookingStore] 开始申请拼场')
        this.setLoading(true)

        const response = await sharingApi.applySharedBooking(orderId, data)

        showSuccess('拼场申请已发送')
        console.log('[BookingStore] 拼场申请成功')
        return response
      } catch (error) {
        console.error('[BookingStore] 申请拼场失败:', error)
        showError(error.message || '申请拼场失败')
        throw error
      } finally {
        this.setLoading(false)
      }
    },

    // 获取可拼场的订单列表
    async getSharingOrdersList(params = {}) {
      try {
        console.log('[BookingStore] 开始获取拼场订单，参数:', params)
        this.setLoading(true)

        const response = await sharingApi.getJoinableSharingOrders(params)
        console.log('[BookingStore] 拼场订单API响应:', response)

        if (response && (response.list || response.data)) {
          // 处理两种可能的响应格式：直接返回数据 或 包装在data中
          const responseData = response.list ? response : response.data
          const orders = responseData.list || responseData.data || []

          console.log('[BookingStore] 解析的拼场订单:', orders)

          if (params.page === 1 || params.refresh) {
            this.setSharingOrders(orders)
          } else {
            // 追加数据
            this.sharingOrders = [...this.sharingOrders, ...orders]
          }

          // 更新分页信息
          if (responseData.pagination) {
            this.setPagination(responseData.pagination)
          }
        }

        return response
      } catch (error) {
        console.error('[BookingStore] 获取拼场订单失败:', error)
        showError(error.message || '获取拼场订单失败')
        throw error
      } finally {
        this.setLoading(false)
      }
    },

    // 创建拼场订单
    async createSharingOrderNew(sharingData) {
      try {
        console.log('[BookingStore] 开始创建拼场订单')

        const response = await sharingApi.createSharingOrder(sharingData)

        showSuccess('拼场订单创建成功')
        console.log('[BookingStore] 拼场订单创建成功')
        return response.data
      } catch (error) {
        console.error('[BookingStore] 创建拼场订单失败:', error)
        showError(error.message || '创建拼场订单失败')
        throw error
      }
    },

    // 获取拼场订单详情
    async getSharingOrderDetail(orderId) {
      try {
        console.log('[BookingStore] 开始获取拼场订单详情:', orderId)

        const response = await sharingApi.getSharingOrderById(orderId)

        this.setSharingDetail(response.data)
        console.log('[BookingStore] 拼场订单详情获取成功')
        return response.data
      } catch (error) {
        console.error('[BookingStore] 获取拼场订单详情失败:', error)
        showError(error.message || '获取拼场订单详情失败')
        throw error
      }
    },

    // 加入拼场订单
    async joinSharingOrder(orderId) {
      try {
        console.log('[BookingStore] 开始加入拼场订单:', orderId)

        const response = await sharingApi.joinSharingOrder(orderId)

        showSuccess('加入拼场成功')
        console.log('[BookingStore] 加入拼场成功')
        return response.data
      } catch (error) {
        console.error('[BookingStore] 加入拼场失败:', error)
        showError(error.message || '加入拼场失败')
        throw error
      }
    },

    // 获取我创建的拼场订单
    async getMyCreatedSharingOrders() {
      try {
        console.log('[BookingStore] 开始获取我创建的拼场订单')

        const response = await sharingApi.getMyCreatedSharingOrders()

        this.setUserSharingOrders(response.data || [])
        console.log('[BookingStore] 我创建的拼场订单获取成功')
        return response.data
      } catch (error) {
        console.error('[BookingStore] 获取我创建的拼场订单失败:', error)
        showError(error.message || '获取我创建的拼场订单失败')
        throw error
      }
    },

    // 处理拼场申请
    async handleSharingRequest({ requestId, data }) {
      try {
        console.log('[BookingStore] 开始处理拼场申请:', { requestId, data })

        const response = await sharingApi.handleSharedRequest(requestId, data)

        showSuccess(data.status === 'APPROVED' ? '已同意拼场申请' : '已拒绝拼场申请')
        console.log('[BookingStore] 拼场申请处理成功')
        return response
      } catch (error) {
        console.error('[BookingStore] 处理拼场申请失败:', error)
        showError(error.message || '处理拼场申请失败')
        throw error
      }
    },

    // 获取我发出的拼场申请
    async getUserSharingOrders(params = {}) {
      try {
        console.log('[BookingStore] 开始获取我发出的拼场申请')

        const response = await sharingApi.getMySharedRequests(params)

        this.setUserSharingOrders(response.data || [])
        console.log('[BookingStore] 我发出的拼场申请获取成功')
        return response
      } catch (error) {
        console.error('[BookingStore] 获取拼场申请失败:', error)
        showError(error.message || '获取拼场申请失败')
        throw error
      }
    },

    // 获取我收到的拼场申请
    async getUserJoinedSharingOrders(params = {}) {
      try {
        console.log('[BookingStore] 开始获取我收到的拼场申请')

        const response = await sharingApi.getReceivedSharedRequests(params)

        this.setJoinedSharingOrders(response.data || [])
        console.log('[BookingStore] 我收到的拼场申请获取成功')
        return response
      } catch (error) {
        console.error('[BookingStore] 获取拼场申请失败:', error)
        showError(error.message || '获取拼场申请失败')
        throw error
      }
    },

    // 获取拼场详情
    async getSharingDetail(sharingId) {
      try {
        console.log('[BookingStore] 开始获取拼场详情:', sharingId)
        this.setLoading(true)

        const response = await sharingApi.getSharingOrderById(sharingId)

        this.setSharingDetail(response.data)
        console.log('[BookingStore] 拼场详情获取成功')
        return response
      } catch (error) {
        console.error('[BookingStore] 获取拼场详情失败:', error)
        showError(error.message || '获取拼场详情失败')
        throw error
      } finally {
        this.setLoading(false)
      }
    },

    // 移除拼场参与者
    async removeSharingParticipant({ sharingId, participantId }) {
      try {
        console.log('[BookingStore] 开始移除拼场参与者:', { sharingId, participantId })
        this.setLoading(true)

        // 调用API移除参与者
        const response = await sharingApi.removeSharingParticipant(sharingId, participantId)

        // 重新获取拼场详情以确保数据同步
        await this.getSharingDetail(sharingId)

        showSuccess('参与者已移除')
        console.log('[BookingStore] 移除拼场参与者成功')
        return response
      } catch (error) {
        console.error('[BookingStore] 移除拼场参与者失败:', error)
        showError(error.message || '移除参与者失败')
        throw error
      } finally {
        this.setLoading(false)
      }
    },

    // 更新拼场设置
    async updateSharingSettings({ sharingId, settings }) {
      try {
        console.log('[BookingStore] 开始更新拼场设置:', { sharingId, settings })
        this.setLoading(true)

        // 调用API更新设置
        const response = await sharingApi.updateSharingSettings(sharingId, settings)

        // 重新获取拼场详情以确保数据同步
        await this.getSharingDetail(sharingId)

        showSuccess('拼场设置已更新')
        console.log('[BookingStore] 更新拼场设置成功')
        return response
      } catch (error) {
        console.error('[BookingStore] 更新拼场设置失败:', error)
        showError(error.message || '更新拼场设置失败')
        throw error
      } finally {
        this.setLoading(false)
      }
    }
  }
})
