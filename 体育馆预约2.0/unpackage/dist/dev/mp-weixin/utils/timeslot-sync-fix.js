"use strict";
Object.defineProperty(exports, Symbol.toStringTag, { value: "Module" });
const common_vendor = require("../common/vendor.js");
const api_timeslot = require("../api/timeslot.js");
async function fixTimeSlotGeneration(venueId, date, venueStore) {
  common_vendor.index.__f__("log", "at utils/timeslot-sync-fix.js:10", "🔧 修复时间段生成和同步逻辑");
  const fix = {
    venueId,
    date,
    steps: [],
    success: false,
    generatedSlots: null,
    syncedToBackend: false,
    error: null
  };
  try {
    fix.steps.push("检查当前时间段状态");
    const currentSlots = venueStore.timeSlots || [];
    common_vendor.index.__f__("log", "at utils/timeslot-sync-fix.js:26", "📊 当前时间段数量:", currentSlots.length);
    fix.steps.push("尝试后端生成时间段");
    try {
      common_vendor.index.__f__("log", "at utils/timeslot-sync-fix.js:31", "📡 调用后端生成API...");
      const generateResponse = await api_timeslot.generateTimeSlots(venueId, date);
      common_vendor.index.__f__("log", "at utils/timeslot-sync-fix.js:33", "✅ 后端生成API调用成功:", generateResponse);
      fix.steps.push("获取后端生成的时间段");
      const { getVenueTimeSlots } = await "../api/timeslot.js";
      const fetchResponse = await getVenueTimeSlots(venueId, date, true);
      if (fetchResponse && fetchResponse.data && fetchResponse.data.length > 0) {
        fix.generatedSlots = fetchResponse.data;
        fix.syncedToBackend = true;
        fix.success = true;
        venueStore.setTimeSlots(fetchResponse.data);
        common_vendor.index.__f__("log", "at utils/timeslot-sync-fix.js:47", "✅ 后端生成并同步成功:", fetchResponse.data.length, "个时间段");
        fix.steps.push(`成功生成${fetchResponse.data.length}个时间段`);
      } else {
        common_vendor.index.__f__("warn", "at utils/timeslot-sync-fix.js:51", "⚠️ 后端生成API成功但返回空数据");
        throw new Error("后端生成返回空数据");
      }
    } catch (backendError) {
      common_vendor.index.__f__("warn", "at utils/timeslot-sync-fix.js:56", "⚠️ 后端生成失败，使用前端生成:", backendError);
      fix.steps.push("后端生成失败，使用前端生成");
      const frontendSlots = generateFrontendTimeSlots(venueId, date, venueStore.venueDetail);
      fix.generatedSlots = frontendSlots;
      venueStore.setTimeSlots(frontendSlots);
      common_vendor.index.__f__("log", "at utils/timeslot-sync-fix.js:65", "✅ 前端生成成功:", frontendSlots.length, "个时间段");
      fix.steps.push("尝试同步前端生成的时间段到后端");
      try {
        await syncFrontendSlotsToBackend(frontendSlots, venueId, date);
        fix.syncedToBackend = true;
        common_vendor.index.__f__("log", "at utils/timeslot-sync-fix.js:72", "✅ 前端时间段同步到后端成功");
        fix.steps.push("同步到后端成功");
      } catch (syncError) {
        common_vendor.index.__f__("warn", "at utils/timeslot-sync-fix.js:75", "⚠️ 同步到后端失败:", syncError);
        fix.steps.push("同步到后端失败，但前端可用");
      }
      fix.success = true;
    }
  } catch (error) {
    common_vendor.index.__f__("error", "at utils/timeslot-sync-fix.js:83", "❌ 时间段生成修复失败:", error);
    fix.error = error.message;
    fix.steps.push(`修复失败: ${error.message}`);
  }
  common_vendor.index.__f__("log", "at utils/timeslot-sync-fix.js:88", "🔧 时间段生成修复结果:", fix);
  return fix;
}
function generateFrontendTimeSlots(venueId, date, venue) {
  common_vendor.index.__f__("log", "at utils/timeslot-sync-fix.js:94", "🏗️ 前端生成时间段");
  const venueHourPrice = (venue == null ? void 0 : venue.price) || 120;
  const venueHalfHourPrice = Math.round(venueHourPrice / 2);
  common_vendor.index.__f__("log", "at utils/timeslot-sync-fix.js:100", "💰 场馆小时价格:", venueHourPrice);
  common_vendor.index.__f__("log", "at utils/timeslot-sync-fix.js:101", "💰 场馆半小时价格:", venueHalfHourPrice);
  const slots = [];
  const startHour = 9;
  const endHour = 22;
  for (let hour = startHour; hour < endHour; hour++) {
    for (let minute = 0; minute < 60; minute += 30) {
      const startTime = `${hour.toString().padStart(2, "0")}:${minute.toString().padStart(2, "0")}`;
      const endMinute = minute + 30;
      const endHour2 = endMinute >= 60 ? hour + 1 : hour;
      const actualEndMinute = endMinute >= 60 ? 0 : endMinute;
      const endTime = `${endHour2.toString().padStart(2, "0")}:${actualEndMinute.toString().padStart(2, "0")}`;
      if (endHour2 >= endHour2 && actualEndMinute > 0 && endHour2 >= endHour2) {
        break;
      }
      const slot = {
        id: `default_${venueId}_${date}_${hour}_${minute}`,
        venueId: parseInt(venueId),
        date,
        startTime,
        endTime,
        status: "AVAILABLE",
        price: venueHalfHourPrice,
        isDefault: true
        // 标记为前端生成的默认时间段
      };
      slots.push(slot);
    }
  }
  common_vendor.index.__f__("log", "at utils/timeslot-sync-fix.js:136", "🏗️ 前端生成完成:", slots.length, "个时间段");
  return slots;
}
async function syncFrontendSlotsToBackend(slots, venueId, date) {
  common_vendor.index.__f__("log", "at utils/timeslot-sync-fix.js:142", "🔄 同步前端时间段到后端");
  try {
    await api_timeslot.generateTimeSlots(venueId, date);
    common_vendor.index.__f__("log", "at utils/timeslot-sync-fix.js:148", "✅ 通过后端生成API同步成功");
  } catch (error) {
    common_vendor.index.__f__("warn", "at utils/timeslot-sync-fix.js:150", "⚠️ 后端同步失败:", error);
    throw error;
  }
}
function validateTimeSlots(timeSlots) {
  common_vendor.index.__f__("log", "at utils/timeslot-sync-fix.js:157", "🔍 验证时间段数据完整性");
  const validation = {
    totalSlots: timeSlots.length,
    validSlots: 0,
    invalidSlots: 0,
    issues: [],
    priceIssues: 0,
    timeIssues: 0
  };
  timeSlots.forEach((slot, index) => {
    let isValid = true;
    if (!slot.id || !slot.startTime || !slot.endTime || !slot.status) {
      validation.issues.push(`时间段${index + 1}缺少必需字段`);
      isValid = false;
    }
    if (!slot.price || slot.price <= 0) {
      validation.priceIssues++;
      validation.issues.push(`时间段${index + 1}价格无效`);
      isValid = false;
    }
    const timeRegex = /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/;
    if (!timeRegex.test(slot.startTime) || !timeRegex.test(slot.endTime)) {
      validation.timeIssues++;
      validation.issues.push(`时间段${index + 1}时间格式错误`);
      isValid = false;
    }
    if (isValid) {
      validation.validSlots++;
    } else {
      validation.invalidSlots++;
    }
  });
  common_vendor.index.__f__("log", "at utils/timeslot-sync-fix.js:199", "🔍 时间段验证结果:", validation);
  return validation;
}
async function forceRegenerateTimeSlots(venueId, date, venueStore) {
  var _a;
  common_vendor.index.__f__("log", "at utils/timeslot-sync-fix.js:205", "🔄 强制重新生成时间段");
  const regenerate = {
    success: false,
    method: "unknown",
    slotsCount: 0,
    error: null
  };
  try {
    venueStore.clearTimeSlots();
    await new Promise((resolve) => setTimeout(resolve, 200));
    const fixResult = await fixTimeSlotGeneration(venueId, date, venueStore);
    if (fixResult.success) {
      regenerate.success = true;
      regenerate.method = fixResult.syncedToBackend ? "backend" : "frontend";
      regenerate.slotsCount = ((_a = fixResult.generatedSlots) == null ? void 0 : _a.length) || 0;
      common_vendor.index.__f__("log", "at utils/timeslot-sync-fix.js:228", "✅ 强制重新生成成功");
    } else {
      regenerate.error = fixResult.error;
      common_vendor.index.__f__("error", "at utils/timeslot-sync-fix.js:231", "❌ 强制重新生成失败");
    }
  } catch (error) {
    common_vendor.index.__f__("error", "at utils/timeslot-sync-fix.js:235", "❌ 强制重新生成过程出错:", error);
    regenerate.error = error.message;
  }
  return regenerate;
}
async function checkTimeSlotSyncStatus(venueId, date, venueStore) {
  common_vendor.index.__f__("log", "at utils/timeslot-sync-fix.js:244", "🔍 检查时间段同步状态");
  const status = {
    frontendSlots: 0,
    backendSlots: 0,
    synced: false,
    needsSync: false,
    issues: []
  };
  try {
    const frontendSlots = venueStore.timeSlots || [];
    status.frontendSlots = frontendSlots.length;
    const { getVenueTimeSlots } = await "../api/timeslot.js";
    const backendResponse = await getVenueTimeSlots(venueId, date, true);
    const backendSlots = (backendResponse == null ? void 0 : backendResponse.data) || [];
    status.backendSlots = backendSlots.length;
    if (status.frontendSlots > 0 && status.backendSlots > 0) {
      status.synced = true;
    } else if (status.frontendSlots > 0 && status.backendSlots === 0) {
      status.needsSync = true;
      status.issues.push("前端有时间段但后端没有");
    } else if (status.frontendSlots === 0 && status.backendSlots === 0) {
      status.needsSync = true;
      status.issues.push("前后端都没有时间段数据");
    }
  } catch (error) {
    common_vendor.index.__f__("error", "at utils/timeslot-sync-fix.js:277", "❌ 检查同步状态失败:", error);
    status.issues.push(`检查失败: ${error.message}`);
  }
  common_vendor.index.__f__("log", "at utils/timeslot-sync-fix.js:281", "🔍 时间段同步状态:", status);
  return status;
}
async function autoFixTimeSlotIssues(venueId, date, venueStore) {
  var _a;
  common_vendor.index.__f__("log", "at utils/timeslot-sync-fix.js:287", "🤖 自动修复时间段问题");
  const autoFix = {
    steps: [],
    success: false,
    finalSlotsCount: 0
  };
  try {
    autoFix.steps.push("检查同步状态");
    const syncStatus = await checkTimeSlotSyncStatus(venueId, date, venueStore);
    if (syncStatus.needsSync) {
      autoFix.steps.push("需要同步，执行时间段生成修复");
      const fixResult = await fixTimeSlotGeneration(venueId, date, venueStore);
      if (fixResult.success) {
        autoFix.success = true;
        autoFix.finalSlotsCount = ((_a = fixResult.generatedSlots) == null ? void 0 : _a.length) || 0;
        autoFix.steps.push(`修复成功，生成${autoFix.finalSlotsCount}个时间段`);
      } else {
        autoFix.steps.push("修复失败");
      }
    } else if (syncStatus.synced) {
      autoFix.success = true;
      autoFix.finalSlotsCount = syncStatus.frontendSlots;
      autoFix.steps.push("时间段已同步，无需修复");
    }
  } catch (error) {
    common_vendor.index.__f__("error", "at utils/timeslot-sync-fix.js:319", "❌ 自动修复失败:", error);
    autoFix.steps.push(`自动修复失败: ${error.message}`);
  }
  common_vendor.index.__f__("log", "at utils/timeslot-sync-fix.js:323", "🤖 自动修复结果:", autoFix);
  return autoFix;
}
exports.autoFixTimeSlotIssues = autoFixTimeSlotIssues;
exports.checkTimeSlotSyncStatus = checkTimeSlotSyncStatus;
exports.fixTimeSlotGeneration = fixTimeSlotGeneration;
exports.forceRegenerateTimeSlots = forceRegenerateTimeSlots;
exports.validateTimeSlots = validateTimeSlots;
//# sourceMappingURL=../../.sourcemap/mp-weixin/utils/timeslot-sync-fix.js.map
