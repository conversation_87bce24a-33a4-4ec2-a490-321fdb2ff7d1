"use strict";
const common_vendor = require("../common/vendor.js");
async function validateBackendPriceReceiving(bookingData) {
  common_vendor.index.__f__("log", "at utils/deep-fix-validator.js:8", "🔍 开始验证后端价格接收...");
  const validation = {
    frontendPrice: null,
    backendReceived: null,
    priceTransmitted: false,
    issues: []
  };
  try {
    validation.frontendPrice = parseFloat(bookingData.price) || 0;
    common_vendor.index.__f__("log", "at utils/deep-fix-validator.js:20", "💰 前端发送的价格:", validation.frontendPrice);
    if (validation.frontendPrice <= 0) {
      validation.issues.push("前端发送的价格为0或无效");
    }
    common_vendor.index.__f__("log", "at utils/deep-fix-validator.js:28", "📡 检查后端是否正确接收价格...");
    const priceChecks = {
      hasPrice: bookingData.price !== void 0 && bookingData.price !== null,
      priceIsNumber: !isNaN(parseFloat(bookingData.price)),
      priceIsPositive: parseFloat(bookingData.price) > 0,
      priceIsReasonable: parseFloat(bookingData.price) >= 50 && parseFloat(bookingData.price) <= 1e3
    };
    common_vendor.index.__f__("log", "at utils/deep-fix-validator.js:38", "🔍 价格检查结果:", priceChecks);
    validation.priceTransmitted = Object.values(priceChecks).every((check) => check);
    if (!priceChecks.hasPrice) {
      validation.issues.push("价格字段缺失");
    }
    if (!priceChecks.priceIsNumber) {
      validation.issues.push("价格不是有效数字");
    }
    if (!priceChecks.priceIsPositive) {
      validation.issues.push("价格不是正数");
    }
    if (!priceChecks.priceIsReasonable) {
      validation.issues.push("价格不在合理范围内");
    }
  } catch (error) {
    common_vendor.index.__f__("error", "at utils/deep-fix-validator.js:56", "❌ 验证后端价格接收失败:", error);
    validation.issues.push(`验证失败: ${error.message}`);
  }
  common_vendor.index.__f__("log", "at utils/deep-fix-validator.js:60", "📊 后端价格接收验证结果:", validation);
  return validation;
}
async function validateTimeSlotRefreshDeep(venueId, date, venueStore) {
  common_vendor.index.__f__("log", "at utils/deep-fix-validator.js:66", "🔍 开始深度验证时间段刷新...");
  const validation = {
    beforeRefresh: null,
    afterRefresh: null,
    cacheCleared: false,
    dataChanged: false,
    refreshSuccess: false,
    issues: []
  };
  try {
    validation.beforeRefresh = {
      slotsCount: venueStore.timeSlots ? venueStore.timeSlots.length : 0,
      slotsData: venueStore.timeSlots ? [...venueStore.timeSlots] : [],
      timestamp: Date.now()
    };
    common_vendor.index.__f__("log", "at utils/deep-fix-validator.js:85", "📊 刷新前状态:", validation.beforeRefresh);
    const cacheKeys = [];
    if (typeof window !== "undefined" && window.cacheManager) {
      for (const key of window.cacheManager.cache.keys()) {
        if (key.includes("timeslots") || key.includes(venueId)) {
          cacheKeys.push(key);
        }
      }
    }
    common_vendor.index.__f__("log", "at utils/deep-fix-validator.js:97", "🔍 刷新前缓存键:", cacheKeys);
    common_vendor.index.__f__("log", "at utils/deep-fix-validator.js:100", "🚀 执行强力刷新...");
    const refreshResult = await venueStore.forceRefreshTimeSlots(venueId, date);
    const cacheKeysAfter = [];
    if (typeof window !== "undefined" && window.cacheManager) {
      for (const key of window.cacheManager.cache.keys()) {
        if (key.includes("timeslots") || key.includes(venueId)) {
          cacheKeysAfter.push(key);
        }
      }
    }
    validation.cacheCleared = cacheKeysAfter.length < cacheKeys.length;
    common_vendor.index.__f__("log", "at utils/deep-fix-validator.js:114", "🧹 缓存清除检查:", {
      before: cacheKeys.length,
      after: cacheKeysAfter.length,
      cleared: validation.cacheCleared
    });
    validation.afterRefresh = {
      slotsCount: venueStore.timeSlots ? venueStore.timeSlots.length : 0,
      slotsData: venueStore.timeSlots ? [...venueStore.timeSlots] : [],
      timestamp: Date.now()
    };
    common_vendor.index.__f__("log", "at utils/deep-fix-validator.js:127", "📊 刷新后状态:", validation.afterRefresh);
    const beforeDataStr = JSON.stringify(validation.beforeRefresh.slotsData);
    const afterDataStr = JSON.stringify(validation.afterRefresh.slotsData);
    validation.dataChanged = beforeDataStr !== afterDataStr;
    common_vendor.index.__f__("log", "at utils/deep-fix-validator.js:134", "🔄 数据变化检查:", {
      dataChanged: validation.dataChanged,
      beforeHash: beforeDataStr.substring(0, 50) + "...",
      afterHash: afterDataStr.substring(0, 50) + "..."
    });
    validation.refreshSuccess = refreshResult.success && validation.afterRefresh.slotsCount > 0;
    if (!refreshResult.success) {
      validation.issues.push("刷新操作失败");
    }
    if (!validation.cacheCleared) {
      validation.issues.push("缓存未被正确清除");
    }
    if (validation.afterRefresh.slotsCount === 0) {
      validation.issues.push("刷新后没有获取到时间段数据");
    }
    if (!validation.dataChanged && validation.beforeRefresh.slotsCount > 0) {
      validation.issues.push("数据没有发生变化，可能仍在使用缓存");
    }
  } catch (error) {
    common_vendor.index.__f__("error", "at utils/deep-fix-validator.js:158", "❌ 深度验证时间段刷新失败:", error);
    validation.issues.push(`验证失败: ${error.message}`);
  }
  common_vendor.index.__f__("log", "at utils/deep-fix-validator.js:162", "📊 时间段刷新深度验证结果:", validation);
  return validation;
}
async function runDeepValidation(bookingData, venueId, date, venueStore) {
  common_vendor.index.__f__("log", "at utils/deep-fix-validator.js:168", "🚀 开始综合深度验证...");
  const results = {
    priceValidation: null,
    timeSlotValidation: null,
    overallSuccess: false,
    summary: ""
  };
  try {
    common_vendor.index.__f__("log", "at utils/deep-fix-validator.js:179", "\n=== 验证1: 后端价格接收 ===");
    results.priceValidation = await validateBackendPriceReceiving(bookingData);
    common_vendor.index.__f__("log", "at utils/deep-fix-validator.js:183", "\n=== 验证2: 时间段刷新 ===");
    results.timeSlotValidation = await validateTimeSlotRefreshDeep(venueId, date, venueStore);
    const priceOk = results.priceValidation.priceTransmitted && results.priceValidation.issues.length === 0;
    const timeSlotOk = results.timeSlotValidation.refreshSuccess && results.timeSlotValidation.issues.length === 0;
    results.overallSuccess = priceOk && timeSlotOk;
    const priceStatus = priceOk ? "✅ 通过" : "❌ 失败";
    const timeSlotStatus = timeSlotOk ? "✅ 通过" : "❌ 失败";
    results.summary = `价格传递: ${priceStatus}, 时间段刷新: ${timeSlotStatus}`;
    common_vendor.index.__f__("log", "at utils/deep-fix-validator.js:198", "\n📋 综合深度验证结果:");
    common_vendor.index.__f__("log", "at utils/deep-fix-validator.js:199", `${priceOk ? "✅" : "❌"} 价格传递验证: ${results.priceValidation.issues.length === 0 ? "无问题" : results.priceValidation.issues.join(", ")}`);
    common_vendor.index.__f__("log", "at utils/deep-fix-validator.js:200", `${timeSlotOk ? "✅" : "❌"} 时间段刷新验证: ${results.timeSlotValidation.issues.length === 0 ? "无问题" : results.timeSlotValidation.issues.join(", ")}`);
    common_vendor.index.__f__("log", "at utils/deep-fix-validator.js:201", `${results.overallSuccess ? "🎉" : "⚠️"} 总体评估: ${results.overallSuccess ? "全部修复成功" : "仍有问题需要解决"}`);
  } catch (error) {
    common_vendor.index.__f__("error", "at utils/deep-fix-validator.js:204", "❌ 综合深度验证失败:", error);
    results.error = error.message;
  }
  return results;
}
exports.runDeepValidation = runDeepValidation;
//# sourceMappingURL=../../.sourcemap/mp-weixin/utils/deep-fix-validator.js.map
