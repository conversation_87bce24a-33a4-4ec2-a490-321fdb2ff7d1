"use strict";
const common_vendor = require("../common/vendor.js");
function debugOrderAmount(orderInfo) {
  common_vendor.index.__f__("log", "at utils/payment-debug.js:8", "🔍 [支付调试] 开始分析订单金额问题");
  common_vendor.index.__f__("log", "at utils/payment-debug.js:9", "📋 订单信息:", orderInfo);
  if (!orderInfo) {
    common_vendor.index.__f__("log", "at utils/payment-debug.js:12", "❌ 订单信息为空");
    return { success: false, error: "订单信息为空" };
  }
  const priceFields = {
    totalPrice: orderInfo.totalPrice,
    paymentAmount: orderInfo.paymentAmount,
    price: orderInfo.price,
    amount: orderInfo.amount
  };
  common_vendor.index.__f__("log", "at utils/payment-debug.js:24", "💰 价格字段检查:", priceFields);
  const timeFields = {
    startTime: orderInfo.startTime,
    endTime: orderInfo.endTime,
    bookingTime: orderInfo.bookingTime,
    bookingDate: orderInfo.bookingDate
  };
  common_vendor.index.__f__("log", "at utils/payment-debug.js:34", "⏰ 时间字段检查:", timeFields);
  const typeFields = {
    bookingType: orderInfo.bookingType,
    isVirtualOrder: orderInfo.isVirtualOrder,
    orderType: orderInfo.orderType
  };
  common_vendor.index.__f__("log", "at utils/payment-debug.js:43", "🏷️ 类型字段检查:", typeFields);
  let calculatedPrice = 0;
  let calculationMethod = "";
  if (orderInfo.totalPrice && orderInfo.totalPrice > 0) {
    calculatedPrice = orderInfo.totalPrice;
    calculationMethod = "totalPrice字段";
  } else if (orderInfo.paymentAmount && orderInfo.paymentAmount > 0) {
    calculatedPrice = orderInfo.paymentAmount;
    calculationMethod = "paymentAmount字段";
  } else if (orderInfo.price && orderInfo.price > 0) {
    calculatedPrice = orderInfo.price;
    calculationMethod = "price字段";
  }
  if (calculatedPrice === 0 && orderInfo.startTime && orderInfo.endTime) {
    try {
      const startHour = parseInt(orderInfo.startTime.split(":")[0]);
      const startMinute = parseInt(orderInfo.startTime.split(":")[1]);
      const endHour = parseInt(orderInfo.endTime.split(":")[0]);
      const endMinute = parseInt(orderInfo.endTime.split(":")[1]);
      const duration = endHour + endMinute / 60 - (startHour + startMinute / 60);
      const hourlyRate = 120;
      calculatedPrice = duration * hourlyRate;
      calculationMethod = `时间段计算 (${duration}小时 × ${hourlyRate}元/小时)`;
      common_vendor.index.__f__("log", "at utils/payment-debug.js:75", `⏱️ 时间段计算: ${orderInfo.startTime}-${orderInfo.endTime} = ${duration}小时`);
    } catch (error) {
      common_vendor.index.__f__("error", "at utils/payment-debug.js:77", "时间段计算失败:", error);
    }
  }
  if (calculatedPrice === 0) {
    if (orderInfo.bookingType === "SHARED" || orderInfo.isVirtualOrder) {
      calculatedPrice = 120;
      calculationMethod = "拼场默认价格";
    } else {
      calculatedPrice = 240;
      calculationMethod = "独享默认价格";
    }
  }
  const result = {
    success: true,
    originalPrice: orderInfo.totalPrice || 0,
    calculatedPrice,
    calculationMethod,
    recommendation: calculatedPrice > 0 ? "使用计算价格" : "需要检查后端数据"
  };
  common_vendor.index.__f__("log", "at utils/payment-debug.js:100", "✅ [支付调试] 分析结果:", result);
  return result;
}
function debugTimeSlotRefresh(venueId, date, venueStore) {
  common_vendor.index.__f__("log", "at utils/payment-debug.js:106", "🔄 [时间段调试] 开始分析时间段刷新问题");
  common_vendor.index.__f__("log", "at utils/payment-debug.js:107", "📍 参数:", { venueId, date });
  if (!venueStore) {
    common_vendor.index.__f__("log", "at utils/payment-debug.js:110", "❌ VenueStore未提供");
    return { success: false, error: "VenueStore未提供" };
  }
  const currentTimeSlots = venueStore.timeSlots || [];
  common_vendor.index.__f__("log", "at utils/payment-debug.js:116", "📊 当前时间段数量:", currentTimeSlots.length);
  common_vendor.index.__f__("log", "at utils/payment-debug.js:117", "📊 当前时间段状态:", currentTimeSlots.map((slot) => ({
    id: slot.id,
    time: `${slot.startTime}-${slot.endTime}`,
    status: slot.status
  })));
  return {
    success: true,
    currentSlotsCount: currentTimeSlots.length,
    currentSlots: currentTimeSlots,
    recommendation: "执行强制刷新"
  };
}
async function forceRefreshTimeSlots(venueId, date, venueStore) {
  common_vendor.index.__f__("log", "at utils/payment-debug.js:133", "🚀 [时间段调试] 执行强制刷新");
  try {
    common_vendor.index.__f__("log", "at utils/payment-debug.js:137", "🧹 步骤1: 清除所有相关缓存");
    venueStore.clearTimeSlots();
    if (typeof window !== "undefined" && window.cacheManager) {
      window.cacheManager.clearUrl(`/timeslots/venue/${venueId}`);
      window.cacheManager.clearUrl(`/timeslots`);
      common_vendor.index.__f__("log", "at utils/payment-debug.js:145", "🧹 已清除请求缓存");
    }
    common_vendor.index.__f__("log", "at utils/payment-debug.js:149", "⏳ 步骤2: 等待缓存清除");
    await new Promise((resolve) => setTimeout(resolve, 300));
    common_vendor.index.__f__("log", "at utils/payment-debug.js:153", "📡 步骤3: 强制获取新数据");
    let attempts = 0;
    let success = false;
    let newTimeSlots = [];
    while (attempts < 3 && !success) {
      attempts++;
      common_vendor.index.__f__("log", "at utils/payment-debug.js:160", `📡 尝试第${attempts}次获取数据...`);
      try {
        await venueStore.getTimeSlots(venueId, date, true);
        newTimeSlots = venueStore.timeSlots || [];
        if (newTimeSlots.length > 0) {
          success = true;
          common_vendor.index.__f__("log", "at utils/payment-debug.js:168", `✅ 第${attempts}次尝试成功`);
        } else {
          common_vendor.index.__f__("log", "at utils/payment-debug.js:170", `⚠️ 第${attempts}次尝试获取到空数据`);
          if (attempts < 3) {
            await new Promise((resolve) => setTimeout(resolve, 500));
          }
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at utils/payment-debug.js:176", `❌ 第${attempts}次尝试失败:`, error);
        if (attempts < 3) {
          await new Promise((resolve) => setTimeout(resolve, 500));
        }
      }
    }
    common_vendor.index.__f__("log", "at utils/payment-debug.js:184", "✅ 步骤4: 验证结果");
    common_vendor.index.__f__("log", "at utils/payment-debug.js:185", "📊 刷新后时间段数量:", newTimeSlots.length);
    common_vendor.index.__f__("log", "at utils/payment-debug.js:186", "📊 刷新后时间段状态:", newTimeSlots.map((slot) => ({
      id: slot.id,
      time: `${slot.startTime}-${slot.endTime}`,
      status: slot.status
    })));
    return {
      success,
      newSlotsCount: newTimeSlots.length,
      newSlots: newTimeSlots,
      attempts
    };
  } catch (error) {
    common_vendor.index.__f__("error", "at utils/payment-debug.js:199", "❌ 强制刷新失败:", error);
    return {
      success: false,
      error: error.message
    };
  }
}
exports.debugOrderAmount = debugOrderAmount;
exports.debugTimeSlotRefresh = debugTimeSlotRefresh;
exports.forceRefreshTimeSlots = forceRefreshTimeSlots;
//# sourceMappingURL=../../.sourcemap/mp-weixin/utils/payment-debug.js.map
