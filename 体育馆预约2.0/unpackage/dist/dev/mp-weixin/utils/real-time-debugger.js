"use strict";
const common_vendor = require("../common/vendor.js");
function validatePriceTransmission(bookingData) {
  common_vendor.index.__f__("log", "at utils/real-time-debugger.js:176", "🔍 实时价格传递验证");
  const validation = {
    frontendData: {
      hasPrice: bookingData.price !== void 0,
      priceValue: bookingData.price,
      priceType: typeof bookingData.price,
      isNumber: !isNaN(bookingData.price),
      isPositive: bookingData.price > 0
    },
    issues: []
  };
  common_vendor.index.__f__("log", "at utils/real-time-debugger.js:189", "📊 [价格验证] 前端数据:", validation.frontendData);
  if (!validation.frontendData.hasPrice) {
    validation.issues.push("价格字段缺失");
  }
  if (validation.frontendData.hasPrice && !validation.frontendData.isNumber) {
    validation.issues.push("价格不是有效数字");
  }
  if (validation.frontendData.isNumber && !validation.frontendData.isPositive) {
    validation.issues.push("价格不是正数");
  }
  try {
    const serialized = JSON.stringify(bookingData);
    const deserialized = JSON.parse(serialized);
    if (deserialized.price !== bookingData.price) {
      validation.issues.push("价格在序列化过程中发生变化");
    }
    common_vendor.index.__f__("log", "at utils/real-time-debugger.js:213", "📊 [价格验证] 序列化测试:", {
      original: bookingData.price,
      serialized: serialized.includes('"price"'),
      deserialized: deserialized.price
    });
  } catch (error) {
    validation.issues.push("数据序列化失败");
  }
  validation.isValid = validation.issues.length === 0;
  common_vendor.index.__f__("log", "at utils/real-time-debugger.js:224", "📊 [价格验证] 结果:", {
    isValid: validation.isValid,
    issues: validation.issues
  });
  return validation;
}
exports.validatePriceTransmission = validatePriceTransmission;
//# sourceMappingURL=../../.sourcemap/mp-weixin/utils/real-time-debugger.js.map
