"use strict";
const common_vendor = require("../common/vendor.js");
function interceptApiCalls() {
  common_vendor.index.__f__("log", "at utils/real-time-debugger.js:8", "🔍 启动API调用拦截器");
  const originalRequest = common_vendor.index.request;
  common_vendor.index.request = function(options) {
    common_vendor.index.__f__("log", "at utils/real-time-debugger.js:15", "🚀 [API拦截] 发起请求:", {
      url: options.url,
      method: options.method,
      data: options.data,
      header: options.header
    });
    if (options.url && (options.url.includes("/bookings") || options.url.includes("/timeslots"))) {
      common_vendor.index.__f__("log", "at utils/real-time-debugger.js:24", "📋 [预约API] 详细数据:", JSON.stringify(options.data, null, 2));
      if (options.data && options.data.price !== void 0) {
        common_vendor.index.__f__("log", "at utils/real-time-debugger.js:28", "💰 [价格检查] 发送的价格:", {
          value: options.data.price,
          type: typeof options.data.price,
          isNumber: !isNaN(options.data.price),
          isPositive: options.data.price > 0
        });
      }
    }
    const originalSuccess = options.success;
    options.success = function(response) {
      common_vendor.index.__f__("log", "at utils/real-time-debugger.js:40", "✅ [API拦截] 请求成功:", {
        url: options.url,
        statusCode: response.statusCode,
        data: response.data
      });
      if (options.url && (options.url.includes("/bookings") || options.url.includes("/timeslots"))) {
        common_vendor.index.__f__("log", "at utils/real-time-debugger.js:48", "📋 [预约API] 响应详情:", JSON.stringify(response.data, null, 2));
      }
      if (originalSuccess) {
        originalSuccess(response);
      }
    };
    const originalFail = options.fail;
    options.fail = function(error) {
      common_vendor.index.__f__("error", "at utils/real-time-debugger.js:59", "❌ [API拦截] 请求失败:", {
        url: options.url,
        error
      });
      if (originalFail) {
        originalFail(error);
      }
    };
    return originalRequest.call(this, options);
  };
  common_vendor.index.__f__("log", "at utils/real-time-debugger.js:73", "✅ API调用拦截器已启动");
}
function monitorTimeSlotChanges(venueStore) {
  common_vendor.index.__f__("log", "at utils/real-time-debugger.js:78", "🔍 启动时间段变化监控");
  let lastTimeSlots = null;
  let changeCount = 0;
  const monitor = setInterval(() => {
    const currentTimeSlots = venueStore.timeSlots || [];
    const currentSlotsStr = JSON.stringify(currentTimeSlots.map((slot) => ({
      id: slot.id,
      status: slot.status,
      time: `${slot.startTime}-${slot.endTime}`
    })));
    if (lastTimeSlots !== null && lastTimeSlots !== currentSlotsStr) {
      changeCount++;
      common_vendor.index.__f__("log", "at utils/real-time-debugger.js:93", `🔄 [时间段监控] 检测到变化 #${changeCount}:`);
      const lastSlots = JSON.parse(lastTimeSlots);
      const currentSlots = JSON.parse(currentSlotsStr);
      const changes = [];
      for (let i = 0; i < Math.max(lastSlots.length, currentSlots.length); i++) {
        const lastSlot = lastSlots[i];
        const currentSlot = currentSlots[i];
        if (!lastSlot && currentSlot) {
          changes.push({ type: "ADDED", slot: currentSlot });
        } else if (lastSlot && !currentSlot) {
          changes.push({ type: "REMOVED", slot: lastSlot });
        } else if (lastSlot && currentSlot && lastSlot.status !== currentSlot.status) {
          changes.push({
            type: "STATUS_CHANGED",
            slot: currentSlot,
            from: lastSlot.status,
            to: currentSlot.status
          });
        }
      }
      common_vendor.index.__f__("log", "at utils/real-time-debugger.js:118", "📊 [时间段监控] 变化详情:", changes);
    }
    lastTimeSlots = currentSlotsStr;
  }, 1e3);
  return () => {
    clearInterval(monitor);
    common_vendor.index.__f__("log", "at utils/real-time-debugger.js:127", "🛑 时间段变化监控已停止");
  };
}
function validatePriceTransmission(bookingData) {
  common_vendor.index.__f__("log", "at utils/real-time-debugger.js:176", "🔍 实时价格传递验证");
  const validation = {
    frontendData: {
      hasPrice: bookingData.price !== void 0,
      priceValue: bookingData.price,
      priceType: typeof bookingData.price,
      isNumber: !isNaN(bookingData.price),
      isPositive: bookingData.price > 0
    },
    issues: []
  };
  common_vendor.index.__f__("log", "at utils/real-time-debugger.js:189", "📊 [价格验证] 前端数据:", validation.frontendData);
  if (!validation.frontendData.hasPrice) {
    validation.issues.push("价格字段缺失");
  }
  if (validation.frontendData.hasPrice && !validation.frontendData.isNumber) {
    validation.issues.push("价格不是有效数字");
  }
  if (validation.frontendData.isNumber && !validation.frontendData.isPositive) {
    validation.issues.push("价格不是正数");
  }
  try {
    const serialized = JSON.stringify(bookingData);
    const deserialized = JSON.parse(serialized);
    if (deserialized.price !== bookingData.price) {
      validation.issues.push("价格在序列化过程中发生变化");
    }
    common_vendor.index.__f__("log", "at utils/real-time-debugger.js:213", "📊 [价格验证] 序列化测试:", {
      original: bookingData.price,
      serialized: serialized.includes('"price"'),
      deserialized: deserialized.price
    });
  } catch (error) {
    validation.issues.push("数据序列化失败");
  }
  validation.isValid = validation.issues.length === 0;
  common_vendor.index.__f__("log", "at utils/real-time-debugger.js:224", "📊 [价格验证] 结果:", {
    isValid: validation.isValid,
    issues: validation.issues
  });
  return validation;
}
function startRealTimeDebugging(venueStore) {
  common_vendor.index.__f__("log", "at utils/real-time-debugger.js:234", "🚀 启动综合实时调试");
  const debugSession = {
    apiInterceptor: null,
    timeSlotMonitor: null,
    isActive: false,
    logs: []
  };
  interceptApiCalls();
  debugSession.apiInterceptor = true;
  debugSession.timeSlotMonitor = monitorTimeSlotChanges(venueStore);
  debugSession.isActive = true;
  common_vendor.index.__f__("log", "at utils/real-time-debugger.js:252", "✅ 综合实时调试已启动");
  return {
    stop() {
      if (debugSession.timeSlotMonitor) {
        debugSession.timeSlotMonitor();
      }
      debugSession.isActive = false;
      common_vendor.index.__f__("log", "at utils/real-time-debugger.js:261", "🛑 综合实时调试已停止");
    },
    isActive() {
      return debugSession.isActive;
    },
    getLogs() {
      return debugSession.logs;
    }
  };
}
async function quickDiagnosis(bookingData, venueStore) {
  common_vendor.index.__f__("log", "at utils/real-time-debugger.js:276", "🔍 开始快速问题诊断...");
  const diagnosis = {
    timestamp: (/* @__PURE__ */ new Date()).toISOString(),
    priceIssues: [],
    timeSlotIssues: [],
    overallStatus: "UNKNOWN"
  };
  common_vendor.index.__f__("log", "at utils/real-time-debugger.js:286", "💰 诊断价格问题...");
  const priceValidation = validatePriceTransmission(bookingData);
  if (!priceValidation.isValid) {
    diagnosis.priceIssues = priceValidation.issues;
  }
  common_vendor.index.__f__("log", "at utils/real-time-debugger.js:293", "⏰ 诊断时间段问题...");
  const timeSlots = venueStore.timeSlots || [];
  if (timeSlots.length === 0) {
    diagnosis.timeSlotIssues.push("没有时间段数据");
  }
  const reservedSlots = timeSlots.filter((slot) => slot.status === "RESERVED");
  if (reservedSlots.length === 0) {
    diagnosis.timeSlotIssues.push("没有已预约的时间段");
  }
  const hasIssues = diagnosis.priceIssues.length > 0 || diagnosis.timeSlotIssues.length > 0;
  diagnosis.overallStatus = hasIssues ? "ISSUES_FOUND" : "OK";
  common_vendor.index.__f__("log", "at utils/real-time-debugger.js:309", "📊 快速诊断结果:", diagnosis);
  return diagnosis;
}
exports.quickDiagnosis = quickDiagnosis;
exports.startRealTimeDebugging = startRealTimeDebugging;
exports.validatePriceTransmission = validatePriceTransmission;
//# sourceMappingURL=../../.sourcemap/mp-weixin/utils/real-time-debugger.js.map
