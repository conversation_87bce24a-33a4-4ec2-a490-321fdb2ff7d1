"use strict";
const common_vendor = require("../common/vendor.js");
function validateBookingData(bookingData) {
  common_vendor.index.__f__("log", "at utils/booking-price-validator.js:70", "🔍 开始验证预约数据完整性...");
  const validation = {
    valid: true,
    errors: [],
    warnings: [],
    data: bookingData
  };
  const requiredFields = ["venueId", "date", "startTime", "endTime", "price"];
  requiredFields.forEach((field) => {
    if (!bookingData[field]) {
      validation.errors.push(`缺少必需字段: ${field}`);
      validation.valid = false;
    }
  });
  if (bookingData.price !== void 0) {
    const price = parseFloat(bookingData.price);
    if (isNaN(price) || price <= 0) {
      validation.errors.push(`价格无效: ${bookingData.price}`);
      validation.valid = false;
    } else if (price < 50) {
      validation.warnings.push(`价格可能过低: ¥${price}`);
    } else if (price > 1e3) {
      validation.warnings.push(`价格可能过高: ¥${price}`);
    }
  }
  if (bookingData.startTime && bookingData.endTime) {
    const timeRegex = /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/;
    if (!timeRegex.test(bookingData.startTime)) {
      validation.errors.push(`开始时间格式错误: ${bookingData.startTime}`);
      validation.valid = false;
    }
    if (!timeRegex.test(bookingData.endTime)) {
      validation.errors.push(`结束时间格式错误: ${bookingData.endTime}`);
      validation.valid = false;
    }
    if (timeRegex.test(bookingData.startTime) && timeRegex.test(bookingData.endTime)) {
      const [startHour, startMin] = bookingData.startTime.split(":").map(Number);
      const [endHour, endMin] = bookingData.endTime.split(":").map(Number);
      const startMinutes = startHour * 60 + startMin;
      const endMinutes = endHour * 60 + endMin;
      if (endMinutes <= startMinutes) {
        validation.errors.push("结束时间必须晚于开始时间");
        validation.valid = false;
      }
    }
  }
  if (bookingData.date) {
    const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
    if (!dateRegex.test(bookingData.date)) {
      validation.errors.push(`日期格式错误: ${bookingData.date}`);
      validation.valid = false;
    }
  }
  common_vendor.index.__f__("log", "at utils/booking-price-validator.js:140", `✅ 数据验证${validation.valid ? "通过" : "失败"}`);
  if (validation.errors.length > 0) {
    common_vendor.index.__f__("log", "at utils/booking-price-validator.js:142", "❌ 错误:", validation.errors);
  }
  if (validation.warnings.length > 0) {
    common_vendor.index.__f__("log", "at utils/booking-price-validator.js:145", "⚠️ 警告:", validation.warnings);
  }
  return validation;
}
function quickPriceCheck(price) {
  const numPrice = parseFloat(price);
  if (isNaN(numPrice)) {
    return { valid: false, message: "价格不是有效数字" };
  }
  if (numPrice <= 0) {
    return { valid: false, message: "价格必须大于0" };
  }
  if (numPrice < 30) {
    return { valid: true, message: "价格偏低，请确认", level: "warning" };
  }
  if (numPrice > 500) {
    return { valid: true, message: "价格偏高，请确认", level: "warning" };
  }
  return { valid: true, message: "价格正常", level: "success" };
}
exports.quickPriceCheck = quickPriceCheck;
exports.validateBookingData = validateBookingData;
//# sourceMappingURL=../../.sourcemap/mp-weixin/utils/booking-price-validator.js.map
