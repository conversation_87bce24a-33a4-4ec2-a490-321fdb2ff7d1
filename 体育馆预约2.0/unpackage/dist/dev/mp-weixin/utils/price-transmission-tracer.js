"use strict";
const common_vendor = require("../common/vendor.js");
function quickPriceDiagnosis(selectedSlots, venue) {
  common_vendor.index.__f__("log", "at utils/price-transmission-tracer.js:280", "⚡ 快速价格问题诊断");
  const diagnosis = {
    timestamp: (/* @__PURE__ */ new Date()).toISOString(),
    issues: [],
    warnings: [],
    summary: ""
  };
  if (!selectedSlots || selectedSlots.length === 0) {
    diagnosis.issues.push("没有选中的时间段");
  } else {
    selectedSlots.forEach((slot, index) => {
      if (!slot.price && !slot.pricePerHour) {
        diagnosis.warnings.push(`时间段${index + 1}缺少价格信息`);
      }
      if (slot.price === 0) {
        diagnosis.issues.push(`时间段${index + 1}价格为0`);
      }
    });
  }
  if (!venue) {
    diagnosis.issues.push("场馆数据缺失");
  } else if (!venue.price || venue.price === 0) {
    diagnosis.warnings.push("场馆价格缺失或为0");
  }
  let totalPrice = 0;
  if (selectedSlots && selectedSlots.length > 0) {
    selectedSlots.forEach((slot) => {
      if (slot.price && slot.price > 0) {
        totalPrice += parseFloat(slot.price);
      } else if ((venue == null ? void 0 : venue.price) && venue.price > 0) {
        totalPrice += parseFloat(venue.price) / 2;
      } else {
        totalPrice += 60;
      }
    });
  }
  if (totalPrice === 0) {
    diagnosis.issues.push("计算的总价格为0");
  }
  const issueCount = diagnosis.issues.length;
  const warningCount = diagnosis.warnings.length;
  if (issueCount === 0 && warningCount === 0) {
    diagnosis.summary = "✅ 价格计算正常";
  } else if (issueCount === 0) {
    diagnosis.summary = `⚠️ 有${warningCount}个警告`;
  } else {
    diagnosis.summary = `❌ 有${issueCount}个问题，${warningCount}个警告`;
  }
  diagnosis.calculatedPrice = totalPrice;
  common_vendor.index.__f__("log", "at utils/price-transmission-tracer.js:342", "⚡ 快速诊断结果:", diagnosis);
  return diagnosis;
}
exports.quickPriceDiagnosis = quickPriceDiagnosis;
//# sourceMappingURL=../../.sourcemap/mp-weixin/utils/price-transmission-tracer.js.map
