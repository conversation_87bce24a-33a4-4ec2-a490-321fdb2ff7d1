"use strict";
Object.defineProperty(exports, Symbol.toStringTag, { value: "Module" });
const common_vendor = require("../common/vendor.js");
const api_timeslot = require("../api/timeslot.js");
async function ensureTimeSlotsExistInBackend(venueId, date, selectedSlots) {
  common_vendor.index.__f__("log", "at utils/emergency-timeslot-fix.js:10", "🚨 紧急修复：确保时间段在后端存在");
  const fix = {
    venueId,
    date,
    selectedSlots,
    backendGenerated: false,
    backendSlots: null,
    mappedSlots: [],
    success: false,
    error: null
  };
  try {
    common_vendor.index.__f__("log", "at utils/emergency-timeslot-fix.js:25", "📡 强制调用后端生成时间段API...");
    await api_timeslot.generateTimeSlots(venueId, date);
    fix.backendGenerated = true;
    common_vendor.index.__f__("log", "at utils/emergency-timeslot-fix.js:28", "✅ 后端时间段生成成功");
    common_vendor.index.__f__("log", "at utils/emergency-timeslot-fix.js:31", "📡 获取后端生成的时间段...");
    const { getVenueTimeSlots } = await "../api/timeslot.js";
    const response = await getVenueTimeSlots(venueId, date, true);
    if (response && response.data && response.data.length > 0) {
      fix.backendSlots = response.data;
      common_vendor.index.__f__("log", "at utils/emergency-timeslot-fix.js:37", "✅ 获取到后端时间段:", response.data.length, "个");
      fix.mappedSlots = mapFrontendSlotsToBackend(selectedSlots, response.data);
      common_vendor.index.__f__("log", "at utils/emergency-timeslot-fix.js:41", "✅ 时间段映射完成:", fix.mappedSlots.length, "个");
      fix.success = fix.mappedSlots.length > 0;
    } else {
      throw new Error("后端生成时间段后仍然获取不到数据");
    }
  } catch (error) {
    common_vendor.index.__f__("error", "at utils/emergency-timeslot-fix.js:49", "❌ 紧急修复失败:", error);
    fix.error = error.message;
  }
  common_vendor.index.__f__("log", "at utils/emergency-timeslot-fix.js:53", "🚨 紧急修复结果:", fix);
  return fix;
}
function mapFrontendSlotsToBackend(frontendSlots, backendSlots) {
  common_vendor.index.__f__("log", "at utils/emergency-timeslot-fix.js:59", "🔄 映射前端时间段到后端时间段");
  const mappedSlots = [];
  frontendSlots.forEach((frontendSlot) => {
    const matchingBackendSlot = backendSlots.find(
      (backendSlot) => backendSlot.startTime === frontendSlot.startTime && backendSlot.endTime === frontendSlot.endTime
    );
    if (matchingBackendSlot) {
      mappedSlots.push({
        frontendId: frontendSlot.id,
        backendId: matchingBackendSlot.id,
        startTime: frontendSlot.startTime,
        endTime: frontendSlot.endTime,
        price: matchingBackendSlot.price || frontendSlot.price
      });
      common_vendor.index.__f__("log", "at utils/emergency-timeslot-fix.js:78", `✅ 映射成功: ${frontendSlot.startTime}-${frontendSlot.endTime} -> ID ${matchingBackendSlot.id}`);
    } else {
      common_vendor.index.__f__("warn", "at utils/emergency-timeslot-fix.js:80", `⚠️ 未找到匹配的后端时间段: ${frontendSlot.startTime}-${frontendSlot.endTime}`);
    }
  });
  common_vendor.index.__f__("log", "at utils/emergency-timeslot-fix.js:84", "🔄 映射完成，成功映射", mappedSlots.length, "个时间段");
  return mappedSlots;
}
function fixBookingDataSlotIds(bookingData, mappedSlots) {
  common_vendor.index.__f__("log", "at utils/emergency-timeslot-fix.js:90", "🔧 修复预约数据中的时间段ID");
  const fixedData = { ...bookingData };
  fixedData.slotIds = mappedSlots.map((slot) => slot.backendId);
  common_vendor.index.__f__("log", "at utils/emergency-timeslot-fix.js:97", "🔧 原始slotIds:", bookingData.slotIds);
  common_vendor.index.__f__("log", "at utils/emergency-timeslot-fix.js:98", "🔧 修复后slotIds:", fixedData.slotIds);
  return fixedData;
}
async function emergencyBookingFix(venueId, date, selectedSlots, bookingData) {
  common_vendor.index.__f__("log", "at utils/emergency-timeslot-fix.js:105", "🚨 启动综合紧急修复流程");
  const emergencyFix = {
    timestamp: (/* @__PURE__ */ new Date()).toISOString(),
    originalBookingData: bookingData,
    fixedBookingData: null,
    timeslotFix: null,
    success: false,
    error: null
  };
  try {
    emergencyFix.timeslotFix = await ensureTimeSlotsExistInBackend(venueId, date, selectedSlots);
    if (emergencyFix.timeslotFix.success) {
      emergencyFix.fixedBookingData = fixBookingDataSlotIds(
        bookingData,
        emergencyFix.timeslotFix.mappedSlots
      );
      emergencyFix.success = true;
      common_vendor.index.__f__("log", "at utils/emergency-timeslot-fix.js:128", "✅ 综合紧急修复成功");
    } else {
      throw new Error("时间段修复失败");
    }
  } catch (error) {
    common_vendor.index.__f__("error", "at utils/emergency-timeslot-fix.js:134", "❌ 综合紧急修复失败:", error);
    emergencyFix.error = error.message;
  }
  common_vendor.index.__f__("log", "at utils/emergency-timeslot-fix.js:138", "🚨 综合紧急修复结果:", emergencyFix);
  return emergencyFix;
}
async function quickCheckTimeslotSync(venueId, date, selectedSlots) {
  common_vendor.index.__f__("log", "at utils/emergency-timeslot-fix.js:144", "⚡ 快速检查时间段同步状态");
  const check = {
    venueId,
    date,
    frontendSlots: selectedSlots.length,
    backendSlots: 0,
    needsSync: false,
    hasDefaultIds: false,
    issues: []
  };
  try {
    const defaultIdPattern = /^default_/;
    check.hasDefaultIds = selectedSlots.some((slot) => defaultIdPattern.test(slot.id));
    if (check.hasDefaultIds) {
      check.issues.push("前端使用默认生成的时间段ID");
      check.needsSync = true;
    }
    const { getVenueTimeSlots } = await "../api/timeslot.js";
    const response = await getVenueTimeSlots(venueId, date, true);
    if (response && response.data) {
      check.backendSlots = response.data.length;
    }
    if (check.backendSlots === 0) {
      check.issues.push("后端没有时间段数据");
      check.needsSync = true;
    }
    common_vendor.index.__f__("log", "at utils/emergency-timeslot-fix.js:179", "⚡ 时间段同步状态检查结果:", check);
  } catch (error) {
    common_vendor.index.__f__("error", "at utils/emergency-timeslot-fix.js:182", "❌ 检查时间段同步状态失败:", error);
    check.issues.push(`检查失败: ${error.message}`);
  }
  return check;
}
async function smartBookingDataFix(venueId, date, selectedSlots, bookingData) {
  common_vendor.index.__f__("log", "at utils/emergency-timeslot-fix.js:191", "🧠 智能预约数据修复");
  const smartFix = {
    timestamp: (/* @__PURE__ */ new Date()).toISOString(),
    originalData: bookingData,
    fixedData: null,
    appliedFixes: [],
    success: false
  };
  try {
    const syncCheck = await quickCheckTimeslotSync(venueId, date, selectedSlots);
    if (syncCheck.needsSync) {
      common_vendor.index.__f__("log", "at utils/emergency-timeslot-fix.js:206", "🔧 检测到需要同步，执行紧急修复...");
      const emergencyResult = await emergencyBookingFix(venueId, date, selectedSlots, bookingData);
      if (emergencyResult.success) {
        smartFix.fixedData = emergencyResult.fixedBookingData;
        smartFix.appliedFixes.push("时间段ID映射修复");
        smartFix.appliedFixes.push("后端时间段生成");
        smartFix.success = true;
      } else {
        throw new Error("紧急修复失败");
      }
    } else {
      common_vendor.index.__f__("log", "at utils/emergency-timeslot-fix.js:220", "✅ 时间段同步状态正常，无需修复");
      smartFix.fixedData = bookingData;
      smartFix.success = true;
    }
  } catch (error) {
    common_vendor.index.__f__("error", "at utils/emergency-timeslot-fix.js:226", "❌ 智能修复失败:", error);
    smartFix.fixedData = bookingData;
    smartFix.appliedFixes.push("修复失败，使用原始数据");
  }
  common_vendor.index.__f__("log", "at utils/emergency-timeslot-fix.js:232", "🧠 智能修复结果:", smartFix);
  return smartFix;
}
const emergencyTimeslotFix = {
  ensureTimeSlotsExistInBackend,
  mapFrontendSlotsToBackend,
  fixBookingDataSlotIds,
  emergencyBookingFix,
  quickCheckTimeslotSync,
  smartBookingDataFix
};
exports.default = emergencyTimeslotFix;
exports.emergencyBookingFix = emergencyBookingFix;
exports.ensureTimeSlotsExistInBackend = ensureTimeSlotsExistInBackend;
exports.fixBookingDataSlotIds = fixBookingDataSlotIds;
exports.quickCheckTimeslotSync = quickCheckTimeslotSync;
exports.smartBookingDataFix = smartBookingDataFix;
//# sourceMappingURL=../../.sourcemap/mp-weixin/utils/emergency-timeslot-fix.js.map
