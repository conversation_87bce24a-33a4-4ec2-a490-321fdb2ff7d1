"use strict";
Object.defineProperty(exports, Symbol.toStringTag, { value: "Module" });
const common_vendor = require("../common/vendor.js");
const api_timeslot = require("../api/timeslot.js");
async function forceRefreshTimeSlots(venueId, date, venueStore) {
  common_vendor.index.__f__("log", "at utils/comprehensive-fix.js:67", "🔧 强力刷新时间段数据");
  const refresh = {
    venueId,
    date,
    attempts: 0,
    maxAttempts: 3,
    success: false,
    newData: null,
    errors: []
  };
  common_vendor.index.__f__("log", "at utils/comprehensive-fix.js:80", "🧹 清除所有缓存...");
  venueStore.clearTimeSlots();
  if (typeof window !== "undefined" && window.cacheManager) {
    const cacheKeys = Array.from(window.cacheManager.cache.keys());
    const timeslotKeys = cacheKeys.filter(
      (key) => key.includes("timeslots") || key.includes(venueId) || key.includes(date)
    );
    timeslotKeys.forEach((key) => {
      window.cacheManager.cache.delete(key);
    });
    common_vendor.index.__f__("log", "at utils/comprehensive-fix.js:98", `🧹 清除了${timeslotKeys.length}个相关缓存键`);
  }
  await new Promise((resolve) => setTimeout(resolve, 500));
  while (refresh.attempts < refresh.maxAttempts && !refresh.success) {
    refresh.attempts++;
    common_vendor.index.__f__("log", "at utils/comprehensive-fix.js:107", `📡 第${refresh.attempts}次尝试获取时间段数据...`);
    try {
      const response = await api_timeslot.getVenueTimeSlots(venueId, date, true);
      if (response && response.data && Array.isArray(response.data) && response.data.length > 0) {
        refresh.newData = response.data;
        refresh.success = true;
        venueStore.setTimeSlots(response.data);
        common_vendor.index.__f__("log", "at utils/comprehensive-fix.js:120", `✅ 第${refresh.attempts}次尝试成功，获取到${response.data.length}个时间段`);
        const validSlots = response.data.filter(
          (slot) => slot.id && slot.startTime && slot.endTime && slot.status
        );
        if (validSlots.length !== response.data.length) {
          refresh.errors.push(`数据质量问题：${response.data.length - validSlots.length}个时间段数据不完整`);
        }
      } else {
        const error = `第${refresh.attempts}次尝试获取到空数据或格式错误`;
        refresh.errors.push(error);
        common_vendor.index.__f__("warn", "at utils/comprehensive-fix.js:134", `⚠️ ${error}`);
        if (refresh.attempts < refresh.maxAttempts) {
          common_vendor.index.__f__("log", "at utils/comprehensive-fix.js:137", `⏳ 等待${refresh.attempts * 1e3}ms后重试...`);
          await new Promise((resolve) => setTimeout(resolve, refresh.attempts * 1e3));
        }
      }
    } catch (error) {
      const errorMsg = `第${refresh.attempts}次尝试失败: ${error.message}`;
      refresh.errors.push(errorMsg);
      common_vendor.index.__f__("error", "at utils/comprehensive-fix.js:145", `❌ ${errorMsg}`);
      if (refresh.attempts < refresh.maxAttempts) {
        common_vendor.index.__f__("log", "at utils/comprehensive-fix.js:148", `⏳ 等待${refresh.attempts * 1e3}ms后重试...`);
        await new Promise((resolve) => setTimeout(resolve, refresh.attempts * 1e3));
      }
    }
  }
  if (!refresh.success) {
    common_vendor.index.__f__("error", "at utils/comprehensive-fix.js:155", "❌ 所有刷新尝试都失败了");
    const existingSlots = venueStore.timeSlots || [];
    if (existingSlots.length > 0) {
      common_vendor.index.__f__("log", "at utils/comprehensive-fix.js:160", "💡 API刷新失败，但发现现有时间段数据，将其标记为成功");
      refresh.success = true;
      refresh.newData = existingSlots;
      refresh.errors.push("API刷新失败，但使用了现有数据");
    }
  }
  return refresh;
}
exports.forceRefreshTimeSlots = forceRefreshTimeSlots;
//# sourceMappingURL=../../.sourcemap/mp-weixin/utils/comprehensive-fix.js.map
