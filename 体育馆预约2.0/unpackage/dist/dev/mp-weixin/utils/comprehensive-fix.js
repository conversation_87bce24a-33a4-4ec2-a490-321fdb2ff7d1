"use strict";
Object.defineProperty(exports, Symbol.toStringTag, { value: "Module" });
const common_vendor = require("../common/vendor.js");
const api_timeslot = require("../api/timeslot.js");
function fixPriceCalculation(selectedSlots, venue) {
  common_vendor.index.__f__("log", "at utils/comprehensive-fix.js:10", "🔧 修复价格计算问题");
  const fix = {
    originalSlots: selectedSlots,
    venue,
    fixedSlots: [],
    totalPrice: 0,
    fixes: []
  };
  let venueHourlyPrice = 120;
  if (venue && venue.price && venue.price > 0) {
    venueHourlyPrice = parseFloat(venue.price);
  } else {
    fix.fixes.push("场馆价格缺失，使用默认价格120元/小时");
  }
  const venueHalfHourPrice = venueHourlyPrice / 2;
  selectedSlots.forEach((slot, index) => {
    const fixedSlot = { ...slot };
    let slotPrice = 0;
    let priceSource = "";
    if (slot.price && slot.price > 0) {
      slotPrice = parseFloat(slot.price);
      priceSource = "slot.price";
    } else if (slot.pricePerHour && slot.pricePerHour > 0) {
      slotPrice = parseFloat(slot.pricePerHour);
      priceSource = "slot.pricePerHour";
    } else {
      slotPrice = venueHalfHourPrice;
      priceSource = "venue.price/2";
      fixedSlot.price = slotPrice;
      fix.fixes.push(`时间段${index + 1}(${slot.startTime}-${slot.endTime})价格缺失，设置为${slotPrice}元`);
    }
    fixedSlot.calculatedPrice = slotPrice;
    fixedSlot.priceSource = priceSource;
    fix.fixedSlots.push(fixedSlot);
    fix.totalPrice += slotPrice;
    common_vendor.index.__f__("log", "at utils/comprehensive-fix.js:56", `💰 时间段${index + 1}: ${slot.startTime}-${slot.endTime} = ¥${slotPrice} (${priceSource})`);
  });
  common_vendor.index.__f__("log", "at utils/comprehensive-fix.js:59", `💰 修复后总价格: ¥${fix.totalPrice}`);
  common_vendor.index.__f__("log", "at utils/comprehensive-fix.js:60", "🔧 价格修复详情:", fix.fixes);
  return fix;
}
async function forceRefreshTimeSlots(venueId, date, venueStore) {
  common_vendor.index.__f__("log", "at utils/comprehensive-fix.js:67", "🔧 强力刷新时间段数据");
  const refresh = {
    venueId,
    date,
    attempts: 0,
    maxAttempts: 3,
    success: false,
    newData: null,
    errors: []
  };
  common_vendor.index.__f__("log", "at utils/comprehensive-fix.js:80", "🧹 清除所有缓存...");
  venueStore.clearTimeSlots();
  if (typeof window !== "undefined" && window.cacheManager) {
    const cacheKeys = Array.from(window.cacheManager.cache.keys());
    const timeslotKeys = cacheKeys.filter(
      (key) => key.includes("timeslots") || key.includes(venueId) || key.includes(date)
    );
    timeslotKeys.forEach((key) => {
      window.cacheManager.cache.delete(key);
    });
    common_vendor.index.__f__("log", "at utils/comprehensive-fix.js:98", `🧹 清除了${timeslotKeys.length}个相关缓存键`);
  }
  await new Promise((resolve) => setTimeout(resolve, 500));
  while (refresh.attempts < refresh.maxAttempts && !refresh.success) {
    refresh.attempts++;
    common_vendor.index.__f__("log", "at utils/comprehensive-fix.js:107", `📡 第${refresh.attempts}次尝试获取时间段数据...`);
    try {
      const response = await api_timeslot.getVenueTimeSlots(venueId, date, true);
      if (response && response.data && Array.isArray(response.data) && response.data.length > 0) {
        refresh.newData = response.data;
        refresh.success = true;
        venueStore.setTimeSlots(response.data);
        common_vendor.index.__f__("log", "at utils/comprehensive-fix.js:120", `✅ 第${refresh.attempts}次尝试成功，获取到${response.data.length}个时间段`);
        const validSlots = response.data.filter(
          (slot) => slot.id && slot.startTime && slot.endTime && slot.status
        );
        if (validSlots.length !== response.data.length) {
          refresh.errors.push(`数据质量问题：${response.data.length - validSlots.length}个时间段数据不完整`);
        }
      } else {
        const error = `第${refresh.attempts}次尝试获取到空数据或格式错误`;
        refresh.errors.push(error);
        common_vendor.index.__f__("warn", "at utils/comprehensive-fix.js:134", `⚠️ ${error}`);
        if (refresh.attempts < refresh.maxAttempts) {
          common_vendor.index.__f__("log", "at utils/comprehensive-fix.js:137", `⏳ 等待${refresh.attempts * 1e3}ms后重试...`);
          await new Promise((resolve) => setTimeout(resolve, refresh.attempts * 1e3));
        }
      }
    } catch (error) {
      const errorMsg = `第${refresh.attempts}次尝试失败: ${error.message}`;
      refresh.errors.push(errorMsg);
      common_vendor.index.__f__("error", "at utils/comprehensive-fix.js:145", `❌ ${errorMsg}`);
      if (refresh.attempts < refresh.maxAttempts) {
        common_vendor.index.__f__("log", "at utils/comprehensive-fix.js:148", `⏳ 等待${refresh.attempts * 1e3}ms后重试...`);
        await new Promise((resolve) => setTimeout(resolve, refresh.attempts * 1e3));
      }
    }
  }
  if (!refresh.success) {
    common_vendor.index.__f__("error", "at utils/comprehensive-fix.js:155", "❌ 所有刷新尝试都失败了");
  }
  return refresh;
}
async function comprehensiveFix(selectedSlots, venue, venueId, date, venueStore) {
  common_vendor.index.__f__("log", "at utils/comprehensive-fix.js:163", "🚀 开始综合问题修复");
  const result = {
    timestamp: (/* @__PURE__ */ new Date()).toISOString(),
    priceFixResult: null,
    refreshResult: null,
    finalBookingData: null,
    success: false,
    issues: []
  };
  try {
    common_vendor.index.__f__("log", "at utils/comprehensive-fix.js:176", "\n=== 步骤1: 修复价格计算 ===");
    result.priceFixResult = fixPriceCalculation(selectedSlots, venue);
    if (result.priceFixResult.totalPrice <= 0) {
      result.issues.push("价格修复后仍为0");
    }
    common_vendor.index.__f__("log", "at utils/comprehensive-fix.js:184", "\n=== 步骤2: 强力刷新时间段 ===");
    result.refreshResult = await forceRefreshTimeSlots(venueId, date, venueStore);
    if (!result.refreshResult.success) {
      result.issues.push("时间段刷新失败");
    }
    common_vendor.index.__f__("log", "at utils/comprehensive-fix.js:192", "\n=== 步骤3: 构建最终预约数据 ===");
    const firstSlot = selectedSlots[0];
    const lastSlot = selectedSlots[selectedSlots.length - 1];
    result.finalBookingData = {
      venueId: parseInt(venueId),
      date,
      startTime: firstSlot.startTime,
      endTime: lastSlot.endTime,
      slotIds: selectedSlots.map((slot) => slot.id),
      bookingType: "EXCLUSIVE",
      description: "",
      price: result.priceFixResult.totalPrice
      // 使用修复后的价格
    };
    if (result.finalBookingData.price > 0 && result.finalBookingData.slotIds.length > 0) {
      result.success = true;
    } else {
      if (result.finalBookingData.price <= 0) {
        result.issues.push("最终价格仍为0");
      }
      if (result.finalBookingData.slotIds.length === 0) {
        result.issues.push("没有选中的时间段");
      }
    }
    common_vendor.index.__f__("log", "at utils/comprehensive-fix.js:219", "\n📋 综合修复结果:");
    common_vendor.index.__f__("log", "at utils/comprehensive-fix.js:220", `💰 价格修复: ${result.priceFixResult.fixes.length}个问题已修复`);
    common_vendor.index.__f__("log", "at utils/comprehensive-fix.js:221", `⏰ 时间段刷新: ${result.refreshResult.success ? "成功" : "失败"}`);
    common_vendor.index.__f__("log", "at utils/comprehensive-fix.js:222", `📊 最终价格: ¥${result.finalBookingData.price}`);
    common_vendor.index.__f__("log", "at utils/comprehensive-fix.js:223", `🎯 综合成功: ${result.success ? "是" : "否"}`);
    if (result.issues.length > 0) {
      common_vendor.index.__f__("log", "at utils/comprehensive-fix.js:226", `❌ 剩余问题: ${result.issues.join(", ")}`);
    }
  } catch (error) {
    common_vendor.index.__f__("error", "at utils/comprehensive-fix.js:230", "❌ 综合修复过程出错:", error);
    result.issues.push(`修复过程出错: ${error.message}`);
  }
  return result;
}
exports.comprehensiveFix = comprehensiveFix;
exports.fixPriceCalculation = fixPriceCalculation;
exports.forceRefreshTimeSlots = forceRefreshTimeSlots;
//# sourceMappingURL=../../.sourcemap/mp-weixin/utils/comprehensive-fix.js.map
