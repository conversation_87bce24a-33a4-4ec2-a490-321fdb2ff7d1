"use strict";
Object.defineProperty(exports, Symbol.toStringTag, { value: "Module" });
const common_vendor = require("../common/vendor.js");
function diagnoseSharingPriceCalculation(selectedSlots, venue, bookingForm) {
  common_vendor.index.__f__("log", "at utils/sharing-fix-diagnosis.js:8", "🔍 拼场价格计算诊断");
  const diagnosis = {
    timestamp: (/* @__PURE__ */ new Date()).toISOString(),
    selectedSlots,
    venue,
    bookingForm,
    priceCalculation: {
      totalOriginalPrice: 0,
      pricePerTeam: 0,
      discountAmount: 0,
      discountPercentage: 50
    },
    issues: [],
    recommendations: []
  };
  try {
    let totalPrice = 0;
    selectedSlots.forEach((slot, index) => {
      let slotPrice = 0;
      if (slot.price && slot.price > 0) {
        slotPrice = parseFloat(slot.price);
      } else if (venue && venue.price && venue.price > 0) {
        slotPrice = parseFloat(venue.price) / 2;
      } else {
        slotPrice = 60;
        diagnosis.issues.push(`时间段${index + 1}使用默认价格`);
      }
      totalPrice += slotPrice;
      common_vendor.index.__f__("log", "at utils/sharing-fix-diagnosis.js:41", `💰 拼场时间段${index + 1}: ${slot.startTime}-${slot.endTime} = ¥${slotPrice}`);
    });
    diagnosis.priceCalculation.totalOriginalPrice = totalPrice;
    diagnosis.priceCalculation.pricePerTeam = totalPrice / 2;
    diagnosis.priceCalculation.discountAmount = totalPrice / 2;
    common_vendor.index.__f__("log", "at utils/sharing-fix-diagnosis.js:48", "💰 拼场价格计算结果:", diagnosis.priceCalculation);
    if (totalPrice <= 0) {
      diagnosis.issues.push("总价格为0或负数");
    }
    if (diagnosis.priceCalculation.pricePerTeam < 30) {
      diagnosis.issues.push("每队价格过低，可能计算有误");
    }
    if (diagnosis.priceCalculation.pricePerTeam > 500) {
      diagnosis.issues.push("每队价格过高，请检查计算逻辑");
    }
    if (diagnosis.issues.length === 0) {
      diagnosis.recommendations.push("价格计算正常");
    } else {
      diagnosis.recommendations.push("需要检查价格计算逻辑");
      diagnosis.recommendations.push("确保时间段价格数据完整");
    }
  } catch (error) {
    common_vendor.index.__f__("error", "at utils/sharing-fix-diagnosis.js:72", "❌ 拼场价格计算诊断失败:", error);
    diagnosis.issues.push(`诊断过程出错: ${error.message}`);
  }
  common_vendor.index.__f__("log", "at utils/sharing-fix-diagnosis.js:76", "🔍 拼场价格诊断结果:", diagnosis);
  return diagnosis;
}
function diagnoseSharingDataStructure(bookingData) {
  common_vendor.index.__f__("log", "at utils/sharing-fix-diagnosis.js:82", "🔍 拼场数据结构诊断");
  const diagnosis = {
    timestamp: (/* @__PURE__ */ new Date()).toISOString(),
    bookingData,
    requiredFields: {
      venueId: false,
      date: false,
      startTime: false,
      teamName: false,
      contactInfo: false,
      maxParticipants: false,
      price: false
    },
    dataTypes: {},
    issues: [],
    recommendations: []
  };
  try {
    const requiredFieldsList = Object.keys(diagnosis.requiredFields);
    requiredFieldsList.forEach((field) => {
      const hasField = bookingData.hasOwnProperty(field) && bookingData[field] !== null && bookingData[field] !== void 0;
      diagnosis.requiredFields[field] = hasField;
      diagnosis.dataTypes[field] = {
        value: bookingData[field],
        type: typeof bookingData[field],
        hasValue: hasField
      };
      if (!hasField) {
        diagnosis.issues.push(`缺少必需字段: ${field}`);
      }
    });
    if (bookingData.venueId && typeof bookingData.venueId !== "number") {
      diagnosis.issues.push("venueId应该是数字类型");
    }
    if (bookingData.price && typeof bookingData.price !== "number") {
      diagnosis.issues.push("price应该是数字类型");
    }
    if (bookingData.maxParticipants && bookingData.maxParticipants !== 2) {
      diagnosis.issues.push("拼场maxParticipants应该固定为2");
    }
    if (!bookingData.teamName || bookingData.teamName.trim() === "") {
      diagnosis.issues.push("拼场必须提供队伍名称");
    }
    if (!bookingData.contactInfo || bookingData.contactInfo.trim() === "") {
      diagnosis.issues.push("拼场必须提供联系方式");
    }
    if (diagnosis.issues.length === 0) {
      diagnosis.recommendations.push("数据结构完整");
    } else {
      diagnosis.recommendations.push("需要补充缺失的字段");
      diagnosis.recommendations.push("检查数据类型是否正确");
    }
  } catch (error) {
    common_vendor.index.__f__("error", "at utils/sharing-fix-diagnosis.js:153", "❌ 拼场数据结构诊断失败:", error);
    diagnosis.issues.push(`诊断过程出错: ${error.message}`);
  }
  common_vendor.index.__f__("log", "at utils/sharing-fix-diagnosis.js:157", "🔍 拼场数据结构诊断结果:", diagnosis);
  return diagnosis;
}
function diagnoseSharingApiPath() {
  common_vendor.index.__f__("log", "at utils/sharing-fix-diagnosis.js:163", "🔍 拼场API路径诊断");
  const diagnosis = {
    timestamp: (/* @__PURE__ */ new Date()).toISOString(),
    availableApis: {
      createSharedBooking: "/bookings/shared",
      createSharingOrder: "/sharing-orders",
      applySharingOrder: "/sharing-orders/{id}/apply-join",
      joinSharingOrder: "/bookings/shared/{id}/apply"
    },
    recommendations: [],
    preferredFlow: []
  };
  diagnosis.recommendations.push("建议统一使用 /bookings/shared 系列API");
  diagnosis.recommendations.push("避免混用 /sharing-orders 和 /bookings/shared");
  diagnosis.recommendations.push("确保前后端API路径一致");
  diagnosis.preferredFlow = [
    "1. 创建拼场: POST /bookings/shared",
    "2. 申请加入: POST /bookings/shared/{id}/apply",
    "3. 处理申请: PUT /bookings/shared/requests/{requestId}",
    "4. 支付完成: 更新申请状态为PAID"
  ];
  common_vendor.index.__f__("log", "at utils/sharing-fix-diagnosis.js:190", "🔍 拼场API路径诊断结果:", diagnosis);
  return diagnosis;
}
async function diagnoseSharingTimeSlotSync(venueId, date, selectedSlots) {
  common_vendor.index.__f__("log", "at utils/sharing-fix-diagnosis.js:196", "🔍 拼场时间段同步诊断");
  const diagnosis = {
    timestamp: (/* @__PURE__ */ new Date()).toISOString(),
    venueId,
    date,
    selectedSlots,
    syncStatus: {
      frontendSlots: selectedSlots.length,
      backendSlots: 0,
      hasDefaultIds: false,
      needsSync: false
    },
    issues: [],
    recommendations: []
  };
  try {
    const defaultIdPattern = /^default_/;
    diagnosis.syncStatus.hasDefaultIds = selectedSlots.some(
      (slot) => defaultIdPattern.test(slot.id)
    );
    if (diagnosis.syncStatus.hasDefaultIds) {
      diagnosis.issues.push("使用前端生成的默认时间段ID");
      diagnosis.syncStatus.needsSync = true;
    }
    diagnosis.recommendations.push("需要检查后端是否有对应的时间段数据");
    diagnosis.recommendations.push("确保时间段ID能被后端正确识别");
    if (diagnosis.syncStatus.needsSync) {
      diagnosis.recommendations.push("建议在创建拼场前先同步时间段到后端");
    }
  } catch (error) {
    common_vendor.index.__f__("error", "at utils/sharing-fix-diagnosis.js:235", "❌ 拼场时间段同步诊断失败:", error);
    diagnosis.issues.push(`诊断过程出错: ${error.message}`);
  }
  common_vendor.index.__f__("log", "at utils/sharing-fix-diagnosis.js:239", "🔍 拼场时间段同步诊断结果:", diagnosis);
  return diagnosis;
}
async function comprehensiveSharingDiagnosis(selectedSlots, venue, bookingForm, venueId, date) {
  var _a;
  common_vendor.index.__f__("log", "at utils/sharing-fix-diagnosis.js:245", "🚀 开始综合拼场问题诊断");
  const diagnosis = {
    timestamp: (/* @__PURE__ */ new Date()).toISOString(),
    overallStatus: "UNKNOWN",
    priceCalculationDiagnosis: null,
    dataStructureDiagnosis: null,
    apiPathDiagnosis: null,
    timeSlotSyncDiagnosis: null,
    allIssues: [],
    allRecommendations: [],
    summary: ""
  };
  try {
    common_vendor.index.__f__("log", "at utils/sharing-fix-diagnosis.js:261", "\n=== 拼场价格计算诊断 ===");
    diagnosis.priceCalculationDiagnosis = diagnoseSharingPriceCalculation(selectedSlots, venue, bookingForm);
    const mockBookingData = {
      venueId: parseInt(venueId),
      date,
      startTime: (_a = selectedSlots[0]) == null ? void 0 : _a.startTime,
      teamName: bookingForm.teamName,
      contactInfo: bookingForm.contactInfo,
      maxParticipants: 2,
      price: diagnosis.priceCalculationDiagnosis.priceCalculation.pricePerTeam,
      description: bookingForm.description
    };
    common_vendor.index.__f__("log", "at utils/sharing-fix-diagnosis.js:276", "\n=== 拼场数据结构诊断 ===");
    diagnosis.dataStructureDiagnosis = diagnoseSharingDataStructure(mockBookingData);
    common_vendor.index.__f__("log", "at utils/sharing-fix-diagnosis.js:280", "\n=== 拼场API路径诊断 ===");
    diagnosis.apiPathDiagnosis = diagnoseSharingApiPath();
    common_vendor.index.__f__("log", "at utils/sharing-fix-diagnosis.js:284", "\n=== 拼场时间段同步诊断 ===");
    diagnosis.timeSlotSyncDiagnosis = await diagnoseSharingTimeSlotSync(venueId, date, selectedSlots);
    const allDiagnoses = [
      diagnosis.priceCalculationDiagnosis,
      diagnosis.dataStructureDiagnosis,
      diagnosis.timeSlotSyncDiagnosis
    ];
    allDiagnoses.forEach((d) => {
      if (d && d.issues) {
        diagnosis.allIssues.push(...d.issues);
      }
      if (d && d.recommendations) {
        diagnosis.allRecommendations.push(...d.recommendations);
      }
    });
    if (diagnosis.apiPathDiagnosis && diagnosis.apiPathDiagnosis.recommendations) {
      diagnosis.allRecommendations.push(...diagnosis.apiPathDiagnosis.recommendations);
    }
    if (diagnosis.allIssues.length === 0) {
      diagnosis.overallStatus = "HEALTHY";
      diagnosis.summary = "✅ 拼场系统状态良好";
    } else if (diagnosis.allIssues.length <= 3) {
      diagnosis.overallStatus = "WARNING";
      diagnosis.summary = `⚠️ 发现${diagnosis.allIssues.length}个问题，需要注意`;
    } else {
      diagnosis.overallStatus = "ERROR";
      diagnosis.summary = `❌ 发现${diagnosis.allIssues.length}个问题，需要修复`;
    }
    common_vendor.index.__f__("log", "at utils/sharing-fix-diagnosis.js:320", "\n📋 综合拼场诊断结果:");
    common_vendor.index.__f__("log", "at utils/sharing-fix-diagnosis.js:321", `📊 总体状态: ${diagnosis.overallStatus}`);
    common_vendor.index.__f__("log", "at utils/sharing-fix-diagnosis.js:322", `📊 发现问题: ${diagnosis.allIssues.length}个`);
    common_vendor.index.__f__("log", "at utils/sharing-fix-diagnosis.js:323", `📊 修复建议: ${diagnosis.allRecommendations.length}个`);
    common_vendor.index.__f__("log", "at utils/sharing-fix-diagnosis.js:324", `📊 总结: ${diagnosis.summary}`);
    if (diagnosis.allIssues.length > 0) {
      common_vendor.index.__f__("log", "at utils/sharing-fix-diagnosis.js:327", "\n❌ 发现的问题:");
      diagnosis.allIssues.forEach((issue, index) => {
        common_vendor.index.__f__("log", "at utils/sharing-fix-diagnosis.js:329", `   ${index + 1}. ${issue}`);
      });
    }
    if (diagnosis.allRecommendations.length > 0) {
      common_vendor.index.__f__("log", "at utils/sharing-fix-diagnosis.js:334", "\n💡 修复建议:");
      diagnosis.allRecommendations.forEach((rec, index) => {
        common_vendor.index.__f__("log", "at utils/sharing-fix-diagnosis.js:336", `   ${index + 1}. ${rec}`);
      });
    }
  } catch (error) {
    common_vendor.index.__f__("error", "at utils/sharing-fix-diagnosis.js:341", "❌ 综合拼场诊断失败:", error);
    diagnosis.overallStatus = "ERROR";
    diagnosis.summary = `❌ 诊断过程出错: ${error.message}`;
  }
  return diagnosis;
}
function quickSharingFix(selectedSlots, venue, bookingForm, venueId, date) {
  common_vendor.index.__f__("log", "at utils/sharing-fix-diagnosis.js:351", "🔧 拼场问题快速修复");
  const fix = {
    timestamp: (/* @__PURE__ */ new Date()).toISOString(),
    originalData: {
      selectedSlots,
      venue,
      bookingForm
    },
    fixedData: null,
    appliedFixes: [],
    success: false
  };
  try {
    let totalPrice = 0;
    selectedSlots.forEach((slot) => {
      let slotPrice = 0;
      if (slot.price && slot.price > 0) {
        slotPrice = parseFloat(slot.price);
      } else if (venue && venue.price && venue.price > 0) {
        slotPrice = parseFloat(venue.price) / 2;
        fix.appliedFixes.push(`修复时间段${slot.startTime}-${slot.endTime}价格`);
      } else {
        slotPrice = 60;
        fix.appliedFixes.push(`设置时间段${slot.startTime}-${slot.endTime}默认价格`);
      }
      totalPrice += slotPrice;
    });
    const pricePerTeam = totalPrice / 2;
    fix.fixedData = {
      venueId: parseInt(venueId),
      date,
      startTime: selectedSlots[0].startTime,
      endTime: selectedSlots[selectedSlots.length - 1].endTime,
      teamName: bookingForm.teamName || "",
      contactInfo: bookingForm.contactInfo || "",
      maxParticipants: 2,
      description: bookingForm.description || "",
      price: pricePerTeam,
      // 每队支付的价格
      totalOriginalPrice: totalPrice,
      // 原始总价
      slotIds: selectedSlots.map((slot) => slot.id)
    };
    if (fix.fixedData.price > 0 && fix.fixedData.teamName.trim() !== "" && fix.fixedData.contactInfo.trim() !== "") {
      fix.success = true;
      fix.appliedFixes.push("数据验证通过");
    } else {
      fix.appliedFixes.push("数据验证失败");
    }
    common_vendor.index.__f__("log", "at utils/sharing-fix-diagnosis.js:411", "🔧 拼场快速修复结果:", fix);
  } catch (error) {
    common_vendor.index.__f__("error", "at utils/sharing-fix-diagnosis.js:414", "❌ 拼场快速修复失败:", error);
    fix.appliedFixes.push(`修复失败: ${error.message}`);
  }
  return fix;
}
const sharingFixDiagnosis = {
  diagnoseSharingPriceCalculation,
  diagnoseSharingDataStructure,
  diagnoseSharingApiPath,
  diagnoseSharingTimeSlotSync,
  comprehensiveSharingDiagnosis,
  quickSharingFix
};
exports.comprehensiveSharingDiagnosis = comprehensiveSharingDiagnosis;
exports.default = sharingFixDiagnosis;
exports.diagnoseSharingApiPath = diagnoseSharingApiPath;
exports.diagnoseSharingDataStructure = diagnoseSharingDataStructure;
exports.diagnoseSharingPriceCalculation = diagnoseSharingPriceCalculation;
exports.diagnoseSharingTimeSlotSync = diagnoseSharingTimeSlotSync;
exports.quickSharingFix = quickSharingFix;
//# sourceMappingURL=../../.sourcemap/mp-weixin/utils/sharing-fix-diagnosis.js.map
