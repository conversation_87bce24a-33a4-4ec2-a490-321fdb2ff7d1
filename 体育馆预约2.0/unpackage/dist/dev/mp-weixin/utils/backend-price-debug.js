"use strict";
Object.defineProperty(exports, Symbol.toStringTag, { value: "Module" });
const common_vendor = require("../common/vendor.js");
function debugBookingDataSend(bookingData) {
  common_vendor.index.__f__("log", "at utils/backend-price-debug.js:8", "🔍 调试预约数据发送");
  const debug = {
    timestamp: (/* @__PURE__ */ new Date()).toISOString(),
    originalData: bookingData,
    priceAnalysis: {
      hasPrice: bookingData.hasOwnProperty("price"),
      priceValue: bookingData.price,
      priceType: typeof bookingData.price,
      isNumber: !isNaN(parseFloat(bookingData.price)),
      isPositive: parseFloat(bookingData.price) > 0
    },
    dataStructure: {
      venueId: {
        value: bookingData.venueId,
        type: typeof bookingData.venueId
      },
      date: {
        value: bookingData.date,
        type: typeof bookingData.date
      },
      startTime: {
        value: bookingData.startTime,
        type: typeof bookingData.startTime
      },
      endTime: {
        value: bookingData.endTime,
        type: typeof bookingData.endTime
      },
      price: {
        value: bookingData.price,
        type: typeof bookingData.price
      },
      slotIds: {
        value: bookingData.slotIds,
        type: typeof bookingData.slotIds,
        length: Array.isArray(bookingData.slotIds) ? bookingData.slotIds.length : "N/A"
      }
    },
    serialization: null,
    issues: []
  };
  if (!debug.priceAnalysis.hasPrice) {
    debug.issues.push("缺少price字段");
  } else if (!debug.priceAnalysis.isNumber) {
    debug.issues.push("price不是有效数字");
  } else if (!debug.priceAnalysis.isPositive) {
    debug.issues.push("price不是正数");
  }
  if (typeof bookingData.venueId === "string") {
    debug.issues.push("venueId是字符串类型，后端可能需要数字类型");
  }
  try {
    const serialized = JSON.stringify(bookingData);
    const deserialized = JSON.parse(serialized);
    debug.serialization = {
      success: true,
      serializedLength: serialized.length,
      deserializedPrice: deserialized.price,
      pricePreserved: deserialized.price === bookingData.price
    };
  } catch (error) {
    debug.serialization = {
      success: false,
      error: error.message
    };
    debug.issues.push("数据序列化失败");
  }
  common_vendor.index.__f__("log", "at utils/backend-price-debug.js:83", "🔍 预约数据发送调试结果:", debug);
  return debug;
}
function debugBackendResponse(response) {
  common_vendor.index.__f__("log", "at utils/backend-price-debug.js:89", "🔍 调试后端响应");
  const debug = {
    timestamp: (/* @__PURE__ */ new Date()).toISOString(),
    originalResponse: response,
    responseAnalysis: {
      hasData: response.hasOwnProperty("data"),
      hasOrderId: response.hasOwnProperty("orderId"),
      success: response.success,
      message: response.message
    },
    priceAnalysis: null,
    issues: []
  };
  if (response.data) {
    debug.priceAnalysis = {
      hasTotalPrice: response.data.hasOwnProperty("totalPrice"),
      totalPriceValue: response.data.totalPrice,
      totalPriceType: typeof response.data.totalPrice,
      isZero: response.data.totalPrice === 0,
      orderFields: Object.keys(response.data)
    };
    if (debug.priceAnalysis.isZero) {
      debug.issues.push("后端返回的totalPrice为0");
    }
  } else {
    debug.issues.push("响应中没有data字段");
  }
  common_vendor.index.__f__("log", "at utils/backend-price-debug.js:121", "🔍 后端响应调试结果:", debug);
  return debug;
}
function compareFrontendBackendPrice(sentData, receivedResponse) {
  var _a, _b, _c;
  common_vendor.index.__f__("log", "at utils/backend-price-debug.js:127", "🔍 比较前端发送和后端返回的价格");
  const comparison = {
    timestamp: (/* @__PURE__ */ new Date()).toISOString(),
    frontend: {
      sentPrice: sentData.price,
      sentPriceType: typeof sentData.price
    },
    backend: {
      receivedPrice: (_a = receivedResponse.data) == null ? void 0 : _a.totalPrice,
      receivedPriceType: typeof ((_b = receivedResponse.data) == null ? void 0 : _b.totalPrice)
    },
    match: false,
    priceLoss: 0,
    issues: []
  };
  const frontendPrice = parseFloat(sentData.price);
  const backendPrice = parseFloat(((_c = receivedResponse.data) == null ? void 0 : _c.totalPrice) || 0);
  comparison.match = frontendPrice === backendPrice;
  comparison.priceLoss = frontendPrice - backendPrice;
  if (!comparison.match) {
    comparison.issues.push(`价格不匹配: 发送${frontendPrice}，接收${backendPrice}`);
  }
  if (backendPrice === 0 && frontendPrice > 0) {
    comparison.issues.push("后端价格被重置为0");
  }
  common_vendor.index.__f__("log", "at utils/backend-price-debug.js:158", "🔍 价格比较结果:", comparison);
  return comparison;
}
function generatePriceTransmissionReport(sentData, receivedResponse) {
  common_vendor.index.__f__("log", "at utils/backend-price-debug.js:164", "📊 生成价格传递报告");
  const sendDebug = debugBookingDataSend(sentData);
  const responseDebug = debugBackendResponse(receivedResponse);
  const priceComparison = compareFrontendBackendPrice(sentData, receivedResponse);
  const report = {
    timestamp: (/* @__PURE__ */ new Date()).toISOString(),
    title: "价格传递调试报告",
    summary: "",
    details: {
      sendDebug,
      responseDebug,
      priceComparison
    },
    overallIssues: [],
    recommendations: []
  };
  report.overallIssues.push(...sendDebug.issues);
  report.overallIssues.push(...responseDebug.issues);
  report.overallIssues.push(...priceComparison.issues);
  if (priceComparison.priceLoss > 0) {
    report.recommendations.push("检查后端是否正确接收和处理price字段");
    report.recommendations.push("验证后端Order模型的totalPrice设置逻辑");
  }
  if (sendDebug.issues.includes("venueId是字符串类型，后端可能需要数字类型")) {
    report.recommendations.push("考虑将venueId转换为数字类型");
  }
  if (responseDebug.issues.includes("后端返回的totalPrice为0")) {
    report.recommendations.push("检查后端是否有重置价格的逻辑");
    report.recommendations.push("验证后端价格计算和保存流程");
  }
  if (report.overallIssues.length === 0) {
    report.summary = "✅ 价格传递正常";
  } else {
    report.summary = `❌ 发现${report.overallIssues.length}个问题`;
  }
  common_vendor.index.__f__("log", "at utils/backend-price-debug.js:210", "📊 价格传递报告:", report);
  return report;
}
function startPriceTransmissionMonitor() {
  common_vendor.index.__f__("log", "at utils/backend-price-debug.js:216", "🔍 启动价格传递监控");
  const monitor = {
    isActive: false,
    transmissions: [],
    start() {
      this.isActive = true;
      common_vendor.index.__f__("log", "at utils/backend-price-debug.js:224", "✅ 价格传递监控已启动");
    },
    recordTransmission(sentData, receivedResponse) {
      var _a, _b;
      if (!this.isActive)
        return;
      const transmission = {
        timestamp: (/* @__PURE__ */ new Date()).toISOString(),
        sentPrice: sentData.price,
        receivedPrice: (_a = receivedResponse.data) == null ? void 0 : _a.totalPrice,
        success: sentData.price === ((_b = receivedResponse.data) == null ? void 0 : _b.totalPrice),
        report: generatePriceTransmissionReport(sentData, receivedResponse)
      };
      this.transmissions.push(transmission);
      if (this.transmissions.length > 10) {
        this.transmissions.shift();
      }
      common_vendor.index.__f__("log", "at utils/backend-price-debug.js:245", "📊 记录价格传递:", transmission);
      if (!transmission.success) {
        common_vendor.index.__f__("warn", "at utils/backend-price-debug.js:248", "⚠️ 价格传递失败:", transmission.report.overallIssues);
      }
    },
    stop() {
      this.isActive = false;
      common_vendor.index.__f__("log", "at utils/backend-price-debug.js:254", "🛑 价格传递监控已停止");
      return this.transmissions;
    },
    getReport() {
      const successCount = this.transmissions.filter((t) => t.success).length;
      const failureCount = this.transmissions.length - successCount;
      return {
        totalTransmissions: this.transmissions.length,
        successCount,
        failureCount,
        successRate: this.transmissions.length > 0 ? Math.round(successCount / this.transmissions.length * 100) : 0,
        recentTransmissions: this.transmissions.slice(-5)
      };
    }
  };
  monitor.start();
  return monitor;
}
async function quickPriceTransmissionTest(bookingStore, testData) {
  var _a;
  common_vendor.index.__f__("log", "at utils/backend-price-debug.js:279", "⚡ 快速价格传递测试");
  const test = {
    timestamp: (/* @__PURE__ */ new Date()).toISOString(),
    testData,
    result: null,
    success: false,
    error: null
  };
  try {
    const monitor = startPriceTransmissionMonitor();
    const response = await bookingStore.createBooking(testData);
    monitor.recordTransmission(testData, response);
    const monitorReport = monitor.getReport();
    test.result = {
      response,
      monitorReport
    };
    test.success = testData.price === ((_a = response.data) == null ? void 0 : _a.totalPrice);
  } catch (error) {
    common_vendor.index.__f__("error", "at utils/backend-price-debug.js:310", "❌ 价格传递测试失败:", error);
    test.error = error.message;
  }
  common_vendor.index.__f__("log", "at utils/backend-price-debug.js:314", "⚡ 价格传递测试结果:", test);
  return test;
}
exports.compareFrontendBackendPrice = compareFrontendBackendPrice;
exports.debugBackendResponse = debugBackendResponse;
exports.debugBookingDataSend = debugBookingDataSend;
exports.generatePriceTransmissionReport = generatePriceTransmissionReport;
exports.quickPriceTransmissionTest = quickPriceTransmissionTest;
exports.startPriceTransmissionMonitor = startPriceTransmissionMonitor;
//# sourceMappingURL=../../.sourcemap/mp-weixin/utils/backend-price-debug.js.map
