"use strict";
const common_vendor = require("../../common/vendor.js");
const api_payment = require("../../api/payment.js");
const api_order = require("../../api/order.js");
const utils_request = require("../../utils/request.js");
const stores_booking = require("../../stores/booking.js");
const _sfc_main = {
  name: "PaymentPage",
  data() {
    return {
      bookingStore: null,
      orderId: null,
      orderType: "booking",
      // booking 或 sharing
      orderInfo: null,
      loading: true,
      selectedMethod: "wechat",
      paying: false,
      fromPage: "",
      // 记录来源页面
      paymentResult: {
        success: false,
        title: "",
        message: "",
        buttonText: "确定"
      }
    };
  },
  computed: {
    canPay() {
      if (!this.orderInfo || !this.selectedMethod || this.paying)
        return false;
      if (this.orderInfo.isVirtualOrder) {
        return this.orderInfo.status === "PENDING";
      } else {
        return this.orderInfo.status === "PENDING";
      }
    },
    payButtonText() {
      var _a;
      if (this.paying)
        return "支付中...";
      if (!this.orderInfo)
        return "加载中...";
      if (this.orderInfo.isVirtualOrder) {
        if (this.orderInfo.status === "PENDING") {
          const amount = this.orderInfo.paymentAmount || this.orderInfo.totalPrice;
          return `立即支付 ¥${(amount == null ? void 0 : amount.toFixed(2)) || "0.00"}`;
        } else {
          const statusMessages = {
            "SHARING_SUCCESS": "拼场已成功",
            "CANCELLED": "申请已取消",
            "EXPIRED": "申请已过期",
            "NOT_FOUND": "申请不存在",
            "ACCESS_DENIED": "无权访问"
          };
          return statusMessages[this.orderInfo.status] || "订单状态异常";
        }
      } else {
        if (this.orderInfo.status === "PENDING") {
          return `立即支付 ¥${((_a = this.orderInfo.totalPrice) == null ? void 0 : _a.toFixed(2)) || "0.00"}`;
        } else {
          return "订单状态异常";
        }
      }
    }
  },
  onLoad(options) {
    common_vendor.index.__f__("log", "at pages/payment/index.vue:200", "支付页面参数:", options);
    this.bookingStore = stores_booking.useBookingStore();
    if (options.orderId) {
      this.orderId = options.orderId;
      this.orderType = options.type || "booking";
      this.fromPage = options.from || "";
      this.loadOrderInfo();
    } else {
      common_vendor.index.showToast({
        title: "订单ID缺失",
        icon: "error"
      });
      setTimeout(() => {
        common_vendor.index.navigateBack();
      }, 1500);
    }
  },
  methods: {
    // 加载订单信息
    async loadOrderInfo() {
      var _a;
      try {
        this.loading = true;
        const isVirtualOrder = this.orderId < 0;
        common_vendor.index.__f__("log", "at pages/payment/index.vue:229", "订单ID:", this.orderId, "是否为虚拟订单:", isVirtualOrder);
        let response;
        if (isVirtualOrder) {
          const requestId = Math.abs(this.orderId);
          common_vendor.index.__f__("log", "at pages/payment/index.vue:235", "获取虚拟订单详情，申请ID:", requestId);
          try {
            response = await utils_request.get(`/users/me/virtual-order/${requestId}`);
          } catch (error) {
            common_vendor.index.__f__("error", "at pages/payment/index.vue:240", "获取虚拟订单失败:", error);
            if (error.status === 404) {
              response = {
                data: {
                  id: this.orderId,
                  orderNo: `REQ_${requestId}`,
                  status: "NOT_FOUND",
                  isVirtualOrder: true,
                  venueName: "未知场馆",
                  totalPrice: 0,
                  paymentAmount: 0
                }
              };
            } else if (error.status === 403) {
              response = {
                data: {
                  id: this.orderId,
                  orderNo: `REQ_${requestId}`,
                  status: "ACCESS_DENIED",
                  isVirtualOrder: true,
                  venueName: "未知场馆",
                  totalPrice: 0,
                  paymentAmount: 0
                }
              };
            } else {
              throw error;
            }
          }
        } else {
          common_vendor.index.__f__("log", "at pages/payment/index.vue:272", "获取真实订单详情，订单ID:", this.orderId);
          common_vendor.index.__f__("log", "at pages/payment/index.vue:273", "bookingStore:", this.bookingStore);
          common_vendor.index.__f__("log", "at pages/payment/index.vue:274", "bookingStore.getBookingDetail:", (_a = this.bookingStore) == null ? void 0 : _a.getBookingDetail);
          common_vendor.index.__f__("log", "at pages/payment/index.vue:275", "bookingStore类型:", typeof this.bookingStore);
          common_vendor.index.__f__("log", "at pages/payment/index.vue:278", "检查bookingStore方法:", {
            hasStore: !!this.bookingStore,
            hasGetBookingDetailAction: this.bookingStore && typeof this.bookingStore.getBookingDetail === "function",
            storeKeys: this.bookingStore ? Object.keys(this.bookingStore) : "no store"
          });
          if (!this.bookingStore) {
            common_vendor.index.__f__("warn", "at pages/payment/index.vue:285", "bookingStore未初始化，使用原API作为备用");
            response = await api_order.getOrderDetail(this.orderId);
          } else {
            try {
              common_vendor.index.__f__("log", "at pages/payment/index.vue:290", "尝试调用bookingStore.getBookingDetail action...");
              await this.bookingStore.getBookingDetail(this.orderId);
              common_vendor.index.__f__("log", "at pages/payment/index.vue:292", "Booking Store action调用完成");
              const storeData = this.bookingStore.bookingDetail;
              common_vendor.index.__f__("log", "at pages/payment/index.vue:296", "从Store state获取的数据:", storeData);
              common_vendor.index.__f__("log", "at pages/payment/index.vue:297", "Store state数据类型:", typeof storeData);
              common_vendor.index.__f__("log", "at pages/payment/index.vue:298", "Store state数据内容:", storeData);
              if (storeData) {
                response = { data: storeData };
                common_vendor.index.__f__("log", "at pages/payment/index.vue:302", "使用Store数据成功");
              } else {
                common_vendor.index.__f__("warn", "at pages/payment/index.vue:304", "Store数据为空，使用原API作为备用");
                response = await api_order.getOrderDetail(this.orderId);
              }
            } catch (storeError) {
              common_vendor.index.__f__("error", "at pages/payment/index.vue:308", "Store调用失败，使用原API作为备用:", storeError);
              response = await api_order.getOrderDetail(this.orderId);
            }
          }
        }
        this.orderInfo = response.data || response;
        common_vendor.index.__f__("log", "at pages/payment/index.vue:316", "最终订单信息:", this.orderInfo);
        if (this.orderInfo && !isVirtualOrder && (this.orderInfo.totalPrice === 0 || !this.orderInfo.totalPrice)) {
          common_vendor.index.__f__("log", "at pages/payment/index.vue:320", "检测到订单金额为0，尝试修复...");
          if (this.orderInfo.price && this.orderInfo.price > 0) {
            common_vendor.index.__f__("log", "at pages/payment/index.vue:324", "使用订单的price字段:", this.orderInfo.price);
            this.orderInfo.totalPrice = this.orderInfo.price;
          } else {
            const calculatedPrice = this.calculateOrderPrice();
            if (calculatedPrice > 0) {
              common_vendor.index.__f__("log", "at pages/payment/index.vue:330", "使用计算的价格:", calculatedPrice);
              this.orderInfo.totalPrice = calculatedPrice;
            }
          }
        }
        if (isVirtualOrder) {
          this.orderInfo.isVirtualOrder = true;
          common_vendor.index.__f__("log", "at pages/payment/index.vue:339", "虚拟订单详细信息:");
          common_vendor.index.__f__("log", "at pages/payment/index.vue:340", "- 订单状态:", this.orderInfo.status);
          common_vendor.index.__f__("log", "at pages/payment/index.vue:341", "- 申请状态:", this.orderInfo.requestStatus);
          common_vendor.index.__f__("log", "at pages/payment/index.vue:342", "- 支付金额:", this.orderInfo.paymentAmount);
          common_vendor.index.__f__("log", "at pages/payment/index.vue:343", "- 总价:", this.orderInfo.totalPrice);
          common_vendor.index.__f__("log", "at pages/payment/index.vue:344", "- 队伍名称:", this.orderInfo.applicantTeamName);
          common_vendor.index.__f__("log", "at pages/payment/index.vue:345", "- 联系方式:", this.orderInfo.applicantContact);
          common_vendor.index.__f__("log", "at pages/payment/index.vue:346", "- 预约时间:", this.orderInfo.bookingTime);
          common_vendor.index.__f__("log", "at pages/payment/index.vue:347", "- 结束时间:", this.orderInfo.endTime);
          common_vendor.index.__f__("log", "at pages/payment/index.vue:348", "- 原始响应:", response);
          if (!this.orderInfo.status) {
            common_vendor.index.__f__("error", "at pages/payment/index.vue:352", "虚拟订单状态为空！");
            this.orderInfo.status = "PENDING";
          }
          if (!this.orderInfo.paymentAmount && !this.orderInfo.totalPrice) {
            common_vendor.index.__f__("error", "at pages/payment/index.vue:358", "虚拟订单金额为空！");
          }
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/payment/index.vue:363", "加载订单信息失败:", error);
        common_vendor.index.showToast({
          title: "加载失败",
          icon: "error"
        });
        setTimeout(() => {
          common_vendor.index.navigateBack();
        }, 1500);
      } finally {
        this.loading = false;
      }
    },
    // 选择支付方式
    selectMethod(method) {
      this.selectedMethod = method;
    },
    // 处理支付
    async handlePayment() {
      if (!this.canPay)
        return;
      try {
        this.paying = true;
        common_vendor.index.showLoading({ title: "支付中..." });
        await new Promise((resolve) => setTimeout(resolve, 2e3));
        const isSuccess = Math.random() > 0.2;
        common_vendor.index.hideLoading();
        if (isSuccess) {
          const response = await api_payment.payOrder(this.orderId, this.selectedMethod);
          if (response.success) {
            let successUrl = `/pages/payment/success?orderId=${this.orderId}`;
            if (this.fromPage) {
              successUrl += `&from=${this.fromPage}`;
            }
            common_vendor.index.redirectTo({
              url: successUrl
            });
          } else {
            throw new Error(response.message || "支付失败");
          }
        } else {
          throw new Error("支付失败，请检查账户余额或重试");
        }
      } catch (error) {
        common_vendor.index.hideLoading();
        common_vendor.index.__f__("error", "at pages/payment/index.vue:422", "支付失败:", error);
        common_vendor.index.redirectTo({
          url: `/pages/payment/failed?orderId=${this.orderId}&reason=${encodeURIComponent(error.message)}`
        });
      } finally {
        this.paying = false;
      }
    },
    // 处理结果操作（保留用于其他用途）
    handleResultAction() {
      this.$refs.resultPopup.close();
      if (this.paymentResult.success) {
        common_vendor.index.redirectTo({
          url: "/pages/booking/list"
        });
      }
    },
    // 格式化日期时间
    formatDateTime(date, startTime, endTime) {
      if (!date || !startTime)
        return "未设置";
      let dateStr = "";
      if (typeof date === "string" && date.includes("-")) {
        const [year, month, day] = date.split("-");
        dateStr = `${month}-${day}`;
      } else {
        const dateObj = new Date(date);
        dateStr = dateObj.toLocaleDateString("zh-CN", {
          month: "2-digit",
          day: "2-digit"
        });
      }
      const timeStr = endTime ? `${startTime}-${endTime}` : startTime;
      return `${dateStr} ${timeStr}`;
    },
    // 返回
    goBack() {
      common_vendor.index.navigateBack();
    },
    // 格式化订单时间（处理虚拟订单和普通订单的差异）
    formatOrderDateTime() {
      if (!this.orderInfo)
        return "未设置";
      common_vendor.index.__f__("log", "at pages/payment/index.vue:480", "formatOrderDateTime - 订单数据:", this.orderInfo);
      common_vendor.index.__f__("log", "at pages/payment/index.vue:481", "formatOrderDateTime - 字段检查:");
      common_vendor.index.__f__("log", "at pages/payment/index.vue:482", "- bookingDate:", this.orderInfo.bookingDate);
      common_vendor.index.__f__("log", "at pages/payment/index.vue:483", "- bookingTime:", this.orderInfo.bookingTime);
      common_vendor.index.__f__("log", "at pages/payment/index.vue:484", "- startTime:", this.orderInfo.startTime);
      common_vendor.index.__f__("log", "at pages/payment/index.vue:485", "- endTime:", this.orderInfo.endTime);
      common_vendor.index.__f__("log", "at pages/payment/index.vue:486", "- isVirtualOrder:", this.orderInfo.isVirtualOrder);
      if (this.orderInfo.isVirtualOrder) {
        const startTime = this.orderInfo.bookingTime;
        const endTime = this.orderInfo.endTime;
        common_vendor.index.__f__("log", "at pages/payment/index.vue:493", "虚拟订单时间格式化 - 原始数据:", { startTime, endTime });
        if (!startTime) {
          common_vendor.index.__f__("warn", "at pages/payment/index.vue:496", "虚拟订单开始时间为空");
          return "未设置";
        }
        try {
          let startDateTime, endDateTime;
          if (typeof startTime === "string") {
            let isoTime = startTime;
            if (startTime.includes(" ") && !startTime.includes("T")) {
              isoTime = startTime.replace(" ", "T");
            }
            startDateTime = new Date(isoTime);
            common_vendor.index.__f__("log", "at pages/payment/index.vue:512", "支付页面时间转换 - 原始:", startTime, "转换后:", isoTime, "解析结果:", startDateTime);
          } else {
            startDateTime = new Date(startTime);
          }
          if (endTime) {
            if (typeof endTime === "string") {
              let isoEndTime = endTime;
              if (endTime.includes(" ") && !endTime.includes("T")) {
                isoEndTime = endTime.replace(" ", "T");
              }
              endDateTime = new Date(isoEndTime);
              common_vendor.index.__f__("log", "at pages/payment/index.vue:524", "支付页面结束时间转换 - 原始:", endTime, "转换后:", isoEndTime, "解析结果:", endDateTime);
            } else {
              endDateTime = new Date(endTime);
            }
          }
          if (isNaN(startDateTime.getTime())) {
            common_vendor.index.__f__("error", "at pages/payment/index.vue:532", "无效的开始时间:", startTime);
            return "时间格式错误";
          }
          const dateStr = startDateTime.toLocaleDateString("zh-CN", {
            month: "2-digit",
            day: "2-digit"
          });
          const startTimeStr = startDateTime.toLocaleTimeString("zh-CN", {
            hour: "2-digit",
            minute: "2-digit",
            hour12: false
          });
          let endTimeStr = "";
          if (endDateTime && !isNaN(endDateTime.getTime())) {
            endTimeStr = endDateTime.toLocaleTimeString("zh-CN", {
              hour: "2-digit",
              minute: "2-digit",
              hour12: false
            });
          }
          const result = `${dateStr} ${startTimeStr}${endTimeStr ? "-" + endTimeStr : ""}`;
          common_vendor.index.__f__("log", "at pages/payment/index.vue:560", "虚拟订单时间格式化结果:", result);
          return result;
        } catch (error) {
          common_vendor.index.__f__("error", "at pages/payment/index.vue:563", "虚拟订单时间格式化错误:", error, "原始数据:", { startTime, endTime });
          return "时间格式错误";
        }
      } else {
        common_vendor.index.__f__("log", "at pages/payment/index.vue:568", "普通订单时间格式化");
        if (this.orderInfo.bookingTime) {
          const bookingTime = this.orderInfo.bookingTime;
          let endTime = this.orderInfo.endTime;
          if (!endTime) {
            common_vendor.index.__f__("log", "at pages/payment/index.vue:579", "没有endTime，检查其他字段:");
            common_vendor.index.__f__("log", "at pages/payment/index.vue:580", "- duration:", this.orderInfo.duration);
            common_vendor.index.__f__("log", "at pages/payment/index.vue:581", "- slotIds:", this.orderInfo.slotIds);
            common_vendor.index.__f__("log", "at pages/payment/index.vue:582", "- bookingType:", this.orderInfo.bookingType);
            if (this.orderInfo.duration) {
              try {
                const startDateTime = new Date(bookingTime.replace(" ", "T"));
                const durationHours = parseFloat(this.orderInfo.duration);
                const endDateTime = new Date(startDateTime.getTime() + durationHours * 60 * 60 * 1e3);
                endTime = endDateTime.toISOString().replace("T", " ").substring(0, 19);
                common_vendor.index.__f__("log", "at pages/payment/index.vue:591", "根据duration计算的endTime:", endTime);
              } catch (error) {
                common_vendor.index.__f__("error", "at pages/payment/index.vue:593", "计算结束时间失败:", error);
              }
            } else {
              common_vendor.index.__f__("log", "at pages/payment/index.vue:597", "没有duration，尝试根据其他信息推算");
              try {
                const startDateTime = new Date(bookingTime.replace(" ", "T"));
                common_vendor.index.__f__("log", "at pages/payment/index.vue:603", "开始时间解析:", startDateTime);
                const endDateTime = new Date(startDateTime.getTime() + 2 * 60 * 60 * 1e3);
                common_vendor.index.__f__("log", "at pages/payment/index.vue:607", "计算的结束时间:", endDateTime);
                const year = endDateTime.getFullYear();
                const month = String(endDateTime.getMonth() + 1).padStart(2, "0");
                const day = String(endDateTime.getDate()).padStart(2, "0");
                const hours = String(endDateTime.getHours()).padStart(2, "0");
                const minutes = String(endDateTime.getMinutes()).padStart(2, "0");
                const seconds = String(endDateTime.getSeconds()).padStart(2, "0");
                endTime = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
                common_vendor.index.__f__("log", "at pages/payment/index.vue:618", "根据默认时长推算的endTime:", endTime);
              } catch (error) {
                common_vendor.index.__f__("error", "at pages/payment/index.vue:620", "推算结束时间失败:", error);
              }
            }
          }
          common_vendor.index.__f__("log", "at pages/payment/index.vue:625", "使用bookingTime字段:", bookingTime, "endTime:", endTime);
          try {
            let startDateTime;
            if (typeof bookingTime === "string") {
              let isoTime = bookingTime;
              if (bookingTime.includes(" ") && !bookingTime.includes("T")) {
                isoTime = bookingTime.replace(" ", "T");
              }
              startDateTime = new Date(isoTime);
            } else {
              startDateTime = new Date(bookingTime);
            }
            const dateStr = startDateTime.toLocaleDateString("zh-CN", {
              month: "2-digit",
              day: "2-digit"
            });
            const startTimeStr = startDateTime.toLocaleTimeString("zh-CN", {
              hour: "2-digit",
              minute: "2-digit",
              hour12: false
            });
            let endTimeStr = "";
            if (endTime) {
              let endDateTime;
              if (typeof endTime === "string") {
                let isoEndTime = endTime;
                if (endTime.includes(" ") && !endTime.includes("T")) {
                  isoEndTime = endTime.replace(" ", "T");
                }
                endDateTime = new Date(isoEndTime);
              } else {
                endDateTime = new Date(endTime);
              }
              if (!isNaN(endDateTime.getTime())) {
                endTimeStr = endDateTime.toLocaleTimeString("zh-CN", {
                  hour: "2-digit",
                  minute: "2-digit",
                  hour12: false
                });
              }
            }
            const result = `${dateStr} ${startTimeStr}${endTimeStr ? "-" + endTimeStr : ""}`;
            common_vendor.index.__f__("log", "at pages/payment/index.vue:676", "普通订单时间格式化结果:", result);
            return result;
          } catch (error) {
            common_vendor.index.__f__("error", "at pages/payment/index.vue:679", "普通订单时间格式化错误:", error);
            return "时间格式错误";
          }
        } else {
          common_vendor.index.__f__("log", "at pages/payment/index.vue:684", "使用旧格式字段");
          return this.formatDateTime(this.orderInfo.bookingDate, this.orderInfo.startTime, this.orderInfo.endTime);
        }
      }
    },
    // 获取队伍名称
    getTeamName() {
      if (!this.orderInfo)
        return "未设置";
      if (this.orderInfo.isVirtualOrder) {
        return this.orderInfo.applicantTeamName || "未设置";
      } else {
        return this.orderInfo.teamName || "未设置";
      }
    },
    // 获取联系方式
    getContactInfo() {
      if (!this.orderInfo)
        return "未设置";
      if (this.orderInfo.isVirtualOrder) {
        return this.orderInfo.applicantContact || "未设置";
      } else {
        return this.orderInfo.contactInfo || "未设置";
      }
    },
    // 获取订单金额
    getOrderAmount() {
      if (!this.orderInfo)
        return "0.00";
      common_vendor.index.__f__("log", "at pages/payment/index.vue:716", "getOrderAmount - 订单信息:", this.orderInfo);
      common_vendor.index.__f__("log", "at pages/payment/index.vue:717", "getOrderAmount - 字段检查:");
      common_vendor.index.__f__("log", "at pages/payment/index.vue:718", "- totalPrice:", this.orderInfo.totalPrice);
      common_vendor.index.__f__("log", "at pages/payment/index.vue:719", "- paymentAmount:", this.orderInfo.paymentAmount);
      common_vendor.index.__f__("log", "at pages/payment/index.vue:720", "- price:", this.orderInfo.price);
      common_vendor.index.__f__("log", "at pages/payment/index.vue:721", "- isVirtualOrder:", this.orderInfo.isVirtualOrder);
      let amount;
      if (this.orderInfo.isVirtualOrder) {
        amount = this.orderInfo.paymentAmount;
      } else {
        amount = this.orderInfo.totalPrice;
        if (!amount || amount === 0) {
          common_vendor.index.__f__("log", "at pages/payment/index.vue:730", "totalPrice为0，尝试使用price字段:", this.orderInfo.price);
          amount = this.orderInfo.price;
          if (!amount || amount === 0) {
            common_vendor.index.__f__("log", "at pages/payment/index.vue:735", "price字段也为0，尝试计算价格");
            amount = this.calculateOrderPrice();
          }
        }
      }
      const result = (amount == null ? void 0 : amount.toFixed(2)) || "0.00";
      common_vendor.index.__f__("log", "at pages/payment/index.vue:742", "getOrderAmount - 最终金额:", result);
      return result;
    },
    // 计算订单价格（当后端价格为0时的备用方案）
    calculateOrderPrice() {
      if (!this.orderInfo)
        return 0;
      common_vendor.index.__f__("log", "at pages/payment/index.vue:750", "calculateOrderPrice - 开始计算价格");
      const bookingTime = this.orderInfo.bookingTime;
      const endTime = this.orderInfo.endTime;
      const venueId = this.orderInfo.venueId;
      common_vendor.index.__f__("log", "at pages/payment/index.vue:757", "价格计算参数:", { bookingTime, endTime, venueId });
      if (bookingTime) {
        try {
          const startDateTime = new Date(bookingTime.replace(" ", "T"));
          let duration = 1;
          if (endTime) {
            const endDateTime = new Date(endTime.replace(" ", "T"));
            duration = (endDateTime - startDateTime) / (1e3 * 60 * 60);
          } else {
            duration = 2;
            common_vendor.index.__f__("log", "at pages/payment/index.vue:778", "没有结束时间，使用默认时长:", duration, "小时");
          }
          common_vendor.index.__f__("log", "at pages/payment/index.vue:781", "计算的时长:", duration, "小时");
          let hourlyRate = 120;
          if (this.orderInfo.venueName) {
            common_vendor.index.__f__("log", "at pages/payment/index.vue:789", "场馆名称:", this.orderInfo.venueName);
            hourlyRate = 120;
          }
          const calculatedPrice = duration * hourlyRate;
          common_vendor.index.__f__("log", "at pages/payment/index.vue:799", "计算的价格:", calculatedPrice);
          return calculatedPrice;
        } catch (error) {
          common_vendor.index.__f__("error", "at pages/payment/index.vue:802", "价格计算失败:", error);
          return 0;
        }
      }
      return 0;
    },
    // 获取预约类型文本
    getBookingTypeText() {
      if (!this.orderInfo)
        return "未知";
      if (this.orderInfo.isVirtualOrder) {
        return "拼场";
      }
      return this.orderInfo.bookingType === "SHARED" ? "拼场" : "独享";
    }
  }
};
if (!Array) {
  const _easycom_uni_popup2 = common_vendor.resolveComponent("uni-popup");
  _easycom_uni_popup2();
}
const _easycom_uni_popup = () => "../../uni_modules/uni-popup/components/uni-popup/uni-popup.js";
if (!Math) {
  _easycom_uni_popup();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.o((...args) => $options.goBack && $options.goBack(...args)),
    b: $data.loading
  }, $data.loading ? {} : $data.orderInfo ? common_vendor.e({
    d: common_vendor.t($data.orderInfo.orderNo),
    e: common_vendor.t($data.orderInfo.venueName),
    f: common_vendor.t($options.formatOrderDateTime()),
    g: common_vendor.t($options.getBookingTypeText()),
    h: $data.orderInfo.bookingType === "SHARED" || $data.orderInfo.isVirtualOrder
  }, $data.orderInfo.bookingType === "SHARED" || $data.orderInfo.isVirtualOrder ? {
    i: common_vendor.t($options.getTeamName())
  } : {}, {
    j: common_vendor.t($options.getContactInfo()),
    k: common_vendor.t($options.getOrderAmount()),
    l: $data.orderInfo.bookingType === "SHARED" || $data.orderInfo.isVirtualOrder
  }, $data.orderInfo.bookingType === "SHARED" || $data.orderInfo.isVirtualOrder ? {} : {}) : {}, {
    c: $data.orderInfo,
    m: $data.selectedMethod === "wechat"
  }, $data.selectedMethod === "wechat" ? {} : {}, {
    n: $data.selectedMethod === "wechat" ? 1 : "",
    o: common_vendor.o(($event) => $options.selectMethod("wechat")),
    p: $data.selectedMethod === "alipay"
  }, $data.selectedMethod === "alipay" ? {} : {}, {
    q: $data.selectedMethod === "alipay" ? 1 : "",
    r: common_vendor.o(($event) => $options.selectMethod("alipay")),
    s: common_vendor.t($options.getOrderAmount()),
    t: common_vendor.t($options.payButtonText),
    v: !$options.canPay ? 1 : "",
    w: !$options.canPay,
    x: common_vendor.o((...args) => $options.handlePayment && $options.handlePayment(...args)),
    y: $data.paymentResult.success
  }, $data.paymentResult.success ? {} : {}, {
    z: common_vendor.t($data.paymentResult.title),
    A: common_vendor.t($data.paymentResult.message),
    B: common_vendor.t($data.paymentResult.buttonText),
    C: common_vendor.o((...args) => $options.handleResultAction && $options.handleResultAction(...args)),
    D: common_vendor.sr("resultPopup", "7695f594-0"),
    E: common_vendor.p({
      type: "center",
      ["mask-click"]: false
    })
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-7695f594"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/payment/index.js.map
