
.container.data-v-dc96c85d {
  padding: 40rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}
.header.data-v-dc96c85d {
  text-align: center;
  margin-bottom: 60rpx;
}
.title.data-v-dc96c85d {
  font-size: 48rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 20rpx;
}
.subtitle.data-v-dc96c85d {
  font-size: 28rpx;
  color: #666;
  display: block;
}
.problem-section.data-v-dc96c85d, .solution-section.data-v-dc96c85d, .test-section.data-v-dc96c85d, .results-section.data-v-dc96c85d {
  background: white;
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}
.section-title.data-v-dc96c85d {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 30rpx;
}
.problem-card.data-v-dc96c85d, .solution-card.data-v-dc96c85d {
  background: #f8f9fa;
  border-radius: 15rpx;
  padding: 30rpx;
  border-left: 6rpx solid #ff6b6b;
}
.solution-card.data-v-dc96c85d {
  border-left-color: #4caf50;
}
.problem-title.data-v-dc96c85d, .solution-title.data-v-dc96c85d {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 15rpx;
}
.problem-desc.data-v-dc96c85d {
  font-size: 26rpx;
  color: #666;
  display: block;
  margin-bottom: 15rpx;
}
.code-line.data-v-dc96c85d {
  font-size: 24rpx;
  color: #555;
  font-family: 'Courier New', monospace;
  background: rgba(0,0,0,0.05);
  padding: 8rpx 15rpx;
  border-radius: 8rpx;
  display: block;
  margin-bottom: 10rpx;
}
.problem-result.data-v-dc96c85d {
  font-size: 26rpx;
  color: #ff6b6b;
  font-weight: bold;
  display: block;
  margin-top: 15rpx;
}
.test-btn.data-v-dc96c85d {
  width: 100%;
  height: 80rpx;
  color: white;
  border: none;
  border-radius: 40rpx;
  font-size: 28rpx;
  margin-bottom: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.test-btn.primary.data-v-dc96c85d {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}
.test-btn.info.data-v-dc96c85d {
  background: linear-gradient(135deg, #2196f3 0%, #1976d2 100%);
}
.test-btn.success.data-v-dc96c85d {
  background: linear-gradient(135deg, #4caf50 0%, #45a049 100%);
}
.test-btn.data-v-dc96c85d:disabled {
  background: #ccc !important;
}
.log-container.data-v-dc96c85d {
  max-height: 800rpx;
  overflow-y: auto;
}
.log-item.data-v-dc96c85d {
  padding: 20rpx;
  margin-bottom: 10rpx;
  border-radius: 10rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.log-item.info.data-v-dc96c85d {
  background-color: #e3f2fd;
  border-left: 4rpx solid #2196f3;
}
.log-item.success.data-v-dc96c85d {
  background-color: #e8f5e8;
  border-left: 4rpx solid #4caf50;
}
.log-item.error.data-v-dc96c85d {
  background-color: #ffebee;
  border-left: 4rpx solid #f44336;
}
.log-item.warning.data-v-dc96c85d {
  background-color: #fff3e0;
  border-left: 4rpx solid #ff9800;
}
.log-text.data-v-dc96c85d {
  font-size: 26rpx;
  flex: 1;
}
.log-time.data-v-dc96c85d {
  font-size: 22rpx;
  color: #999;
  margin-left: 20rpx;
}
