
.container.data-v-d873979e {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;
}
.header.data-v-d873979e {
  text-align: center;
  margin-bottom: 30px;
}
.title.data-v-d873979e {
  font-size: 24px;
  font-weight: bold;
  color: #333;
}
.test-section.data-v-d873979e {
  background-color: white;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}
.section-title.data-v-d873979e {
  font-size: 18px;
  font-weight: bold;
  color: #333;
  margin-bottom: 15px;
  border-bottom: 2px solid #007AFF;
  padding-bottom: 5px;
}
.test-item.data-v-d873979e {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}
.test-label.data-v-d873979e {
  width: 80px;
  font-size: 14px;
  color: #666;
}
.test-input.data-v-d873979e {
  flex: 1;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}
.test-button.data-v-d873979e {
  background-color: #007AFF;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 12px 24px;
  font-size: 16px;
  margin-bottom: 15px;
}
.test-button.secondary.data-v-d873979e {
  background-color: #666;
}
.test-result.data-v-d873979e {
  background-color: #f8f9fa;
  border-radius: 6px;
  padding: 15px;
  border-left: 4px solid #007AFF;
}
.result-title.data-v-d873979e {
  font-weight: bold;
  color: #333;
  margin-bottom: 10px;
  display: block;
}
.result-item.data-v-d873979e {
  margin-bottom: 8px;
}
.result-item text.data-v-d873979e {
  font-size: 14px;
  color: #555;
}
.test-logs.data-v-d873979e {
  max-height: 300px;
  overflow-y: auto;
  background-color: #f8f9fa;
  border-radius: 6px;
  padding: 15px;
}
.log-item.data-v-d873979e {
  margin-bottom: 5px;
  font-size: 12px;
  color: #666;
  font-family: monospace;
}
