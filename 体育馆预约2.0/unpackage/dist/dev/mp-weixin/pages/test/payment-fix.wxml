<view class="test-container data-v-b512490a"><view class="test-header data-v-b512490a"><text class="test-title data-v-b512490a">支付问题修复测试</text></view><view class="test-section data-v-b512490a"><view class="section-title data-v-b512490a">测试1: 订单金额显示修复</view><view class="test-item data-v-b512490a"><text class="test-label data-v-b512490a">测试订单ID:</text><input placeholder="输入订单ID" class="test-input data-v-b512490a" value="{{a}}" bindinput="{{b}}"/></view><button bindtap="{{c}}" class="test-button data-v-b512490a">测试订单金额计算</button><view wx:if="{{d}}" class="test-result data-v-b512490a"><text class="result-title data-v-b512490a">测试结果:</text><view class="result-item data-v-b512490a"><text class="data-v-b512490a">原始价格: ¥{{e}}</text></view><view class="result-item data-v-b512490a"><text class="data-v-b512490a">计算价格: ¥{{f}}</text></view><view class="result-item data-v-b512490a"><text class="data-v-b512490a">计算方法: {{g}}</text></view><view class="result-item data-v-b512490a"><text class="data-v-b512490a">建议: {{h}}</text></view></view></view><view class="test-section data-v-b512490a"><view class="section-title data-v-b512490a">测试2: 时间段刷新修复</view><view class="test-item data-v-b512490a"><text class="test-label data-v-b512490a">场馆ID:</text><input placeholder="输入场馆ID" class="test-input data-v-b512490a" value="{{i}}" bindinput="{{j}}"/></view><view class="test-item data-v-b512490a"><text class="test-label data-v-b512490a">日期:</text><input placeholder="YYYY-MM-DD" class="test-input data-v-b512490a" value="{{k}}" bindinput="{{l}}"/></view><button bindtap="{{m}}" class="test-button data-v-b512490a">测试时间段刷新</button><view wx:if="{{n}}" class="test-result data-v-b512490a"><text class="result-title data-v-b512490a">刷新前状态:</text><view class="result-item data-v-b512490a"><text class="data-v-b512490a">时间段数量: {{o}}</text></view><text class="result-title data-v-b512490a">刷新后状态:</text><view class="result-item data-v-b512490a"><text class="data-v-b512490a">时间段数量: {{p}}</text></view><view class="result-item data-v-b512490a"><text class="data-v-b512490a">刷新成功: {{q}}</text></view></view></view><view class="test-section data-v-b512490a"><view class="section-title data-v-b512490a">测试3: 价格传递调试</view><view class="test-item data-v-b512490a"><text class="test-label data-v-b512490a">测试价格:</text><input placeholder="输入价格" class="test-input data-v-b512490a" value="{{r}}" bindinput="{{s}}"/></view><button bindtap="{{t}}" class="test-button data-v-b512490a">测试价格传递</button><view wx:if="{{v}}" class="test-result data-v-b512490a"><text class="result-title data-v-b512490a">价格传递测试结果:</text><view class="result-item data-v-b512490a"><text class="data-v-b512490a">成功: {{w}}</text></view><view class="result-item data-v-b512490a"><text class="data-v-b512490a">原始价格: {{x}}</text></view><view class="result-item data-v-b512490a"><text class="data-v-b512490a">最终价格: {{y}}</text></view><view class="result-item data-v-b512490a"><text class="data-v-b512490a">价格保持: {{z}}</text></view><view wx:if="{{A}}" class="result-item data-v-b512490a"><text class="data-v-b512490a">问题: {{B}}</text></view></view></view><view class="test-section data-v-b512490a"><view class="section-title data-v-b512490a">测试4: 深度问题验证</view><button bindtap="{{C}}" class="test-button data-v-b512490a">深度验证修复效果</button><view wx:if="{{D}}" class="test-result data-v-b512490a"><text class="result-title data-v-b512490a">深度验证结果:</text><view class="result-item data-v-b512490a"><text class="data-v-b512490a">总体成功: {{E}}</text></view><view class="result-item data-v-b512490a"><text class="data-v-b512490a">总结: {{F}}</text></view><view wx:if="{{G}}" class="result-item data-v-b512490a"><text class="data-v-b512490a">价格传递: {{H}}</text></view><view wx:if="{{I}}" class="result-item data-v-b512490a"><text class="data-v-b512490a">时间段刷新: {{J}}</text></view></view></view><view class="test-section data-v-b512490a"><view class="section-title data-v-b512490a">测试5: 创建预约流程测试</view><button bindtap="{{K}}" class="test-button data-v-b512490a">测试完整预约流程</button><view wx:if="{{L}}" class="test-result data-v-b512490a"><text class="result-title data-v-b512490a">流程测试结果:</text><view wx:for="{{M}}" wx:for-item="step" wx:key="d" class="result-item data-v-b512490a"><text class="data-v-b512490a">{{step.a}}: {{step.b}} {{step.c}}</text></view></view></view><view class="test-section data-v-b512490a"><view class="section-title data-v-b512490a">测试日志</view><view class="log-container data-v-b512490a"><text wx:for="{{N}}" wx:for-item="log" wx:key="b" class="log-item data-v-b512490a">{{log.a}}</text></view></view></view>