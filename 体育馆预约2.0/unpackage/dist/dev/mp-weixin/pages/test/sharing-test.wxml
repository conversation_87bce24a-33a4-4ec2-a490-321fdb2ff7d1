<view class="container data-v-d873979e"><view class="header data-v-d873979e"><text class="title data-v-d873979e">拼场功能测试</text></view><view class="test-section data-v-d873979e"><view class="section-title data-v-d873979e">测试参数</view><view class="test-item data-v-d873979e"><text class="test-label data-v-d873979e">场馆ID:</text><input placeholder="输入场馆ID" class="test-input data-v-d873979e" value="{{a}}" bindinput="{{b}}"/></view><view class="test-item data-v-d873979e"><text class="test-label data-v-d873979e">测试日期:</text><input placeholder="YYYY-MM-DD" class="test-input data-v-d873979e" value="{{c}}" bindinput="{{d}}"/></view><view class="test-item data-v-d873979e"><text class="test-label data-v-d873979e">队伍名称:</text><input placeholder="输入队伍名称" class="test-input data-v-d873979e" value="{{e}}" bindinput="{{f}}"/></view><view class="test-item data-v-d873979e"><text class="test-label data-v-d873979e">联系方式:</text><input placeholder="输入联系方式" class="test-input data-v-d873979e" value="{{g}}" bindinput="{{h}}"/></view></view><view class="test-section data-v-d873979e"><view class="section-title data-v-d873979e">测试1: 拼场价格计算诊断</view><button bindtap="{{i}}" class="test-button data-v-d873979e">测试价格计算</button><view wx:if="{{j}}" class="test-result data-v-d873979e"><text class="result-title data-v-d873979e">价格计算结果:</text><view class="result-item data-v-d873979e"><text class="data-v-d873979e">总原价: ¥{{k}}</text></view><view class="result-item data-v-d873979e"><text class="data-v-d873979e">每队价格: ¥{{l}}</text></view><view class="result-item data-v-d873979e"><text class="data-v-d873979e">优惠金额: ¥{{m}}</text></view><view wx:if="{{n}}" class="result-item data-v-d873979e"><text class="data-v-d873979e">问题: {{o}}</text></view></view></view><view class="test-section data-v-d873979e"><view class="section-title data-v-d873979e">测试2: 拼场数据结构诊断</view><button bindtap="{{p}}" class="test-button data-v-d873979e">测试数据结构</button><view wx:if="{{q}}" class="test-result data-v-d873979e"><text class="result-title data-v-d873979e">数据结构检查:</text><view wx:for="{{r}}" wx:for-item="field" wx:key="c" class="result-item data-v-d873979e"><text class="data-v-d873979e">{{field.a}}: {{field.b}}</text></view><view wx:if="{{s}}" class="result-item data-v-d873979e"><text class="data-v-d873979e">问题: {{t}}</text></view></view></view><view class="test-section data-v-d873979e"><view class="section-title data-v-d873979e">测试3: 综合拼场诊断</view><button bindtap="{{v}}" class="test-button data-v-d873979e">综合诊断</button><view wx:if="{{w}}" class="test-result data-v-d873979e"><text class="result-title data-v-d873979e">综合诊断结果:</text><view class="result-item data-v-d873979e"><text class="data-v-d873979e">总体状态: {{x}}</text></view><view class="result-item data-v-d873979e"><text class="data-v-d873979e">总结: {{y}}</text></view><view class="result-item data-v-d873979e"><text class="data-v-d873979e">发现问题: {{z}}个</text></view><view class="result-item data-v-d873979e"><text class="data-v-d873979e">修复建议: {{A}}个</text></view></view></view><view class="test-section data-v-d873979e"><view class="section-title data-v-d873979e">测试4: 拼场快速修复</view><button bindtap="{{B}}" class="test-button data-v-d873979e">快速修复</button><view wx:if="{{C}}" class="test-result data-v-d873979e"><text class="result-title data-v-d873979e">快速修复结果:</text><view class="result-item data-v-d873979e"><text class="data-v-d873979e">修复成功: {{D}}</text></view><view class="result-item data-v-d873979e"><text class="data-v-d873979e">应用修复: {{E}}个</text></view><view wx:if="{{F}}" class="result-item data-v-d873979e"><text class="data-v-d873979e">修复后价格: ¥{{G}}</text></view></view></view><view class="test-section data-v-d873979e"><view class="section-title data-v-d873979e">测试5: 模拟拼场创建</view><button bindtap="{{H}}" class="test-button data-v-d873979e">模拟创建拼场</button><view wx:if="{{I}}" class="test-result data-v-d873979e"><text class="result-title data-v-d873979e">创建结果:</text><view class="result-item data-v-d873979e"><text class="data-v-d873979e">成功: {{J}}</text></view><view wx:if="{{K}}" class="result-item data-v-d873979e"><text class="data-v-d873979e">订单ID: {{L}}</text></view><view wx:if="{{M}}" class="result-item data-v-d873979e"><text class="data-v-d873979e">错误: {{N}}</text></view></view></view><view class="test-section data-v-d873979e"><view class="section-title data-v-d873979e">测试日志</view><button bindtap="{{O}}" class="test-button secondary data-v-d873979e">清除日志</button><view class="test-logs data-v-d873979e"><view wx:for="{{P}}" wx:for-item="log" wx:key="b" class="log-item data-v-d873979e"><text class="data-v-d873979e">{{log.a}}</text></view></view></view></view>