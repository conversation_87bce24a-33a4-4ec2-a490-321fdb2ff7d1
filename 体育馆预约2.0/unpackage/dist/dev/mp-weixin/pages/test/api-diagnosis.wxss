
.container.data-v-b05c72fc {
  padding: 40rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}
.header.data-v-b05c72fc {
  text-align: center;
  margin-bottom: 60rpx;
}
.title.data-v-b05c72fc {
  font-size: 48rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 20rpx;
}
.subtitle.data-v-b05c72fc {
  font-size: 28rpx;
  color: #666;
  display: block;
}
.test-section.data-v-b05c72fc {
  background: white;
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}
.section-title.data-v-b05c72fc {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 30rpx;
}
.test-btn.data-v-b05c72fc {
  width: 100%;
  height: 80rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 40rpx;
  font-size: 28rpx;
  margin-bottom: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.test-btn.data-v-b05c72fc:disabled {
  background: #ccc;
}
.results-section.data-v-b05c72fc {
  background: white;
  border-radius: 20rpx;
  padding: 40rpx;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}
.log-container.data-v-b05c72fc {
  max-height: 800rpx;
  overflow-y: auto;
}
.log-item.data-v-b05c72fc {
  padding: 20rpx;
  margin-bottom: 10rpx;
  border-radius: 10rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.log-item.info.data-v-b05c72fc {
  background-color: #e3f2fd;
  border-left: 4rpx solid #2196f3;
}
.log-item.success.data-v-b05c72fc {
  background-color: #e8f5e8;
  border-left: 4rpx solid #4caf50;
}
.log-item.error.data-v-b05c72fc {
  background-color: #ffebee;
  border-left: 4rpx solid #f44336;
}
.log-text.data-v-b05c72fc {
  font-size: 26rpx;
  flex: 1;
}
.log-time.data-v-b05c72fc {
  font-size: 22rpx;
  color: #999;
  margin-left: 20rpx;
}
