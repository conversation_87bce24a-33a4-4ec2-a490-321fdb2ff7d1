
.test-container.data-v-b512490a {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}
.test-header.data-v-b512490a {
  text-align: center;
  margin-bottom: 40rpx;
}
.test-title.data-v-b512490a {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}
.test-section.data-v-b512490a {
  background-color: white;
  margin-bottom: 30rpx;
  padding: 30rpx;
  border-radius: 10rpx;
}
.section-title.data-v-b512490a {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}
.test-item.data-v-b512490a {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}
.test-label.data-v-b512490a {
  width: 200rpx;
  font-size: 28rpx;
  color: #666;
}
.test-input.data-v-b512490a {
  flex: 1;
  padding: 20rpx;
  border: 1px solid #ddd;
  border-radius: 5rpx;
  font-size: 28rpx;
}
.test-button.data-v-b512490a {
  background-color: #007aff;
  color: white;
  padding: 20rpx 40rpx;
  border-radius: 5rpx;
  font-size: 28rpx;
  margin: 20rpx 0;
}
.test-result.data-v-b512490a {
  margin-top: 20rpx;
  padding: 20rpx;
  background-color: #f8f8f8;
  border-radius: 5rpx;
}
.result-title.data-v-b512490a {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}
.result-item.data-v-b512490a {
  margin-bottom: 10rpx;
}
.result-item text.data-v-b512490a {
  font-size: 26rpx;
  color: #666;
}
.log-container.data-v-b512490a {
  max-height: 400rpx;
  overflow-y: auto;
  background-color: #f8f8f8;
  padding: 20rpx;
  border-radius: 5rpx;
}
.log-item.data-v-b512490a {
  display: block;
  font-size: 24rpx;
  color: #666;
  margin-bottom: 10rpx;
  font-family: monospace;
}
