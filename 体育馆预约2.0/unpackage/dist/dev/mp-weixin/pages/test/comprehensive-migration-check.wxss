
.container.data-v-aeba93fb {
  padding: 40rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}
.header.data-v-aeba93fb {
  text-align: center;
  margin-bottom: 60rpx;
}
.title.data-v-aeba93fb {
  font-size: 48rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 20rpx;
}
.subtitle.data-v-aeba93fb {
  font-size: 28rpx;
  color: #666;
  display: block;
}
.category-section.data-v-aeba93fb {
  background: white;
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}
.section-title.data-v-aeba93fb {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 30rpx;
}
.category-grid.data-v-aeba93fb {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
  margin-bottom: 40rpx;
}
.category-btn.data-v-aeba93fb {
  height: 80rpx;
  border: none;
  border-radius: 40rpx;
  font-size: 26rpx;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
}
.category-btn.a-class.data-v-aeba93fb { background: linear-gradient(135deg, #ff6b6b, #ee5a52);
}
.category-btn.b-class.data-v-aeba93fb { background: linear-gradient(135deg, #4ecdc4, #44a08d);
}
.category-btn.c-class.data-v-aeba93fb { background: linear-gradient(135deg, #45b7d1, #96c93d);
}
.category-btn.d-class.data-v-aeba93fb { background: linear-gradient(135deg, #f093fb, #f5576c);
}
.category-btn.e-class.data-v-aeba93fb { background: linear-gradient(135deg, #4facfe, #00f2fe);
}
.category-btn.f-class.data-v-aeba93fb { background: linear-gradient(135deg, #43e97b, #38f9d7);
}
.full-check-btn.data-v-aeba93fb {
  width: 100%;
  height: 100rpx;
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  border: none;
  border-radius: 50rpx;
  font-size: 32rpx;
  font-weight: bold;
}
.progress-section.data-v-aeba93fb {
  background: white;
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}
.progress-bar.data-v-aeba93fb {
  width: 100%;
  height: 20rpx;
  background-color: #f0f0f0;
  border-radius: 10rpx;
  overflow: hidden;
  margin-bottom: 20rpx;
}
.progress-fill.data-v-aeba93fb {
  height: 100%;
  background: linear-gradient(90deg, #4facfe, #00f2fe);
  transition: width 0.3s ease;
}
.progress-text.data-v-aeba93fb {
  font-size: 28rpx;
  color: #666;
  text-align: center;
  display: block;
}
.stats-section.data-v-aeba93fb {
  background: white;
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}
.stats-grid.data-v-aeba93fb {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20rpx;
}
.stat-item.data-v-aeba93fb {
  text-align: center;
  padding: 30rpx;
  border-radius: 15rpx;
}
.stat-item.success.data-v-aeba93fb { background-color: #e8f5e8;
}
.stat-item.warning.data-v-aeba93fb { background-color: #fff3e0;
}
.stat-item.error.data-v-aeba93fb { background-color: #ffebee;
}
.stat-item.info.data-v-aeba93fb { background-color: #e3f2fd;
}
.stat-number.data-v-aeba93fb {
  font-size: 48rpx;
  font-weight: bold;
  display: block;
  margin-bottom: 10rpx;
}
.stat-item.success .stat-number.data-v-aeba93fb { color: #4caf50;
}
.stat-item.warning .stat-number.data-v-aeba93fb { color: #ff9800;
}
.stat-item.error .stat-number.data-v-aeba93fb { color: #f44336;
}
.stat-item.info .stat-number.data-v-aeba93fb { color: #2196f3;
}
.stat-label.data-v-aeba93fb {
  font-size: 24rpx;
  color: #666;
  display: block;
}
.results-section.data-v-aeba93fb {
  background: white;
  border-radius: 20rpx;
  padding: 40rpx;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}
.results-container.data-v-aeba93fb {
  max-height: 1000rpx;
  overflow-y: auto;
}
.result-item.data-v-aeba93fb {
  padding: 30rpx;
  margin-bottom: 20rpx;
  border-radius: 15rpx;
  border-left: 6rpx solid;
}
.result-item.success.data-v-aeba93fb {
  background-color: #e8f5e8;
  border-left-color: #4caf50;
}
.result-item.warning.data-v-aeba93fb {
  background-color: #fff3e0;
  border-left-color: #ff9800;
}
.result-item.error.data-v-aeba93fb {
  background-color: #ffebee;
  border-left-color: #f44336;
}
.result-header.data-v-aeba93fb {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15rpx;
}
.result-title.data-v-aeba93fb {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}
.result-status.data-v-aeba93fb {
  font-size: 24rpx;
  font-weight: bold;
}
.result-status.success.data-v-aeba93fb { color: #4caf50;
}
.result-status.warning.data-v-aeba93fb { color: #ff9800;
}
.result-status.error.data-v-aeba93fb { color: #f44336;
}
.result-description.data-v-aeba93fb {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
  display: block;
  margin-bottom: 10rpx;
}
.result-suggestion.data-v-aeba93fb {
  font-size: 24rpx;
  color: #2196f3;
  line-height: 1.4;
  display: block;
  font-style: italic;
}
