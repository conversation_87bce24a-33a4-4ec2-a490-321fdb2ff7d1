"use strict";
const common_vendor = require("../../common/vendor.js");
const stores_user = require("../../stores/user.js");
const stores_booking = require("../../stores/booking.js");
const stores_sharing = require("../../stores/sharing.js");
const _sfc_main = {
  name: "UserProfile",
  data() {
    return {
      userStore: null,
      bookingStore: null,
      sharingStore: null,
      pendingBookings: 0,
      pendingSharings: 0,
      pendingRequests: 0,
      receivedRequests: 0
    };
  },
  computed: {
    userInfo() {
      var _a;
      return ((_a = this.userStore) == null ? void 0 : _a.userInfoGetter) || {};
    },
    userStats() {
      var _a;
      return ((_a = this.userStore) == null ? void 0 : _a.userStats) || {};
    },
    isLoggedIn() {
      var _a;
      return ((_a = this.userStore) == null ? void 0 : _a.getIsLoggedIn) || false;
    }
  },
  onLoad() {
    this.userStore = stores_user.useUserStore();
    this.bookingStore = stores_booking.useBookingStore();
    this.sharingStore = stores_sharing.useSharingStore();
    this.loadUserData();
  },
  onShow() {
    this.loadUserData();
  },
  methods: {
    // 加载用户数据
    async loadUserData() {
      try {
        common_vendor.index.__f__("log", "at pages/user/profile.vue:198", "[Profile] 开始加载用户数据");
        await this.userStore.getUserInfo();
        common_vendor.index.__f__("log", "at pages/user/profile.vue:201", "[Profile] 用户信息:", this.userInfo);
        try {
          await this.userStore.getUserStats();
        } catch (statsError) {
          common_vendor.index.__f__("error", "at pages/user/profile.vue:207", "获取用户统计失败:", statsError);
          this.userStats = {
            totalBookings: 0,
            pendingBookings: 0,
            completedBookings: 0,
            totalSpent: 0
          };
        }
        await this.loadPendingCounts();
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/user/profile.vue:221", "加载用户数据失败:", error);
        common_vendor.index.showToast({
          title: "加载用户信息失败",
          icon: "none"
        });
      }
    },
    // 加载待处理数量
    async loadPendingCounts() {
      try {
        const bookingResult = await this.bookingStore.getUserBookings({
          status: "pending",
          page: 1,
          pageSize: 1
        });
        this.pendingBookings = bookingResult.total || 0;
        const sharingResult = await this.bookingStore.getUserSharingOrders({
          status: "pending",
          page: 1,
          pageSize: 1
        });
        if (Array.isArray(sharingResult.data)) {
          this.pendingSharings = sharingResult.data.length;
        } else {
          this.pendingSharings = sharingResult.total || 0;
        }
        const myRequestsResult = await this.sharingStore.getMySharingRequests();
        if (Array.isArray(myRequestsResult)) {
          this.pendingRequests = myRequestsResult.filter((req) => req.status === "pending").length;
        } else {
          this.pendingRequests = 0;
        }
        const receivedRequestsResult = await this.sharingStore.getReceivedSharingRequests();
        if (Array.isArray(receivedRequestsResult)) {
          this.receivedRequests = receivedRequestsResult.filter((req) => req.status === "pending").length;
        } else {
          this.receivedRequests = 0;
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/user/profile.vue:270", "加载待处理数量失败:", error);
        this.pendingBookings = 0;
        this.pendingSharings = 0;
        this.pendingRequests = 0;
        this.receivedRequests = 0;
      }
    },
    // 格式化手机号
    formatPhone(phone) {
      if (!phone)
        return "";
      return phone.replace(/(\d{3})(\d{4})(\d{4})/, "$1****$3");
    },
    // 更换头像
    changeAvatar() {
      common_vendor.index.chooseImage({
        count: 1,
        sizeType: ["compressed"],
        sourceType: ["album", "camera"],
        success: (res) => {
          const tempFilePath = res.tempFilePaths[0];
          this.uploadAvatar(tempFilePath);
        }
      });
    },
    // 上传头像
    async uploadAvatar(filePath) {
      try {
        common_vendor.index.showLoading({ title: "上传中..." });
        common_vendor.index.hideLoading();
        common_vendor.index.showToast({
          title: "头像更新成功",
          icon: "success"
        });
        await this.userStore.getUserInfo();
      } catch (error) {
        common_vendor.index.hideLoading();
        common_vendor.index.__f__("error", "at pages/user/profile.vue:317", "上传头像失败:", error);
        common_vendor.index.showToast({
          title: "上传失败",
          icon: "error"
        });
      }
    },
    // 编辑资料
    editProfile() {
      if (!this.userInfo) {
        this.loadUserData();
      }
      common_vendor.index.navigateTo({
        url: "/pages/user/edit-profile"
      });
    },
    // 页面跳转
    navigateTo(url) {
      const tabbarPages = [
        "/pages/index/index",
        "/pages/venue/list",
        "/pages/sharing/list",
        "/pages/booking/list",
        "/pages/user/profile"
      ];
      const pagePath = url.split("?")[0];
      if (tabbarPages.includes(pagePath)) {
        common_vendor.index.switchTab({ url: pagePath });
      } else {
        common_vendor.index.navigateTo({ url });
      }
    },
    // 显示退出登录确认
    showLogoutConfirm() {
      this.$refs.logoutPopup.open();
    },
    // 取消退出登录
    handleLogoutCancel() {
      this.$refs.logoutPopup.close();
    },
    // 确认退出登录
    async handleLogoutConfirm() {
      try {
        await this.userStore.logout();
        this.$refs.logoutPopup.close();
        common_vendor.index.showToast({
          title: "已退出登录",
          icon: "success"
        });
        setTimeout(() => {
          common_vendor.index.reLaunch({
            url: "/pages/user/login"
          });
        }, 1e3);
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/user/profile.vue:389", "退出登录失败:", error);
        common_vendor.index.showToast({
          title: "退出失败",
          icon: "error"
        });
      }
    }
  }
};
if (!Array) {
  const _easycom_uni_popup_dialog2 = common_vendor.resolveComponent("uni-popup-dialog");
  const _easycom_uni_popup2 = common_vendor.resolveComponent("uni-popup");
  (_easycom_uni_popup_dialog2 + _easycom_uni_popup2)();
}
const _easycom_uni_popup_dialog = () => "../../uni_modules/uni-popup/components/uni-popup-dialog/uni-popup-dialog.js";
const _easycom_uni_popup = () => "../../uni_modules/uni-popup/components/uni-popup/uni-popup.js";
if (!Math) {
  (_easycom_uni_popup_dialog + _easycom_uni_popup)();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  var _a, _b, _c, _d, _e;
  return common_vendor.e({
    a: ((_a = $options.userInfo) == null ? void 0 : _a.avatar) || "/static/images/default-avatar.svg",
    b: common_vendor.o((...args) => $options.changeAvatar && $options.changeAvatar(...args)),
    c: common_vendor.t(((_b = $options.userInfo) == null ? void 0 : _b.nickname) || ((_c = $options.userInfo) == null ? void 0 : _c.username) || "未设置昵称"),
    d: common_vendor.t(((_d = $options.userInfo) == null ? void 0 : _d.username) || "未设置用户名"),
    e: common_vendor.t($options.formatPhone(((_e = $options.userInfo) == null ? void 0 : _e.phone) || "")),
    f: common_vendor.o((...args) => $options.editProfile && $options.editProfile(...args)),
    g: common_vendor.t($options.userStats.totalBookings || 0),
    h: common_vendor.o(($event) => $options.navigateTo("/pages/booking/list")),
    i: common_vendor.t($options.userStats.totalSharings || 0),
    j: common_vendor.o(($event) => $options.navigateTo("/pages/sharing/list?tab=my")),
    k: $data.pendingBookings > 0
  }, $data.pendingBookings > 0 ? {
    l: common_vendor.t($data.pendingBookings)
  } : {}, {
    m: common_vendor.o(($event) => $options.navigateTo("/pages/booking/list")),
    n: $data.pendingSharings > 0
  }, $data.pendingSharings > 0 ? {
    o: common_vendor.t($data.pendingSharings)
  } : {}, {
    p: common_vendor.o(($event) => $options.navigateTo("/pages/sharing/list?tab=my")),
    q: $data.pendingRequests > 0
  }, $data.pendingRequests > 0 ? {
    r: common_vendor.t($data.pendingRequests)
  } : {}, {
    s: common_vendor.o(($event) => $options.navigateTo("/pages/sharing/requests")),
    t: $data.receivedRequests > 0
  }, $data.receivedRequests > 0 ? {
    v: common_vendor.t($data.receivedRequests)
  } : {}, {
    w: common_vendor.o(($event) => $options.navigateTo("/pages/sharing/received")),
    x: common_vendor.o(($event) => $options.navigateTo("/pages/test/index")),
    y: common_vendor.o((...args) => $options.showLogoutConfirm && $options.showLogoutConfirm(...args)),
    z: common_vendor.o($options.handleLogoutCancel),
    A: common_vendor.o($options.handleLogoutConfirm),
    B: common_vendor.p({
      type: "warn",
      title: "确认退出",
      content: "确定要退出登录吗？",
      ["before-close"]: true
    }),
    C: common_vendor.sr("logoutPopup", "036958a5-0"),
    D: common_vendor.p({
      type: "dialog"
    })
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-036958a5"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/user/profile.js.map
