"use strict";
const common_vendor = require("../../common/vendor.js");
const stores_venue = require("../../stores/venue.js");
const stores_booking = require("../../stores/booking.js");
const stores_user = require("../../stores/user.js");
const utils_bookingPriceValidator = require("../../utils/booking-price-validator.js");
const utils_realTimeDebugger = require("../../utils/real-time-debugger.js");
const utils_priceTransmissionTracer = require("../../utils/price-transmission-tracer.js");
require("../../utils/request.js");
const _sfc_main = {
  name: "BookingCreate",
  data() {
    return {
      venueStore: null,
      bookingStore: null,
      userStore: null,
      venueId: null,
      selectedDate: "",
      selectedSlot: null,
      selectedSlots: [],
      // 存储多个选中的时间段
      bookingForm: {
        bookingType: "EXCLUSIVE",
        teamName: "",
        contactInfo: "",
        description: ""
      }
    };
  },
  computed: {
    timeSlots() {
      var _a;
      return ((_a = this.venueStore) == null ? void 0 : _a.timeSlots) || [];
    },
    userInfo() {
      var _a;
      return ((_a = this.userStore) == null ? void 0 : _a.getUserInfo) || {};
    },
    // 获取场馆信息，确保有默认值
    venue() {
      var _a, _b;
      const venueData = ((_a = this.venueStore) == null ? void 0 : _a.venueDetailGetter) || ((_b = this.venueStore) == null ? void 0 : _b.venueDetail);
      common_vendor.index.__f__("log", "at pages/booking/create.vue:244", "venue计算属性 - 原始数据:", venueData);
      common_vendor.index.__f__("log", "at pages/booking/create.vue:245", "venue计算属性 - venueStore:", this.venueStore);
      if (!venueData) {
        common_vendor.index.__f__("log", "at pages/booking/create.vue:248", "venue计算属性 - 无数据");
        return null;
      }
      const result = {
        ...venueData,
        supportSharing: venueData.supportSharing !== void 0 ? venueData.supportSharing : true,
        price: venueData.price || 0
      };
      common_vendor.index.__f__("log", "at pages/booking/create.vue:258", "venue计算属性 - 处理后数据:", result);
      common_vendor.index.__f__("log", "at pages/booking/create.vue:259", "venue计算属性 - 价格:", result.price);
      return result;
    },
    totalCost() {
      var _a, _b;
      if (this.selectedSlots && this.selectedSlots.length > 0) {
        const total = this.selectedSlots.reduce((sum, slot) => {
          var _a2;
          let slotPrice = 0;
          if (slot.price) {
            slotPrice = parseFloat(slot.price);
          } else if (slot.pricePerHour) {
            slotPrice = parseFloat(slot.pricePerHour);
          } else {
            const venuePrice2 = ((_a2 = this.venue) == null ? void 0 : _a2.price) || 0;
            slotPrice = parseFloat(venuePrice2) || 0;
          }
          common_vendor.index.__f__("log", "at pages/booking/create.vue:280", "时间段价格:", slot, slotPrice);
          return sum + slotPrice;
        }, 0);
        common_vendor.index.__f__("log", "at pages/booking/create.vue:284", "totalCost - 多时间段总价格:", total);
        return total;
      }
      if (this.selectedSlot && this.selectedSlot.price) {
        common_vendor.index.__f__("log", "at pages/booking/create.vue:290", "totalCost - 使用时间段价格:", this.selectedSlot.price);
        return parseFloat(this.selectedSlot.price);
      }
      if (this.selectedSlot && this.selectedSlot.pricePerHour) {
        common_vendor.index.__f__("log", "at pages/booking/create.vue:296", "totalCost - 使用时间段每小时价格:", this.selectedSlot.pricePerHour);
        return parseFloat(this.selectedSlot.pricePerHour);
      }
      const venuePrice = ((_a = this.venue) == null ? void 0 : _a.pricePerHour) || ((_b = this.venue) == null ? void 0 : _b.price) || 0;
      common_vendor.index.__f__("log", "at pages/booking/create.vue:301", "totalCost - 使用场馆价格:", venuePrice);
      return parseFloat(venuePrice) || 0;
    },
    canConfirm() {
      var _a, _b, _c, _d, _e;
      const hasDate = !!this.selectedDate;
      const hasSlot = !!(((_a = this.selectedSlots) == null ? void 0 : _a.length) > 0 || this.selectedSlot);
      const hasVenue = !!((_b = this.venue) == null ? void 0 : _b.id);
      const hasPrice = !!((_c = this.venue) == null ? void 0 : _c.price);
      const baseValid = hasDate && hasSlot && hasVenue && hasPrice;
      common_vendor.index.__f__("log", "at pages/booking/create.vue:316", "canConfirm 详细检查:", {
        selectedDate: this.selectedDate,
        hasDate,
        selectedSlot: this.selectedSlot,
        hasSlot,
        venue: this.venue,
        hasVenue,
        hasPrice,
        venuePrice: (_d = this.venue) == null ? void 0 : _d.price,
        venuePricePerHour: (_e = this.venue) == null ? void 0 : _e.price,
        bookingType: this.bookingForm.bookingType,
        teamName: this.bookingForm.teamName,
        contactInfo: this.bookingForm.contactInfo,
        baseValid
      });
      if (this.bookingForm.bookingType === "SHARED") {
        const hasTeamName = !!(this.bookingForm.teamName && this.bookingForm.teamName.trim());
        const hasContactInfo = !!(this.bookingForm.contactInfo && this.bookingForm.contactInfo.trim());
        const result = baseValid && hasTeamName && hasContactInfo;
        common_vendor.index.__f__("log", "at pages/booking/create.vue:337", "拼场模式额外检查:", {
          hasTeamName,
          hasContactInfo,
          finalResult: result
        });
        return result;
      }
      common_vendor.index.__f__("log", "at pages/booking/create.vue:345", "独占模式 canConfirm 结果:", baseValid);
      return baseValid;
    }
  },
  onLoad(options) {
    this.venueStore = stores_venue.useVenueStore();
    this.bookingStore = stores_booking.useBookingStore();
    this.userStore = stores_user.useUserStore();
    common_vendor.index.__f__("log", "at pages/booking/create.vue:356", "页面加载参数:", options);
    this.venueId = options.venueId;
    this.selectedDate = options.date;
    if (!this.venueId) {
      common_vendor.index.__f__("error", "at pages/booking/create.vue:362", "警告: venueId为空!");
      common_vendor.index.showToast({
        title: "场馆ID缺失，请返回重试",
        icon: "none"
      });
      setTimeout(() => {
        common_vendor.index.navigateBack();
      }, 2e3);
      return;
    }
    if (options.bookingType) {
      this.bookingForm.bookingType = options.bookingType;
    }
    if (options.selectedSlots) {
      try {
        this.selectedSlots = JSON.parse(decodeURIComponent(options.selectedSlots));
        this.selectedSlot = this.selectedSlots[0];
        common_vendor.index.__f__("log", "at pages/booking/create.vue:383", "接收到的时间段数据:", this.selectedSlots);
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/booking/create.vue:385", "解析时间段数据失败:", error);
      }
      this.loadVenueDetail();
    } else if (options.slotId) {
      this.loadVenueAndSlot(options.slotId);
    } else {
      this.loadVenueDetail();
    }
  },
  methods: {
    // 加载场馆详情
    async loadVenueDetail() {
      try {
        common_vendor.index.__f__("log", "at pages/booking/create.vue:401", "开始加载场馆详情，venueId:", this.venueId);
        await this.venueStore.getVenueDetail(this.venueId);
        common_vendor.index.__f__("log", "at pages/booking/create.vue:403", "场馆详情加载完成，数据:", this.venueStore.venueDetailGetter);
        if (this.selectedDate) {
          await this.loadTimeSlots();
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/booking/create.vue:410", "加载场馆详情失败:", error);
        common_vendor.index.__f__("log", "at pages/booking/create.vue:413", "设置模拟场馆数据用于测试");
        this.venueStore.setVenueDetail({
          id: this.venueId || 1,
          name: "测试体育馆",
          price: 120,
          supportSharing: true,
          location: "测试地址",
          openingHours: "08:00 - 22:00"
        });
        if (this.selectedDate) {
          this.venueStore.setTimeSlots([
            {
              id: 1,
              startTime: "09:00",
              endTime: "10:00",
              status: "AVAILABLE",
              price: 120
            },
            {
              id: 2,
              startTime: "10:00",
              endTime: "11:00",
              status: "AVAILABLE",
              price: 120
            }
          ]);
        }
        common_vendor.index.showToast({
          title: "加载失败",
          icon: "error"
        });
      }
    },
    // 加载场馆和指定时间段
    async loadVenueAndSlot(slotId) {
      try {
        common_vendor.index.__f__("log", "at pages/booking/create.vue:453", "loadVenueAndSlot 开始，slotId:", slotId);
        await this.venueStore.getVenueDetail(this.venueId);
        await this.loadTimeSlots();
        common_vendor.index.__f__("log", "at pages/booking/create.vue:457", "可用时间段:", this.timeSlots);
        let slot = this.timeSlots.find((s) => s.id == slotId);
        if (!slot && slotId.includes("-")) {
          const [startTime, endTime] = slotId.split("-");
          slot = this.timeSlots.find((s) => s.startTime === startTime && s.endTime === endTime);
        }
        common_vendor.index.__f__("log", "at pages/booking/create.vue:468", "找到的时间段:", slot);
        if (slot) {
          this.selectedSlot = slot;
          common_vendor.index.__f__("log", "at pages/booking/create.vue:472", "已设置 selectedSlot:", this.selectedSlot);
        } else {
          common_vendor.index.__f__("warn", "at pages/booking/create.vue:474", "未找到指定的时间段:", slotId);
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/booking/create.vue:477", "加载失败:", error);
        common_vendor.index.__f__("log", "at pages/booking/create.vue:480", "设置模拟数据用于测试");
        this.venueStore.setVenueDetail({
          id: this.venueId || 1,
          name: "测试体育馆",
          price: 120,
          supportSharing: true,
          location: "测试地址",
          openingHours: "08:00 - 22:00"
        });
        const mockSlots = [
          {
            id: 1,
            startTime: "09:00",
            endTime: "10:00",
            status: "AVAILABLE",
            price: 120
          },
          {
            id: 2,
            startTime: "10:00",
            endTime: "11:00",
            status: "AVAILABLE",
            price: 120
          },
          {
            id: 3,
            startTime: "14:00",
            endTime: "15:00",
            status: "AVAILABLE",
            price: 120
          }
        ];
        this.venueStore.setTimeSlots(mockSlots);
        if (slotId) {
          let slot = mockSlots.find((s) => s.id == slotId);
          if (!slot && slotId.includes("-")) {
            const [startTime, endTime] = slotId.split("-");
            slot = mockSlots.find((s) => s.startTime === startTime && s.endTime === endTime);
          }
          if (slot) {
            this.selectedSlot = slot;
            common_vendor.index.__f__("log", "at pages/booking/create.vue:529", "已设置模拟 selectedSlot:", this.selectedSlot);
          }
        }
        common_vendor.index.showToast({
          title: "使用模拟数据",
          icon: "none"
        });
      }
    },
    // 加载时间段
    async loadTimeSlots() {
      if (!this.selectedDate)
        return;
      try {
        common_vendor.index.__f__("log", "at pages/booking/create.vue:545", "loadTimeSlots 调用参数:", { venueId: this.venueId, date: this.selectedDate });
        await this.venueStore.getTimeSlots(this.venueId, this.selectedDate);
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/booking/create.vue:548", "加载时间段失败:", error);
      }
    },
    // 格式化日期时间
    formatDateTime(date, slot) {
      common_vendor.index.__f__("log", "at pages/booking/create.vue:562", "formatDateTime 调用:", { date, slot, selectedSlots: this.selectedSlots });
      if (!date) {
        common_vendor.index.__f__("log", "at pages/booking/create.vue:565", "formatDateTime 返回默认值: 请选择时间");
        return "请选择时间";
      }
      try {
        const dateObj = new Date(date);
        const year = dateObj.getFullYear();
        const month = dateObj.getMonth() + 1;
        const day = dateObj.getDate();
        const weekDay = ["周日", "周一", "周二", "周三", "周四", "周五", "周六"][dateObj.getDay()];
        const dateStr = `${year}年${month}月${day}日 ${weekDay}`;
        if (this.selectedSlots && this.selectedSlots.length > 0) {
          const timeSlots = this.selectedSlots.map((slot2) => {
            let startTime2 = slot2.startTime;
            let endTime2 = slot2.endTime;
            if (startTime2 && startTime2.length > 5) {
              startTime2 = startTime2.substring(0, 5);
            }
            if (endTime2 && endTime2.length > 5) {
              endTime2 = endTime2.substring(0, 5);
            }
            return `${startTime2}-${endTime2}`;
          });
          const totalDuration = this.selectedSlots.reduce((total, slot2) => {
            return total + this.calculateDuration(slot2.startTime, slot2.endTime);
          }, 0);
          const durationText2 = totalDuration % 1 === 0 ? totalDuration : totalDuration.toFixed(1);
          const result2 = `${dateStr} ${timeSlots.join("、")} (共${durationText2}小时)`;
          common_vendor.index.__f__("log", "at pages/booking/create.vue:603", "formatDateTime 多时间段结果:", result2);
          return result2;
        }
        if (!slot) {
          common_vendor.index.__f__("log", "at pages/booking/create.vue:609", "formatDateTime 返回默认值: 请选择时间");
          return "请选择时间";
        }
        let startTime = slot.startTime;
        let endTime = slot.endTime;
        if (startTime && startTime.length > 5) {
          startTime = startTime.substring(0, 5);
        }
        if (endTime && endTime.length > 5) {
          endTime = endTime.substring(0, 5);
        }
        const duration = this.calculateDuration(startTime, endTime);
        const durationText = duration % 1 === 0 ? duration : duration.toFixed(1);
        const timeStr = `${startTime}-${endTime}`;
        const result = `${dateStr} ${timeStr} (${durationText}小时)`;
        common_vendor.index.__f__("log", "at pages/booking/create.vue:633", "formatDateTime 单时间段结果:", result);
        return result;
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/booking/create.vue:636", "formatDateTime 错误:", error);
        return "时间格式错误";
      }
    },
    // 计算时长
    calculateDuration(startTime, endTime) {
      try {
        const [startHour, startMinute] = startTime.split(":").map(Number);
        const [endHour, endMinute] = endTime.split(":").map(Number);
        const startMinutes = startHour * 60 + startMinute;
        const endMinutes = endHour * 60 + endMinute;
        const durationMinutes = endMinutes - startMinutes;
        const hours = durationMinutes / 60;
        return Math.round(hours * 10) / 10;
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/booking/create.vue:656", "计算时长错误:", error);
        return 1;
      }
    },
    // 获取单个时间段的价格
    getSlotPrice(slot) {
      var _a, _b;
      common_vendor.index.__f__("log", "at pages/booking/create.vue:663", "🔍 getSlotPrice 调用，slot:", slot);
      common_vendor.index.__f__("log", "at pages/booking/create.vue:664", "🔍 slot.price:", slot.price);
      common_vendor.index.__f__("log", "at pages/booking/create.vue:665", "🔍 slot.pricePerHour:", slot.pricePerHour);
      common_vendor.index.__f__("log", "at pages/booking/create.vue:666", "🔍 venue.price:", (_a = this.venue) == null ? void 0 : _a.price);
      let price = 0;
      if (slot.price && slot.price > 0) {
        price = parseFloat(slot.price);
        common_vendor.index.__f__("log", "at pages/booking/create.vue:673", "✅ 使用slot.price:", price);
        return price;
      }
      if (slot.pricePerHour && slot.pricePerHour > 0) {
        price = parseFloat(slot.pricePerHour);
        common_vendor.index.__f__("log", "at pages/booking/create.vue:680", "✅ 使用slot.pricePerHour:", price);
        return price;
      }
      const venuePrice = ((_b = this.venue) == null ? void 0 : _b.price) || 0;
      if (venuePrice > 0) {
        price = parseFloat(venuePrice) / 2;
        common_vendor.index.__f__("log", "at pages/booking/create.vue:689", "✅ 使用venue.price/2 (半小时):", price);
        return price;
      }
      price = 60;
      common_vendor.index.__f__("log", "at pages/booking/create.vue:695", "⚠️ 使用默认价格:", price);
      return price;
    },
    // 获取时间段状态文本
    getSlotStatusText(status) {
      const statusMap = {
        "AVAILABLE": "可预约",
        "RESERVED": "已预约",
        "OCCUPIED": "已占用",
        "MAINTENANCE": "维护中"
      };
      return statusMap[status] || status;
    },
    // 确认预约
    async confirmBooking() {
      var _a, _b, _c, _d, _e, _f;
      common_vendor.index.__f__("log", "at pages/booking/create.vue:712", "确认预约开始");
      common_vendor.index.__f__("log", "at pages/booking/create.vue:713", "canConfirm:", this.canConfirm);
      common_vendor.index.__f__("log", "at pages/booking/create.vue:714", "selectedSlots:", this.selectedSlots);
      common_vendor.index.__f__("log", "at pages/booking/create.vue:715", "selectedSlot:", this.selectedSlot);
      common_vendor.index.__f__("log", "at pages/booking/create.vue:716", "bookingType:", this.bookingType);
      if (!this.canConfirm) {
        common_vendor.index.__f__("log", "at pages/booking/create.vue:719", "无法确认预约，canConfirm为false");
        return;
      }
      if (!this.validateForm()) {
        common_vendor.index.__f__("log", "at pages/booking/create.vue:725", "表单验证失败");
        return;
      }
      try {
        common_vendor.index.showLoading({ title: "创建中..." });
        let result;
        common_vendor.index.__f__("log", "at pages/booking/create.vue:734", "预约创建 - 检查时间段选择:");
        common_vendor.index.__f__("log", "at pages/booking/create.vue:735", "- selectedSlots:", this.selectedSlots);
        common_vendor.index.__f__("log", "at pages/booking/create.vue:736", "- selectedSlots.length:", (_a = this.selectedSlots) == null ? void 0 : _a.length);
        common_vendor.index.__f__("log", "at pages/booking/create.vue:737", "- selectedSlot:", this.selectedSlot);
        common_vendor.index.__f__("log", "at pages/booking/create.vue:738", "- 条件判断结果:", this.selectedSlots && this.selectedSlots.length > 0);
        if (this.selectedSlots && this.selectedSlots.length > 0) {
          const firstSlot = this.selectedSlots[0];
          common_vendor.index.__f__("log", "at pages/booking/create.vue:745", "多时间段预约 - bookingType:", this.bookingForm.bookingType);
          common_vendor.index.__f__("log", "at pages/booking/create.vue:746", "多时间段预约 - 是否为拼场:", this.bookingForm.bookingType === "SHARED");
          if (this.bookingForm.bookingType === "SHARED") {
            const calculatedPrice = this.selectedSlots.reduce((total, slot) => {
              const slotPrice = this.getSlotPrice(slot);
              common_vendor.index.__f__("log", "at pages/booking/create.vue:753", `💰 拼场时间段 ${slot.startTime}-${slot.endTime} 价格:`, slotPrice);
              return total + slotPrice;
            }, 0);
            common_vendor.index.__f__("log", "at pages/booking/create.vue:757", "💰 拼场预约计算的总价格:", calculatedPrice);
            const sharedBookingData = {
              venueId: parseInt(this.venueId),
              date: this.selectedDate,
              startTime: firstSlot.startTime,
              teamName: this.bookingForm.teamName || "",
              contactInfo: this.bookingForm.contactInfo || "",
              maxParticipants: 2,
              // 表示需要两个球队才能成功拼场
              description: this.bookingForm.description || "",
              slotIds: this.selectedSlots.map((slot) => slot.id),
              // 传递所有选中的时间段ID
              price: calculatedPrice
              // 🔧 修复：添加价格字段
            };
            common_vendor.index.__f__("log", "at pages/booking/create.vue:770", "发送拼场预约数据:", sharedBookingData);
            common_vendor.index.__f__("log", "at pages/booking/create.vue:771", "firstSlot.startTime 类型和值:", typeof firstSlot.startTime, firstSlot.startTime);
            common_vendor.index.__f__("log", "at pages/booking/create.vue:772", "完整的 firstSlot 对象:", firstSlot);
            result = await this.bookingStore.createSharedBooking(sharedBookingData);
          } else {
            common_vendor.index.__f__("log", "at pages/booking/create.vue:776", "进入独享预约分支");
            common_vendor.index.__f__("log", "at pages/booking/create.vue:777", "selectedSlots数量:", this.selectedSlots.length);
            common_vendor.index.__f__("log", "at pages/booking/create.vue:778", "firstSlot:", firstSlot);
            const calculatedPrice = this.selectedSlots.reduce((total, slot) => {
              const slotPrice = this.getSlotPrice(slot);
              common_vendor.index.__f__("log", "at pages/booking/create.vue:783", `💰 时间段 ${slot.startTime}-${slot.endTime} 价格:`, slotPrice);
              return total + slotPrice;
            }, 0);
            common_vendor.index.__f__("log", "at pages/booking/create.vue:787", "💰 计算的总价格:", calculatedPrice);
            let finalPrice = calculatedPrice;
            if (calculatedPrice <= 0) {
              common_vendor.index.__f__("error", "at pages/booking/create.vue:794", "❌ 计算的价格为0或负数，使用备用价格计算");
              const slotCount = this.selectedSlots.length;
              const defaultPricePerSlot = 60;
              finalPrice = slotCount * defaultPricePerSlot;
              common_vendor.index.__f__("log", "at pages/booking/create.vue:801", `💰 备用价格计算: ${slotCount}个时间段 × ${defaultPricePerSlot}元 = ${finalPrice}元`);
            }
            let bookingData = {
              venueId: parseInt(this.venueId),
              // 🔧 确保venueId是数字类型
              date: this.selectedDate,
              startTime: firstSlot.startTime,
              endTime: this.selectedSlots[this.selectedSlots.length - 1].endTime,
              slotIds: this.selectedSlots.map((slot) => slot.id),
              bookingType: this.bookingForm.bookingType,
              description: this.bookingForm.description,
              price: parseFloat(finalPrice)
              // 🔧 确保price是数字类型
            };
            common_vendor.index.__f__("log", "at pages/booking/create.vue:815", "📤 发送给后端的预约数据:", bookingData);
            common_vendor.index.__f__("log", "at pages/booking/create.vue:816", "💰 最终价格:", finalPrice);
            const priceValidation = utils_realTimeDebugger.validatePriceTransmission(bookingData);
            if (!priceValidation.isValid) {
              common_vendor.index.__f__("error", "at pages/booking/create.vue:821", "❌ 实时价格验证失败:", priceValidation.issues);
            }
            const validation = utils_bookingPriceValidator.validateBookingData(bookingData);
            if (!validation.valid) {
              common_vendor.index.__f__("error", "at pages/booking/create.vue:827", "❌ 预约数据验证失败:", validation.errors);
              throw new Error(`数据验证失败: ${validation.errors.join(", ")}`);
            }
            if (validation.warnings.length > 0) {
              common_vendor.index.__f__("warn", "at pages/booking/create.vue:832", "⚠️ 预约数据警告:", validation.warnings);
            }
            common_vendor.index.__f__("log", "at pages/booking/create.vue:836", "✅ 价格计算正常，准备创建预约");
            common_vendor.index.__f__("log", "at pages/booking/create.vue:837", "📊 原始预约数据:", bookingData);
            common_vendor.index.__f__("log", "at pages/booking/create.vue:840", "🚨 执行紧急时间段同步修复...");
            try {
              const hasDefaultIds = bookingData.slotIds.some((id) => typeof id === "string" && id.startsWith("default_"));
              if (hasDefaultIds) {
                common_vendor.index.__f__("log", "at pages/booking/create.vue:846", "🔧 检测到默认时间段ID，尝试生成后端时间段...");
                const { generateTimeSlots, getVenueTimeSlots } = await "../../api/timeslot.js";
                await generateTimeSlots(this.venueId, this.selectedDate);
                common_vendor.index.__f__("log", "at pages/booking/create.vue:851", "✅ 后端时间段生成成功");
                const response = await getVenueTimeSlots(this.venueId, this.selectedDate, true);
                if (response && response.data && response.data.length > 0) {
                  common_vendor.index.__f__("log", "at pages/booking/create.vue:856", "✅ 获取到后端时间段:", response.data.length, "个");
                  const mappedIds = [];
                  this.selectedSlots.forEach((frontendSlot) => {
                    const backendSlot = response.data.find(
                      (bs) => bs.startTime === frontendSlot.startTime && bs.endTime === frontendSlot.endTime
                    );
                    if (backendSlot) {
                      mappedIds.push(backendSlot.id);
                      common_vendor.index.__f__("log", "at pages/booking/create.vue:866", `✅ 映射: ${frontendSlot.startTime}-${frontendSlot.endTime} -> ID ${backendSlot.id}`);
                    }
                  });
                  if (mappedIds.length > 0) {
                    bookingData.slotIds = mappedIds;
                    common_vendor.index.__f__("log", "at pages/booking/create.vue:872", "✅ 时间段ID映射成功:", mappedIds);
                  }
                }
              }
            } catch (fixError) {
              common_vendor.index.__f__("warn", "at pages/booking/create.vue:877", "⚠️ 紧急修复失败，使用原始数据:", fixError);
            }
            common_vendor.index.__f__("log", "at pages/booking/create.vue:881", "🔍 最终发送的预约数据:", {
              venueId: bookingData.venueId,
              venueIdType: typeof bookingData.venueId,
              price: bookingData.price,
              priceType: typeof bookingData.price,
              slotIds: bookingData.slotIds,
              slotIdsLength: bookingData.slotIds.length,
              slotIdsTypes: bookingData.slotIds.map((id) => typeof id)
            });
            result = await this.bookingStore.createBooking(bookingData);
            common_vendor.index.__f__("log", "at pages/booking/create.vue:895", "🔍 后端响应调试:", {
              success: result.success,
              orderId: result.orderId,
              returnedPrice: (_b = result.data) == null ? void 0 : _b.totalPrice,
              returnedPriceType: typeof ((_c = result.data) == null ? void 0 : _c.totalPrice),
              sentPrice: bookingData.price,
              priceMatch: ((_d = result.data) == null ? void 0 : _d.totalPrice) === bookingData.price,
              priceDifference: bookingData.price - (((_e = result.data) == null ? void 0 : _e.totalPrice) || 0)
            });
            const priceDiagnosis = utils_priceTransmissionTracer.quickPriceDiagnosis(this.selectedSlots, this.venue);
            common_vendor.index.__f__("log", "at pages/booking/create.vue:907", "📊 价格问题诊断:", priceDiagnosis);
          }
          common_vendor.index.__f__("log", "at pages/booking/create.vue:910", "预约创建结果:", result);
        } else {
          const bookingData = {
            venueId: this.venueId,
            date: this.selectedDate,
            startTime: this.selectedSlot.startTime,
            endTime: this.selectedSlot.endTime,
            slotId: this.selectedSlot.id,
            bookingType: this.bookingForm.bookingType,
            description: this.bookingForm.description,
            price: this.getSlotPrice(this.selectedSlot)
          };
          try {
            if (this.bookingForm.bookingType === "SHARED") {
              const slotPrice = this.getSlotPrice(this.selectedSlot);
              common_vendor.index.__f__("log", "at pages/booking/create.vue:930", "💰 单时间段拼场预约价格:", slotPrice);
              const sharedBookingData = {
                venueId: parseInt(this.venueId),
                date: this.selectedDate,
                startTime: this.selectedSlot.startTime,
                teamName: this.bookingForm.teamName || "",
                contactInfo: this.bookingForm.contactInfo || "",
                maxParticipants: 2,
                // 修改为2，表示需要两个球队才能成功拼场
                description: this.bookingForm.description || "",
                price: slotPrice
                // 🔧 修复：添加价格字段
              };
              common_vendor.index.__f__("log", "at pages/booking/create.vue:942", "发送单时间段拼场预约数据:", sharedBookingData);
              common_vendor.index.__f__("log", "at pages/booking/create.vue:943", "selectedSlot.startTime 类型和值:", typeof this.selectedSlot.startTime, this.selectedSlot.startTime);
              common_vendor.index.__f__("log", "at pages/booking/create.vue:944", "完整的 selectedSlot 对象:", this.selectedSlot);
              result = await this.bookingStore.createSharedBooking(sharedBookingData);
              common_vendor.index.__f__("log", "at pages/booking/create.vue:946", "单时间段拼场预约创建结果:", result);
            } else {
              result = await this.bookingStore.createBooking(bookingData);
              common_vendor.index.__f__("log", "at pages/booking/create.vue:949", "单时间段预约创建结果:", result);
            }
          } catch (error) {
            common_vendor.index.__f__("error", "at pages/booking/create.vue:952", "预约创建失败:", error);
            throw error;
          }
        }
        try {
          common_vendor.index.__f__("log", "at pages/booking/create.vue:960", "🔄 开始使用时间段同步修复工具刷新状态");
          common_vendor.index.__f__("log", "at pages/booking/create.vue:963", "🔄 刷新时间段状态...");
          await this.venueStore.getTimeSlots(this.venueId, this.selectedDate, true);
          common_vendor.index.__f__("log", "at pages/booking/create.vue:965", "✅ 时间段状态刷新完成");
          this.selectedSlots = [];
          this.selectedSlot = null;
          common_vendor.index.__f__("log", "at pages/booking/create.vue:970", "🧹 已清除选中状态");
          this.$forceUpdate();
          common_vendor.index.__f__("log", "at pages/booking/create.vue:974", "🔄 强制更新页面");
        } catch (error) {
          common_vendor.index.__f__("error", "at pages/booking/create.vue:976", "❌ 时间段同步修复失败:", error);
          this.selectedSlots = [];
          this.selectedSlot = null;
          this.$forceUpdate();
        }
        common_vendor.index.hideLoading();
        common_vendor.index.showToast({
          title: "预约成功",
          icon: "success"
        });
        common_vendor.index.__f__("log", "at pages/booking/create.vue:990", "🎉 预约创建成功！准备跳转到支付页面");
        common_vendor.index.__f__("log", "at pages/booking/create.vue:993", "📋 预约创建结果详情:", result);
        common_vendor.index.__f__("log", "at pages/booking/create.vue:994", "📋 result.id:", result.id);
        common_vendor.index.__f__("log", "at pages/booking/create.vue:995", "📋 result.orderId:", result.orderId);
        common_vendor.index.__f__("log", "at pages/booking/create.vue:996", "📋 result.data:", result.data);
        common_vendor.index.__f__("log", "at pages/booking/create.vue:997", "📋 result.data?.id:", (_f = result.data) == null ? void 0 : _f.id);
        let orderId = null;
        if (result.id) {
          orderId = result.id;
        } else if (result.orderId) {
          orderId = result.orderId;
        } else if (result.data && result.data.id) {
          orderId = result.data.id;
        } else if (result.data && result.data.orderId) {
          orderId = result.data.orderId;
        } else if (typeof result === "number") {
          orderId = result;
        }
        common_vendor.index.__f__("log", "at pages/booking/create.vue:1015", "🆔 提取的订单ID:", orderId);
        common_vendor.index.__f__("log", "at pages/booking/create.vue:1016", "🆔 订单ID类型:", typeof orderId);
        if (orderId && (typeof orderId === "number" || typeof orderId === "string")) {
          common_vendor.index.__f__("log", "at pages/booking/create.vue:1020", "✅ 订单ID有效，准备跳转到支付页面");
          common_vendor.index.redirectTo({
            url: `/pages/payment/index?orderId=${orderId}&type=booking&from=create`,
            success: () => {
              common_vendor.index.__f__("log", "at pages/booking/create.vue:1026", "✅ 成功跳转到支付页面");
            },
            fail: (error) => {
              common_vendor.index.__f__("error", "at pages/booking/create.vue:1029", "❌ 跳转支付页面失败:", error);
              common_vendor.index.navigateTo({
                url: `/pages/payment/index?orderId=${orderId}&type=booking&from=create`
              });
            }
          });
        } else {
          common_vendor.index.__f__("error", "at pages/booking/create.vue:1037", "❌ 无法获取有效的订单ID，跳转到预约列表");
          common_vendor.index.__f__("error", "at pages/booking/create.vue:1038", "❌ 原始结果:", result);
          common_vendor.index.showModal({
            title: "提示",
            content: '预约创建成功，但无法获取订单信息。请到"我的预约"中查看。',
            success: () => {
              common_vendor.index.redirectTo({
                url: "/pages/booking/list"
              });
            }
          });
        }
      } catch (error) {
        common_vendor.index.hideLoading();
        common_vendor.index.__f__("error", "at pages/booking/create.vue:1053", "创建预约失败:", error);
        common_vendor.index.showToast({
          title: error.message || "创建失败",
          icon: "error"
        });
      }
    },
    // 验证表单
    validateForm() {
      if (this.bookingForm.bookingType === "SHARED") {
        if (!this.bookingForm.teamName.trim()) {
          common_vendor.index.showToast({
            title: "请输入队伍名称",
            icon: "error"
          });
          return false;
        }
        if (!this.bookingForm.contactInfo.trim()) {
          common_vendor.index.showToast({
            title: "请输入联系方式",
            icon: "error"
          });
          return false;
        }
      }
      return true;
    },
    // 返回
    goBack() {
      common_vendor.index.navigateBack();
    }
  },
  watch: {
    showTimeSelector(val) {
      if (val) {
        this.tempDate = this.selectedDate || (/* @__PURE__ */ new Date()).toISOString().split("T")[0];
        this.tempSlot = this.selectedSlot;
        this.$refs.timePopup.open();
      }
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  var _a;
  return common_vendor.e({
    a: $options.venue
  }, $options.venue ? {
    b: $options.venue.image || "https://via.placeholder.com/400x200?text=场馆图片",
    c: common_vendor.t($options.venue.name),
    d: common_vendor.t($options.venue.location),
    e: common_vendor.t($options.venue.price)
  } : {}, {
    f: common_vendor.t($data.bookingForm.bookingType === "EXCLUSIVE" ? "独享预约" : "拼场预约"),
    g: $options.venue && $options.venue.supportSharing && $data.bookingForm.bookingType === "SHARED"
  }, $options.venue && $options.venue.supportSharing && $data.bookingForm.bookingType === "SHARED" ? {} : {}, {
    h: common_vendor.t($options.formatDateTime($data.selectedDate, $data.selectedSlot)),
    i: $data.bookingForm.bookingType === "SHARED"
  }, $data.bookingForm.bookingType === "SHARED" ? {
    j: $data.bookingForm.teamName,
    k: common_vendor.o(($event) => $data.bookingForm.teamName = $event.detail.value),
    l: $data.bookingForm.contactInfo,
    m: common_vendor.o(($event) => $data.bookingForm.contactInfo = $event.detail.value)
  } : {}, {
    n: common_vendor.t($data.bookingForm.bookingType === "SHARED" ? "拼场说明" : "备注信息"),
    o: $data.bookingForm.bookingType === "SHARED" ? "球队实力中等，出汗局" : "请输入备注信息（可选）",
    p: $data.bookingForm.description,
    q: common_vendor.o(($event) => $data.bookingForm.description = $event.detail.value),
    r: $data.selectedSlots && $data.selectedSlots.length > 0
  }, $data.selectedSlots && $data.selectedSlots.length > 0 ? {
    s: common_vendor.f($data.selectedSlots, (slot, index, i0) => {
      return {
        a: common_vendor.t(slot.startTime),
        b: common_vendor.t(slot.endTime),
        c: common_vendor.t($options.getSlotPrice(slot)),
        d: index
      };
    })
  } : $data.selectedSlot ? {
    v: common_vendor.t($data.selectedSlot.startTime),
    w: common_vendor.t($data.selectedSlot.endTime),
    x: common_vendor.t($options.getSlotPrice($data.selectedSlot))
  } : {
    y: common_vendor.t(((_a = $options.venue) == null ? void 0 : _a.price) || 0)
  }, {
    t: $data.selectedSlot,
    z: $data.bookingForm.bookingType === "SHARED"
  }, $data.bookingForm.bookingType === "SHARED" ? {
    A: common_vendor.t(($options.totalCost / 2).toFixed(2)),
    B: common_vendor.t($options.totalCost),
    C: common_vendor.t(($options.totalCost / 2).toFixed(2))
  } : {
    D: common_vendor.t($options.totalCost)
  }, {
    E: common_vendor.t($data.bookingForm.bookingType === "SHARED" ? "实付金额：" : "总费用："),
    F: common_vendor.t($data.bookingForm.bookingType === "SHARED" ? ($options.totalCost / 2).toFixed(2) : $options.totalCost.toFixed(2)),
    G: $data.bookingForm.bookingType === "SHARED" ? 1 : "",
    H: common_vendor.o((...args) => $options.goBack && $options.goBack(...args)),
    I: !$options.canConfirm,
    J: common_vendor.o((...args) => $options.confirmBooking && $options.confirmBooking(...args))
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-d639e0f6"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/booking/create.js.map
