"use strict";
const common_vendor = require("../common/vendor.js");
const utils_request = require("../utils/request.js");
function getVenueTimeSlots(venueId, date, forceRefresh = false) {
  const options = {
    cache: !forceRefresh,
    // 如果forceRefresh为true，则禁用缓存
    cacheTTL: forceRefresh ? 0 : 6e4
    // 强制刷新时设置缓存时间为0
  };
  common_vendor.index.__f__("log", "at api/timeslot.js:9", `[API] getVenueTimeSlots - venueId: ${venueId}, date: ${date}, forceRefresh: ${forceRefresh}, options:`, options);
  return utils_request.get(`/timeslots/venue/${venueId}/date/${date}`, {}, options);
}
function generateTimeSlots(venueId, date) {
  return utils_request.post(`/timeslots/venue/${venueId}/date/${date}/generate`);
}
exports.generateTimeSlots = generateTimeSlots;
exports.getVenueTimeSlots = getVenueTimeSlots;
//# sourceMappingURL=../../.sourcemap/mp-weixin/api/timeslot.js.map
