"use strict";
const utils_request = require("../utils/request.js");
function getVenueTimeSlots(venueId, date, forceRefresh = false) {
  const options = {
    cache: !forceRefresh
    // 如果forceRefresh为true，则禁用缓存
  };
  return utils_request.get(`/timeslots/venue/${venueId}/date/${date}`, {}, options);
}
function generateTimeSlots(venueId, date) {
  return utils_request.post(`/timeslots/venue/${venueId}/date/${date}/generate`);
}
exports.generateTimeSlots = generateTimeSlots;
exports.getVenueTimeSlots = getVenueTimeSlots;
//# sourceMappingURL=../../.sourcemap/mp-weixin/api/timeslot.js.map
