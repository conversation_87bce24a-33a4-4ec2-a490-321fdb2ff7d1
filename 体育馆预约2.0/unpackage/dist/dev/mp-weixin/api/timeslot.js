"use strict";
const common_vendor = require("../common/vendor.js");
const utils_request = require("../utils/request.js");
function getVenueTimeSlots(venueId, date, forceRefresh = false) {
  const options = {
    cache: !forceRefresh,
    // 如果forceRefresh为true，则禁用缓存
    cacheTTL: forceRefresh ? 0 : 6e4
    // 强制刷新时设置缓存时间为0
  };
  if (forceRefresh) {
    const timestamp = Date.now();
    const randomId = Math.random().toString(36).substring(7);
    const params = {
      _t: timestamp,
      _r: randomId,
      _nocache: 1
    };
    options.cache = false;
    options.cacheTTL = 0;
    options.forceRefresh = true;
    options.headers = {
      "Cache-Control": "no-cache, no-store, must-revalidate",
      "Pragma": "no-cache",
      "Expires": "0"
    };
    common_vendor.index.__f__("log", "at api/timeslot.js:31", `🚫 [API] 强制无缓存获取时间段 - venueId: ${venueId}, date: ${date}, params:`, params);
    return utils_request.get(`/timeslots/venue/${venueId}/date/${date}`, params, options);
  }
  common_vendor.index.__f__("log", "at api/timeslot.js:35", `[API] getVenueTimeSlots - venueId: ${venueId}, date: ${date}, forceRefresh: ${forceRefresh}, options:`, options);
  return utils_request.get(`/timeslots/venue/${venueId}/date/${date}`, {}, options);
}
function generateTimeSlots(venueId, date) {
  return utils_request.post(`/timeslots/venue/${venueId}/date/${date}/generate`);
}
exports.generateTimeSlots = generateTimeSlots;
exports.getVenueTimeSlots = getVenueTimeSlots;
//# sourceMappingURL=../../.sourcemap/mp-weixin/api/timeslot.js.map
