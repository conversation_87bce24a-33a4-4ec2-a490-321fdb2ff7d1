"use strict";
const common_vendor = require("../common/vendor.js");
const api_venue = require("../api/venue.js");
const api_timeslot = require("../api/timeslot.js");
const utils_ui = require("../utils/ui.js");
const useVenueStore = common_vendor.defineStore("venue", {
  state: () => ({
    venueList: [],
    popularVenues: [],
    venueDetail: null,
    venueTypes: [],
    timeSlots: [],
    searchResults: [],
    loading: false,
    pagination: {
      current: 1,
      pageSize: 10,
      total: 0,
      totalPages: 1
    }
  }),
  getters: {
    // 场馆列表相关 - 这些应该是getter，返回状态值
    venueListGetter: (state) => state.venueList,
    popularVenuesGetter: (state) => state.popularVenues,
    venueDetailGetter: (state) => state.venueDetail,
    venueTypesGetter: (state) => state.venueTypes,
    timeSlotsGetter: (state) => state.timeSlots,
    searchResultsGetter: (state) => state.searchResults,
    // 状态相关
    isLoading: (state) => state.loading,
    getPagination: (state) => state.pagination,
    // 计算属性
    totalVenues: (state) => state.venueList.length,
    hasMoreVenues: (state) => state.pagination.current < state.pagination.totalPages,
    // 按类型筛选场馆
    getVenuesByType: (state) => (typeId) => {
      if (!typeId)
        return state.venueList;
      return state.venueList.filter((venue) => venue.typeId === typeId);
    },
    // 获取可用时间段
    getAvailableTimeSlots: (state) => {
      return state.timeSlots.filter((slot) => slot.status === "AVAILABLE");
    }
  },
  actions: {
    // 设置加载状态
    setLoading(loading) {
      this.loading = loading;
    },
    // 设置场馆列表
    setVenueList({ list, pagination }) {
      this.venueList = list;
      if (pagination) {
        this.pagination = { ...this.pagination, ...pagination };
      }
    },
    // 追加场馆列表（分页加载）
    appendVenueList(list) {
      this.venueList = [...this.venueList, ...list];
    },
    // 设置热门场馆
    setPopularVenues(venues) {
      this.popularVenues = venues;
    },
    // 设置场馆详情
    setVenueDetail(venue) {
      this.venueDetail = venue;
    },
    // 设置场馆类型
    setVenueTypes(types) {
      this.venueTypes = types;
    },
    // 设置时间段
    setTimeSlots(slots) {
      common_vendor.index.__f__("log", "at stores/venue.js:88", "[VenueStore] setTimeSlots 被调用，参数:", slots);
      common_vendor.index.__f__("log", "at stores/venue.js:89", "[VenueStore] setTimeSlots 参数类型:", typeof slots);
      common_vendor.index.__f__("log", "at stores/venue.js:90", "[VenueStore] setTimeSlots 是否为数组:", Array.isArray(slots));
      if (Array.isArray(slots)) {
        this.timeSlots = slots;
      } else {
        common_vendor.index.__f__("warn", "at stores/venue.js:96", "[VenueStore] setTimeSlots 收到非数组参数，强制设置为空数组");
        this.timeSlots = [];
      }
      common_vendor.index.__f__("log", "at stores/venue.js:100", "[VenueStore] setTimeSlots 设置后的值:", this.timeSlots);
    },
    // 设置搜索结果
    setSearchResults(results) {
      this.searchResults = results;
    },
    // 设置分页信息
    setPagination(pagination) {
      this.pagination = { ...this.pagination, ...pagination };
    },
    // 获取场馆列表
    async getVenueList(params = {}) {
      try {
        common_vendor.index.__f__("log", "at stores/venue.js:116", "[VenueStore] 开始获取场馆列表，参数:", params);
        this.setLoading(true);
        const timeoutPromise = new Promise((_, reject) => {
          setTimeout(() => reject(new Error("请求超时")), 1e4);
        });
        const apiPromise = api_venue.getVenueList(params);
        const response = await Promise.race([apiPromise, timeoutPromise]);
        common_vendor.index.__f__("log", "at stores/venue.js:127", "[VenueStore] 场馆API响应:", response);
        let list = [];
        let pagination = {
          current: 1,
          pageSize: 10,
          total: 0,
          totalPages: 1
        };
        if (response && response.data) {
          if (Array.isArray(response.data)) {
            list = response.data;
            pagination = {
              current: response.page || params.page || 1,
              pageSize: response.pageSize || params.pageSize || 10,
              total: response.total || response.data.length,
              totalPages: response.totalPages || 1
            };
          } else {
            common_vendor.index.__f__("warn", "at stores/venue.js:148", "[VenueStore] API响应数据格式异常，使用空数组:", response);
          }
        } else if (response && Array.isArray(response)) {
          list = response;
          pagination.total = response.length;
        } else {
          common_vendor.index.__f__("warn", "at stores/venue.js:155", "[VenueStore] API响应为空或格式错误，使用空数组:", response);
        }
        common_vendor.index.__f__("log", "at stores/venue.js:158", "[VenueStore] 解析的场馆列表:", list);
        common_vendor.index.__f__("log", "at stores/venue.js:159", "[VenueStore] 分页信息:", pagination);
        if (params.page === 1 || params.refresh) {
          this.setVenueList({ list, pagination });
        } else {
          this.appendVenueList(list);
          this.setVenueList({ list: this.venueList, pagination });
        }
        return response;
      } catch (error) {
        common_vendor.index.__f__("error", "at stores/venue.js:170", "[VenueStore] 获取场馆列表失败:", error);
        utils_ui.showError(error.message || "获取场馆列表失败");
        throw error;
      } finally {
        this.setLoading(false);
      }
    },
    // 获取热门场馆
    async getPopularVenues() {
      try {
        common_vendor.index.__f__("log", "at stores/venue.js:181", "[VenueStore] 开始获取热门场馆");
        const response = await api_venue.getPopularVenues();
        if (response && response.data) {
          this.setPopularVenues(response.data);
          common_vendor.index.__f__("log", "at stores/venue.js:186", "[VenueStore] 热门场馆获取成功:", response.data);
        }
        return response;
      } catch (error) {
        common_vendor.index.__f__("error", "at stores/venue.js:191", "[VenueStore] 获取热门场馆失败:", error);
        utils_ui.showError(error.message || "获取热门场馆失败");
        throw error;
      }
    },
    // 获取场馆详情
    async getVenueDetail(venueId) {
      try {
        common_vendor.index.__f__("log", "at stores/venue.js:200", "[VenueStore] 开始获取场馆详情:", venueId);
        this.setLoading(true);
        const response = await api_venue.getVenueDetail(venueId);
        common_vendor.index.__f__("log", "at stores/venue.js:204", "[VenueStore] 完整API响应:", response);
        common_vendor.index.__f__("log", "at stores/venue.js:205", "[VenueStore] 响应数据类型:", typeof response);
        common_vendor.index.__f__("log", "at stores/venue.js:206", "[VenueStore] 响应数据结构:", Object.keys(response || {}));
        if (response && response.data) {
          this.setVenueDetail(response.data);
          common_vendor.index.__f__("log", "at stores/venue.js:210", "[VenueStore] 场馆详情获取成功:", response.data);
        } else if (response) {
          this.setVenueDetail(response);
          common_vendor.index.__f__("log", "at stores/venue.js:214", "[VenueStore] 场馆详情获取成功（直接响应）:", response);
        }
        return response;
      } catch (error) {
        common_vendor.index.__f__("error", "at stores/venue.js:219", "[VenueStore] 获取场馆详情失败:", error);
        utils_ui.showError(error.message || "获取场馆详情失败");
        throw error;
      } finally {
        this.setLoading(false);
      }
    },
    // 获取场馆类型
    async getVenueTypes() {
      try {
        common_vendor.index.__f__("log", "at stores/venue.js:230", "[VenueStore] 开始获取场馆类型");
        const response = await api_venue.getVenueTypes();
        if (response && response.data) {
          this.setVenueTypes(response.data);
          common_vendor.index.__f__("log", "at stores/venue.js:235", "[VenueStore] 场馆类型获取成功:", response.data);
        }
        return response;
      } catch (error) {
        common_vendor.index.__f__("error", "at stores/venue.js:240", "[VenueStore] 获取场馆类型失败:", error);
        utils_ui.showError(error.message || "获取场馆类型失败");
        throw error;
      }
    },
    // 获取时间段
    async getTimeSlots(venueId, date) {
      try {
        common_vendor.index.__f__("log", "at stores/venue.js:249", "[VenueStore] 开始获取时间段:", { venueId, date });
        this.setLoading(true);
        const response = await api_timeslot.getVenueTimeSlots(venueId, date);
        if (response && response.data && response.data.length > 0) {
          this.setTimeSlots(response.data);
          common_vendor.index.__f__("log", "at stores/venue.js:256", "[VenueStore] 时间段获取成功:", response.data);
        } else {
          common_vendor.index.__f__("log", "at stores/venue.js:259", "[VenueStore] 没有时间段数据，尝试生成...");
          try {
            await api_timeslot.generateTimeSlots(venueId, date);
            common_vendor.index.__f__("log", "at stores/venue.js:264", "[VenueStore] 后端生成时间段API调用成功");
            const retryResponse = await api_timeslot.getVenueTimeSlots(venueId, date);
            if (retryResponse && retryResponse.data && retryResponse.data.length > 0) {
              this.setTimeSlots(retryResponse.data);
              common_vendor.index.__f__("log", "at stores/venue.js:270", "[VenueStore] 生成并获取时间段成功:", retryResponse.data);
            } else {
              common_vendor.index.__f__("warn", "at stores/venue.js:272", "[VenueStore] 后端生成API调用成功但没有返回数据，使用前端生成");
              this.generateDefaultTimeSlots(venueId, date);
            }
          } catch (generateError) {
            common_vendor.index.__f__("warn", "at stores/venue.js:276", "[VenueStore] 后端生成时间段失败，使用前端生成:", generateError);
            this.generateDefaultTimeSlots(venueId, date);
          }
        }
        return response;
      } catch (error) {
        common_vendor.index.__f__("error", "at stores/venue.js:284", "[VenueStore] 获取时间段失败:", error);
        common_vendor.index.__f__("log", "at stores/venue.js:286", "[VenueStore] API失败，生成默认时间段作为备用");
        this.generateDefaultTimeSlots(venueId, date);
      } finally {
        this.setLoading(false);
      }
    },
    // 生成默认时间段（半小时间隔）
    generateDefaultTimeSlots(venueId, date) {
      var _a;
      common_vendor.index.__f__("log", "at stores/venue.js:296", "[VenueStore] 开始生成默认时间段（半小时间隔）");
      const venueHourPrice = ((_a = this.venueDetail) == null ? void 0 : _a.price) || 100;
      const venueHalfHourPrice = Math.round(venueHourPrice / 2);
      common_vendor.index.__f__("log", "at stores/venue.js:301", "[VenueStore] 场馆小时价格:", venueHourPrice);
      common_vendor.index.__f__("log", "at stores/venue.js:302", "[VenueStore] 场馆半小时价格:", venueHalfHourPrice);
      const defaultSlots = [];
      const startHour = 9;
      const endHour = 22;
      for (let hour = startHour; hour < endHour; hour++) {
        for (let minute = 0; minute < 60; minute += 30) {
          const startTime = `${hour.toString().padStart(2, "0")}:${minute.toString().padStart(2, "0")}`;
          const endMinute = minute + 30;
          const nextHour = endMinute >= 60 ? hour + 1 : hour;
          const actualEndMinute = endMinute >= 60 ? 0 : endMinute;
          const endTime = `${nextHour.toString().padStart(2, "0")}:${actualEndMinute.toString().padStart(2, "0")}`;
          if (nextHour > endHour)
            break;
          defaultSlots.push({
            id: `default_${venueId}_${date}_${hour}_${minute}`,
            venueId: parseInt(venueId),
            date,
            startTime,
            endTime,
            price: venueHalfHourPrice,
            // 使用场馆的半小时价格
            status: "AVAILABLE"
          });
        }
      }
      common_vendor.index.__f__("log", "at stores/venue.js:332", "[VenueStore] 生成的默认时间段数量:", defaultSlots.length);
      common_vendor.index.__f__("log", "at stores/venue.js:333", "[VenueStore] 生成的默认时间段详情:", defaultSlots);
      this.setTimeSlots(defaultSlots);
      common_vendor.index.__f__("log", "at stores/venue.js:336", "[VenueStore] 默认时间段设置完成");
    },
    // 获取场馆时间段（别名方法，用于兼容性）
    async getVenueTimeSlots(params) {
      if (typeof params === "object" && params.venueId && params.date) {
        return await this.getTimeSlots(params.venueId, params.date);
      }
      return await this.getTimeSlots(params);
    },
    // 搜索场馆
    async searchVenues(params) {
      try {
        common_vendor.index.__f__("log", "at stores/venue.js:351", "[VenueStore] 开始搜索场馆:", params);
        this.setLoading(true);
        const response = await api_venue.searchVenues(params);
        if (response && response.data) {
          this.setSearchResults(response.data);
          common_vendor.index.__f__("log", "at stores/venue.js:358", "[VenueStore] 场馆搜索成功:", response.data);
        } else {
          common_vendor.index.__f__("log", "at stores/venue.js:361", "[VenueStore] 搜索API返回空结果，尝试本地过滤...");
          await this.searchVenuesLocally(params);
        }
        return response;
      } catch (error) {
        common_vendor.index.__f__("error", "at stores/venue.js:367", "[VenueStore] 搜索场馆失败:", error);
        common_vendor.index.__f__("log", "at stores/venue.js:369", "[VenueStore] 搜索API失败，尝试本地过滤...");
        try {
          await this.searchVenuesLocally(params);
        } catch (localError) {
          common_vendor.index.__f__("error", "at stores/venue.js:373", "[VenueStore] 本地搜索也失败:", localError);
          utils_ui.showError("搜索功能暂时不可用");
        }
        throw error;
      } finally {
        this.setLoading(false);
      }
    },
    // 本地搜索备用方案
    async searchVenuesLocally(params) {
      var _a;
      try {
        const allVenuesResponse = await api_venue.getVenueList({ page: 1, pageSize: 100 });
        if (allVenuesResponse && allVenuesResponse.data) {
          const allVenues = allVenuesResponse.data;
          const keyword = ((_a = params.keyword) == null ? void 0 : _a.toLowerCase()) || "";
          const filteredVenues = allVenues.filter((venue) => {
            var _a2, _b, _c, _d;
            const nameMatch = (_a2 = venue.name) == null ? void 0 : _a2.toLowerCase().includes(keyword);
            const typeMatch = (_b = venue.type) == null ? void 0 : _b.toLowerCase().includes(keyword);
            const locationMatch = (_c = venue.location) == null ? void 0 : _c.toLowerCase().includes(keyword);
            const descriptionMatch = (_d = venue.description) == null ? void 0 : _d.toLowerCase().includes(keyword);
            return nameMatch || typeMatch || locationMatch || descriptionMatch;
          });
          this.setSearchResults(filteredVenues);
          common_vendor.index.__f__("log", "at stores/venue.js:403", "[VenueStore] 本地搜索完成，结果:", filteredVenues);
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at stores/venue.js:406", "[VenueStore] 本地搜索失败:", error);
        throw error;
      }
    },
    // 清空场馆详情
    clearVenueDetail() {
      this.venueDetail = null;
    },
    // 清空搜索结果
    clearSearchResults() {
      this.searchResults = [];
    },
    // 重置分页
    resetPagination() {
      this.pagination = {
        current: 1,
        pageSize: 10,
        total: 0,
        totalPages: 1
      };
    },
    // 清除时间段缓存
    clearTimeSlots() {
      common_vendor.index.__f__("log", "at stores/venue.js:433", "[VenueStore] 清除时间段缓存");
      this.timeSlots = [];
      this.selectedTimeSlots = [];
      if (typeof window !== "undefined" && window.requestCache) {
        Object.keys(window.requestCache).forEach((key) => {
          if (key.includes("/api/timeslots/")) {
            delete window.requestCache[key];
            common_vendor.index.__f__("log", "at stores/venue.js:442", "[VenueStore] 清除缓存键:", key);
          }
        });
      }
    }
  }
});
exports.useVenueStore = useVenueStore;
//# sourceMappingURL=../../.sourcemap/mp-weixin/stores/venue.js.map
