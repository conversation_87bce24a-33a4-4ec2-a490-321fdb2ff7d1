"use strict";
const common_vendor = require("../common/vendor.js");
const api_booking = require("../api/booking.js");
const api_user = require("../api/user.js");
const api_sharing = require("../api/sharing.js");
const utils_ui = require("../utils/ui.js");
const useBookingStore = common_vendor.defineStore("booking", {
  state: () => ({
    bookingList: [],
    bookingDetail: null,
    sharingOrders: [],
    userSharingOrders: [],
    joinedSharingOrders: [],
    sharingDetail: null,
    loading: false,
    pagination: {
      current: 1,
      pageSize: 10,
      total: 0,
      totalPages: 1,
      currentPage: 1
    }
  }),
  getters: {
    // 基础getters
    getBookingList: (state) => state.bookingList,
    getBookingDetail: (state) => state.bookingDetail,
    getSharingOrders: (state) => state.sharingOrders,
    getUserSharingOrders: (state) => state.userSharingOrders,
    getJoinedSharingOrders: (state) => state.joinedSharingOrders,
    getSharingDetail: (state) => state.sharingDetail,
    isLoading: (state) => state.loading,
    getPagination: (state) => state.pagination,
    // 计算属性
    totalBookings: (state) => state.bookingList.length,
    totalSharingOrders: (state) => state.sharingOrders.length,
    totalUserSharingOrders: (state) => state.userSharingOrders.length,
    totalJoinedSharingOrders: (state) => state.joinedSharingOrders.length,
    // 按状态筛选预订
    getBookingsByStatus: (state) => (status) => {
      return state.bookingList.filter((booking) => booking.status === status);
    },
    // 待确认的预订
    getPendingBookings: (state) => {
      return state.bookingList.filter((booking) => booking.status === "PENDING");
    },
    // 已确认的预订
    getConfirmedBookings: (state) => {
      return state.bookingList.filter((booking) => booking.status === "CONFIRMED");
    },
    // 是否有更多数据
    hasMoreData: (state) => {
      return state.pagination.current < state.pagination.totalPages;
    }
  },
  actions: {
    // 设置加载状态
    setLoading(loading) {
      this.loading = loading;
    },
    // 设置预订列表
    setBookingList({ list, pagination }) {
      this.bookingList = Array.isArray(list) ? list : [];
      if (pagination) {
        this.pagination = { ...this.pagination, ...pagination };
      }
    },
    // 追加预订列表
    appendBookingList(list) {
      const newList = Array.isArray(list) ? list : [];
      this.bookingList = [...this.bookingList, ...newList];
    },
    // 设置预订详情
    setBookingDetail(detail) {
      this.bookingDetail = detail;
    },
    // 设置分享订单列表
    setSharingOrders(orders) {
      this.sharingOrders = Array.isArray(orders) ? orders : [];
    },
    // 设置用户分享订单
    setUserSharingOrders(orders) {
      this.userSharingOrders = Array.isArray(orders) ? orders : [];
    },
    // 设置加入的分享订单
    setJoinedSharingOrders(orders) {
      this.joinedSharingOrders = Array.isArray(orders) ? orders : [];
    },
    // 设置分享详情
    setSharingDetail(detail) {
      this.sharingDetail = detail;
    },
    // 设置分页信息
    setPagination(pagination) {
      this.pagination = { ...this.pagination, ...pagination };
    },
    // 更新预订状态
    updateBookingStatus({ bookingId, status }) {
      const booking = this.bookingList.find((b) => b.id === bookingId);
      if (booking) {
        booking.status = status;
      }
    },
    // 创建预订
    async createBooking(bookingData) {
      var _a;
      try {
        common_vendor.index.__f__("log", "at stores/booking.js:124", "[BookingStore] 发起预约创建请求，数据:", bookingData);
        this.setLoading(true);
        const response = await api_booking.createBooking(bookingData);
        common_vendor.index.__f__("log", "at stores/booking.js:128", "[BookingStore] 预约创建API响应:", response);
        common_vendor.index.__f__("log", "at stores/booking.js:129", "[BookingStore] response.data:", response.data);
        common_vendor.index.__f__("log", "at stores/booking.js:130", "[BookingStore] response.data?.id:", (_a = response.data) == null ? void 0 : _a.id);
        utils_ui.showSuccess("预约成功");
        const result = response.data || response;
        common_vendor.index.__f__("log", "at stores/booking.js:136", "[BookingStore] 最终返回结果:", result);
        return result;
      } catch (error) {
        common_vendor.index.__f__("error", "at stores/booking.js:139", "[BookingStore] 创建预约失败:", error);
        utils_ui.showError(error.message || "预约失败");
        throw error;
      } finally {
        this.setLoading(false);
      }
    },
    // 获取我的预订列表（别名方法，用于测试兼容性）
    async getMyBookings(params = {}) {
      return await this.getBookingList(params);
    },
    // 获取预订详情
    async getBookingDetails(bookingId) {
      try {
        common_vendor.index.__f__("log", "at stores/booking.js:155", "[BookingStore] 获取预订详情，ID:", bookingId);
        this.setLoading(true);
        const response = await (void 0)(bookingId);
        common_vendor.index.__f__("log", "at stores/booking.js:159", "[BookingStore] 预订详情获取成功:", response);
        return response;
      } catch (error) {
        common_vendor.index.__f__("error", "at stores/booking.js:163", "[BookingStore] 获取预订详情失败:", error);
        utils_ui.showError(error.message || "获取预订详情失败");
        throw error;
      } finally {
        this.setLoading(false);
      }
    },
    // 创建拼场预约
    async createSharedBooking(bookingData) {
      try {
        common_vendor.index.__f__("log", "at stores/booking.js:174", "[BookingStore] 开始创建拼场预约");
        this.setLoading(true);
        const response = await api_booking.createSharedBooking(bookingData);
        utils_ui.showSuccess("拼场预约成功");
        common_vendor.index.__f__("log", "at stores/booking.js:180", "[BookingStore] 拼场预约创建成功");
        return response;
      } catch (error) {
        common_vendor.index.__f__("error", "at stores/booking.js:184", "[BookingStore] 创建拼场预约失败:", error);
        utils_ui.showError(error.message || "拼场预约失败");
        throw error;
      } finally {
        this.setLoading(false);
      }
    },
    // 获取用户预约列表
    async getUserBookings(params = {}) {
      try {
        common_vendor.index.__f__("log", "at stores/booking.js:195", "[BookingStore] 开始获取用户预约列表，参数:", params);
        this.setLoading(true);
        const response = await api_user.getUserBookings(params);
        common_vendor.index.__f__("log", "at stores/booking.js:200", "[BookingStore] API响应原始数据:", response);
        common_vendor.index.__f__("log", "at stores/booking.js:201", "[BookingStore] response.data:", response.data);
        common_vendor.index.__f__("log", "at stores/booking.js:202", "[BookingStore] response.data类型:", typeof response.data);
        const { data, total, page, pageSize, totalPages } = response;
        common_vendor.index.__f__("log", "at stores/booking.js:206", "[BookingStore] 解构后的数据:");
        common_vendor.index.__f__("log", "at stores/booking.js:207", "data:", data);
        common_vendor.index.__f__("log", "at stores/booking.js:208", "data类型:", typeof data);
        common_vendor.index.__f__("log", "at stores/booking.js:209", "data是否为数组:", Array.isArray(data));
        common_vendor.index.__f__("log", "at stores/booking.js:210", "total:", total);
        common_vendor.index.__f__("log", "at stores/booking.js:211", "page:", page);
        common_vendor.index.__f__("log", "at stores/booking.js:212", "pageSize:", pageSize);
        common_vendor.index.__f__("log", "at stores/booking.js:213", "totalPages:", totalPages);
        const pagination = {
          current: page,
          pageSize,
          total,
          totalPages,
          currentPage: page
        };
        if (params.page === 1 || params.refresh) {
          common_vendor.index.__f__("log", "at stores/booking.js:224", "[BookingStore] 设置新的预约列表，数据长度:", (data || []).length);
          this.setBookingList({ list: data || [], pagination });
        } else {
          common_vendor.index.__f__("log", "at stores/booking.js:227", "[BookingStore] 追加预约列表，新增数据长度:", (data || []).length);
          this.appendBookingList(data || []);
          this.setPagination(pagination);
        }
        return response;
      } catch (error) {
        common_vendor.index.__f__("error", "at stores/booking.js:234", "[BookingStore] 获取用户预约列表失败:", error);
        this.setBookingList({
          list: [],
          pagination: { current: 1, pageSize: 10, total: 0, totalPages: 1, currentPage: 1 }
        });
        utils_ui.showError(error.message || "获取预约列表失败");
        throw error;
      } finally {
        this.setLoading(false);
      }
    },
    // 获取预约详情
    async getBookingDetail(bookingId) {
      try {
        common_vendor.index.__f__("log", "at stores/booking.js:250", "[BookingStore] 🌐 发起API请求获取订单详情, ID:", bookingId);
        common_vendor.index.__f__("log", "at stores/booking.js:251", "[BookingStore] 🌐 ID类型:", typeof bookingId);
        this.setLoading(true);
        if (!bookingId) {
          throw new Error("订单ID不能为空");
        }
        const response = await api_booking.getBookingDetail(bookingId);
        common_vendor.index.__f__("log", "at stores/booking.js:259", "[BookingStore] 📡 完整API响应:", response);
        common_vendor.index.__f__("log", "at stores/booking.js:260", "[BookingStore] 📡 响应类型:", typeof response);
        common_vendor.index.__f__("log", "at stores/booking.js:261", "[BookingStore] 📡 响应是否为空:", !response);
        let bookingData = null;
        if (response && typeof response === "object") {
          if (response.id || response.orderNo) {
            bookingData = response;
            common_vendor.index.__f__("log", "at stores/booking.js:269", "[BookingStore] 📡 使用response作为数据");
          } else if (response.data) {
            bookingData = response.data;
            common_vendor.index.__f__("log", "at stores/booking.js:274", "[BookingStore] 📡 使用response.data作为数据");
          } else if (response.result) {
            bookingData = response.result;
            common_vendor.index.__f__("log", "at stores/booking.js:279", "[BookingStore] 📡 使用response.result作为数据");
          } else {
            common_vendor.index.__f__("warn", "at stores/booking.js:282", "[BookingStore] 📡 响应数据结构未知:", Object.keys(response));
            bookingData = response;
          }
        } else {
          common_vendor.index.__f__("error", "at stores/booking.js:287", "[BookingStore] 📡 响应数据无效:", response);
          throw new Error("服务器返回的数据格式不正确");
        }
        common_vendor.index.__f__("log", "at stores/booking.js:291", "[BookingStore] 📡 处理后的订单数据:", bookingData);
        common_vendor.index.__f__("log", "at stores/booking.js:292", "[BookingStore] 📡 数据类型:", typeof bookingData);
        common_vendor.index.__f__("log", "at stores/booking.js:293", "[BookingStore] 📡 数据键:", bookingData ? Object.keys(bookingData) : "null");
        common_vendor.index.__f__("log", "at stores/booking.js:294", "[BookingStore] ⏰ API返回的开始时间:", bookingData == null ? void 0 : bookingData.startTime);
        common_vendor.index.__f__("log", "at stores/booking.js:295", "[BookingStore] ⏰ API返回的结束时间:", bookingData == null ? void 0 : bookingData.endTime);
        common_vendor.index.__f__("log", "at stores/booking.js:296", "[BookingStore] 💰 API返回的总价格:", bookingData == null ? void 0 : bookingData.totalPrice);
        common_vendor.index.__f__("log", "at stores/booking.js:297", "[BookingStore] 🏷️ API返回的订单号(orderNo):", bookingData == null ? void 0 : bookingData.orderNo);
        common_vendor.index.__f__("log", "at stores/booking.js:298", "[BookingStore] 🏷️ API返回的订单号(orderNumber):", bookingData == null ? void 0 : bookingData.orderNumber);
        common_vendor.index.__f__("log", "at stores/booking.js:299", "[BookingStore] 🆔 API返回的ID:", bookingData == null ? void 0 : bookingData.id);
        if (!bookingData) {
          throw new Error("未能获取到有效的订单数据");
        }
        if (bookingData.orderNumber && !bookingData.orderNo) {
          bookingData.orderNo = bookingData.orderNumber;
          common_vendor.index.__f__("log", "at stores/booking.js:308", "[BookingStore] 🔄 字段映射: orderNumber -> orderNo:", bookingData.orderNo);
        }
        this.setBookingDetail(bookingData);
        common_vendor.index.__f__("log", "at stores/booking.js:312", "[BookingStore] ✅ 数据已存储到store");
        return response;
      } catch (error) {
        common_vendor.index.__f__("error", "at stores/booking.js:315", "[BookingStore] ❌ API请求失败:", error);
        common_vendor.index.__f__("error", "at stores/booking.js:316", "[BookingStore] ❌ 错误类型:", error.constructor.name);
        common_vendor.index.__f__("error", "at stores/booking.js:317", "[BookingStore] ❌ 错误消息:", error.message);
        common_vendor.index.__f__("error", "at stores/booking.js:318", "[BookingStore] ❌ 错误堆栈:", error.stack);
        this.setBookingDetail(null);
        utils_ui.showError(error.message || "获取预约详情失败");
        throw error;
      } finally {
        this.setLoading(false);
      }
    },
    // 取消预约
    async cancelBooking(bookingId) {
      try {
        common_vendor.index.__f__("log", "at stores/booking.js:333", "[BookingStore] 开始取消预约:", bookingId);
        const response = await api_booking.cancelBooking(bookingId);
        this.updateBookingStatus({ bookingId, status: "CANCELLED" });
        common_vendor.index.__f__("log", "at stores/booking.js:341", "[BookingStore] 发送订单取消事件通知");
        common_vendor.index.$emit("orderCancelled", {
          orderId: bookingId,
          type: "booking"
        });
        setTimeout(() => {
          this.getUserBookings({ page: 1, pageSize: 10, refresh: true });
        }, 1e3);
        utils_ui.showSuccess("预约已取消");
        common_vendor.index.__f__("log", "at stores/booking.js:353", "[BookingStore] 预约取消成功");
        return response;
      } catch (error) {
        common_vendor.index.__f__("error", "at stores/booking.js:356", "[BookingStore] 取消预约失败:", error);
        utils_ui.showError(error.message || "取消预约失败");
        throw error;
      }
    },
    // 清空预订详情
    clearBookingDetail() {
      this.bookingDetail = null;
    },
    // 重置分页
    resetPagination() {
      this.pagination = {
        current: 1,
        pageSize: 10,
        total: 0,
        totalPages: 1,
        currentPage: 1
      };
    },
    // === 分享相关功能 ===
    // 申请拼场
    async createSharingOrder({ orderId, data }) {
      try {
        common_vendor.index.__f__("log", "at stores/booking.js:383", "[BookingStore] 开始申请拼场");
        this.setLoading(true);
        const response = await api_sharing.applySharedBooking(orderId, data);
        utils_ui.showSuccess("拼场申请已发送");
        common_vendor.index.__f__("log", "at stores/booking.js:389", "[BookingStore] 拼场申请成功");
        return response;
      } catch (error) {
        common_vendor.index.__f__("error", "at stores/booking.js:392", "[BookingStore] 申请拼场失败:", error);
        utils_ui.showError(error.message || "申请拼场失败");
        throw error;
      } finally {
        this.setLoading(false);
      }
    },
    // 获取可拼场的订单列表
    async getSharingOrdersList(params = {}) {
      try {
        common_vendor.index.__f__("log", "at stores/booking.js:403", "[BookingStore] 开始获取拼场订单，参数:", params);
        this.setLoading(true);
        const response = await api_sharing.getJoinableSharingOrders(params);
        common_vendor.index.__f__("log", "at stores/booking.js:407", "[BookingStore] 拼场订单API响应:", response);
        if (response && (response.list || response.data)) {
          const responseData = response.list ? response : response.data;
          const orders = responseData.list || responseData.data || [];
          common_vendor.index.__f__("log", "at stores/booking.js:414", "[BookingStore] 解析的拼场订单:", orders);
          if (params.page === 1 || params.refresh) {
            this.setSharingOrders(orders);
          } else {
            this.sharingOrders = [...this.sharingOrders, ...orders];
          }
          if (responseData.pagination) {
            this.setPagination(responseData.pagination);
          }
        }
        return response;
      } catch (error) {
        common_vendor.index.__f__("error", "at stores/booking.js:431", "[BookingStore] 获取拼场订单失败:", error);
        utils_ui.showError(error.message || "获取拼场订单失败");
        throw error;
      } finally {
        this.setLoading(false);
      }
    },
    // 创建拼场订单
    async createSharingOrderNew(sharingData) {
      try {
        common_vendor.index.__f__("log", "at stores/booking.js:442", "[BookingStore] 开始创建拼场订单");
        const response = await api_sharing.createSharingOrder(sharingData);
        utils_ui.showSuccess("拼场订单创建成功");
        common_vendor.index.__f__("log", "at stores/booking.js:447", "[BookingStore] 拼场订单创建成功");
        return response.data;
      } catch (error) {
        common_vendor.index.__f__("error", "at stores/booking.js:450", "[BookingStore] 创建拼场订单失败:", error);
        utils_ui.showError(error.message || "创建拼场订单失败");
        throw error;
      }
    },
    // 获取拼场订单详情
    async getSharingOrderDetail(orderId) {
      try {
        common_vendor.index.__f__("log", "at stores/booking.js:459", "[BookingStore] 开始获取拼场订单详情:", orderId);
        const response = await api_sharing.getSharingOrderById(orderId);
        this.setSharingDetail(response.data);
        common_vendor.index.__f__("log", "at stores/booking.js:464", "[BookingStore] 拼场订单详情获取成功");
        return response.data;
      } catch (error) {
        common_vendor.index.__f__("error", "at stores/booking.js:467", "[BookingStore] 获取拼场订单详情失败:", error);
        utils_ui.showError(error.message || "获取拼场订单详情失败");
        throw error;
      }
    },
    // 加入拼场订单
    async joinSharingOrder(orderId) {
      try {
        common_vendor.index.__f__("log", "at stores/booking.js:476", "[BookingStore] 开始加入拼场订单:", orderId);
        const response = await api_sharing.joinSharingOrder(orderId);
        utils_ui.showSuccess("加入拼场成功");
        common_vendor.index.__f__("log", "at stores/booking.js:481", "[BookingStore] 加入拼场成功");
        return response.data;
      } catch (error) {
        common_vendor.index.__f__("error", "at stores/booking.js:484", "[BookingStore] 加入拼场失败:", error);
        utils_ui.showError(error.message || "加入拼场失败");
        throw error;
      }
    },
    // 获取我创建的拼场订单
    async getMyCreatedSharingOrders() {
      try {
        common_vendor.index.__f__("log", "at stores/booking.js:493", "[BookingStore] 开始获取我创建的拼场订单");
        const response = await api_sharing.getMyCreatedSharingOrders();
        this.setUserSharingOrders(response.data || []);
        common_vendor.index.__f__("log", "at stores/booking.js:498", "[BookingStore] 我创建的拼场订单获取成功");
        return response.data;
      } catch (error) {
        common_vendor.index.__f__("error", "at stores/booking.js:501", "[BookingStore] 获取我创建的拼场订单失败:", error);
        utils_ui.showError(error.message || "获取我创建的拼场订单失败");
        throw error;
      }
    },
    // 处理拼场申请
    async handleSharingRequest({ requestId, data }) {
      try {
        common_vendor.index.__f__("log", "at stores/booking.js:510", "[BookingStore] 开始处理拼场申请:", { requestId, data });
        const response = await api_sharing.handleSharedRequest(requestId, data);
        utils_ui.showSuccess(data.status === "APPROVED" ? "已同意拼场申请" : "已拒绝拼场申请");
        common_vendor.index.__f__("log", "at stores/booking.js:515", "[BookingStore] 拼场申请处理成功");
        return response;
      } catch (error) {
        common_vendor.index.__f__("error", "at stores/booking.js:518", "[BookingStore] 处理拼场申请失败:", error);
        utils_ui.showError(error.message || "处理拼场申请失败");
        throw error;
      }
    },
    // 获取我发出的拼场申请
    async getUserSharingOrders(params = {}) {
      try {
        common_vendor.index.__f__("log", "at stores/booking.js:527", "[BookingStore] 开始获取我发出的拼场申请");
        const response = await api_sharing.getMySharedRequests(params);
        this.setUserSharingOrders(response.data || []);
        common_vendor.index.__f__("log", "at stores/booking.js:532", "[BookingStore] 我发出的拼场申请获取成功");
        return response;
      } catch (error) {
        common_vendor.index.__f__("error", "at stores/booking.js:535", "[BookingStore] 获取拼场申请失败:", error);
        utils_ui.showError(error.message || "获取拼场申请失败");
        throw error;
      }
    },
    // 获取我收到的拼场申请
    async getUserJoinedSharingOrders(params = {}) {
      try {
        common_vendor.index.__f__("log", "at stores/booking.js:544", "[BookingStore] 开始获取我收到的拼场申请");
        const response = await api_sharing.getReceivedSharedRequests(params);
        this.setJoinedSharingOrders(response.data || []);
        common_vendor.index.__f__("log", "at stores/booking.js:549", "[BookingStore] 我收到的拼场申请获取成功");
        return response;
      } catch (error) {
        common_vendor.index.__f__("error", "at stores/booking.js:552", "[BookingStore] 获取拼场申请失败:", error);
        utils_ui.showError(error.message || "获取拼场申请失败");
        throw error;
      }
    },
    // 获取拼场详情
    async getSharingDetail(sharingId) {
      try {
        common_vendor.index.__f__("log", "at stores/booking.js:561", "[BookingStore] 开始获取拼场详情:", sharingId);
        this.setLoading(true);
        const response = await api_sharing.getSharingOrderById(sharingId);
        this.setSharingDetail(response.data);
        common_vendor.index.__f__("log", "at stores/booking.js:567", "[BookingStore] 拼场详情获取成功");
        return response;
      } catch (error) {
        common_vendor.index.__f__("error", "at stores/booking.js:570", "[BookingStore] 获取拼场详情失败:", error);
        utils_ui.showError(error.message || "获取拼场详情失败");
        throw error;
      } finally {
        this.setLoading(false);
      }
    },
    // 移除拼场参与者
    async removeSharingParticipant({ sharingId, participantId }) {
      try {
        common_vendor.index.__f__("log", "at stores/booking.js:581", "[BookingStore] 开始移除拼场参与者:", { sharingId, participantId });
        this.setLoading(true);
        const response = await api_sharing.removeSharingParticipant(sharingId, participantId);
        await this.getSharingDetail(sharingId);
        utils_ui.showSuccess("参与者已移除");
        common_vendor.index.__f__("log", "at stores/booking.js:591", "[BookingStore] 移除拼场参与者成功");
        return response;
      } catch (error) {
        common_vendor.index.__f__("error", "at stores/booking.js:594", "[BookingStore] 移除拼场参与者失败:", error);
        utils_ui.showError(error.message || "移除参与者失败");
        throw error;
      } finally {
        this.setLoading(false);
      }
    },
    // 更新拼场设置
    async updateSharingSettings({ sharingId, settings }) {
      try {
        common_vendor.index.__f__("log", "at stores/booking.js:605", "[BookingStore] 开始更新拼场设置:", { sharingId, settings });
        this.setLoading(true);
        const response = await api_sharing.updateSharingSettings(sharingId, settings);
        await this.getSharingDetail(sharingId);
        utils_ui.showSuccess("拼场设置已更新");
        common_vendor.index.__f__("log", "at stores/booking.js:615", "[BookingStore] 更新拼场设置成功");
        return response;
      } catch (error) {
        common_vendor.index.__f__("error", "at stores/booking.js:618", "[BookingStore] 更新拼场设置失败:", error);
        utils_ui.showError(error.message || "更新拼场设置失败");
        throw error;
      } finally {
        this.setLoading(false);
      }
    }
  }
});
exports.useBookingStore = useBookingStore;
//# sourceMappingURL=../../.sourcemap/mp-weixin/stores/booking.js.map
