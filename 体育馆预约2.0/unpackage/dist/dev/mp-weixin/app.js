"use strict";
Object.defineProperty(exports, Symbol.toStringTag, { value: "Module" });
const common_vendor = require("./common/vendor.js");
const utils_routerGuardNew = require("./utils/router-guard-new.js");
const stores_user = require("./stores/user.js");
const stores_app = require("./stores/app.js");
const stores_index = require("./stores/index.js");
if (!Math) {
  "./pages/index/index.js";
  "./pages/venue/list.js";
  "./pages/venue/detail.js";
  "./pages/sharing/list.js";
  "./pages/test/index.js";
  "./pages/test/migration-validation.js";
  "./pages/test/api-diagnosis.js";
  "./pages/test/quick-fix-validation.js";
  "./pages/test/comprehensive-migration-check.js";
  "./pages/test/naming-conflict-fix.js";
  "./pages/booking/list.js";
  "./pages/user/profile.js";
  "./pages/booking/create.js";
  "./pages/booking/detail.js";
  "./pages/sharing/create.js";
  "./pages/sharing/detail.js";
  "./pages/sharing/manage.js";
  "./pages/sharing/my-orders.js";
  "./pages/user/login.js";
  "./pages/user/register.js";
  "./pages/user/edit-profile.js";
  "./pages/test/auth-test.js";
  "./pages/sharing/requests.js";
  "./pages/sharing/received.js";
  "./pages/test/simple-test.js";
  "./pages/test/timeslot-sync.js";
  "./pages/test/payment-test.js";
  "./pages/test/order-status-test.js";
  "./pages/test/payment-fix.js";
  "./pages/payment/index.js";
  "./pages/payment/success.js";
  "./pages/payment/failed.js";
}
const _sfc_main = {
  data() {
    return {
      userStore: null,
      appStore: null
    };
  },
  onLaunch: function() {
    common_vendor.index.__f__("log", "at App.vue:14", "[App] 应用启动");
    this.userStore = stores_user.useUserStore();
    this.appStore = stores_app.useAppStore();
    utils_routerGuardNew.setupRouterGuard();
    this.userStore.initUserState();
    this.checkAndRedirectToLogin();
    this.$nextTick(() => {
      this.setupNetworkListener();
    });
  },
  onShow: function() {
    common_vendor.index.__f__("log", "at App.vue:35", "App Show");
  },
  onHide: function() {
    common_vendor.index.__f__("log", "at App.vue:38", "App Hide");
  },
  methods: {
    // 检查登录状态并跳转到登录页
    checkAndRedirectToLogin() {
      try {
        common_vendor.index.__f__("log", "at App.vue:45", "[App] 检查登录状态");
        const token = common_vendor.index.getStorageSync("token");
        const userInfo = common_vendor.index.getStorageSync("userInfo");
        if (!token || !userInfo) {
          common_vendor.index.__f__("log", "at App.vue:50", "[App] 未登录，跳转到登录页");
          common_vendor.index.reLaunch({
            url: "/pages/user/login"
          });
          return;
        }
        common_vendor.index.__f__("log", "at App.vue:57", "[App] 已登录，继续正常流程");
      } catch (error) {
        common_vendor.index.__f__("warn", "at App.vue:59", "[App] 登录状态检查失败:", error.message);
        common_vendor.index.reLaunch({
          url: "/pages/user/login"
        });
      }
    },
    // 设置网络监听
    setupNetworkListener() {
      common_vendor.index.onNetworkStatusChange((res) => {
        this.appStore.setNetworkStatus(res.isConnected);
        if (!res.isConnected) {
          common_vendor.index.showToast({
            title: "网络连接已断开",
            icon: "none"
          });
        }
      });
    }
  }
};
function createApp() {
  const app = common_vendor.createSSRApp(_sfc_main);
  app.use(stores_index.pinia);
  return {
    app
  };
}
createApp().app.mount("#app");
exports.createApp = createApp;
//# sourceMappingURL=../.sourcemap/mp-weixin/app.js.map
