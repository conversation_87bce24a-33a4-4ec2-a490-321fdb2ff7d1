{"version": 3, "file": "sharing.js", "sources": ["stores/sharing.js"], "sourcesContent": ["import { defineStore } from 'pinia'\nimport * as sharingApi from '@/api/sharing.js'\nimport { showSuccess, showError } from '@/utils/ui.js'\n\nexport const useSharingStore = defineStore('sharing', {\n  state: () => ({\n    sharingOrders: [],\n    mySharingOrders: [],\n    receivedRequests: [],\n    sentRequests: [],\n    sharingOrderDetail: null,\n    loading: false,\n    pagination: {\n      current: 1,\n      pageSize: 10,\n      total: 0,\n      totalPages: 1\n    }\n  }),\n\n  getters: {\n    // 基础getters - 修复命名冲突，避免与actions同名\n    sharingOrdersGetter: (state) => state.sharingOrders,\n    mySharingOrdersGetter: (state) => state.mySharingOrders,\n    receivedRequestsGetter: (state) => state.receivedRequests,\n    sentRequestsGetter: (state) => state.sentRequests,\n    sharingOrderDetailGetter: (state) => state.sharingOrderDetail,\n    isLoading: (state) => state.loading,\n    getPagination: (state) => state.pagination,\n    \n    // 计算属性\n    totalSharingOrders: (state) => state.sharingOrders.length,\n    totalMySharingOrders: (state) => state.mySharingOrders.length,\n    totalReceivedRequests: (state) => state.receivedRequests.length,\n    totalSentRequests: (state) => state.sentRequests.length,\n    \n    // 按状态筛选\n    getOrdersByStatus: (state) => (status) => {\n      return state.sharingOrders.filter(order => order.status === status)\n    },\n    \n    // 待处理的请求\n    getPendingRequests: (state) => {\n      return state.receivedRequests.filter(request => request.status === 'PENDING')\n    },\n    \n    // 是否有更多数据\n    hasMoreData: (state) => {\n      return state.pagination.current < state.pagination.totalPages\n    }\n  },\n\n  actions: {\n    // 设置加载状态\n    setLoading(loading) {\n      this.loading = loading\n    },\n    \n    // 设置分享订单列表\n    setSharingOrders(orders) {\n      this.sharingOrders = Array.isArray(orders) ? orders : []\n    },\n    \n    // 设置我的分享订单\n    setMySharingOrders(orders) {\n      this.mySharingOrders = Array.isArray(orders) ? orders : []\n    },\n    \n    // 设置收到的请求\n    setReceivedRequests(requests) {\n      this.receivedRequests = Array.isArray(requests) ? requests : []\n    },\n    \n    // 设置发送的请求\n    setSentRequests(requests) {\n      this.sentRequests = Array.isArray(requests) ? requests : []\n    },\n    \n    // 设置分享订单详情\n    setSharingOrderDetail(order) {\n      this.sharingOrderDetail = order\n    },\n    \n    // 设置分页信息\n    setPagination(pagination) {\n      this.pagination = { ...this.pagination, ...pagination }\n    },\n    \n    // 更新订单状态\n    updateOrderStatus({ orderId, status }) {\n      const order = this.sharingOrders.find(o => o.id === orderId)\n      if (order) {\n        order.status = status\n      }\n      \n      const myOrder = this.mySharingOrders.find(o => o.id === orderId)\n      if (myOrder) {\n        myOrder.status = status\n      }\n    },\n    \n    // 获取分享订单列表\n    async getSharingOrdersList(params = {}) {\n      try {\n        console.log('[SharingStore] 开始获取分享订单列表，参数:', params)\n        this.setLoading(true)\n\n        const response = await sharingApi.getJoinableSharingOrders(params)\n\n        if (response && response.data) {\n          const orders = Array.isArray(response.data) ? response.data : []\n          this.setSharingOrders(orders)\n\n          if (response.pagination) {\n            this.setPagination(response.pagination)\n          }\n\n          console.log('[SharingStore] 分享订单列表获取成功:', orders.length, '条')\n        }\n\n        return response\n      } catch (error) {\n        console.error('[SharingStore] 获取分享订单列表失败:', error)\n        showError(error.message || '获取分享订单列表失败')\n        throw error\n      } finally {\n        this.setLoading(false)\n      }\n    },\n\n    // 获取可加入的拼场订单\n    async getJoinableSharingOrders(params = {}) {\n      try {\n        console.log('[SharingStore] 开始获取可加入的拼场订单，参数:', params)\n        this.setLoading(true)\n\n        const response = await sharingApi.getJoinableSharingOrders(params)\n\n        if (response) {\n          // 处理后端返回的数据格式：{list: [...], pagination: {...}}\n          const orders = response.list || response.data || []\n\n          console.log('[SharingStore] 可加入拼场订单后端返回数据格式:', {\n            hasData: !!response.data,\n            hasList: !!response.list,\n            ordersLength: orders.length,\n            pagination: response.pagination\n          })\n\n          // 如果是刷新或第一页，替换数据；否则追加数据\n          if (params.refresh || params.page === 1) {\n            this.setSharingOrders(orders)\n          } else {\n            this.sharingOrders.push(...orders)\n          }\n\n          if (response.pagination) {\n            this.setPagination(response.pagination)\n          }\n\n          console.log('[SharingStore] 可加入拼场订单获取成功:', orders.length, '条')\n        }\n\n        return response\n      } catch (error) {\n        console.error('[SharingStore] 获取可加入拼场订单失败:', error)\n        showError(error.message || '获取可加入拼场订单失败')\n        throw error\n      } finally {\n        this.setLoading(false)\n      }\n    },\n\n    // 获取所有拼场订单\n    async getAllSharingOrders(params = {}) {\n      try {\n        console.log('[SharingStore] 开始获取所有拼场订单，参数:', params)\n        this.setLoading(true)\n\n        // 添加超时处理\n        const timeoutPromise = new Promise((_, reject) => {\n          setTimeout(() => reject(new Error('请求超时')), 10000) // 10秒超时\n        })\n\n        const apiPromise = sharingApi.getAllSharingOrders(params)\n        const response = await Promise.race([apiPromise, timeoutPromise])\n\n        if (response) {\n          // 处理后端返回的数据格式：{list: [...], pagination: {...}}\n          const orders = response.list || response.data || []\n\n          console.log('[SharingStore] 后端返回数据格式:', {\n            hasData: !!response.data,\n            hasList: !!response.list,\n            ordersLength: orders.length,\n            pagination: response.pagination\n          })\n\n          // 如果是刷新或第一页，替换数据；否则追加数据\n          if (params.refresh || params.page === 1) {\n            this.setSharingOrders(orders)\n          } else {\n            this.sharingOrders.push(...orders)\n          }\n\n          if (response.pagination) {\n            this.setPagination(response.pagination)\n          }\n\n          console.log('[SharingStore] 所有拼场订单获取成功:', orders.length, '条')\n        } else {\n          console.warn('[SharingStore] 获取所有拼场订单返回空数据')\n          // 如果是刷新，清空数据\n          if (params.refresh || params.page === 1) {\n            this.setSharingOrders([])\n          }\n        }\n\n        return response\n      } catch (error) {\n        console.error('[SharingStore] 获取所有拼场订单失败:', error)\n\n        // 如果是超时错误，显示特定提示\n        if (error.message === '请求超时') {\n          showError('加载超时，请检查网络连接')\n        } else {\n          showError(error.message || '获取所有拼场订单失败')\n        }\n\n        // 如果是刷新，确保清空加载状态\n        if (params.refresh || params.page === 1) {\n          this.setSharingOrders([])\n        }\n\n        throw error\n      } finally {\n        this.setLoading(false)\n      }\n    },\n    \n    // 获取我的分享订单\n    async getMyOrders(params = {}) {\n      try {\n        console.log('[SharingStore] 开始获取我的分享订单')\n        this.setLoading(true)\n\n        const response = await sharingApi.getMyCreatedSharingOrders(params)\n\n        if (response && response.data) {\n          const orders = Array.isArray(response.data) ? response.data : []\n          this.setMySharingOrders(orders)\n          console.log('[SharingStore] 我的分享订单获取成功:', orders.length, '条')\n        }\n\n        return response\n      } catch (error) {\n        console.error('[SharingStore] 获取我的分享订单失败:', error)\n        showError(error.message || '获取我的分享订单失败')\n        throw error\n      } finally {\n        this.setLoading(false)\n      }\n    },\n    \n    // 获取收到的请求\n    async getReceivedRequestsList(params = {}) {\n      try {\n        console.log('[SharingStore] 开始获取收到的请求')\n        this.setLoading(true)\n\n        const response = await sharingApi.getReceivedSharedRequests(params)\n\n        if (response && response.data) {\n          const requests = Array.isArray(response.data) ? response.data : []\n          this.setReceivedRequests(requests)\n          console.log('[SharingStore] 收到的请求获取成功:', requests.length, '条')\n        }\n\n        return response\n      } catch (error) {\n        console.error('[SharingStore] 获取收到的请求失败:', error)\n        showError(error.message || '获取收到的请求失败')\n        throw error\n      } finally {\n        this.setLoading(false)\n      }\n    },\n    \n    // 获取发送的请求\n    async getSentRequestsList(params = {}) {\n      try {\n        console.log('[SharingStore] 开始获取发送的请求')\n        this.setLoading(true)\n\n        const response = await sharingApi.getMySharedRequests(params)\n\n        if (response && response.data) {\n          const requests = Array.isArray(response.data) ? response.data : []\n          this.setSentRequests(requests)\n          console.log('[SharingStore] 发送的请求获取成功:', requests.length, '条')\n        }\n\n        return response\n      } catch (error) {\n        console.error('[SharingStore] 获取发送的请求失败:', error)\n        showError(error.message || '获取发送的请求失败')\n        throw error\n      } finally {\n        this.setLoading(false)\n      }\n    },\n    \n    // 获取分享订单详情\n    async getOrderDetail(orderId, forceRefresh = false) {\n      try {\n        console.log('[SharingStore] 开始获取分享订单详情:', orderId, forceRefresh ? '(强制刷新)' : '')\n        this.setLoading(true)\n\n        // 如果是强制刷新，清除当前数据\n        if (forceRefresh) {\n          this.sharingOrderDetail = null\n        }\n\n        const response = await sharingApi.getSharingOrderById(orderId)\n\n        console.log('[SharingStore] API响应详情:', response)\n\n        if (response) {\n          // 检查是否是错误响应（包含message字段）\n          if (response.message && !response.id) {\n            console.warn('[SharingStore] 获取分享订单详情失败:', response.message)\n            this.setSharingOrderDetail(null)\n          } else if (response.id) {\n            // 如果响应包含id字段，说明是有效的拼场订单数据\n            this.setSharingOrderDetail(response)\n            console.log('[SharingStore] 分享订单详情获取成功:', response)\n          } else {\n            console.warn('[SharingStore] 获取分享订单详情返回无效数据:', response)\n            this.setSharingOrderDetail(null)\n          }\n        } else {\n          console.warn('[SharingStore] 获取分享订单详情返回空响应')\n          this.setSharingOrderDetail(null)\n        }\n\n        return response\n      } catch (error) {\n        console.error('[SharingStore] 获取分享订单详情失败:', error)\n        showError(error.message || '获取分享订单详情失败')\n        throw error\n      } finally {\n        this.setLoading(false)\n      }\n    },\n\n    // 通过主订单ID获取分享订单详情\n    async getOrderDetailByMainOrderId(mainOrderId) {\n      try {\n        console.log('[SharingStore] 通过主订单ID获取分享订单详情:', mainOrderId)\n        this.setLoading(true)\n\n        const response = await sharingApi.getSharingOrderByMainOrderId(mainOrderId)\n\n        console.log('[SharingStore] 主订单API响应详情:', response)\n\n        if (response) {\n          // 检查是否是错误响应（包含message字段）\n          if (response.message && !response.id) {\n            console.warn('[SharingStore] 通过主订单ID获取分享订单详情失败:', response.message)\n            this.setSharingOrderDetail(null)\n          } else if (response.id) {\n            // 如果响应包含id字段，说明是有效的拼场订单数据\n            this.setSharingOrderDetail(response)\n            console.log('[SharingStore] 通过主订单ID获取分享订单详情成功:', response)\n            return response.id // 返回拼场订单ID\n          } else {\n            console.warn('[SharingStore] 通过主订单ID获取分享订单详情返回无效数据:', response)\n            this.setSharingOrderDetail(null)\n          }\n        } else {\n          console.warn('[SharingStore] 通过主订单ID获取分享订单详情返回空响应')\n          this.setSharingOrderDetail(null)\n        }\n\n        return response\n      } catch (error) {\n        console.error('[SharingStore] 通过主订单ID获取分享订单详情失败:', error)\n        showError(error.message || '获取分享订单详情失败')\n        throw error\n      } finally {\n        this.setLoading(false)\n      }\n    },\n\n    // 创建分享订单\n    async createOrder(orderData) {\n      try {\n        console.log('[SharingStore] 开始创建分享订单')\n        this.setLoading(true)\n\n        const response = await sharingApi.createSharingOrder(orderData)\n\n        if (response && response.data) {\n          // 创建成功后，刷新我的订单列表\n          await this.getMyOrders()\n          showSuccess('分享订单创建成功')\n          console.log('[SharingStore] 分享订单创建成功')\n        }\n\n        return response\n      } catch (error) {\n        console.error('[SharingStore] 创建分享订单失败:', error)\n        showError(error.message || '创建分享订单失败')\n        throw error\n      } finally {\n        this.setLoading(false)\n      }\n    },\n\n    // 创建拼场订单（别名方法，用于测试兼容性）\n    async createSharingOrder(orderData) {\n      return await this.createOrder(orderData)\n    },\n\n    // 处理分享请求\n    async handleRequest({ requestId, action }) {\n      try {\n        console.log('[SharingStore] 开始处理分享请求:', { requestId, action })\n        this.setLoading(true)\n\n        const response = await sharingApi.handleSharedRequest(requestId, action)\n\n        if (response && response.success) {\n          // 处理成功后，刷新相关列表\n          await this.getReceivedRequestsList()\n          showSuccess(`请求${action === 'accept' ? '接受' : '拒绝'}成功`)\n          console.log('[SharingStore] 分享请求处理成功')\n        }\n\n        return response\n      } catch (error) {\n        console.error('[SharingStore] 处理分享请求失败:', error)\n        showError(error.message || '处理分享请求失败')\n        throw error\n      } finally {\n        this.setLoading(false)\n      }\n    },\n\n    // 处理拼场申请（新增方法，对应Vuex中的processSharingRequest）\n    async processSharingRequest({ requestId, action, reason = '' }) {\n      try {\n        console.log('[SharingStore] 开始处理拼场申请:', { requestId, action, reason })\n        this.setLoading(true)\n\n        const data = {\n          action: action, // 直接传递action参数：'approve' 或 'reject'\n          responseMessage: reason || ''\n        }\n\n        const response = await sharingApi.handleSharedRequest(requestId, data)\n\n        if (response && response.success) {\n          showSuccess(action === 'approve' ? '已同意拼场申请' : '已拒绝拼场申请')\n          console.log('[SharingStore] 拼场申请处理成功')\n\n          // 刷新相关列表\n          await this.getReceivedRequestsList()\n        }\n\n        return response\n      } catch (error) {\n        console.error('[SharingStore] 处理拼场申请失败:', error)\n\n        // 检查是否是需要支付的错误\n        if (error.needPayment) {\n          // 保留完整的错误信息，包括needPayment和orderId\n          const enhancedError = new Error(error.message || '处理拼场申请失败')\n          enhancedError.needPayment = error.needPayment\n          enhancedError.orderId = error.orderId\n          enhancedError.orderStatus = error.orderStatus\n          throw enhancedError\n        } else {\n          showError(error.message || '处理拼场申请失败')\n          throw error\n        }\n      } finally {\n        this.setLoading(false)\n      }\n    },\n\n    // 申请加入拼场订单（需要支付）\n    async applyJoinSharingOrder(orderId) {\n      try {\n        console.log('[SharingStore] 开始申请加入拼场订单:', orderId)\n        this.setLoading(true)\n\n        const response = await sharingApi.applyJoinSharingOrder(orderId)\n\n        if (response && response.success) {\n          console.log('[SharingStore] 申请加入拼场订单成功')\n          return response\n        } else {\n          throw new Error(response.message || '申请失败')\n        }\n      } catch (error) {\n        console.error('[SharingStore] 申请加入拼场订单失败:', error)\n        showError(error.message || '申请加入拼场订单失败')\n        throw error\n      } finally {\n        this.setLoading(false)\n      }\n    },\n\n    // 更新拼场设置\n    async updateSharingSettings({ sharingId, settings }) {\n      try {\n        console.log('[SharingStore] 开始更新拼场设置:', { sharingId, settings })\n        this.setLoading(true)\n\n        const response = await sharingApi.updateSharingSettings(sharingId, settings)\n\n        showSuccess('设置已更新')\n        console.log('[SharingStore] 拼场设置更新成功')\n\n        // 刷新订单详情\n        await this.getOrderDetail(sharingId)\n\n        return response\n      } catch (error) {\n        console.error('[SharingStore] 更新拼场设置失败:', error)\n        showError(error.message || '更新设置失败')\n        throw error\n      } finally {\n        this.setLoading(false)\n      }\n    },\n\n    // 申请拼场\n    async applySharingOrder({ orderId, data }) {\n      try {\n        console.log('[SharingStore] 开始申请拼场:', { orderId, data })\n        this.setLoading(true)\n\n        const response = await sharingApi.applySharedBooking(orderId, data)\n\n        // 不在这里显示消息，让前端页面根据响应状态决定显示内容\n        console.log('[SharingStore] 拼场申请成功')\n\n        return response\n      } catch (error) {\n        console.error('[SharingStore] 申请拼场失败:', error)\n        showError(error.message || '申请拼场失败')\n        throw error\n      } finally {\n        this.setLoading(false)\n      }\n    },\n\n    // 移除拼场参与者\n    async removeSharingParticipant({ sharingId, participantId }) {\n      try {\n        console.log('[SharingStore] 开始移除拼场参与者:', { sharingId, participantId })\n        this.setLoading(true)\n\n        const response = await sharingApi.removeSharingParticipant(sharingId, participantId)\n\n        if (response && response.success) {\n          showSuccess('参与者移除成功')\n          console.log('[SharingStore] 参与者移除成功')\n\n          // 刷新订单详情\n          await this.getOrderDetail(sharingId)\n        }\n\n        return response\n      } catch (error) {\n        console.error('[SharingStore] 移除参与者失败:', error)\n        showError(error.message || '移除参与者失败')\n        throw error\n      } finally {\n        this.setLoading(false)\n      }\n    },\n\n    // 确认拼场订单\n    async confirmSharingOrder(orderId) {\n      try {\n        console.log('[SharingStore] 开始确认拼场订单:', orderId)\n        this.setLoading(true)\n\n        const response = await sharingApi.confirmSharingOrder(orderId)\n\n        if (response && response.success) {\n          showSuccess('拼场订单确认成功')\n          console.log('[SharingStore] 拼场订单确认成功')\n\n          // 刷新相关列表\n          await this.getMyOrders()\n        }\n\n        return response\n      } catch (error) {\n        console.error('[SharingStore] 确认拼场订单失败:', error)\n        showError(error.message || '确认拼场订单失败')\n        throw error\n      } finally {\n        this.setLoading(false)\n      }\n    },\n\n    // 取消拼场订单\n    async cancelSharingOrder(orderId) {\n      try {\n        console.log('[SharingStore] 开始取消拼场订单:', orderId)\n        this.setLoading(true)\n\n        const response = await sharingApi.cancelSharingOrder(orderId)\n\n        if (response && response.success) {\n          showSuccess('拼场订单取消成功')\n          console.log('[SharingStore] 拼场订单取消成功')\n\n          // 刷新相关列表\n          await this.getMyOrders()\n        }\n\n        return response\n      } catch (error) {\n        console.error('[SharingStore] 取消拼场订单失败:', error)\n        showError(error.message || '取消拼场订单失败')\n        throw error\n      } finally {\n        this.setLoading(false)\n      }\n    },\n\n    // 清空订单详情\n    clearOrderDetail() {\n      this.sharingOrderDetail = null\n    },\n\n    // 重置分页\n    resetPagination() {\n      this.pagination = {\n        current: 1,\n        pageSize: 10,\n        total: 0,\n        totalPages: 1\n      }\n    }\n  }\n})\n"], "names": ["defineStore", "uni", "sharingApi.getJoinableSharingOrders", "showError", "sharingApi.getAllSharingOrders", "sharingApi.getMyCreatedSharingOrders", "sharingApi.getReceivedSharedRequests", "sharingApi.getMySharedRequests", "sharingApi.getSharingOrderById", "sharingApi.getSharingOrderByMainOrderId", "sharingApi.createSharingOrder", "showSuccess", "sharingApi.handleSharedRequest", "sharingApi.applyJoinSharingOrder", "sharingApi.updateSharingSettings", "sharingApi.applySharedBooking", "sharingApi.removeSharingParticipant", "sharingApi.confirmSharingOrder", "sharingApi.cancelSharingOrder"], "mappings": ";;;;AAIY,MAAC,kBAAkBA,cAAW,YAAC,WAAW;AAAA,EACpD,OAAO,OAAO;AAAA,IACZ,eAAe,CAAE;AAAA,IACjB,iBAAiB,CAAE;AAAA,IACnB,kBAAkB,CAAE;AAAA,IACpB,cAAc,CAAE;AAAA,IAChB,oBAAoB;AAAA,IACpB,SAAS;AAAA,IACT,YAAY;AAAA,MACV,SAAS;AAAA,MACT,UAAU;AAAA,MACV,OAAO;AAAA,MACP,YAAY;AAAA,IACb;AAAA,EACL;AAAA,EAEE,SAAS;AAAA;AAAA,IAEP,qBAAqB,CAAC,UAAU,MAAM;AAAA,IACtC,uBAAuB,CAAC,UAAU,MAAM;AAAA,IACxC,wBAAwB,CAAC,UAAU,MAAM;AAAA,IACzC,oBAAoB,CAAC,UAAU,MAAM;AAAA,IACrC,0BAA0B,CAAC,UAAU,MAAM;AAAA,IAC3C,WAAW,CAAC,UAAU,MAAM;AAAA,IAC5B,eAAe,CAAC,UAAU,MAAM;AAAA;AAAA,IAGhC,oBAAoB,CAAC,UAAU,MAAM,cAAc;AAAA,IACnD,sBAAsB,CAAC,UAAU,MAAM,gBAAgB;AAAA,IACvD,uBAAuB,CAAC,UAAU,MAAM,iBAAiB;AAAA,IACzD,mBAAmB,CAAC,UAAU,MAAM,aAAa;AAAA;AAAA,IAGjD,mBAAmB,CAAC,UAAU,CAAC,WAAW;AACxC,aAAO,MAAM,cAAc,OAAO,WAAS,MAAM,WAAW,MAAM;AAAA,IACnE;AAAA;AAAA,IAGD,oBAAoB,CAAC,UAAU;AAC7B,aAAO,MAAM,iBAAiB,OAAO,aAAW,QAAQ,WAAW,SAAS;AAAA,IAC7E;AAAA;AAAA,IAGD,aAAa,CAAC,UAAU;AACtB,aAAO,MAAM,WAAW,UAAU,MAAM,WAAW;AAAA,IACpD;AAAA,EACF;AAAA,EAED,SAAS;AAAA;AAAA,IAEP,WAAW,SAAS;AAClB,WAAK,UAAU;AAAA,IAChB;AAAA;AAAA,IAGD,iBAAiB,QAAQ;AACvB,WAAK,gBAAgB,MAAM,QAAQ,MAAM,IAAI,SAAS,CAAE;AAAA,IACzD;AAAA;AAAA,IAGD,mBAAmB,QAAQ;AACzB,WAAK,kBAAkB,MAAM,QAAQ,MAAM,IAAI,SAAS,CAAE;AAAA,IAC3D;AAAA;AAAA,IAGD,oBAAoB,UAAU;AAC5B,WAAK,mBAAmB,MAAM,QAAQ,QAAQ,IAAI,WAAW,CAAE;AAAA,IAChE;AAAA;AAAA,IAGD,gBAAgB,UAAU;AACxB,WAAK,eAAe,MAAM,QAAQ,QAAQ,IAAI,WAAW,CAAE;AAAA,IAC5D;AAAA;AAAA,IAGD,sBAAsB,OAAO;AAC3B,WAAK,qBAAqB;AAAA,IAC3B;AAAA;AAAA,IAGD,cAAc,YAAY;AACxB,WAAK,aAAa,EAAE,GAAG,KAAK,YAAY,GAAG,WAAY;AAAA,IACxD;AAAA;AAAA,IAGD,kBAAkB,EAAE,SAAS,UAAU;AACrC,YAAM,QAAQ,KAAK,cAAc,KAAK,OAAK,EAAE,OAAO,OAAO;AAC3D,UAAI,OAAO;AACT,cAAM,SAAS;AAAA,MAChB;AAED,YAAM,UAAU,KAAK,gBAAgB,KAAK,OAAK,EAAE,OAAO,OAAO;AAC/D,UAAI,SAAS;AACX,gBAAQ,SAAS;AAAA,MAClB;AAAA,IACF;AAAA;AAAA,IAGD,MAAM,qBAAqB,SAAS,IAAI;AACtC,UAAI;AACFC,sBAAAA,+CAAY,iCAAiC,MAAM;AACnD,aAAK,WAAW,IAAI;AAEpB,cAAM,WAAW,MAAMC,YAAmC,yBAAC,MAAM;AAEjE,YAAI,YAAY,SAAS,MAAM;AAC7B,gBAAM,SAAS,MAAM,QAAQ,SAAS,IAAI,IAAI,SAAS,OAAO,CAAE;AAChE,eAAK,iBAAiB,MAAM;AAE5B,cAAI,SAAS,YAAY;AACvB,iBAAK,cAAc,SAAS,UAAU;AAAA,UACvC;AAEDD,wBAAY,MAAA,MAAA,OAAA,4BAAA,8BAA8B,OAAO,QAAQ,GAAG;AAAA,QAC7D;AAED,eAAO;AAAA,MACR,SAAQ,OAAO;AACdA,sBAAAA,MAAc,MAAA,SAAA,4BAAA,8BAA8B,KAAK;AACjDE,2BAAU,MAAM,WAAW,YAAY;AACvC,cAAM;AAAA,MACd,UAAgB;AACR,aAAK,WAAW,KAAK;AAAA,MACtB;AAAA,IACF;AAAA;AAAA,IAGD,MAAM,yBAAyB,SAAS,IAAI;AAC1C,UAAI;AACFF,sBAAAA,MAAY,MAAA,OAAA,4BAAA,mCAAmC,MAAM;AACrD,aAAK,WAAW,IAAI;AAEpB,cAAM,WAAW,MAAMC,YAAmC,yBAAC,MAAM;AAEjE,YAAI,UAAU;AAEZ,gBAAM,SAAS,SAAS,QAAQ,SAAS,QAAQ,CAAE;AAEnDD,wBAAAA,MAAA,MAAA,OAAA,4BAAY,mCAAmC;AAAA,YAC7C,SAAS,CAAC,CAAC,SAAS;AAAA,YACpB,SAAS,CAAC,CAAC,SAAS;AAAA,YACpB,cAAc,OAAO;AAAA,YACrB,YAAY,SAAS;AAAA,UACjC,CAAW;AAGD,cAAI,OAAO,WAAW,OAAO,SAAS,GAAG;AACvC,iBAAK,iBAAiB,MAAM;AAAA,UACxC,OAAiB;AACL,iBAAK,cAAc,KAAK,GAAG,MAAM;AAAA,UAClC;AAED,cAAI,SAAS,YAAY;AACvB,iBAAK,cAAc,SAAS,UAAU;AAAA,UACvC;AAEDA,wBAAY,MAAA,MAAA,OAAA,4BAAA,+BAA+B,OAAO,QAAQ,GAAG;AAAA,QAC9D;AAED,eAAO;AAAA,MACR,SAAQ,OAAO;AACdA,sBAAAA,iDAAc,+BAA+B,KAAK;AAClDE,2BAAU,MAAM,WAAW,aAAa;AACxC,cAAM;AAAA,MACd,UAAgB;AACR,aAAK,WAAW,KAAK;AAAA,MACtB;AAAA,IACF;AAAA;AAAA,IAGD,MAAM,oBAAoB,SAAS,IAAI;AACrC,UAAI;AACFF,sBAAAA,+CAAY,iCAAiC,MAAM;AACnD,aAAK,WAAW,IAAI;AAGpB,cAAM,iBAAiB,IAAI,QAAQ,CAAC,GAAG,WAAW;AAChD,qBAAW,MAAM,OAAO,IAAI,MAAM,MAAM,CAAC,GAAG,GAAK;AAAA,QAC3D,CAAS;AAED,cAAM,aAAaG,YAA8B,oBAAC,MAAM;AACxD,cAAM,WAAW,MAAM,QAAQ,KAAK,CAAC,YAAY,cAAc,CAAC;AAEhE,YAAI,UAAU;AAEZ,gBAAM,SAAS,SAAS,QAAQ,SAAS,QAAQ,CAAE;AAEnDH,wBAAAA,MAAA,MAAA,OAAA,4BAAY,4BAA4B;AAAA,YACtC,SAAS,CAAC,CAAC,SAAS;AAAA,YACpB,SAAS,CAAC,CAAC,SAAS;AAAA,YACpB,cAAc,OAAO;AAAA,YACrB,YAAY,SAAS;AAAA,UACjC,CAAW;AAGD,cAAI,OAAO,WAAW,OAAO,SAAS,GAAG;AACvC,iBAAK,iBAAiB,MAAM;AAAA,UACxC,OAAiB;AACL,iBAAK,cAAc,KAAK,GAAG,MAAM;AAAA,UAClC;AAED,cAAI,SAAS,YAAY;AACvB,iBAAK,cAAc,SAAS,UAAU;AAAA,UACvC;AAEDA,wBAAY,MAAA,MAAA,OAAA,4BAAA,8BAA8B,OAAO,QAAQ,GAAG;AAAA,QACtE,OAAe;AACLA,wBAAAA,MAAa,MAAA,QAAA,4BAAA,8BAA8B;AAE3C,cAAI,OAAO,WAAW,OAAO,SAAS,GAAG;AACvC,iBAAK,iBAAiB,EAAE;AAAA,UACzB;AAAA,QACF;AAED,eAAO;AAAA,MACR,SAAQ,OAAO;AACdA,sBAAAA,MAAc,MAAA,SAAA,4BAAA,8BAA8B,KAAK;AAGjD,YAAI,MAAM,YAAY,QAAQ;AAC5BE,mBAAAA,UAAU,cAAc;AAAA,QAClC,OAAe;AACLA,6BAAU,MAAM,WAAW,YAAY;AAAA,QACxC;AAGD,YAAI,OAAO,WAAW,OAAO,SAAS,GAAG;AACvC,eAAK,iBAAiB,EAAE;AAAA,QACzB;AAED,cAAM;AAAA,MACd,UAAgB;AACR,aAAK,WAAW,KAAK;AAAA,MACtB;AAAA,IACF;AAAA;AAAA,IAGD,MAAM,YAAY,SAAS,IAAI;AAC7B,UAAI;AACFF,sBAAAA,MAAY,MAAA,OAAA,4BAAA,2BAA2B;AACvC,aAAK,WAAW,IAAI;AAEpB,cAAM,WAAW,MAAMI,YAAoC,0BAAC,MAAM;AAElE,YAAI,YAAY,SAAS,MAAM;AAC7B,gBAAM,SAAS,MAAM,QAAQ,SAAS,IAAI,IAAI,SAAS,OAAO,CAAE;AAChE,eAAK,mBAAmB,MAAM;AAC9BJ,wBAAY,MAAA,MAAA,OAAA,4BAAA,8BAA8B,OAAO,QAAQ,GAAG;AAAA,QAC7D;AAED,eAAO;AAAA,MACR,SAAQ,OAAO;AACdA,sBAAAA,MAAc,MAAA,SAAA,4BAAA,8BAA8B,KAAK;AACjDE,2BAAU,MAAM,WAAW,YAAY;AACvC,cAAM;AAAA,MACd,UAAgB;AACR,aAAK,WAAW,KAAK;AAAA,MACtB;AAAA,IACF;AAAA;AAAA,IAGD,MAAM,wBAAwB,SAAS,IAAI;AACzC,UAAI;AACFF,sBAAAA,MAAY,MAAA,OAAA,4BAAA,0BAA0B;AACtC,aAAK,WAAW,IAAI;AAEpB,cAAM,WAAW,MAAMK,YAAoC,0BAAC,MAAM;AAElE,YAAI,YAAY,SAAS,MAAM;AAC7B,gBAAM,WAAW,MAAM,QAAQ,SAAS,IAAI,IAAI,SAAS,OAAO,CAAE;AAClE,eAAK,oBAAoB,QAAQ;AACjCL,wBAAY,MAAA,MAAA,OAAA,4BAAA,6BAA6B,SAAS,QAAQ,GAAG;AAAA,QAC9D;AAED,eAAO;AAAA,MACR,SAAQ,OAAO;AACdA,sBAAAA,MAAc,MAAA,SAAA,4BAAA,6BAA6B,KAAK;AAChDE,2BAAU,MAAM,WAAW,WAAW;AACtC,cAAM;AAAA,MACd,UAAgB;AACR,aAAK,WAAW,KAAK;AAAA,MACtB;AAAA,IACF;AAAA;AAAA,IAGD,MAAM,oBAAoB,SAAS,IAAI;AACrC,UAAI;AACFF,sBAAAA,MAAY,MAAA,OAAA,4BAAA,0BAA0B;AACtC,aAAK,WAAW,IAAI;AAEpB,cAAM,WAAW,MAAMM,YAA8B,oBAAC,MAAM;AAE5D,YAAI,YAAY,SAAS,MAAM;AAC7B,gBAAM,WAAW,MAAM,QAAQ,SAAS,IAAI,IAAI,SAAS,OAAO,CAAE;AAClE,eAAK,gBAAgB,QAAQ;AAC7BN,wBAAY,MAAA,MAAA,OAAA,4BAAA,6BAA6B,SAAS,QAAQ,GAAG;AAAA,QAC9D;AAED,eAAO;AAAA,MACR,SAAQ,OAAO;AACdA,sBAAAA,MAAc,MAAA,SAAA,4BAAA,6BAA6B,KAAK;AAChDE,2BAAU,MAAM,WAAW,WAAW;AACtC,cAAM;AAAA,MACd,UAAgB;AACR,aAAK,WAAW,KAAK;AAAA,MACtB;AAAA,IACF;AAAA;AAAA,IAGD,MAAM,eAAe,SAAS,eAAe,OAAO;AAClD,UAAI;AACFF,4BAAY,MAAA,OAAA,4BAAA,8BAA8B,SAAS,eAAe,WAAW,EAAE;AAC/E,aAAK,WAAW,IAAI;AAGpB,YAAI,cAAc;AAChB,eAAK,qBAAqB;AAAA,QAC3B;AAED,cAAM,WAAW,MAAMO,YAA8B,oBAAC,OAAO;AAE7DP,sBAAAA,MAAY,MAAA,OAAA,4BAAA,2BAA2B,QAAQ;AAE/C,YAAI,UAAU;AAEZ,cAAI,SAAS,WAAW,CAAC,SAAS,IAAI;AACpCA,0BAAa,MAAA,MAAA,QAAA,4BAAA,8BAA8B,SAAS,OAAO;AAC3D,iBAAK,sBAAsB,IAAI;AAAA,UAC3C,WAAqB,SAAS,IAAI;AAEtB,iBAAK,sBAAsB,QAAQ;AACnCA,0BAAAA,+CAAY,8BAA8B,QAAQ;AAAA,UAC9D,OAAiB;AACLA,0BAAAA,MAAA,MAAA,QAAA,4BAAa,kCAAkC,QAAQ;AACvD,iBAAK,sBAAsB,IAAI;AAAA,UAChC;AAAA,QACX,OAAe;AACLA,wBAAAA,MAAa,MAAA,QAAA,4BAAA,8BAA8B;AAC3C,eAAK,sBAAsB,IAAI;AAAA,QAChC;AAED,eAAO;AAAA,MACR,SAAQ,OAAO;AACdA,sBAAAA,MAAc,MAAA,SAAA,4BAAA,8BAA8B,KAAK;AACjDE,2BAAU,MAAM,WAAW,YAAY;AACvC,cAAM;AAAA,MACd,UAAgB;AACR,aAAK,WAAW,KAAK;AAAA,MACtB;AAAA,IACF;AAAA;AAAA,IAGD,MAAM,4BAA4B,aAAa;AAC7C,UAAI;AACFF,sBAAAA,MAAA,MAAA,OAAA,4BAAY,mCAAmC,WAAW;AAC1D,aAAK,WAAW,IAAI;AAEpB,cAAM,WAAW,MAAMQ,YAAuC,6BAAC,WAAW;AAE1ER,sBAAAA,+CAAY,8BAA8B,QAAQ;AAElD,YAAI,UAAU;AAEZ,cAAI,SAAS,WAAW,CAAC,SAAS,IAAI;AACpCA,0BAAA,MAAA,MAAA,QAAA,4BAAa,qCAAqC,SAAS,OAAO;AAClE,iBAAK,sBAAsB,IAAI;AAAA,UAC3C,WAAqB,SAAS,IAAI;AAEtB,iBAAK,sBAAsB,QAAQ;AACnCA,0BAAAA,MAAY,MAAA,OAAA,4BAAA,qCAAqC,QAAQ;AACzD,mBAAO,SAAS;AAAA,UAC5B,OAAiB;AACLA,0BAAAA,MAAA,MAAA,QAAA,4BAAa,yCAAyC,QAAQ;AAC9D,iBAAK,sBAAsB,IAAI;AAAA,UAChC;AAAA,QACX,OAAe;AACLA,wBAAAA,MAAA,MAAA,QAAA,4BAAa,qCAAqC;AAClD,eAAK,sBAAsB,IAAI;AAAA,QAChC;AAED,eAAO;AAAA,MACR,SAAQ,OAAO;AACdA,sBAAAA,MAAc,MAAA,SAAA,4BAAA,qCAAqC,KAAK;AACxDE,2BAAU,MAAM,WAAW,YAAY;AACvC,cAAM;AAAA,MACd,UAAgB;AACR,aAAK,WAAW,KAAK;AAAA,MACtB;AAAA,IACF;AAAA;AAAA,IAGD,MAAM,YAAY,WAAW;AAC3B,UAAI;AACFF,sBAAAA,MAAY,MAAA,OAAA,4BAAA,yBAAyB;AACrC,aAAK,WAAW,IAAI;AAEpB,cAAM,WAAW,MAAMS,YAA6B,mBAAC,SAAS;AAE9D,YAAI,YAAY,SAAS,MAAM;AAE7B,gBAAM,KAAK,YAAa;AACxBC,mBAAAA,YAAY,UAAU;AACtBV,wBAAAA,+CAAY,yBAAyB;AAAA,QACtC;AAED,eAAO;AAAA,MACR,SAAQ,OAAO;AACdA,sBAAAA,MAAc,MAAA,SAAA,4BAAA,4BAA4B,KAAK;AAC/CE,2BAAU,MAAM,WAAW,UAAU;AACrC,cAAM;AAAA,MACd,UAAgB;AACR,aAAK,WAAW,KAAK;AAAA,MACtB;AAAA,IACF;AAAA;AAAA,IAGD,MAAM,mBAAmB,WAAW;AAClC,aAAO,MAAM,KAAK,YAAY,SAAS;AAAA,IACxC;AAAA;AAAA,IAGD,MAAM,cAAc,EAAE,WAAW,UAAU;AACzC,UAAI;AACFF,sBAAY,MAAA,MAAA,OAAA,4BAAA,4BAA4B,EAAE,WAAW,QAAQ;AAC7D,aAAK,WAAW,IAAI;AAEpB,cAAM,WAAW,MAAMW,gCAA+B,WAAW,MAAM;AAEvE,YAAI,YAAY,SAAS,SAAS;AAEhC,gBAAM,KAAK,wBAAyB;AACpCD,mBAAW,YAAC,KAAK,WAAW,WAAW,OAAO,IAAI,IAAI;AACtDV,wBAAAA,+CAAY,yBAAyB;AAAA,QACtC;AAED,eAAO;AAAA,MACR,SAAQ,OAAO;AACdA,sBAAAA,MAAc,MAAA,SAAA,4BAAA,4BAA4B,KAAK;AAC/CE,2BAAU,MAAM,WAAW,UAAU;AACrC,cAAM;AAAA,MACd,UAAgB;AACR,aAAK,WAAW,KAAK;AAAA,MACtB;AAAA,IACF;AAAA;AAAA,IAGD,MAAM,sBAAsB,EAAE,WAAW,QAAQ,SAAS,GAAE,GAAI;AAC9D,UAAI;AACFF,4BAAA,MAAA,OAAA,4BAAY,4BAA4B,EAAE,WAAW,QAAQ,QAAQ;AACrE,aAAK,WAAW,IAAI;AAEpB,cAAM,OAAO;AAAA,UACX;AAAA;AAAA,UACA,iBAAiB,UAAU;AAAA,QAC5B;AAED,cAAM,WAAW,MAAMW,gCAA+B,WAAW,IAAI;AAErE,YAAI,YAAY,SAAS,SAAS;AAChCD,mBAAAA,YAAY,WAAW,YAAY,YAAY,SAAS;AACxDV,wBAAAA,+CAAY,yBAAyB;AAGrC,gBAAM,KAAK,wBAAyB;AAAA,QACrC;AAED,eAAO;AAAA,MACR,SAAQ,OAAO;AACdA,sBAAAA,MAAc,MAAA,SAAA,4BAAA,4BAA4B,KAAK;AAG/C,YAAI,MAAM,aAAa;AAErB,gBAAM,gBAAgB,IAAI,MAAM,MAAM,WAAW,UAAU;AAC3D,wBAAc,cAAc,MAAM;AAClC,wBAAc,UAAU,MAAM;AAC9B,wBAAc,cAAc,MAAM;AAClC,gBAAM;AAAA,QAChB,OAAe;AACLE,6BAAU,MAAM,WAAW,UAAU;AACrC,gBAAM;AAAA,QACP;AAAA,MACT,UAAgB;AACR,aAAK,WAAW,KAAK;AAAA,MACtB;AAAA,IACF;AAAA;AAAA,IAGD,MAAM,sBAAsB,SAAS;AACnC,UAAI;AACFF,sBAAAA,+CAAY,8BAA8B,OAAO;AACjD,aAAK,WAAW,IAAI;AAEpB,cAAM,WAAW,MAAMY,YAAgC,sBAAC,OAAO;AAE/D,YAAI,YAAY,SAAS,SAAS;AAChCZ,wBAAAA,MAAA,MAAA,OAAA,4BAAY,2BAA2B;AACvC,iBAAO;AAAA,QACjB,OAAe;AACL,gBAAM,IAAI,MAAM,SAAS,WAAW,MAAM;AAAA,QAC3C;AAAA,MACF,SAAQ,OAAO;AACdA,sBAAAA,MAAc,MAAA,SAAA,4BAAA,8BAA8B,KAAK;AACjDE,2BAAU,MAAM,WAAW,YAAY;AACvC,cAAM;AAAA,MACd,UAAgB;AACR,aAAK,WAAW,KAAK;AAAA,MACtB;AAAA,IACF;AAAA;AAAA,IAGD,MAAM,sBAAsB,EAAE,WAAW,YAAY;AACnD,UAAI;AACFF,sBAAY,MAAA,MAAA,OAAA,4BAAA,4BAA4B,EAAE,WAAW,UAAU;AAC/D,aAAK,WAAW,IAAI;AAEpB,cAAM,WAAW,MAAMa,kCAAiC,WAAW,QAAQ;AAE3EH,iBAAAA,YAAY,OAAO;AACnBV,sBAAAA,MAAY,MAAA,OAAA,4BAAA,yBAAyB;AAGrC,cAAM,KAAK,eAAe,SAAS;AAEnC,eAAO;AAAA,MACR,SAAQ,OAAO;AACdA,sBAAAA,MAAc,MAAA,SAAA,4BAAA,4BAA4B,KAAK;AAC/CE,2BAAU,MAAM,WAAW,QAAQ;AACnC,cAAM;AAAA,MACd,UAAgB;AACR,aAAK,WAAW,KAAK;AAAA,MACtB;AAAA,IACF;AAAA;AAAA,IAGD,MAAM,kBAAkB,EAAE,SAAS,QAAQ;AACzC,UAAI;AACFF,sBAAY,MAAA,MAAA,OAAA,4BAAA,0BAA0B,EAAE,SAAS,MAAM;AACvD,aAAK,WAAW,IAAI;AAEpB,cAAM,WAAW,MAAMc,+BAA8B,SAAS,IAAI;AAGlEd,sBAAAA,MAAA,MAAA,OAAA,4BAAY,uBAAuB;AAEnC,eAAO;AAAA,MACR,SAAQ,OAAO;AACdA,sBAAAA,MAAA,MAAA,SAAA,4BAAc,0BAA0B,KAAK;AAC7CE,2BAAU,MAAM,WAAW,QAAQ;AACnC,cAAM;AAAA,MACd,UAAgB;AACR,aAAK,WAAW,KAAK;AAAA,MACtB;AAAA,IACF;AAAA;AAAA,IAGD,MAAM,yBAAyB,EAAE,WAAW,iBAAiB;AAC3D,UAAI;AACFF,sBAAA,MAAA,MAAA,OAAA,4BAAY,6BAA6B,EAAE,WAAW,eAAe;AACrE,aAAK,WAAW,IAAI;AAEpB,cAAM,WAAW,MAAMe,qCAAoC,WAAW,aAAa;AAEnF,YAAI,YAAY,SAAS,SAAS;AAChCL,mBAAAA,YAAY,SAAS;AACrBV,wBAAAA,+CAAY,wBAAwB;AAGpC,gBAAM,KAAK,eAAe,SAAS;AAAA,QACpC;AAED,eAAO;AAAA,MACR,SAAQ,OAAO;AACdA,sBAAAA,MAAc,MAAA,SAAA,4BAAA,2BAA2B,KAAK;AAC9CE,2BAAU,MAAM,WAAW,SAAS;AACpC,cAAM;AAAA,MACd,UAAgB;AACR,aAAK,WAAW,KAAK;AAAA,MACtB;AAAA,IACF;AAAA;AAAA,IAGD,MAAM,oBAAoB,SAAS;AACjC,UAAI;AACFF,sBAAAA,MAAY,MAAA,OAAA,4BAAA,4BAA4B,OAAO;AAC/C,aAAK,WAAW,IAAI;AAEpB,cAAM,WAAW,MAAMgB,YAA8B,oBAAC,OAAO;AAE7D,YAAI,YAAY,SAAS,SAAS;AAChCN,mBAAAA,YAAY,UAAU;AACtBV,wBAAAA,+CAAY,yBAAyB;AAGrC,gBAAM,KAAK,YAAa;AAAA,QACzB;AAED,eAAO;AAAA,MACR,SAAQ,OAAO;AACdA,sBAAAA,MAAc,MAAA,SAAA,4BAAA,4BAA4B,KAAK;AAC/CE,2BAAU,MAAM,WAAW,UAAU;AACrC,cAAM;AAAA,MACd,UAAgB;AACR,aAAK,WAAW,KAAK;AAAA,MACtB;AAAA,IACF;AAAA;AAAA,IAGD,MAAM,mBAAmB,SAAS;AAChC,UAAI;AACFF,sBAAAA,MAAY,MAAA,OAAA,4BAAA,4BAA4B,OAAO;AAC/C,aAAK,WAAW,IAAI;AAEpB,cAAM,WAAW,MAAMiB,YAA6B,mBAAC,OAAO;AAE5D,YAAI,YAAY,SAAS,SAAS;AAChCP,mBAAAA,YAAY,UAAU;AACtBV,wBAAAA,+CAAY,yBAAyB;AAGrC,gBAAM,KAAK,YAAa;AAAA,QACzB;AAED,eAAO;AAAA,MACR,SAAQ,OAAO;AACdA,sBAAAA,MAAc,MAAA,SAAA,4BAAA,4BAA4B,KAAK;AAC/CE,2BAAU,MAAM,WAAW,UAAU;AACrC,cAAM;AAAA,MACd,UAAgB;AACR,aAAK,WAAW,KAAK;AAAA,MACtB;AAAA,IACF;AAAA;AAAA,IAGD,mBAAmB;AACjB,WAAK,qBAAqB;AAAA,IAC3B;AAAA;AAAA,IAGD,kBAAkB;AAChB,WAAK,aAAa;AAAA,QAChB,SAAS;AAAA,QACT,UAAU;AAAA,QACV,OAAO;AAAA,QACP,YAAY;AAAA,MACb;AAAA,IACF;AAAA,EACF;AACH,CAAC;;"}