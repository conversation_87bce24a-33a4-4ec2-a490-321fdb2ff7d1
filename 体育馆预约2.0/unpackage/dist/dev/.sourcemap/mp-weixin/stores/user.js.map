{"version": 3, "file": "user.js", "sources": ["stores/user.js"], "sourcesContent": ["import { defineStore } from 'pinia'\nimport * as authApi from '@/api/auth.js'\nimport * as userApi from '@/api/user.js'\nimport { setToken, removeToken, setUserInfo, removeUserInfo, getUserInfo, getToken } from '@/utils/auth.js'\nimport { showSuccess, showError } from '@/utils/ui.js'\nimport { clearAuthCache, updateAuthCache } from '@/utils/router-guard-new.js'\n\nexport const useUserStore = defineStore('user', {\n  state: () => ({\n    token: getToken(),\n    userInfo: getUserInfo(),\n    userStats: {\n      totalBookings: 0,\n      totalSharings: 0\n    },\n    isLoggedIn: !!(getToken() && getUserInfo()),\n    loginChecking: false // 是否正在检查登录状态\n  }),\n\n  getters: {\n    // Token相关\n    getToken: (state) => state.token,\n\n    // 用户信息相关\n    userInfoGetter: (state) => state.userInfo,\n    getIsLoggedIn: (state) => state.isLoggedIn,\n    userId: (state) => state.userInfo?.id,\n    username: (state) => state.userInfo?.username,\n    nickname: (state) => state.userInfo?.nickname || state.userInfo?.username,\n    avatar: (state) => state.userInfo?.avatar,\n    phone: (state) => state.userInfo?.phone,\n    email: (state) => state.userInfo?.email,\n\n    // 统计信息\n    totalBookings: (state) => state.userStats.totalBookings,\n    totalSharings: (state) => state.userStats.totalSharings,\n\n    // 状态检查\n    isLoginChecking: (state) => state.loginChecking\n  },\n\n  actions: {\n    // 初始化用户状态（从本地存储恢复）\n    initUserState() {\n      console.log('[UserStore] 初始化用户状态')\n      const token = getToken()\n      const userInfo = getUserInfo()\n\n      if (token && userInfo) {\n        this.token = token\n        this.userInfo = userInfo\n        this.isLoggedIn = true\n        console.log('[UserStore] 从本地存储恢复用户状态成功')\n      } else {\n        console.log('[UserStore] 本地存储中无有效用户状态')\n        this.logout()\n      }\n    },\n\n    // 设置token\n    setToken(token) {\n      this.token = token\n      setToken(token)\n    },\n    \n    // 设置用户信息\n    setUserInfo(userInfo) {\n      this.userInfo = userInfo\n      setUserInfo(userInfo)\n    },\n    \n    // 设置登录状态\n    setLoginStatus(status) {\n      console.log('[UserStore] 设置登录状态:', status)\n      this.isLoggedIn = status\n      // 同步更新路由守卫缓存\n      updateAuthCache(status)\n    },\n    \n    // 设置登录检查状态\n    setLoginChecking(checking) {\n      this.loginChecking = checking\n    },\n    \n    // 设置用户统计\n    setUserStats(stats) {\n      this.userStats = stats\n    },\n    \n    // 清除用户数据\n    clearUserData() {\n      console.log('[UserStore] 清除用户数据')\n      this.token = ''\n      this.userInfo = null\n      this.userStats = {\n        totalBookings: 0,\n        totalSharings: 0\n      }\n      this.isLoggedIn = false\n      this.loginChecking = false\n      removeToken()\n      removeUserInfo()\n      // 清除路由守卫缓存\n      clearAuthCache()\n    },\n\n    // 用户登录\n    async login(loginData) {\n      try {\n        console.log('[UserStore] 开始登录')\n        const response = await authApi.login(loginData)\n        console.log('[UserStore] 登录响应:', response)\n        \n        if (!response) {\n          throw new Error('登录响应为空')\n        }\n        \n        const responseData = response.data || response\n        const token = responseData.accessToken || responseData.token\n        \n        if (!token) {\n          console.error('[UserStore] 响应数据:', responseData)\n          throw new Error('未获取到登录令牌')\n        }\n        \n        const user = {\n          id: responseData.id,\n          username: responseData.username,\n          email: responseData.email,\n          phone: responseData.phone,\n          nickname: responseData.nickname,\n          avatar: responseData.avatar,\n          roles: responseData.roles\n        }\n        \n        // 更新状态\n        this.setToken(token)\n        this.setUserInfo(user)\n        this.setLoginStatus(true)\n        \n        console.log('[UserStore] 登录成功，用户信息:', user)\n        return response\n      } catch (error) {\n        console.error('[UserStore] 登录错误:', error)\n        this.setLoginStatus(false)\n        throw error\n      }\n    },\n\n    // 获取用户信息\n    async getUserInfo() {\n      try {\n        console.log('[UserStore] 开始获取用户信息')\n\n        if (!this.token) {\n          throw new Error('用户未登录')\n        }\n\n        const response = await userApi.getUserInfo()\n        console.log('[UserStore] 用户信息获取成功:', response)\n\n        const userInfo = response.data || response\n        this.setUserInfo(userInfo)\n\n        return userInfo\n      } catch (error) {\n        console.error('[UserStore] 获取用户信息失败:', error)\n        // 如果是认证错误，清除用户数据\n        if (error.message?.includes('401') || error.message?.includes('未登录')) {\n          this.clearUserData()\n        }\n        throw error\n      }\n    },\n\n    // 更新用户信息\n    async updateUserInfo(updateData) {\n      try {\n        console.log('[UserStore] 开始更新用户信息:', updateData)\n\n        const response = await userApi.updateUserInfo(updateData)\n        console.log('[UserStore] 用户信息更新成功:', response)\n\n        const updatedUserInfo = response.data || response\n        this.setUserInfo(updatedUserInfo)\n\n        showSuccess('用户信息更新成功')\n        return updatedUserInfo\n      } catch (error) {\n        console.error('[UserStore] 更新用户信息失败:', error)\n        showError(error.message || '更新用户信息失败')\n        throw error\n      }\n    },\n\n    // 用户注册\n    async register(registerData) {\n      try {\n        console.log('[UserStore] 开始注册')\n        const response = await authApi.register(registerData)\n        console.log('[UserStore] 注册响应:', response)\n        \n        showSuccess('注册成功')\n        return response\n      } catch (error) {\n        console.error('[UserStore] 注册错误:', error)\n        showError(error.message || '注册失败')\n        throw error\n      }\n    },\n\n    // 用户退出\n    async logout() {\n      try {\n        console.log('[UserStore] 开始退出登录')\n        \n        // 调用后端退出接口\n        try {\n          await authApi.logout()\n        } catch (error) {\n          console.warn('[UserStore] 后端退出接口调用失败:', error)\n          // 即使后端接口失败，也继续清除本地数据\n        }\n        \n        // 清除本地数据\n        this.clearUserData()\n        \n        console.log('[UserStore] 退出登录成功')\n        showSuccess('退出成功')\n        \n        // 跳转到登录页\n        uni.reLaunch({\n          url: '/pages/user/login'\n        })\n        \n      } catch (error) {\n        console.error('[UserStore] 退出登录错误:', error)\n        // 即使出错也要清除本地数据\n        this.clearUserData()\n        throw error\n      }\n    },\n\n    // 检查登录状态\n    async checkLoginStatus() {\n      if (this.loginChecking) {\n        console.log('[UserStore] 正在检查登录状态，跳过重复检查')\n        return this.isLoggedIn\n      }\n      \n      this.setLoginChecking(true)\n      \n      try {\n        console.log('[UserStore] 开始检查登录状态')\n        \n        const token = getToken()\n        const userInfo = getUserInfo()\n        \n        if (!token || !userInfo) {\n          console.log('[UserStore] 本地无登录信息')\n          this.setLoginStatus(false)\n          return false\n        }\n        \n        // 验证token有效性\n        try {\n          const response = await userApi.getCurrentUser()\n          console.log('[UserStore] 用户信息验证成功:', response)\n          \n          // 更新用户信息\n          if (response && response.data) {\n            this.setUserInfo(response.data)\n          }\n          \n          this.setLoginStatus(true)\n          return true\n        } catch (error) {\n          console.error('[UserStore] Token验证失败:', error)\n          // Token无效，清除本地数据\n          this.clearUserData()\n          return false\n        }\n        \n      } catch (error) {\n        console.error('[UserStore] 检查登录状态错误:', error)\n        this.setLoginStatus(false)\n        return false\n      } finally {\n        this.setLoginChecking(false)\n      }\n    },\n\n    // 获取用户统计信息\n    async getUserStats() {\n      try {\n        console.log('[UserStore] 获取用户统计信息')\n        const response = await userApi.getUserStats()\n        \n        if (response && response.data) {\n          this.setUserStats(response.data)\n          console.log('[UserStore] 用户统计信息更新成功:', response.data)\n        }\n        \n        return response\n      } catch (error) {\n        console.error('[UserStore] 获取用户统计信息失败:', error)\n        throw error\n      }\n    },\n\n    // 更新用户信息\n    async updateProfile(profileData) {\n      try {\n        console.log('[UserStore] 更新用户信息')\n        const response = await userApi.updateProfile(profileData)\n        \n        if (response && response.data) {\n          // 更新本地用户信息\n          this.setUserInfo({\n            ...this.userInfo,\n            ...response.data\n          })\n          console.log('[UserStore] 用户信息更新成功')\n          showSuccess('信息更新成功')\n        }\n        \n        return response\n      } catch (error) {\n        console.error('[UserStore] 更新用户信息失败:', error)\n        showError(error.message || '更新失败')\n        throw error\n      }\n    },\n\n    // 修改密码\n    async changePassword(passwordData) {\n      try {\n        console.log('[UserStore] 修改密码')\n        const response = await userApi.changePassword(passwordData)\n        \n        console.log('[UserStore] 密码修改成功')\n        showSuccess('密码修改成功')\n        \n        return response\n      } catch (error) {\n        console.error('[UserStore] 修改密码失败:', error)\n        showError(error.message || '密码修改失败')\n        throw error\n      }\n    }\n  }\n})\n"], "names": ["defineStore", "getToken", "getUserInfo", "uni", "setToken", "setUserInfo", "updateAuthCache", "removeToken", "removeUserInfo", "clearAuthCache", "authApi.login", "userApi.getUserInfo", "userApi.updateUserInfo", "showSuccess", "showError", "authApi.register", "authApi.logout", "userApi.getCurrentUser", "userApi.getUserStats", "userApi.updateProfile", "userApi.changePassword"], "mappings": ";;;;;;;AAOY,MAAC,eAAeA,cAAW,YAAC,QAAQ;AAAA,EAC9C,OAAO,OAAO;AAAA,IACZ,OAAOC,WAAAA,SAAU;AAAA,IACjB,UAAUC,WAAAA,YAAa;AAAA,IACvB,WAAW;AAAA,MACT,eAAe;AAAA,MACf,eAAe;AAAA,IAChB;AAAA,IACD,YAAY,CAAC,EAAED,oBAAU,KAAIC,WAAW,YAAA;AAAA,IACxC,eAAe;AAAA;AAAA,EACnB;AAAA,EAEE,SAAS;AAAA;AAAA,IAEP,UAAU,CAAC,UAAU,MAAM;AAAA;AAAA,IAG3B,gBAAgB,CAAC,UAAU,MAAM;AAAA,IACjC,eAAe,CAAC,UAAU,MAAM;AAAA,IAChC,QAAQ,CAAC;;AAAU,yBAAM,aAAN,mBAAgB;AAAA;AAAA,IACnC,UAAU,CAAC;;AAAU,yBAAM,aAAN,mBAAgB;AAAA;AAAA,IACrC,UAAU,CAAC;;AAAU,0BAAM,aAAN,mBAAgB,eAAY,WAAM,aAAN,mBAAgB;AAAA;AAAA,IACjE,QAAQ,CAAC;;AAAU,yBAAM,aAAN,mBAAgB;AAAA;AAAA,IACnC,OAAO,CAAC;;AAAU,yBAAM,aAAN,mBAAgB;AAAA;AAAA,IAClC,OAAO,CAAC;;AAAU,yBAAM,aAAN,mBAAgB;AAAA;AAAA;AAAA,IAGlC,eAAe,CAAC,UAAU,MAAM,UAAU;AAAA,IAC1C,eAAe,CAAC,UAAU,MAAM,UAAU;AAAA;AAAA,IAG1C,iBAAiB,CAAC,UAAU,MAAM;AAAA,EACnC;AAAA,EAED,SAAS;AAAA;AAAA,IAEP,gBAAgB;AACdC,oBAAAA,MAAY,MAAA,OAAA,wBAAA,qBAAqB;AACjC,YAAM,QAAQF,WAAAA,SAAU;AACxB,YAAM,WAAWC,WAAAA,YAAa;AAE9B,UAAI,SAAS,UAAU;AACrB,aAAK,QAAQ;AACb,aAAK,WAAW;AAChB,aAAK,aAAa;AAClBC,sBAAAA,MAAY,MAAA,OAAA,wBAAA,2BAA2B;AAAA,MAC/C,OAAa;AACLA,sBAAAA,MAAY,MAAA,OAAA,wBAAA,0BAA0B;AACtC,aAAK,OAAQ;AAAA,MACd;AAAA,IACF;AAAA;AAAA,IAGD,SAAS,OAAO;AACd,WAAK,QAAQ;AACbC,iBAAAA,SAAS,KAAK;AAAA,IACf;AAAA;AAAA,IAGD,YAAY,UAAU;AACpB,WAAK,WAAW;AAChBC,iBAAAA,YAAY,QAAQ;AAAA,IACrB;AAAA;AAAA,IAGD,eAAe,QAAQ;AACrBF,oBAAAA,MAAY,MAAA,OAAA,wBAAA,uBAAuB,MAAM;AACzC,WAAK,aAAa;AAElBG,2BAAAA,gBAAgB,MAAM;AAAA,IACvB;AAAA;AAAA,IAGD,iBAAiB,UAAU;AACzB,WAAK,gBAAgB;AAAA,IACtB;AAAA;AAAA,IAGD,aAAa,OAAO;AAClB,WAAK,YAAY;AAAA,IAClB;AAAA;AAAA,IAGD,gBAAgB;AACdH,oBAAAA,MAAY,MAAA,OAAA,wBAAA,oBAAoB;AAChC,WAAK,QAAQ;AACb,WAAK,WAAW;AAChB,WAAK,YAAY;AAAA,QACf,eAAe;AAAA,QACf,eAAe;AAAA,MAChB;AACD,WAAK,aAAa;AAClB,WAAK,gBAAgB;AACrBI,6BAAa;AACbC,gCAAgB;AAEhBC,0CAAgB;AAAA,IACjB;AAAA;AAAA,IAGD,MAAM,MAAM,WAAW;AACrB,UAAI;AACFN,sBAAAA,MAAY,MAAA,OAAA,yBAAA,kBAAkB;AAC9B,cAAM,WAAW,MAAMO,SAAa,MAAC,SAAS;AAC9CP,sBAAAA,MAAA,MAAA,OAAA,yBAAY,qBAAqB,QAAQ;AAEzC,YAAI,CAAC,UAAU;AACb,gBAAM,IAAI,MAAM,QAAQ;AAAA,QACzB;AAED,cAAM,eAAe,SAAS,QAAQ;AACtC,cAAM,QAAQ,aAAa,eAAe,aAAa;AAEvD,YAAI,CAAC,OAAO;AACVA,wBAAAA,8CAAc,qBAAqB,YAAY;AAC/C,gBAAM,IAAI,MAAM,UAAU;AAAA,QAC3B;AAED,cAAM,OAAO;AAAA,UACX,IAAI,aAAa;AAAA,UACjB,UAAU,aAAa;AAAA,UACvB,OAAO,aAAa;AAAA,UACpB,OAAO,aAAa;AAAA,UACpB,UAAU,aAAa;AAAA,UACvB,QAAQ,aAAa;AAAA,UACrB,OAAO,aAAa;AAAA,QACrB;AAGD,aAAK,SAAS,KAAK;AACnB,aAAK,YAAY,IAAI;AACrB,aAAK,eAAe,IAAI;AAExBA,sBAAAA,MAAA,MAAA,OAAA,yBAAY,0BAA0B,IAAI;AAC1C,eAAO;AAAA,MACR,SAAQ,OAAO;AACdA,sBAAAA,MAAc,MAAA,SAAA,yBAAA,qBAAqB,KAAK;AACxC,aAAK,eAAe,KAAK;AACzB,cAAM;AAAA,MACP;AAAA,IACF;AAAA;AAAA,IAGD,MAAM,cAAc;;AAClB,UAAI;AACFA,sBAAAA,MAAA,MAAA,OAAA,yBAAY,sBAAsB;AAElC,YAAI,CAAC,KAAK,OAAO;AACf,gBAAM,IAAI,MAAM,OAAO;AAAA,QACxB;AAED,cAAM,WAAW,MAAMQ,qBAAqB;AAC5CR,sBAAAA,MAAY,MAAA,OAAA,yBAAA,yBAAyB,QAAQ;AAE7C,cAAM,WAAW,SAAS,QAAQ;AAClC,aAAK,YAAY,QAAQ;AAEzB,eAAO;AAAA,MACR,SAAQ,OAAO;AACdA,sBAAAA,MAAA,MAAA,SAAA,yBAAc,yBAAyB,KAAK;AAE5C,cAAI,WAAM,YAAN,mBAAe,SAAS,aAAU,WAAM,YAAN,mBAAe,SAAS,SAAQ;AACpE,eAAK,cAAe;AAAA,QACrB;AACD,cAAM;AAAA,MACP;AAAA,IACF;AAAA;AAAA,IAGD,MAAM,eAAe,YAAY;AAC/B,UAAI;AACFA,sBAAAA,MAAY,MAAA,OAAA,yBAAA,yBAAyB,UAAU;AAE/C,cAAM,WAAW,MAAMS,SAAsB,eAAC,UAAU;AACxDT,sBAAAA,MAAY,MAAA,OAAA,yBAAA,yBAAyB,QAAQ;AAE7C,cAAM,kBAAkB,SAAS,QAAQ;AACzC,aAAK,YAAY,eAAe;AAEhCU,iBAAAA,YAAY,UAAU;AACtB,eAAO;AAAA,MACR,SAAQ,OAAO;AACdV,sBAAAA,MAAA,MAAA,SAAA,yBAAc,yBAAyB,KAAK;AAC5CW,2BAAU,MAAM,WAAW,UAAU;AACrC,cAAM;AAAA,MACP;AAAA,IACF;AAAA;AAAA,IAGD,MAAM,SAAS,cAAc;AAC3B,UAAI;AACFX,sBAAAA,MAAY,MAAA,OAAA,yBAAA,kBAAkB;AAC9B,cAAM,WAAW,MAAMY,SAAgB,SAAC,YAAY;AACpDZ,sBAAAA,MAAA,MAAA,OAAA,yBAAY,qBAAqB,QAAQ;AAEzCU,iBAAAA,YAAY,MAAM;AAClB,eAAO;AAAA,MACR,SAAQ,OAAO;AACdV,sBAAAA,MAAc,MAAA,SAAA,yBAAA,qBAAqB,KAAK;AACxCW,2BAAU,MAAM,WAAW,MAAM;AACjC,cAAM;AAAA,MACP;AAAA,IACF;AAAA;AAAA,IAGD,MAAM,SAAS;AACb,UAAI;AACFX,sBAAAA,MAAA,MAAA,OAAA,yBAAY,oBAAoB;AAGhC,YAAI;AACF,gBAAMa,gBAAgB;AAAA,QACvB,SAAQ,OAAO;AACdb,wBAAAA,6CAAa,2BAA2B,KAAK;AAAA,QAE9C;AAGD,aAAK,cAAe;AAEpBA,sBAAAA,MAAA,MAAA,OAAA,yBAAY,oBAAoB;AAChCU,iBAAAA,YAAY,MAAM;AAGlBV,sBAAAA,MAAI,SAAS;AAAA,UACX,KAAK;AAAA,QACf,CAAS;AAAA,MAEF,SAAQ,OAAO;AACdA,sBAAAA,MAAA,MAAA,SAAA,yBAAc,uBAAuB,KAAK;AAE1C,aAAK,cAAe;AACpB,cAAM;AAAA,MACP;AAAA,IACF;AAAA;AAAA,IAGD,MAAM,mBAAmB;AACvB,UAAI,KAAK,eAAe;AACtBA,sBAAAA,MAAA,MAAA,OAAA,yBAAY,6BAA6B;AACzC,eAAO,KAAK;AAAA,MACb;AAED,WAAK,iBAAiB,IAAI;AAE1B,UAAI;AACFA,sBAAAA,MAAA,MAAA,OAAA,yBAAY,sBAAsB;AAElC,cAAM,QAAQF,WAAAA,SAAU;AACxB,cAAM,WAAWC,WAAAA,YAAa;AAE9B,YAAI,CAAC,SAAS,CAAC,UAAU;AACvBC,wBAAAA,MAAA,MAAA,OAAA,yBAAY,qBAAqB;AACjC,eAAK,eAAe,KAAK;AACzB,iBAAO;AAAA,QACR;AAGD,YAAI;AACF,gBAAM,WAAW,MAAMc,SAAwB;AAC/Cd,wBAAAA,4CAAY,yBAAyB,QAAQ;AAG7C,cAAI,YAAY,SAAS,MAAM;AAC7B,iBAAK,YAAY,SAAS,IAAI;AAAA,UAC/B;AAED,eAAK,eAAe,IAAI;AACxB,iBAAO;AAAA,QACR,SAAQ,OAAO;AACdA,wBAAAA,MAAc,MAAA,SAAA,yBAAA,0BAA0B,KAAK;AAE7C,eAAK,cAAe;AACpB,iBAAO;AAAA,QACR;AAAA,MAEF,SAAQ,OAAO;AACdA,sBAAAA,MAAA,MAAA,SAAA,yBAAc,yBAAyB,KAAK;AAC5C,aAAK,eAAe,KAAK;AACzB,eAAO;AAAA,MACf,UAAgB;AACR,aAAK,iBAAiB,KAAK;AAAA,MAC5B;AAAA,IACF;AAAA;AAAA,IAGD,MAAM,eAAe;AACnB,UAAI;AACFA,sBAAAA,MAAA,MAAA,OAAA,yBAAY,sBAAsB;AAClC,cAAM,WAAW,MAAMe,sBAAsB;AAE7C,YAAI,YAAY,SAAS,MAAM;AAC7B,eAAK,aAAa,SAAS,IAAI;AAC/Bf,wBAAY,MAAA,MAAA,OAAA,yBAAA,2BAA2B,SAAS,IAAI;AAAA,QACrD;AAED,eAAO;AAAA,MACR,SAAQ,OAAO;AACdA,sBAAAA,MAAc,MAAA,SAAA,yBAAA,2BAA2B,KAAK;AAC9C,cAAM;AAAA,MACP;AAAA,IACF;AAAA;AAAA,IAGD,MAAM,cAAc,aAAa;AAC/B,UAAI;AACFA,sBAAAA,MAAA,MAAA,OAAA,yBAAY,oBAAoB;AAChC,cAAM,WAAW,MAAMgB,SAAsB,WAAW;AAExD,YAAI,YAAY,SAAS,MAAM;AAE7B,eAAK,YAAY;AAAA,YACf,GAAG,KAAK;AAAA,YACR,GAAG,SAAS;AAAA,UACxB,CAAW;AACDhB,wBAAAA,MAAY,MAAA,OAAA,yBAAA,sBAAsB;AAClCU,mBAAAA,YAAY,QAAQ;AAAA,QACrB;AAED,eAAO;AAAA,MACR,SAAQ,OAAO;AACdV,sBAAAA,MAAA,MAAA,SAAA,yBAAc,yBAAyB,KAAK;AAC5CW,2BAAU,MAAM,WAAW,MAAM;AACjC,cAAM;AAAA,MACP;AAAA,IACF;AAAA;AAAA,IAGD,MAAM,eAAe,cAAc;AACjC,UAAI;AACFX,sBAAAA,MAAY,MAAA,OAAA,yBAAA,kBAAkB;AAC9B,cAAM,WAAW,MAAMiB,SAAsB,eAAC,YAAY;AAE1DjB,sBAAAA,MAAA,MAAA,OAAA,yBAAY,oBAAoB;AAChCU,iBAAAA,YAAY,QAAQ;AAEpB,eAAO;AAAA,MACR,SAAQ,OAAO;AACdV,sBAAAA,MAAA,MAAA,SAAA,yBAAc,uBAAuB,KAAK;AAC1CW,2BAAU,MAAM,WAAW,QAAQ;AACnC,cAAM;AAAA,MACP;AAAA,IACF;AAAA,EACF;AACH,CAAC;;"}