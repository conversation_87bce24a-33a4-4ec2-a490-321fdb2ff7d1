{"version": 3, "file": "booking.js", "sources": ["stores/booking.js"], "sourcesContent": ["import { defineStore } from 'pinia'\nimport * as bookingApi from '@/api/booking.js'\nimport * as userApi from '@/api/user.js'\nimport * as sharingApi from '@/api/sharing.js'\nimport { showSuccess, showError } from '@/utils/ui.js'\n\nexport const useBookingStore = defineStore('booking', {\n  state: () => ({\n    bookingList: [],\n    bookingDetail: null,\n    sharingOrders: [],\n    userSharingOrders: [],\n    joinedSharingOrders: [],\n    sharingDetail: null,\n    loading: false,\n    pagination: {\n      current: 1,\n      pageSize: 10,\n      total: 0,\n      totalPages: 1,\n      currentPage: 1\n    }\n  }),\n\n  getters: {\n    // 基础getters\n    getBookingList: (state) => state.bookingList,\n    getBookingDetail: (state) => state.bookingDetail,\n    getSharingOrders: (state) => state.sharingOrders,\n    getUserSharingOrders: (state) => state.userSharingOrders,\n    getJoinedSharingOrders: (state) => state.joinedSharingOrders,\n    getSharingDetail: (state) => state.sharingDetail,\n    isLoading: (state) => state.loading,\n    getPagination: (state) => state.pagination,\n    \n    // 计算属性\n    totalBookings: (state) => state.bookingList.length,\n    totalSharingOrders: (state) => state.sharingOrders.length,\n    totalUserSharingOrders: (state) => state.userSharingOrders.length,\n    totalJoinedSharingOrders: (state) => state.joinedSharingOrders.length,\n    \n    // 按状态筛选预订\n    getBookingsByStatus: (state) => (status) => {\n      return state.bookingList.filter(booking => booking.status === status)\n    },\n    \n    // 待确认的预订\n    getPendingBookings: (state) => {\n      return state.bookingList.filter(booking => booking.status === 'PENDING')\n    },\n    \n    // 已确认的预订\n    getConfirmedBookings: (state) => {\n      return state.bookingList.filter(booking => booking.status === 'CONFIRMED')\n    },\n    \n    // 是否有更多数据\n    hasMoreData: (state) => {\n      return state.pagination.current < state.pagination.totalPages\n    }\n  },\n\n  actions: {\n    // 设置加载状态\n    setLoading(loading) {\n      this.loading = loading\n    },\n    \n    // 设置预订列表\n    setBookingList({ list, pagination }) {\n      this.bookingList = Array.isArray(list) ? list : []\n      if (pagination) {\n        this.pagination = { ...this.pagination, ...pagination }\n      }\n    },\n    \n    // 追加预订列表\n    appendBookingList(list) {\n      const newList = Array.isArray(list) ? list : []\n      this.bookingList = [...this.bookingList, ...newList]\n    },\n    \n    // 设置预订详情\n    setBookingDetail(detail) {\n      this.bookingDetail = detail\n    },\n    \n    // 设置分享订单列表\n    setSharingOrders(orders) {\n      this.sharingOrders = Array.isArray(orders) ? orders : []\n    },\n    \n    // 设置用户分享订单\n    setUserSharingOrders(orders) {\n      this.userSharingOrders = Array.isArray(orders) ? orders : []\n    },\n    \n    // 设置加入的分享订单\n    setJoinedSharingOrders(orders) {\n      this.joinedSharingOrders = Array.isArray(orders) ? orders : []\n    },\n    \n    // 设置分享详情\n    setSharingDetail(detail) {\n      this.sharingDetail = detail\n    },\n    \n    // 设置分页信息\n    setPagination(pagination) {\n      this.pagination = { ...this.pagination, ...pagination }\n    },\n    \n    // 更新预订状态\n    updateBookingStatus({ bookingId, status }) {\n      const booking = this.bookingList.find(b => b.id === bookingId)\n      if (booking) {\n        booking.status = status\n      }\n    },\n    \n    // 创建预订\n    async createBooking(bookingData) {\n      try {\n        console.log('[BookingStore] 发起预约创建请求，数据:', bookingData)\n        this.setLoading(true)\n        \n        const response = await bookingApi.createBooking(bookingData)\n        console.log('[BookingStore] 预约创建API响应:', response)\n        console.log('[BookingStore] response.data:', response.data)\n        console.log('[BookingStore] response.data?.id:', response.data?.id)\n\n        showSuccess('预约成功')\n\n        // 返回响应数据，确保包含订单ID\n        const result = response.data || response\n        console.log('[BookingStore] 最终返回结果:', result)\n        return result\n      } catch (error) {\n        console.error('[BookingStore] 创建预约失败:', error)\n        showError(error.message || '预约失败')\n        throw error\n      } finally {\n        this.setLoading(false)\n      }\n    },\n\n    // 获取我的预订列表（别名方法，用于测试兼容性）\n    async getMyBookings(params = {}) {\n      return await this.getBookingList(params)\n    },\n\n    // 获取预订详情（修复API调用错误）\n    async getBookingDetails(bookingId) {\n      try {\n        console.log('[BookingStore] 获取预订详情，ID:', bookingId)\n        this.setLoading(true)\n\n        // 修复：使用正确的API方法名\n        const response = await bookingApi.getBookingDetail(bookingId)\n        console.log('[BookingStore] 预订详情获取成功:', response)\n\n        // 设置到store状态中\n        this.setBookingDetail(response.data || response)\n\n        return response\n      } catch (error) {\n        console.error('[BookingStore] 获取预订详情失败:', error)\n        showError(error.message || '获取预订详情失败')\n        throw error\n      } finally {\n        this.setLoading(false)\n      }\n    },\n\n    // 创建拼场预约\n    async createSharedBooking(bookingData) {\n      try {\n        console.log('[BookingStore] 开始创建拼场预约')\n        this.setLoading(true)\n        \n        const response = await bookingApi.createSharedBooking(bookingData)\n        \n        showSuccess('拼场预约成功')\n        console.log('[BookingStore] 拼场预约创建成功')\n        \n        return response\n      } catch (error) {\n        console.error('[BookingStore] 创建拼场预约失败:', error)\n        showError(error.message || '拼场预约失败')\n        throw error\n      } finally {\n        this.setLoading(false)\n      }\n    },\n    \n    // 获取用户预约列表\n    async getUserBookings(params = {}) {\n      try {\n        console.log('[BookingStore] 开始获取用户预约列表，参数:', params)\n        this.setLoading(true)\n        \n        const response = await userApi.getUserBookings(params)\n        \n        console.log('[BookingStore] API响应原始数据:', response)\n        console.log('[BookingStore] response.data:', response.data)\n        console.log('[BookingStore] response.data类型:', typeof response.data)\n        \n        const { data, total, page, pageSize, totalPages } = response\n        \n        console.log('[BookingStore] 解构后的数据:')\n        console.log('data:', data)\n        console.log('data类型:', typeof data)\n        console.log('data是否为数组:', Array.isArray(data))\n        console.log('total:', total)\n        console.log('page:', page)\n        console.log('pageSize:', pageSize)\n        console.log('totalPages:', totalPages)\n        \n        const pagination = {\n          current: page,\n          pageSize: pageSize,\n          total: total,\n          totalPages: totalPages,\n          currentPage: page\n        }\n        \n        if (params.page === 1 || params.refresh) {\n          console.log('[BookingStore] 设置新的预约列表，数据长度:', (data || []).length)\n          this.setBookingList({ list: data || [], pagination: pagination })\n        } else {\n          console.log('[BookingStore] 追加预约列表，新增数据长度:', (data || []).length)\n          this.appendBookingList(data || [])\n          this.setPagination(pagination)\n        }\n        \n        return response\n      } catch (error) {\n        console.error('[BookingStore] 获取用户预约列表失败:', error)\n        // 清空列表并重置分页\n        this.setBookingList({ \n          list: [], \n          pagination: { current: 1, pageSize: 10, total: 0, totalPages: 1, currentPage: 1 } \n        })\n        showError(error.message || '获取预约列表失败')\n        throw error\n      } finally {\n        this.setLoading(false)\n      }\n    },\n    \n    // 获取预约详情\n    async getBookingDetail(bookingId) {\n      try {\n        console.log('[BookingStore] 🌐 发起API请求获取订单详情, ID:', bookingId)\n        console.log('[BookingStore] 🌐 ID类型:', typeof bookingId)\n        this.setLoading(true)\n        \n        if (!bookingId) {\n          throw new Error('订单ID不能为空')\n        }\n        \n        const response = await bookingApi.getBookingDetail(bookingId)\n        console.log('[BookingStore] 📡 完整API响应:', response)\n        console.log('[BookingStore] 📡 响应类型:', typeof response)\n        console.log('[BookingStore] 📡 响应是否为空:', !response)\n        \n        // 处理不同的响应数据结构\n        let bookingData = null\n        if (response && typeof response === 'object') {\n          // 如果response直接是数据对象\n          if (response.id || response.orderNo) {\n            bookingData = response\n            console.log('[BookingStore] 📡 使用response作为数据')\n          }\n          // 如果response有data属性\n          else if (response.data) {\n            bookingData = response.data\n            console.log('[BookingStore] 📡 使用response.data作为数据')\n          }\n          // 如果response有result属性\n          else if (response.result) {\n            bookingData = response.result\n            console.log('[BookingStore] 📡 使用response.result作为数据')\n          }\n          else {\n            console.warn('[BookingStore] 📡 响应数据结构未知:', Object.keys(response))\n            // 尝试直接使用response\n            bookingData = response\n          }\n        } else {\n          console.error('[BookingStore] 📡 响应数据无效:', response)\n          throw new Error('服务器返回的数据格式不正确')\n        }\n        \n        console.log('[BookingStore] 📡 处理后的订单数据:', bookingData)\n        console.log('[BookingStore] 📡 数据类型:', typeof bookingData)\n        console.log('[BookingStore] 📡 数据键:', bookingData ? Object.keys(bookingData) : 'null')\n        console.log('[BookingStore] ⏰ API返回的开始时间:', bookingData?.startTime)\n        console.log('[BookingStore] ⏰ API返回的结束时间:', bookingData?.endTime)\n        console.log('[BookingStore] 💰 API返回的总价格:', bookingData?.totalPrice)\n        console.log('[BookingStore] 🏷️ API返回的订单号(orderNo):', bookingData?.orderNo)\n        console.log('[BookingStore] 🏷️ API返回的订单号(orderNumber):', bookingData?.orderNumber)\n        console.log('[BookingStore] 🆔 API返回的ID:', bookingData?.id)\n        \n        if (!bookingData) {\n          throw new Error('未能获取到有效的订单数据')\n        }\n        \n        // 字段映射：如果后端返回的是orderNumber，映射为orderNo\n        if (bookingData.orderNumber && !bookingData.orderNo) {\n          bookingData.orderNo = bookingData.orderNumber\n          console.log('[BookingStore] 🔄 字段映射: orderNumber -> orderNo:', bookingData.orderNo)\n        }\n        \n        this.setBookingDetail(bookingData)\n        console.log('[BookingStore] ✅ 数据已存储到store')\n        return response\n      } catch (error) {\n        console.error('[BookingStore] ❌ API请求失败:', error)\n        console.error('[BookingStore] ❌ 错误类型:', error.constructor.name)\n        console.error('[BookingStore] ❌ 错误消息:', error.message)\n        console.error('[BookingStore] ❌ 错误堆栈:', error.stack)\n        \n        // 清空详情数据\n        this.setBookingDetail(null)\n        \n        showError(error.message || '获取预约详情失败')\n        throw error\n      } finally {\n        this.setLoading(false)\n      }\n    },\n    \n    // 取消预约\n    async cancelBooking(bookingId) {\n      try {\n        console.log('[BookingStore] 开始取消预约:', bookingId)\n\n        const response = await bookingApi.cancelBooking(bookingId)\n\n        // 立即更新本地状态\n        this.updateBookingStatus({ bookingId, status: 'CANCELLED' })\n\n        // 发送全局事件通知拼场大厅刷新（如果是拼场订单）\n        console.log('[BookingStore] 发送订单取消事件通知')\n        uni.$emit('orderCancelled', {\n          orderId: bookingId,\n          type: 'booking'\n        })\n\n        // 延迟重新获取数据以确保服务器状态同步\n        setTimeout(() => {\n          this.getUserBookings({ page: 1, pageSize: 10, refresh: true })\n        }, 1000)\n\n        showSuccess('预约已取消')\n        console.log('[BookingStore] 预约取消成功')\n        return response\n      } catch (error) {\n        console.error('[BookingStore] 取消预约失败:', error)\n        showError(error.message || '取消预约失败')\n        throw error\n      }\n    },\n    \n    // 清空预订详情\n    clearBookingDetail() {\n      this.bookingDetail = null\n    },\n    \n    // 重置分页\n    resetPagination() {\n      this.pagination = {\n        current: 1,\n        pageSize: 10,\n        total: 0,\n        totalPages: 1,\n        currentPage: 1\n      }\n    },\n\n    // === 分享相关功能 ===\n\n    // 申请拼场\n    async createSharingOrder({ orderId, data }) {\n      try {\n        console.log('[BookingStore] 开始申请拼场')\n        this.setLoading(true)\n\n        const response = await sharingApi.applySharedBooking(orderId, data)\n\n        showSuccess('拼场申请已发送')\n        console.log('[BookingStore] 拼场申请成功')\n        return response\n      } catch (error) {\n        console.error('[BookingStore] 申请拼场失败:', error)\n        showError(error.message || '申请拼场失败')\n        throw error\n      } finally {\n        this.setLoading(false)\n      }\n    },\n\n    // 获取可拼场的订单列表\n    async getSharingOrdersList(params = {}) {\n      try {\n        console.log('[BookingStore] 开始获取拼场订单，参数:', params)\n        this.setLoading(true)\n\n        const response = await sharingApi.getJoinableSharingOrders(params)\n        console.log('[BookingStore] 拼场订单API响应:', response)\n\n        if (response && (response.list || response.data)) {\n          // 处理两种可能的响应格式：直接返回数据 或 包装在data中\n          const responseData = response.list ? response : response.data\n          const orders = responseData.list || responseData.data || []\n\n          console.log('[BookingStore] 解析的拼场订单:', orders)\n\n          if (params.page === 1 || params.refresh) {\n            this.setSharingOrders(orders)\n          } else {\n            // 追加数据\n            this.sharingOrders = [...this.sharingOrders, ...orders]\n          }\n\n          // 更新分页信息\n          if (responseData.pagination) {\n            this.setPagination(responseData.pagination)\n          }\n        }\n\n        return response\n      } catch (error) {\n        console.error('[BookingStore] 获取拼场订单失败:', error)\n        showError(error.message || '获取拼场订单失败')\n        throw error\n      } finally {\n        this.setLoading(false)\n      }\n    },\n\n    // 创建拼场订单\n    async createSharingOrderNew(sharingData) {\n      try {\n        console.log('[BookingStore] 开始创建拼场订单')\n\n        const response = await sharingApi.createSharingOrder(sharingData)\n\n        showSuccess('拼场订单创建成功')\n        console.log('[BookingStore] 拼场订单创建成功')\n        return response.data\n      } catch (error) {\n        console.error('[BookingStore] 创建拼场订单失败:', error)\n        showError(error.message || '创建拼场订单失败')\n        throw error\n      }\n    },\n\n    // 获取拼场订单详情\n    async getSharingOrderDetail(orderId) {\n      try {\n        console.log('[BookingStore] 开始获取拼场订单详情:', orderId)\n\n        const response = await sharingApi.getSharingOrderById(orderId)\n\n        this.setSharingDetail(response.data)\n        console.log('[BookingStore] 拼场订单详情获取成功')\n        return response.data\n      } catch (error) {\n        console.error('[BookingStore] 获取拼场订单详情失败:', error)\n        showError(error.message || '获取拼场订单详情失败')\n        throw error\n      }\n    },\n\n    // 加入拼场订单\n    async joinSharingOrder(orderId) {\n      try {\n        console.log('[BookingStore] 开始加入拼场订单:', orderId)\n\n        const response = await sharingApi.joinSharingOrder(orderId)\n\n        showSuccess('加入拼场成功')\n        console.log('[BookingStore] 加入拼场成功')\n        return response.data\n      } catch (error) {\n        console.error('[BookingStore] 加入拼场失败:', error)\n        showError(error.message || '加入拼场失败')\n        throw error\n      }\n    },\n\n    // 获取我创建的拼场订单\n    async getMyCreatedSharingOrders() {\n      try {\n        console.log('[BookingStore] 开始获取我创建的拼场订单')\n\n        const response = await sharingApi.getMyCreatedSharingOrders()\n\n        this.setUserSharingOrders(response.data || [])\n        console.log('[BookingStore] 我创建的拼场订单获取成功')\n        return response.data\n      } catch (error) {\n        console.error('[BookingStore] 获取我创建的拼场订单失败:', error)\n        showError(error.message || '获取我创建的拼场订单失败')\n        throw error\n      }\n    },\n\n    // 处理拼场申请\n    async handleSharingRequest({ requestId, data }) {\n      try {\n        console.log('[BookingStore] 开始处理拼场申请:', { requestId, data })\n\n        const response = await sharingApi.handleSharedRequest(requestId, data)\n\n        showSuccess(data.status === 'APPROVED' ? '已同意拼场申请' : '已拒绝拼场申请')\n        console.log('[BookingStore] 拼场申请处理成功')\n        return response\n      } catch (error) {\n        console.error('[BookingStore] 处理拼场申请失败:', error)\n        showError(error.message || '处理拼场申请失败')\n        throw error\n      }\n    },\n\n    // 获取我发出的拼场申请\n    async getUserSharingOrders(params = {}) {\n      try {\n        console.log('[BookingStore] 开始获取我发出的拼场申请')\n\n        const response = await sharingApi.getMySharedRequests(params)\n\n        this.setUserSharingOrders(response.data || [])\n        console.log('[BookingStore] 我发出的拼场申请获取成功')\n        return response\n      } catch (error) {\n        console.error('[BookingStore] 获取拼场申请失败:', error)\n        showError(error.message || '获取拼场申请失败')\n        throw error\n      }\n    },\n\n    // 获取我收到的拼场申请\n    async getUserJoinedSharingOrders(params = {}) {\n      try {\n        console.log('[BookingStore] 开始获取我收到的拼场申请')\n\n        const response = await sharingApi.getReceivedSharedRequests(params)\n\n        this.setJoinedSharingOrders(response.data || [])\n        console.log('[BookingStore] 我收到的拼场申请获取成功')\n        return response\n      } catch (error) {\n        console.error('[BookingStore] 获取拼场申请失败:', error)\n        showError(error.message || '获取拼场申请失败')\n        throw error\n      }\n    },\n\n    // 获取拼场详情\n    async getSharingDetail(sharingId) {\n      try {\n        console.log('[BookingStore] 开始获取拼场详情:', sharingId)\n        this.setLoading(true)\n\n        const response = await sharingApi.getSharingOrderById(sharingId)\n\n        this.setSharingDetail(response.data)\n        console.log('[BookingStore] 拼场详情获取成功')\n        return response\n      } catch (error) {\n        console.error('[BookingStore] 获取拼场详情失败:', error)\n        showError(error.message || '获取拼场详情失败')\n        throw error\n      } finally {\n        this.setLoading(false)\n      }\n    },\n\n    // 移除拼场参与者\n    async removeSharingParticipant({ sharingId, participantId }) {\n      try {\n        console.log('[BookingStore] 开始移除拼场参与者:', { sharingId, participantId })\n        this.setLoading(true)\n\n        // 调用API移除参与者\n        const response = await sharingApi.removeSharingParticipant(sharingId, participantId)\n\n        // 重新获取拼场详情以确保数据同步\n        await this.getSharingDetail(sharingId)\n\n        showSuccess('参与者已移除')\n        console.log('[BookingStore] 移除拼场参与者成功')\n        return response\n      } catch (error) {\n        console.error('[BookingStore] 移除拼场参与者失败:', error)\n        showError(error.message || '移除参与者失败')\n        throw error\n      } finally {\n        this.setLoading(false)\n      }\n    },\n\n    // 更新拼场设置\n    async updateSharingSettings({ sharingId, settings }) {\n      try {\n        console.log('[BookingStore] 开始更新拼场设置:', { sharingId, settings })\n        this.setLoading(true)\n\n        // 调用API更新设置\n        const response = await sharingApi.updateSharingSettings(sharingId, settings)\n\n        // 重新获取拼场详情以确保数据同步\n        await this.getSharingDetail(sharingId)\n\n        showSuccess('拼场设置已更新')\n        console.log('[BookingStore] 更新拼场设置成功')\n        return response\n      } catch (error) {\n        console.error('[BookingStore] 更新拼场设置失败:', error)\n        showError(error.message || '更新拼场设置失败')\n        throw error\n      } finally {\n        this.setLoading(false)\n      }\n    }\n  }\n})\n"], "names": ["defineStore", "uni", "bookingApi.createBooking", "showSuccess", "showError", "bookingApi.getBookingDetail", "bookingApi.createSharedBooking", "userApi.getUserBookings", "bookingApi.cancelBooking", "sharingApi.applySharedBooking", "sharingApi.getJoinableSharingOrders", "sharingApi.createSharingOrder", "sharingApi.getSharingOrderById", "sharingApi.joinSharingOrder", "sharingApi.getMyCreatedSharingOrders", "sharingApi.handleSharedRequest", "sharingApi.getMySharedRequests", "sharingApi.getReceivedSharedRequests", "sharingApi.removeSharingParticipant", "sharingApi.updateSharingSettings"], "mappings": ";;;;;;AAMY,MAAC,kBAAkBA,cAAW,YAAC,WAAW;AAAA,EACpD,OAAO,OAAO;AAAA,IACZ,aAAa,CAAE;AAAA,IACf,eAAe;AAAA,IACf,eAAe,CAAE;AAAA,IACjB,mBAAmB,CAAE;AAAA,IACrB,qBAAqB,CAAE;AAAA,IACvB,eAAe;AAAA,IACf,SAAS;AAAA,IACT,YAAY;AAAA,MACV,SAAS;AAAA,MACT,UAAU;AAAA,MACV,OAAO;AAAA,MACP,YAAY;AAAA,MACZ,aAAa;AAAA,IACd;AAAA,EACL;AAAA,EAEE,SAAS;AAAA;AAAA,IAEP,gBAAgB,CAAC,UAAU,MAAM;AAAA,IACjC,kBAAkB,CAAC,UAAU,MAAM;AAAA,IACnC,kBAAkB,CAAC,UAAU,MAAM;AAAA,IACnC,sBAAsB,CAAC,UAAU,MAAM;AAAA,IACvC,wBAAwB,CAAC,UAAU,MAAM;AAAA,IACzC,kBAAkB,CAAC,UAAU,MAAM;AAAA,IACnC,WAAW,CAAC,UAAU,MAAM;AAAA,IAC5B,eAAe,CAAC,UAAU,MAAM;AAAA;AAAA,IAGhC,eAAe,CAAC,UAAU,MAAM,YAAY;AAAA,IAC5C,oBAAoB,CAAC,UAAU,MAAM,cAAc;AAAA,IACnD,wBAAwB,CAAC,UAAU,MAAM,kBAAkB;AAAA,IAC3D,0BAA0B,CAAC,UAAU,MAAM,oBAAoB;AAAA;AAAA,IAG/D,qBAAqB,CAAC,UAAU,CAAC,WAAW;AAC1C,aAAO,MAAM,YAAY,OAAO,aAAW,QAAQ,WAAW,MAAM;AAAA,IACrE;AAAA;AAAA,IAGD,oBAAoB,CAAC,UAAU;AAC7B,aAAO,MAAM,YAAY,OAAO,aAAW,QAAQ,WAAW,SAAS;AAAA,IACxE;AAAA;AAAA,IAGD,sBAAsB,CAAC,UAAU;AAC/B,aAAO,MAAM,YAAY,OAAO,aAAW,QAAQ,WAAW,WAAW;AAAA,IAC1E;AAAA;AAAA,IAGD,aAAa,CAAC,UAAU;AACtB,aAAO,MAAM,WAAW,UAAU,MAAM,WAAW;AAAA,IACpD;AAAA,EACF;AAAA,EAED,SAAS;AAAA;AAAA,IAEP,WAAW,SAAS;AAClB,WAAK,UAAU;AAAA,IAChB;AAAA;AAAA,IAGD,eAAe,EAAE,MAAM,cAAc;AACnC,WAAK,cAAc,MAAM,QAAQ,IAAI,IAAI,OAAO,CAAE;AAClD,UAAI,YAAY;AACd,aAAK,aAAa,EAAE,GAAG,KAAK,YAAY,GAAG,WAAY;AAAA,MACxD;AAAA,IACF;AAAA;AAAA,IAGD,kBAAkB,MAAM;AACtB,YAAM,UAAU,MAAM,QAAQ,IAAI,IAAI,OAAO,CAAE;AAC/C,WAAK,cAAc,CAAC,GAAG,KAAK,aAAa,GAAG,OAAO;AAAA,IACpD;AAAA;AAAA,IAGD,iBAAiB,QAAQ;AACvB,WAAK,gBAAgB;AAAA,IACtB;AAAA;AAAA,IAGD,iBAAiB,QAAQ;AACvB,WAAK,gBAAgB,MAAM,QAAQ,MAAM,IAAI,SAAS,CAAE;AAAA,IACzD;AAAA;AAAA,IAGD,qBAAqB,QAAQ;AAC3B,WAAK,oBAAoB,MAAM,QAAQ,MAAM,IAAI,SAAS,CAAE;AAAA,IAC7D;AAAA;AAAA,IAGD,uBAAuB,QAAQ;AAC7B,WAAK,sBAAsB,MAAM,QAAQ,MAAM,IAAI,SAAS,CAAE;AAAA,IAC/D;AAAA;AAAA,IAGD,iBAAiB,QAAQ;AACvB,WAAK,gBAAgB;AAAA,IACtB;AAAA;AAAA,IAGD,cAAc,YAAY;AACxB,WAAK,aAAa,EAAE,GAAG,KAAK,YAAY,GAAG,WAAY;AAAA,IACxD;AAAA;AAAA,IAGD,oBAAoB,EAAE,WAAW,UAAU;AACzC,YAAM,UAAU,KAAK,YAAY,KAAK,OAAK,EAAE,OAAO,SAAS;AAC7D,UAAI,SAAS;AACX,gBAAQ,SAAS;AAAA,MAClB;AAAA,IACF;AAAA;AAAA,IAGD,MAAM,cAAc,aAAa;;AAC/B,UAAI;AACFC,sBAAAA,MAAY,MAAA,OAAA,4BAAA,+BAA+B,WAAW;AACtD,aAAK,WAAW,IAAI;AAEpB,cAAM,WAAW,MAAMC,YAAwB,cAAC,WAAW;AAC3DD,sBAAAA,+CAAY,6BAA6B,QAAQ;AACjDA,sBAAA,MAAA,MAAA,OAAA,4BAAY,iCAAiC,SAAS,IAAI;AAC1DA,sBAAA,MAAA,MAAA,OAAA,4BAAY,sCAAqC,cAAS,SAAT,mBAAe,EAAE;AAElEE,iBAAAA,YAAY,MAAM;AAGlB,cAAM,SAAS,SAAS,QAAQ;AAChCF,sBAAAA,MAAY,MAAA,OAAA,4BAAA,0BAA0B,MAAM;AAC5C,eAAO;AAAA,MACR,SAAQ,OAAO;AACdA,sBAAAA,MAAA,MAAA,SAAA,4BAAc,0BAA0B,KAAK;AAC7CG,2BAAU,MAAM,WAAW,MAAM;AACjC,cAAM;AAAA,MACd,UAAgB;AACR,aAAK,WAAW,KAAK;AAAA,MACtB;AAAA,IACF;AAAA;AAAA,IAGD,MAAM,cAAc,SAAS,IAAI;AAC/B,aAAO,MAAM,KAAK,eAAe,MAAM;AAAA,IACxC;AAAA;AAAA,IAGD,MAAM,kBAAkB,WAAW;AACjC,UAAI;AACFH,sBAAAA,+CAAY,6BAA6B,SAAS;AAClD,aAAK,WAAW,IAAI;AAGpB,cAAM,WAAW,MAAMI,YAA2B,iBAAC,SAAS;AAC5DJ,sBAAAA,+CAAY,4BAA4B,QAAQ;AAGhD,aAAK,iBAAiB,SAAS,QAAQ,QAAQ;AAE/C,eAAO;AAAA,MACR,SAAQ,OAAO;AACdA,sBAAAA,MAAc,MAAA,SAAA,4BAAA,4BAA4B,KAAK;AAC/CG,2BAAU,MAAM,WAAW,UAAU;AACrC,cAAM;AAAA,MACd,UAAgB;AACR,aAAK,WAAW,KAAK;AAAA,MACtB;AAAA,IACF;AAAA;AAAA,IAGD,MAAM,oBAAoB,aAAa;AACrC,UAAI;AACFH,sBAAAA,MAAY,MAAA,OAAA,4BAAA,yBAAyB;AACrC,aAAK,WAAW,IAAI;AAEpB,cAAM,WAAW,MAAMK,YAA8B,oBAAC,WAAW;AAEjEH,iBAAAA,YAAY,QAAQ;AACpBF,sBAAAA,MAAY,MAAA,OAAA,4BAAA,yBAAyB;AAErC,eAAO;AAAA,MACR,SAAQ,OAAO;AACdA,sBAAAA,MAAc,MAAA,SAAA,4BAAA,4BAA4B,KAAK;AAC/CG,2BAAU,MAAM,WAAW,QAAQ;AACnC,cAAM;AAAA,MACd,UAAgB;AACR,aAAK,WAAW,KAAK;AAAA,MACtB;AAAA,IACF;AAAA;AAAA,IAGD,MAAM,gBAAgB,SAAS,IAAI;AACjC,UAAI;AACFH,sBAAAA,+CAAY,iCAAiC,MAAM;AACnD,aAAK,WAAW,IAAI;AAEpB,cAAM,WAAW,MAAMM,SAAuB,gBAAC,MAAM;AAErDN,sBAAAA,+CAAY,6BAA6B,QAAQ;AACjDA,sBAAA,MAAA,MAAA,OAAA,4BAAY,iCAAiC,SAAS,IAAI;AAC1DA,sBAAA,MAAA,MAAA,OAAA,4BAAY,mCAAmC,OAAO,SAAS,IAAI;AAEnE,cAAM,EAAE,MAAM,OAAO,MAAM,UAAU,WAAU,IAAK;AAEpDA,sBAAAA,MAAY,MAAA,OAAA,4BAAA,wBAAwB;AACpCA,sBAAAA,+CAAY,SAAS,IAAI;AACzBA,sBAAA,MAAA,MAAA,OAAA,4BAAY,WAAW,OAAO,IAAI;AAClCA,4BAAY,MAAA,OAAA,4BAAA,cAAc,MAAM,QAAQ,IAAI,CAAC;AAC7CA,sBAAAA,MAAY,MAAA,OAAA,4BAAA,UAAU,KAAK;AAC3BA,sBAAAA,+CAAY,SAAS,IAAI;AACzBA,sBAAAA,MAAA,MAAA,OAAA,4BAAY,aAAa,QAAQ;AACjCA,sBAAAA,MAAY,MAAA,OAAA,4BAAA,eAAe,UAAU;AAErC,cAAM,aAAa;AAAA,UACjB,SAAS;AAAA,UACT;AAAA,UACA;AAAA,UACA;AAAA,UACA,aAAa;AAAA,QACd;AAED,YAAI,OAAO,SAAS,KAAK,OAAO,SAAS;AACvCA,8BAAA,MAAA,OAAA,4BAAY,kCAAkC,QAAQ,CAAE,GAAE,MAAM;AAChE,eAAK,eAAe,EAAE,MAAM,QAAQ,IAAI,YAAwB;AAAA,QAC1E,OAAe;AACLA,8BAAA,MAAA,OAAA,4BAAY,kCAAkC,QAAQ,CAAE,GAAE,MAAM;AAChE,eAAK,kBAAkB,QAAQ,EAAE;AACjC,eAAK,cAAc,UAAU;AAAA,QAC9B;AAED,eAAO;AAAA,MACR,SAAQ,OAAO;AACdA,sBAAAA,MAAc,MAAA,SAAA,4BAAA,8BAA8B,KAAK;AAEjD,aAAK,eAAe;AAAA,UAClB,MAAM,CAAE;AAAA,UACR,YAAY,EAAE,SAAS,GAAG,UAAU,IAAI,OAAO,GAAG,YAAY,GAAG,aAAa,EAAG;AAAA,QAC3F,CAAS;AACDG,2BAAU,MAAM,WAAW,UAAU;AACrC,cAAM;AAAA,MACd,UAAgB;AACR,aAAK,WAAW,KAAK;AAAA,MACtB;AAAA,IACF;AAAA;AAAA,IAGD,MAAM,iBAAiB,WAAW;AAChC,UAAI;AACFH,sBAAAA,MAAY,MAAA,OAAA,4BAAA,wCAAwC,SAAS;AAC7DA,sBAAY,MAAA,MAAA,OAAA,4BAAA,2BAA2B,OAAO,SAAS;AACvD,aAAK,WAAW,IAAI;AAEpB,YAAI,CAAC,WAAW;AACd,gBAAM,IAAI,MAAM,UAAU;AAAA,QAC3B;AAED,cAAM,WAAW,MAAMI,YAA2B,iBAAC,SAAS;AAC5DJ,sBAAAA,+CAAY,8BAA8B,QAAQ;AAClDA,sBAAY,MAAA,MAAA,OAAA,4BAAA,2BAA2B,OAAO,QAAQ;AACtDA,qEAAY,6BAA6B,CAAC,QAAQ;AAGlD,YAAI,cAAc;AAClB,YAAI,YAAY,OAAO,aAAa,UAAU;AAE5C,cAAI,SAAS,MAAM,SAAS,SAAS;AACnC,0BAAc;AACdA,0BAAAA,MAAY,MAAA,OAAA,4BAAA,kCAAkC;AAAA,UAC/C,WAEQ,SAAS,MAAM;AACtB,0BAAc,SAAS;AACvBA,0BAAAA,+CAAY,uCAAuC;AAAA,UACpD,WAEQ,SAAS,QAAQ;AACxB,0BAAc,SAAS;AACvBA,0BAAAA,MAAA,MAAA,OAAA,4BAAY,yCAAyC;AAAA,UACtD,OACI;AACHA,gCAAA,MAAA,QAAA,4BAAa,+BAA+B,OAAO,KAAK,QAAQ,CAAC;AAEjE,0BAAc;AAAA,UACf;AAAA,QACX,OAAe;AACLA,wBAAAA,MAAA,MAAA,SAAA,4BAAc,6BAA6B,QAAQ;AACnD,gBAAM,IAAI,MAAM,eAAe;AAAA,QAChC;AAEDA,sBAAAA,MAAY,MAAA,OAAA,4BAAA,+BAA+B,WAAW;AACtDA,sBAAA,MAAA,MAAA,OAAA,4BAAY,2BAA2B,OAAO,WAAW;AACzDA,sBAAAA,MAAA,MAAA,OAAA,4BAAY,0BAA0B,cAAc,OAAO,KAAK,WAAW,IAAI,MAAM;AACrFA,sBAAA,MAAA,MAAA,OAAA,4BAAY,gCAAgC,2CAAa,SAAS;AAClEA,sBAAA,MAAA,MAAA,OAAA,4BAAY,gCAAgC,2CAAa,OAAO;AAChEA,sBAAA,MAAA,MAAA,OAAA,4BAAY,gCAAgC,2CAAa,UAAU;AACnEA,sBAAY,MAAA,MAAA,OAAA,4BAAA,0CAA0C,2CAAa,OAAO;AAC1EA,sBAAA,MAAA,MAAA,OAAA,4BAAY,8CAA8C,2CAAa,WAAW;AAClFA,sBAAA,MAAA,MAAA,OAAA,4BAAY,+BAA+B,2CAAa,EAAE;AAE1D,YAAI,CAAC,aAAa;AAChB,gBAAM,IAAI,MAAM,cAAc;AAAA,QAC/B;AAGD,YAAI,YAAY,eAAe,CAAC,YAAY,SAAS;AACnD,sBAAY,UAAU,YAAY;AAClCA,wBAAA,MAAA,MAAA,OAAA,4BAAY,mDAAmD,YAAY,OAAO;AAAA,QACnF;AAED,aAAK,iBAAiB,WAAW;AACjCA,sBAAAA,MAAA,MAAA,OAAA,4BAAY,8BAA8B;AAC1C,eAAO;AAAA,MACR,SAAQ,OAAO;AACdA,sBAAAA,MAAc,MAAA,SAAA,4BAAA,6BAA6B,KAAK;AAChDA,sBAAc,MAAA,MAAA,SAAA,4BAAA,0BAA0B,MAAM,YAAY,IAAI;AAC9DA,uEAAc,0BAA0B,MAAM,OAAO;AACrDA,uEAAc,0BAA0B,MAAM,KAAK;AAGnD,aAAK,iBAAiB,IAAI;AAE1BG,2BAAU,MAAM,WAAW,UAAU;AACrC,cAAM;AAAA,MACd,UAAgB;AACR,aAAK,WAAW,KAAK;AAAA,MACtB;AAAA,IACF;AAAA;AAAA,IAGD,MAAM,cAAc,WAAW;AAC7B,UAAI;AACFH,sBAAAA,MAAY,MAAA,OAAA,4BAAA,0BAA0B,SAAS;AAE/C,cAAM,WAAW,MAAMO,YAAwB,cAAC,SAAS;AAGzD,aAAK,oBAAoB,EAAE,WAAW,QAAQ,YAAW,CAAE;AAG3DP,sBAAAA,MAAY,MAAA,OAAA,4BAAA,2BAA2B;AACvCA,sBAAG,MAAC,MAAM,kBAAkB;AAAA,UAC1B,SAAS;AAAA,UACT,MAAM;AAAA,QAChB,CAAS;AAGD,mBAAW,MAAM;AACf,eAAK,gBAAgB,EAAE,MAAM,GAAG,UAAU,IAAI,SAAS,MAAM;AAAA,QAC9D,GAAE,GAAI;AAEPE,iBAAAA,YAAY,OAAO;AACnBF,sBAAAA,MAAA,MAAA,OAAA,4BAAY,uBAAuB;AACnC,eAAO;AAAA,MACR,SAAQ,OAAO;AACdA,sBAAAA,MAAA,MAAA,SAAA,4BAAc,0BAA0B,KAAK;AAC7CG,2BAAU,MAAM,WAAW,QAAQ;AACnC,cAAM;AAAA,MACP;AAAA,IACF;AAAA;AAAA,IAGD,qBAAqB;AACnB,WAAK,gBAAgB;AAAA,IACtB;AAAA;AAAA,IAGD,kBAAkB;AAChB,WAAK,aAAa;AAAA,QAChB,SAAS;AAAA,QACT,UAAU;AAAA,QACV,OAAO;AAAA,QACP,YAAY;AAAA,QACZ,aAAa;AAAA,MACd;AAAA,IACF;AAAA;AAAA;AAAA,IAKD,MAAM,mBAAmB,EAAE,SAAS,QAAQ;AAC1C,UAAI;AACFH,sBAAAA,MAAA,MAAA,OAAA,4BAAY,uBAAuB;AACnC,aAAK,WAAW,IAAI;AAEpB,cAAM,WAAW,MAAMQ,+BAA8B,SAAS,IAAI;AAElEN,iBAAAA,YAAY,SAAS;AACrBF,sBAAAA,MAAA,MAAA,OAAA,4BAAY,uBAAuB;AACnC,eAAO;AAAA,MACR,SAAQ,OAAO;AACdA,sBAAAA,MAAA,MAAA,SAAA,4BAAc,0BAA0B,KAAK;AAC7CG,2BAAU,MAAM,WAAW,QAAQ;AACnC,cAAM;AAAA,MACd,UAAgB;AACR,aAAK,WAAW,KAAK;AAAA,MACtB;AAAA,IACF;AAAA;AAAA,IAGD,MAAM,qBAAqB,SAAS,IAAI;AACtC,UAAI;AACFH,sBAAAA,+CAAY,+BAA+B,MAAM;AACjD,aAAK,WAAW,IAAI;AAEpB,cAAM,WAAW,MAAMS,YAAmC,yBAAC,MAAM;AACjET,sBAAAA,+CAAY,6BAA6B,QAAQ;AAEjD,YAAI,aAAa,SAAS,QAAQ,SAAS,OAAO;AAEhD,gBAAM,eAAe,SAAS,OAAO,WAAW,SAAS;AACzD,gBAAM,SAAS,aAAa,QAAQ,aAAa,QAAQ,CAAE;AAE3DA,wBAAAA,+CAAY,2BAA2B,MAAM;AAE7C,cAAI,OAAO,SAAS,KAAK,OAAO,SAAS;AACvC,iBAAK,iBAAiB,MAAM;AAAA,UACxC,OAAiB;AAEL,iBAAK,gBAAgB,CAAC,GAAG,KAAK,eAAe,GAAG,MAAM;AAAA,UACvD;AAGD,cAAI,aAAa,YAAY;AAC3B,iBAAK,cAAc,aAAa,UAAU;AAAA,UAC3C;AAAA,QACF;AAED,eAAO;AAAA,MACR,SAAQ,OAAO;AACdA,sBAAAA,MAAc,MAAA,SAAA,4BAAA,4BAA4B,KAAK;AAC/CG,2BAAU,MAAM,WAAW,UAAU;AACrC,cAAM;AAAA,MACd,UAAgB;AACR,aAAK,WAAW,KAAK;AAAA,MACtB;AAAA,IACF;AAAA;AAAA,IAGD,MAAM,sBAAsB,aAAa;AACvC,UAAI;AACFH,sBAAAA,MAAY,MAAA,OAAA,4BAAA,yBAAyB;AAErC,cAAM,WAAW,MAAMU,YAA6B,mBAAC,WAAW;AAEhER,iBAAAA,YAAY,UAAU;AACtBF,sBAAAA,MAAY,MAAA,OAAA,4BAAA,yBAAyB;AACrC,eAAO,SAAS;AAAA,MACjB,SAAQ,OAAO;AACdA,sBAAAA,MAAc,MAAA,SAAA,4BAAA,4BAA4B,KAAK;AAC/CG,2BAAU,MAAM,WAAW,UAAU;AACrC,cAAM;AAAA,MACP;AAAA,IACF;AAAA;AAAA,IAGD,MAAM,sBAAsB,SAAS;AACnC,UAAI;AACFH,sBAAAA,+CAAY,8BAA8B,OAAO;AAEjD,cAAM,WAAW,MAAMW,YAA8B,oBAAC,OAAO;AAE7D,aAAK,iBAAiB,SAAS,IAAI;AACnCX,sBAAAA,MAAY,MAAA,OAAA,4BAAA,2BAA2B;AACvC,eAAO,SAAS;AAAA,MACjB,SAAQ,OAAO;AACdA,sBAAAA,MAAc,MAAA,SAAA,4BAAA,8BAA8B,KAAK;AACjDG,2BAAU,MAAM,WAAW,YAAY;AACvC,cAAM;AAAA,MACP;AAAA,IACF;AAAA;AAAA,IAGD,MAAM,iBAAiB,SAAS;AAC9B,UAAI;AACFH,sBAAAA,MAAY,MAAA,OAAA,4BAAA,4BAA4B,OAAO;AAE/C,cAAM,WAAW,MAAMY,YAA2B,iBAAC,OAAO;AAE1DV,iBAAAA,YAAY,QAAQ;AACpBF,sBAAAA,MAAA,MAAA,OAAA,4BAAY,uBAAuB;AACnC,eAAO,SAAS;AAAA,MACjB,SAAQ,OAAO;AACdA,sBAAAA,MAAA,MAAA,SAAA,4BAAc,0BAA0B,KAAK;AAC7CG,2BAAU,MAAM,WAAW,QAAQ;AACnC,cAAM;AAAA,MACP;AAAA,IACF;AAAA;AAAA,IAGD,MAAM,4BAA4B;AAChC,UAAI;AACFH,sBAAAA,MAAA,MAAA,OAAA,4BAAY,6BAA6B;AAEzC,cAAM,WAAW,MAAMa,sCAAsC;AAE7D,aAAK,qBAAqB,SAAS,QAAQ,CAAA,CAAE;AAC7Cb,sBAAAA,MAAA,MAAA,OAAA,4BAAY,6BAA6B;AACzC,eAAO,SAAS;AAAA,MACjB,SAAQ,OAAO;AACdA,sBAAAA,iDAAc,gCAAgC,KAAK;AACnDG,2BAAU,MAAM,WAAW,cAAc;AACzC,cAAM;AAAA,MACP;AAAA,IACF;AAAA;AAAA,IAGD,MAAM,qBAAqB,EAAE,WAAW,QAAQ;AAC9C,UAAI;AACFH,sBAAA,MAAA,MAAA,OAAA,4BAAY,4BAA4B,EAAE,WAAW,MAAM;AAE3D,cAAM,WAAW,MAAMc,gCAA+B,WAAW,IAAI;AAErEZ,iBAAAA,YAAY,KAAK,WAAW,aAAa,YAAY,SAAS;AAC9DF,sBAAAA,MAAY,MAAA,OAAA,4BAAA,yBAAyB;AACrC,eAAO;AAAA,MACR,SAAQ,OAAO;AACdA,sBAAAA,MAAc,MAAA,SAAA,4BAAA,4BAA4B,KAAK;AAC/CG,2BAAU,MAAM,WAAW,UAAU;AACrC,cAAM;AAAA,MACP;AAAA,IACF;AAAA;AAAA,IAGD,MAAM,qBAAqB,SAAS,IAAI;AACtC,UAAI;AACFH,sBAAAA,MAAA,MAAA,OAAA,4BAAY,6BAA6B;AAEzC,cAAM,WAAW,MAAMe,YAA8B,oBAAC,MAAM;AAE5D,aAAK,qBAAqB,SAAS,QAAQ,CAAA,CAAE;AAC7Cf,sBAAAA,MAAA,MAAA,OAAA,4BAAY,6BAA6B;AACzC,eAAO;AAAA,MACR,SAAQ,OAAO;AACdA,sBAAAA,MAAc,MAAA,SAAA,4BAAA,4BAA4B,KAAK;AAC/CG,2BAAU,MAAM,WAAW,UAAU;AACrC,cAAM;AAAA,MACP;AAAA,IACF;AAAA;AAAA,IAGD,MAAM,2BAA2B,SAAS,IAAI;AAC5C,UAAI;AACFH,sBAAAA,MAAA,MAAA,OAAA,4BAAY,6BAA6B;AAEzC,cAAM,WAAW,MAAMgB,YAAoC,0BAAC,MAAM;AAElE,aAAK,uBAAuB,SAAS,QAAQ,CAAA,CAAE;AAC/ChB,sBAAAA,MAAA,MAAA,OAAA,4BAAY,6BAA6B;AACzC,eAAO;AAAA,MACR,SAAQ,OAAO;AACdA,sBAAAA,MAAc,MAAA,SAAA,4BAAA,4BAA4B,KAAK;AAC/CG,2BAAU,MAAM,WAAW,UAAU;AACrC,cAAM;AAAA,MACP;AAAA,IACF;AAAA;AAAA,IAGD,MAAM,iBAAiB,WAAW;AAChC,UAAI;AACFH,sBAAAA,+CAAY,4BAA4B,SAAS;AACjD,aAAK,WAAW,IAAI;AAEpB,cAAM,WAAW,MAAMW,YAA8B,oBAAC,SAAS;AAE/D,aAAK,iBAAiB,SAAS,IAAI;AACnCX,sBAAAA,MAAY,MAAA,OAAA,4BAAA,yBAAyB;AACrC,eAAO;AAAA,MACR,SAAQ,OAAO;AACdA,sBAAAA,MAAc,MAAA,SAAA,4BAAA,4BAA4B,KAAK;AAC/CG,2BAAU,MAAM,WAAW,UAAU;AACrC,cAAM;AAAA,MACd,UAAgB;AACR,aAAK,WAAW,KAAK;AAAA,MACtB;AAAA,IACF;AAAA;AAAA,IAGD,MAAM,yBAAyB,EAAE,WAAW,iBAAiB;AAC3D,UAAI;AACFH,sBAAA,MAAA,MAAA,OAAA,4BAAY,6BAA6B,EAAE,WAAW,eAAe;AACrE,aAAK,WAAW,IAAI;AAGpB,cAAM,WAAW,MAAMiB,qCAAoC,WAAW,aAAa;AAGnF,cAAM,KAAK,iBAAiB,SAAS;AAErCf,iBAAAA,YAAY,QAAQ;AACpBF,sBAAAA,MAAY,MAAA,OAAA,4BAAA,0BAA0B;AACtC,eAAO;AAAA,MACR,SAAQ,OAAO;AACdA,sBAAAA,MAAc,MAAA,SAAA,4BAAA,6BAA6B,KAAK;AAChDG,2BAAU,MAAM,WAAW,SAAS;AACpC,cAAM;AAAA,MACd,UAAgB;AACR,aAAK,WAAW,KAAK;AAAA,MACtB;AAAA,IACF;AAAA;AAAA,IAGD,MAAM,sBAAsB,EAAE,WAAW,YAAY;AACnD,UAAI;AACFH,sBAAY,MAAA,MAAA,OAAA,4BAAA,4BAA4B,EAAE,WAAW,UAAU;AAC/D,aAAK,WAAW,IAAI;AAGpB,cAAM,WAAW,MAAMkB,kCAAiC,WAAW,QAAQ;AAG3E,cAAM,KAAK,iBAAiB,SAAS;AAErChB,iBAAAA,YAAY,SAAS;AACrBF,sBAAAA,MAAY,MAAA,OAAA,4BAAA,yBAAyB;AACrC,eAAO;AAAA,MACR,SAAQ,OAAO;AACdA,sBAAAA,MAAc,MAAA,SAAA,4BAAA,4BAA4B,KAAK;AAC/CG,2BAAU,MAAM,WAAW,UAAU;AACrC,cAAM;AAAA,MACd,UAAgB;AACR,aAAK,WAAW,KAAK;AAAA,MACtB;AAAA,IACF;AAAA,EACF;AACH,CAAC;;"}