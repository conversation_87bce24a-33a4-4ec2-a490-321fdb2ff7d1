{"version": 3, "file": "venue.js", "sources": ["stores/venue.js"], "sourcesContent": ["import { defineStore } from 'pinia'\nimport * as venueApi from '@/api/venue.js'\nimport * as timeslotApi from '@/api/timeslot.js'\nimport { showError } from '@/utils/ui.js'\n\nexport const useVenueStore = defineStore('venue', {\n  state: () => ({\n    venueList: [],\n    popularVenues: [],\n    venueDetail: null,\n    venueTypes: [],\n    timeSlots: [],\n    searchResults: [],\n    loading: false,\n    pagination: {\n      current: 1,\n      pageSize: 10,\n      total: 0,\n      totalPages: 1\n    }\n  }),\n\n  getters: {\n    // 场馆列表相关 - 这些应该是getter，返回状态值\n    venueListGetter: (state) => state.venueList,\n    popularVenuesGetter: (state) => state.popularVenues,\n    venueDetailGetter: (state) => state.venueDetail,\n    venueTypesGetter: (state) => state.venueTypes,\n    timeSlotsGetter: (state) => state.timeSlots,\n    searchResultsGetter: (state) => state.searchResults,\n\n    // 状态相关\n    isLoading: (state) => state.loading,\n    getPagination: (state) => state.pagination,\n\n    // 计算属性\n    totalVenues: (state) => state.venueList.length,\n    hasMoreVenues: (state) => state.pagination.current < state.pagination.totalPages,\n\n    // 按类型筛选场馆\n    getVenuesByType: (state) => (typeId) => {\n      if (!typeId) return state.venueList\n      return state.venueList.filter(venue => venue.typeId === typeId)\n    },\n\n    // 获取可用时间段\n    getAvailableTimeSlots: (state) => {\n      return state.timeSlots.filter(slot => slot.status === 'AVAILABLE')\n    }\n  },\n\n  actions: {\n    // 设置加载状态\n    setLoading(loading) {\n      this.loading = loading\n    },\n    \n    // 设置场馆列表\n    setVenueList({ list, pagination }) {\n      this.venueList = list\n      if (pagination) {\n        this.pagination = { ...this.pagination, ...pagination }\n      }\n    },\n    \n    // 追加场馆列表（分页加载）\n    appendVenueList(list) {\n      this.venueList = [...this.venueList, ...list]\n    },\n    \n    // 设置热门场馆\n    setPopularVenues(venues) {\n      this.popularVenues = venues\n    },\n    \n    // 设置场馆详情\n    setVenueDetail(venue) {\n      this.venueDetail = venue\n    },\n    \n    // 设置场馆类型\n    setVenueTypes(types) {\n      this.venueTypes = types\n    },\n    \n    // 设置时间段\n    setTimeSlots(slots) {\n      console.log('[VenueStore] setTimeSlots 被调用，参数:', slots)\n      console.log('[VenueStore] setTimeSlots 参数类型:', typeof slots)\n      console.log('[VenueStore] setTimeSlots 是否为数组:', Array.isArray(slots))\n\n      // 确保设置的是数组\n      if (Array.isArray(slots)) {\n        this.timeSlots = slots\n      } else {\n        console.warn('[VenueStore] setTimeSlots 收到非数组参数，强制设置为空数组')\n        this.timeSlots = []\n      }\n\n      console.log('[VenueStore] setTimeSlots 设置后的值:', this.timeSlots)\n    },\n    \n    // 设置搜索结果\n    setSearchResults(results) {\n      this.searchResults = results\n    },\n    \n    // 设置分页信息\n    setPagination(pagination) {\n      this.pagination = { ...this.pagination, ...pagination }\n    },\n\n    // 获取场馆列表\n    async getVenueList(params = {}) {\n      try {\n        console.log('[VenueStore] 开始获取场馆列表，参数:', params)\n        this.setLoading(true)\n        \n        // 添加超时处理\n        const timeoutPromise = new Promise((_, reject) => {\n          setTimeout(() => reject(new Error('请求超时')), 10000) // 10秒超时\n        })\n        \n        const apiPromise = venueApi.getVenueList(params)\n        const response = await Promise.race([apiPromise, timeoutPromise])\n        \n        console.log('[VenueStore] 场馆API响应:', response)\n        \n        // 处理响应数据\n        let list = []\n        let pagination = {\n          current: 1,\n          pageSize: 10,\n          total: 0,\n          totalPages: 1\n        }\n        \n        if (response && response.data) {\n          if (Array.isArray(response.data)) {\n            list = response.data\n            pagination = {\n              current: response.page || params.page || 1,\n              pageSize: response.pageSize || params.pageSize || 10,\n              total: response.total || response.data.length,\n              totalPages: response.totalPages || 1\n            }\n          } else {\n            console.warn('[VenueStore] API响应数据格式异常，使用空数组:', response)\n          }\n        } else if (response && Array.isArray(response)) {\n          // 直接返回数组的情况\n          list = response\n          pagination.total = response.length\n        } else {\n          console.warn('[VenueStore] API响应为空或格式错误，使用空数组:', response)\n        }\n        \n        console.log('[VenueStore] 解析的场馆列表:', list)\n        console.log('[VenueStore] 分页信息:', pagination)\n        \n        if (params.page === 1 || params.refresh) {\n          this.setVenueList({ list, pagination })\n        } else {\n          this.appendVenueList(list)\n          this.setVenueList({ list: this.venueList, pagination })\n        }\n        \n        return response\n      } catch (error) {\n        console.error('[VenueStore] 获取场馆列表失败:', error)\n        showError(error.message || '获取场馆列表失败')\n        throw error\n      } finally {\n        this.setLoading(false)\n      }\n    },\n\n    // 获取热门场馆\n    async getPopularVenues() {\n      try {\n        console.log('[VenueStore] 开始获取热门场馆')\n        const response = await venueApi.getPopularVenues()\n        \n        if (response && response.data) {\n          this.setPopularVenues(response.data)\n          console.log('[VenueStore] 热门场馆获取成功:', response.data)\n        }\n        \n        return response\n      } catch (error) {\n        console.error('[VenueStore] 获取热门场馆失败:', error)\n        showError(error.message || '获取热门场馆失败')\n        throw error\n      }\n    },\n\n    // 获取场馆详情\n    async getVenueDetail(venueId) {\n      try {\n        console.log('[VenueStore] 开始获取场馆详情:', venueId)\n        this.setLoading(true)\n        \n        const response = await venueApi.getVenueDetail(venueId)\n        console.log('[VenueStore] 完整API响应:', response)\n        console.log('[VenueStore] 响应数据类型:', typeof response)\n        console.log('[VenueStore] 响应数据结构:', Object.keys(response || {}))\n\n        if (response && response.data) {\n          this.setVenueDetail(response.data)\n          console.log('[VenueStore] 场馆详情获取成功:', response.data)\n        } else if (response) {\n          // 如果没有data字段，可能数据直接在response中\n          this.setVenueDetail(response)\n          console.log('[VenueStore] 场馆详情获取成功（直接响应）:', response)\n        }\n        \n        return response\n      } catch (error) {\n        console.error('[VenueStore] 获取场馆详情失败:', error)\n        showError(error.message || '获取场馆详情失败')\n        throw error\n      } finally {\n        this.setLoading(false)\n      }\n    },\n\n    // 获取场馆类型\n    async getVenueTypes() {\n      try {\n        console.log('[VenueStore] 开始获取场馆类型')\n        const response = await venueApi.getVenueTypes()\n        \n        if (response && response.data) {\n          this.setVenueTypes(response.data)\n          console.log('[VenueStore] 场馆类型获取成功:', response.data)\n        }\n        \n        return response\n      } catch (error) {\n        console.error('[VenueStore] 获取场馆类型失败:', error)\n        showError(error.message || '获取场馆类型失败')\n        throw error\n      }\n    },\n\n    // 获取时间段\n    async getTimeSlots(venueId, date) {\n      try {\n        console.log('[VenueStore] 开始获取时间段:', { venueId, date })\n        this.setLoading(true)\n\n        const response = await timeslotApi.getVenueTimeSlots(venueId, date)\n\n        if (response && response.data && response.data.length > 0) {\n          this.setTimeSlots(response.data)\n          console.log('[VenueStore] 时间段获取成功:', response.data)\n        } else {\n          // 如果没有时间段数据，尝试生成（恢复原有逻辑）\n          console.log('[VenueStore] 没有时间段数据，尝试生成...')\n\n          try {\n            // 先尝试调用后端生成API\n            await timeslotApi.generateTimeSlots(venueId, date)\n            console.log('[VenueStore] 后端生成时间段API调用成功')\n\n            // 重新获取生成的时间段\n            const retryResponse = await timeslotApi.getVenueTimeSlots(venueId, date)\n            if (retryResponse && retryResponse.data && retryResponse.data.length > 0) {\n              this.setTimeSlots(retryResponse.data)\n              console.log('[VenueStore] 生成并获取时间段成功:', retryResponse.data)\n            } else {\n              console.warn('[VenueStore] 后端生成API调用成功但没有返回数据，使用前端生成')\n              this.generateDefaultTimeSlots(venueId, date)\n            }\n          } catch (generateError) {\n            console.warn('[VenueStore] 后端生成时间段失败，使用前端生成:', generateError)\n            // 如果后端生成失败，使用前端生成作为备用方案\n            this.generateDefaultTimeSlots(venueId, date)\n          }\n        }\n\n        return response\n      } catch (error) {\n        console.error('[VenueStore] 获取时间段失败:', error)\n        // 如果API完全失败，生成默认时间段作为备用\n        console.log('[VenueStore] API失败，生成默认时间段作为备用')\n        this.generateDefaultTimeSlots(venueId, date)\n        // 不抛出错误，让用户可以继续使用默认时间段\n      } finally {\n        this.setLoading(false)\n      }\n    },\n\n    // 生成默认时间段（半小时间隔）\n    generateDefaultTimeSlots(venueId, date) {\n      console.log('[VenueStore] 开始生成默认时间段（半小时间隔）')\n\n      // 获取场馆详情中的价格（半小时价格 = 小时价格 / 2）\n      const venueHourPrice = this.venueDetail?.price || 100\n      const venueHalfHourPrice = Math.round(venueHourPrice / 2) // 半小时价格\n      console.log('[VenueStore] 场馆小时价格:', venueHourPrice)\n      console.log('[VenueStore] 场馆半小时价格:', venueHalfHourPrice)\n\n      const defaultSlots = []\n      const startHour = 9\n      const endHour = 22\n\n      // 生成半小时间隔的时间段\n      for (let hour = startHour; hour < endHour; hour++) {\n        for (let minute = 0; minute < 60; minute += 30) {\n          const startTime = `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`\n          const endMinute = minute + 30\n          const nextHour = endMinute >= 60 ? hour + 1 : hour\n          const actualEndMinute = endMinute >= 60 ? 0 : endMinute\n          const endTime = `${nextHour.toString().padStart(2, '0')}:${actualEndMinute.toString().padStart(2, '0')}`\n\n          // 避免超过营业时间\n          if (nextHour > endHour) break\n\n          defaultSlots.push({\n            id: `default_${venueId}_${date}_${hour}_${minute}`,\n            venueId: parseInt(venueId),\n            date: date,\n            startTime: startTime,\n            endTime: endTime,\n            price: venueHalfHourPrice, // 使用场馆的半小时价格\n            status: 'AVAILABLE'\n          })\n        }\n      }\n\n      console.log('[VenueStore] 生成的默认时间段数量:', defaultSlots.length)\n      console.log('[VenueStore] 生成的默认时间段详情:', defaultSlots)\n\n      this.setTimeSlots(defaultSlots)\n      console.log('[VenueStore] 默认时间段设置完成')\n    },\n\n    // 获取场馆时间段（别名方法，用于兼容性）\n    async getVenueTimeSlots(params) {\n      if (typeof params === 'object' && params.venueId && params.date) {\n        return await this.getTimeSlots(params.venueId, params.date)\n      }\n      // 如果参数格式不对，尝试直接调用\n      return await this.getTimeSlots(params)\n    },\n\n    // 搜索场馆\n    async searchVenues(params) {\n      try {\n        console.log('[VenueStore] 开始搜索场馆:', params)\n        this.setLoading(true)\n\n        const response = await venueApi.searchVenues(params)\n\n        if (response && response.data) {\n          this.setSearchResults(response.data)\n          console.log('[VenueStore] 场馆搜索成功:', response.data)\n        } else {\n          // 如果搜索API返回空结果，尝试使用本地过滤作为备用方案\n          console.log('[VenueStore] 搜索API返回空结果，尝试本地过滤...')\n          await this.searchVenuesLocally(params)\n        }\n\n        return response\n      } catch (error) {\n        console.error('[VenueStore] 搜索场馆失败:', error)\n        // 如果搜索API失败，尝试使用本地过滤作为备用方案\n        console.log('[VenueStore] 搜索API失败，尝试本地过滤...')\n        try {\n          await this.searchVenuesLocally(params)\n        } catch (localError) {\n          console.error('[VenueStore] 本地搜索也失败:', localError)\n          showError('搜索功能暂时不可用')\n        }\n        throw error\n      } finally {\n        this.setLoading(false)\n      }\n    },\n\n    // 本地搜索备用方案\n    async searchVenuesLocally(params) {\n      try {\n        // 获取所有场馆数据\n        const allVenuesResponse = await venueApi.getVenueList({ page: 1, pageSize: 100 })\n\n        if (allVenuesResponse && allVenuesResponse.data) {\n          const allVenues = allVenuesResponse.data\n          const keyword = params.keyword?.toLowerCase() || ''\n\n          // 本地过滤\n          const filteredVenues = allVenues.filter(venue => {\n            const nameMatch = venue.name?.toLowerCase().includes(keyword)\n            const typeMatch = venue.type?.toLowerCase().includes(keyword)\n            const locationMatch = venue.location?.toLowerCase().includes(keyword)\n            const descriptionMatch = venue.description?.toLowerCase().includes(keyword)\n\n            return nameMatch || typeMatch || locationMatch || descriptionMatch\n          })\n\n          this.setSearchResults(filteredVenues)\n          console.log('[VenueStore] 本地搜索完成，结果:', filteredVenues)\n        }\n      } catch (error) {\n        console.error('[VenueStore] 本地搜索失败:', error)\n        throw error\n      }\n    },\n\n    // 清空场馆详情\n    clearVenueDetail() {\n      this.venueDetail = null\n    },\n    \n    // 清空搜索结果\n    clearSearchResults() {\n      this.searchResults = []\n    },\n    \n    // 重置分页\n    resetPagination() {\n      this.pagination = {\n        current: 1,\n        pageSize: 10,\n        total: 0,\n        totalPages: 1\n      }\n    },\n\n    // 清除时间段缓存\n    clearTimeSlots() {\n      console.log('[VenueStore] 清除时间段缓存')\n      this.timeSlots = []\n      this.selectedTimeSlots = []\n\n      // 清除旧的请求缓存（兼容性）\n      if (typeof window !== 'undefined' && window.requestCache) {\n        // 清除所有时间段相关的缓存\n        Object.keys(window.requestCache).forEach(key => {\n          if (key.includes('/api/timeslots/') || key.includes('/timeslots/')) {\n            delete window.requestCache[key]\n            console.log('[VenueStore] 清除旧缓存键:', key)\n          }\n        })\n      }\n\n      // 清除新的缓存管理器中的缓存\n      if (typeof window !== 'undefined' && window.cacheManager && window.cacheManager.cache) {\n        let clearedCount = 0\n        for (const [key] of window.cacheManager.cache.entries()) {\n          if (key.includes('/timeslots/')) {\n            window.cacheManager.cache.delete(key)\n            clearedCount++\n            console.log('[VenueStore] 清除缓存键:', key)\n          }\n        }\n        console.log(`[VenueStore] 共清除 ${clearedCount} 个时间段缓存`)\n      }\n    }\n  }\n})\n"], "names": ["defineStore", "uni", "venueApi.getVenueList", "showError", "venueApi.getPopularVenues", "venueApi.getVenueDetail", "venueApi.getVenueTypes", "timeslotApi.getVenueTimeSlots", "timeslotApi.generateTimeSlots", "venueApi.searchVenues", "_a"], "mappings": ";;;;;AAKY,MAAC,gBAAgBA,cAAW,YAAC,SAAS;AAAA,EAChD,OAAO,OAAO;AAAA,IACZ,WAAW,CAAE;AAAA,IACb,eAAe,CAAE;AAAA,IACjB,aAAa;AAAA,IACb,YAAY,CAAE;AAAA,IACd,WAAW,CAAE;AAAA,IACb,eAAe,CAAE;AAAA,IACjB,SAAS;AAAA,IACT,YAAY;AAAA,MACV,SAAS;AAAA,MACT,UAAU;AAAA,MACV,OAAO;AAAA,MACP,YAAY;AAAA,IACb;AAAA,EACL;AAAA,EAEE,SAAS;AAAA;AAAA,IAEP,iBAAiB,CAAC,UAAU,MAAM;AAAA,IAClC,qBAAqB,CAAC,UAAU,MAAM;AAAA,IACtC,mBAAmB,CAAC,UAAU,MAAM;AAAA,IACpC,kBAAkB,CAAC,UAAU,MAAM;AAAA,IACnC,iBAAiB,CAAC,UAAU,MAAM;AAAA,IAClC,qBAAqB,CAAC,UAAU,MAAM;AAAA;AAAA,IAGtC,WAAW,CAAC,UAAU,MAAM;AAAA,IAC5B,eAAe,CAAC,UAAU,MAAM;AAAA;AAAA,IAGhC,aAAa,CAAC,UAAU,MAAM,UAAU;AAAA,IACxC,eAAe,CAAC,UAAU,MAAM,WAAW,UAAU,MAAM,WAAW;AAAA;AAAA,IAGtE,iBAAiB,CAAC,UAAU,CAAC,WAAW;AACtC,UAAI,CAAC;AAAQ,eAAO,MAAM;AAC1B,aAAO,MAAM,UAAU,OAAO,WAAS,MAAM,WAAW,MAAM;AAAA,IAC/D;AAAA;AAAA,IAGD,uBAAuB,CAAC,UAAU;AAChC,aAAO,MAAM,UAAU,OAAO,UAAQ,KAAK,WAAW,WAAW;AAAA,IAClE;AAAA,EACF;AAAA,EAED,SAAS;AAAA;AAAA,IAEP,WAAW,SAAS;AAClB,WAAK,UAAU;AAAA,IAChB;AAAA;AAAA,IAGD,aAAa,EAAE,MAAM,cAAc;AACjC,WAAK,YAAY;AACjB,UAAI,YAAY;AACd,aAAK,aAAa,EAAE,GAAG,KAAK,YAAY,GAAG,WAAY;AAAA,MACxD;AAAA,IACF;AAAA;AAAA,IAGD,gBAAgB,MAAM;AACpB,WAAK,YAAY,CAAC,GAAG,KAAK,WAAW,GAAG,IAAI;AAAA,IAC7C;AAAA;AAAA,IAGD,iBAAiB,QAAQ;AACvB,WAAK,gBAAgB;AAAA,IACtB;AAAA;AAAA,IAGD,eAAe,OAAO;AACpB,WAAK,cAAc;AAAA,IACpB;AAAA;AAAA,IAGD,cAAc,OAAO;AACnB,WAAK,aAAa;AAAA,IACnB;AAAA;AAAA,IAGD,aAAa,OAAO;AAClBC,oBAAAA,MAAY,MAAA,OAAA,yBAAA,qCAAqC,KAAK;AACtDA,gEAAY,mCAAmC,OAAO,KAAK;AAC3DA,0BAAA,MAAA,OAAA,yBAAY,oCAAoC,MAAM,QAAQ,KAAK,CAAC;AAGpE,UAAI,MAAM,QAAQ,KAAK,GAAG;AACxB,aAAK,YAAY;AAAA,MACzB,OAAa;AACLA,sBAAAA,MAAA,MAAA,QAAA,yBAAa,4CAA4C;AACzD,aAAK,YAAY,CAAE;AAAA,MACpB;AAEDA,oBAAY,MAAA,MAAA,OAAA,0BAAA,oCAAoC,KAAK,SAAS;AAAA,IAC/D;AAAA;AAAA,IAGD,iBAAiB,SAAS;AACxB,WAAK,gBAAgB;AAAA,IACtB;AAAA;AAAA,IAGD,cAAc,YAAY;AACxB,WAAK,aAAa,EAAE,GAAG,KAAK,YAAY,GAAG,WAAY;AAAA,IACxD;AAAA;AAAA,IAGD,MAAM,aAAa,SAAS,IAAI;AAC9B,UAAI;AACFA,sBAAAA,MAAY,MAAA,OAAA,0BAAA,6BAA6B,MAAM;AAC/C,aAAK,WAAW,IAAI;AAGpB,cAAM,iBAAiB,IAAI,QAAQ,CAAC,GAAG,WAAW;AAChD,qBAAW,MAAM,OAAO,IAAI,MAAM,MAAM,CAAC,GAAG,GAAK;AAAA,QAC3D,CAAS;AAED,cAAM,aAAaC,UAAqB,aAAC,MAAM;AAC/C,cAAM,WAAW,MAAM,QAAQ,KAAK,CAAC,YAAY,cAAc,CAAC;AAEhED,sBAAAA,MAAY,MAAA,OAAA,0BAAA,yBAAyB,QAAQ;AAG7C,YAAI,OAAO,CAAE;AACb,YAAI,aAAa;AAAA,UACf,SAAS;AAAA,UACT,UAAU;AAAA,UACV,OAAO;AAAA,UACP,YAAY;AAAA,QACb;AAED,YAAI,YAAY,SAAS,MAAM;AAC7B,cAAI,MAAM,QAAQ,SAAS,IAAI,GAAG;AAChC,mBAAO,SAAS;AAChB,yBAAa;AAAA,cACX,SAAS,SAAS,QAAQ,OAAO,QAAQ;AAAA,cACzC,UAAU,SAAS,YAAY,OAAO,YAAY;AAAA,cAClD,OAAO,SAAS,SAAS,SAAS,KAAK;AAAA,cACvC,YAAY,SAAS,cAAc;AAAA,YACpC;AAAA,UACb,OAAiB;AACLA,0BAAAA,MAAA,MAAA,QAAA,0BAAa,mCAAmC,QAAQ;AAAA,UACzD;AAAA,QACF,WAAU,YAAY,MAAM,QAAQ,QAAQ,GAAG;AAE9C,iBAAO;AACP,qBAAW,QAAQ,SAAS;AAAA,QACtC,OAAe;AACLA,wBAAAA,MAAA,MAAA,QAAA,0BAAa,oCAAoC,QAAQ;AAAA,QAC1D;AAEDA,sBAAAA,MAAA,MAAA,OAAA,0BAAY,yBAAyB,IAAI;AACzCA,sBAAAA,MAAY,MAAA,OAAA,0BAAA,sBAAsB,UAAU;AAE5C,YAAI,OAAO,SAAS,KAAK,OAAO,SAAS;AACvC,eAAK,aAAa,EAAE,MAAM,WAAU,CAAE;AAAA,QAChD,OAAe;AACL,eAAK,gBAAgB,IAAI;AACzB,eAAK,aAAa,EAAE,MAAM,KAAK,WAAW,YAAY;AAAA,QACvD;AAED,eAAO;AAAA,MACR,SAAQ,OAAO;AACdA,sBAAAA,MAAA,MAAA,SAAA,0BAAc,0BAA0B,KAAK;AAC7CE,2BAAU,MAAM,WAAW,UAAU;AACrC,cAAM;AAAA,MACd,UAAgB;AACR,aAAK,WAAW,KAAK;AAAA,MACtB;AAAA,IACF;AAAA;AAAA,IAGD,MAAM,mBAAmB;AACvB,UAAI;AACFF,sBAAAA,MAAA,MAAA,OAAA,0BAAY,uBAAuB;AACnC,cAAM,WAAW,MAAMG,2BAA2B;AAElD,YAAI,YAAY,SAAS,MAAM;AAC7B,eAAK,iBAAiB,SAAS,IAAI;AACnCH,wBAAY,MAAA,MAAA,OAAA,0BAAA,0BAA0B,SAAS,IAAI;AAAA,QACpD;AAED,eAAO;AAAA,MACR,SAAQ,OAAO;AACdA,sBAAAA,MAAA,MAAA,SAAA,0BAAc,0BAA0B,KAAK;AAC7CE,2BAAU,MAAM,WAAW,UAAU;AACrC,cAAM;AAAA,MACP;AAAA,IACF;AAAA;AAAA,IAGD,MAAM,eAAe,SAAS;AAC5B,UAAI;AACFF,sBAAAA,MAAY,MAAA,OAAA,0BAAA,0BAA0B,OAAO;AAC7C,aAAK,WAAW,IAAI;AAEpB,cAAM,WAAW,MAAMI,UAAuB,eAAC,OAAO;AACtDJ,sBAAAA,MAAY,MAAA,OAAA,0BAAA,yBAAyB,QAAQ;AAC7CA,mEAAY,wBAAwB,OAAO,QAAQ;AACnDA,4BAAY,MAAA,OAAA,0BAAA,wBAAwB,OAAO,KAAK,YAAY,CAAA,CAAE,CAAC;AAE/D,YAAI,YAAY,SAAS,MAAM;AAC7B,eAAK,eAAe,SAAS,IAAI;AACjCA,wBAAY,MAAA,MAAA,OAAA,0BAAA,0BAA0B,SAAS,IAAI;AAAA,QACpD,WAAU,UAAU;AAEnB,eAAK,eAAe,QAAQ;AAC5BA,wBAAAA,MAAY,MAAA,OAAA,0BAAA,gCAAgC,QAAQ;AAAA,QACrD;AAED,eAAO;AAAA,MACR,SAAQ,OAAO;AACdA,sBAAAA,MAAA,MAAA,SAAA,0BAAc,0BAA0B,KAAK;AAC7CE,2BAAU,MAAM,WAAW,UAAU;AACrC,cAAM;AAAA,MACd,UAAgB;AACR,aAAK,WAAW,KAAK;AAAA,MACtB;AAAA,IACF;AAAA;AAAA,IAGD,MAAM,gBAAgB;AACpB,UAAI;AACFF,sBAAAA,MAAA,MAAA,OAAA,0BAAY,uBAAuB;AACnC,cAAM,WAAW,MAAMK,wBAAwB;AAE/C,YAAI,YAAY,SAAS,MAAM;AAC7B,eAAK,cAAc,SAAS,IAAI;AAChCL,wBAAY,MAAA,MAAA,OAAA,0BAAA,0BAA0B,SAAS,IAAI;AAAA,QACpD;AAED,eAAO;AAAA,MACR,SAAQ,OAAO;AACdA,sBAAAA,MAAA,MAAA,SAAA,0BAAc,0BAA0B,KAAK;AAC7CE,2BAAU,MAAM,WAAW,UAAU;AACrC,cAAM;AAAA,MACP;AAAA,IACF;AAAA;AAAA,IAGD,MAAM,aAAa,SAAS,MAAM;AAChC,UAAI;AACFF,sBAAY,MAAA,MAAA,OAAA,0BAAA,yBAAyB,EAAE,SAAS,MAAM;AACtD,aAAK,WAAW,IAAI;AAEpB,cAAM,WAAW,MAAMM,+BAA8B,SAAS,IAAI;AAElE,YAAI,YAAY,SAAS,QAAQ,SAAS,KAAK,SAAS,GAAG;AACzD,eAAK,aAAa,SAAS,IAAI;AAC/BN,wBAAY,MAAA,MAAA,OAAA,0BAAA,yBAAyB,SAAS,IAAI;AAAA,QAC5D,OAAe;AAELA,wBAAAA,MAAY,MAAA,OAAA,0BAAA,8BAA8B;AAE1C,cAAI;AAEF,kBAAMO,aAA6B,kBAAC,SAAS,IAAI;AACjDP,0BAAAA,6CAAY,6BAA6B;AAGzC,kBAAM,gBAAgB,MAAMM,+BAA8B,SAAS,IAAI;AACvE,gBAAI,iBAAiB,cAAc,QAAQ,cAAc,KAAK,SAAS,GAAG;AACxE,mBAAK,aAAa,cAAc,IAAI;AACpCN,4BAAA,MAAA,MAAA,OAAA,0BAAY,4BAA4B,cAAc,IAAI;AAAA,YACxE,OAAmB;AACLA,4BAAAA,MAAa,MAAA,QAAA,0BAAA,wCAAwC;AACrD,mBAAK,yBAAyB,SAAS,IAAI;AAAA,YAC5C;AAAA,UACF,SAAQ,eAAe;AACtBA,0BAAAA,MAAa,MAAA,QAAA,0BAAA,kCAAkC,aAAa;AAE5D,iBAAK,yBAAyB,SAAS,IAAI;AAAA,UAC5C;AAAA,QACF;AAED,eAAO;AAAA,MACR,SAAQ,OAAO;AACdA,sBAAAA,MAAA,MAAA,SAAA,0BAAc,yBAAyB,KAAK;AAE5CA,sBAAAA,MAAY,MAAA,OAAA,0BAAA,gCAAgC;AAC5C,aAAK,yBAAyB,SAAS,IAAI;AAAA,MAEnD,UAAgB;AACR,aAAK,WAAW,KAAK;AAAA,MACtB;AAAA,IACF;AAAA;AAAA,IAGD,yBAAyB,SAAS,MAAM;;AACtCA,oBAAAA,6CAAY,+BAA+B;AAG3C,YAAM,mBAAiB,UAAK,gBAAL,mBAAkB,UAAS;AAClD,YAAM,qBAAqB,KAAK,MAAM,iBAAiB,CAAC;AACxDA,oBAAAA,MAAY,MAAA,OAAA,0BAAA,wBAAwB,cAAc;AAClDA,oBAAAA,MAAY,MAAA,OAAA,0BAAA,yBAAyB,kBAAkB;AAEvD,YAAM,eAAe,CAAE;AACvB,YAAM,YAAY;AAClB,YAAM,UAAU;AAGhB,eAAS,OAAO,WAAW,OAAO,SAAS,QAAQ;AACjD,iBAAS,SAAS,GAAG,SAAS,IAAI,UAAU,IAAI;AAC9C,gBAAM,YAAY,GAAG,KAAK,SAAU,EAAC,SAAS,GAAG,GAAG,CAAC,IAAI,OAAO,SAAU,EAAC,SAAS,GAAG,GAAG,CAAC;AAC3F,gBAAM,YAAY,SAAS;AAC3B,gBAAM,WAAW,aAAa,KAAK,OAAO,IAAI;AAC9C,gBAAM,kBAAkB,aAAa,KAAK,IAAI;AAC9C,gBAAM,UAAU,GAAG,SAAS,SAAU,EAAC,SAAS,GAAG,GAAG,CAAC,IAAI,gBAAgB,SAAU,EAAC,SAAS,GAAG,GAAG,CAAC;AAGtG,cAAI,WAAW;AAAS;AAExB,uBAAa,KAAK;AAAA,YAChB,IAAI,WAAW,OAAO,IAAI,IAAI,IAAI,IAAI,IAAI,MAAM;AAAA,YAChD,SAAS,SAAS,OAAO;AAAA,YACzB;AAAA,YACA;AAAA,YACA;AAAA,YACA,OAAO;AAAA;AAAA,YACP,QAAQ;AAAA,UACpB,CAAW;AAAA,QACF;AAAA,MACF;AAEDA,iEAAY,4BAA4B,aAAa,MAAM;AAC3DA,oBAAAA,MAAY,MAAA,OAAA,0BAAA,4BAA4B,YAAY;AAEpD,WAAK,aAAa,YAAY;AAC9BA,oBAAAA,MAAA,MAAA,OAAA,0BAAY,wBAAwB;AAAA,IACrC;AAAA;AAAA,IAGD,MAAM,kBAAkB,QAAQ;AAC9B,UAAI,OAAO,WAAW,YAAY,OAAO,WAAW,OAAO,MAAM;AAC/D,eAAO,MAAM,KAAK,aAAa,OAAO,SAAS,OAAO,IAAI;AAAA,MAC3D;AAED,aAAO,MAAM,KAAK,aAAa,MAAM;AAAA,IACtC;AAAA;AAAA,IAGD,MAAM,aAAa,QAAQ;AACzB,UAAI;AACFA,sBAAAA,MAAA,MAAA,OAAA,0BAAY,wBAAwB,MAAM;AAC1C,aAAK,WAAW,IAAI;AAEpB,cAAM,WAAW,MAAMQ,UAAqB,aAAC,MAAM;AAEnD,YAAI,YAAY,SAAS,MAAM;AAC7B,eAAK,iBAAiB,SAAS,IAAI;AACnCR,wBAAA,MAAA,MAAA,OAAA,0BAAY,wBAAwB,SAAS,IAAI;AAAA,QAC3D,OAAe;AAELA,wBAAAA,MAAA,MAAA,OAAA,0BAAY,mCAAmC;AAC/C,gBAAM,KAAK,oBAAoB,MAAM;AAAA,QACtC;AAED,eAAO;AAAA,MACR,SAAQ,OAAO;AACdA,sBAAAA,MAAA,MAAA,SAAA,0BAAc,wBAAwB,KAAK;AAE3CA,sBAAAA,MAAY,MAAA,OAAA,0BAAA,gCAAgC;AAC5C,YAAI;AACF,gBAAM,KAAK,oBAAoB,MAAM;AAAA,QACtC,SAAQ,YAAY;AACnBA,wBAAAA,MAAA,MAAA,SAAA,0BAAc,yBAAyB,UAAU;AACjDE,mBAAAA,UAAU,WAAW;AAAA,QACtB;AACD,cAAM;AAAA,MACd,UAAgB;AACR,aAAK,WAAW,KAAK;AAAA,MACtB;AAAA,IACF;AAAA;AAAA,IAGD,MAAM,oBAAoB,QAAQ;;AAChC,UAAI;AAEF,cAAM,oBAAoB,MAAMD,uBAAsB,EAAE,MAAM,GAAG,UAAU,KAAK;AAEhF,YAAI,qBAAqB,kBAAkB,MAAM;AAC/C,gBAAM,YAAY,kBAAkB;AACpC,gBAAM,YAAU,YAAO,YAAP,mBAAgB,kBAAiB;AAGjD,gBAAM,iBAAiB,UAAU,OAAO,WAAS;;AAC/C,kBAAM,aAAYQ,MAAA,MAAM,SAAN,gBAAAA,IAAY,cAAc,SAAS;AACrD,kBAAM,aAAY,WAAM,SAAN,mBAAY,cAAc,SAAS;AACrD,kBAAM,iBAAgB,WAAM,aAAN,mBAAgB,cAAc,SAAS;AAC7D,kBAAM,oBAAmB,WAAM,gBAAN,mBAAmB,cAAc,SAAS;AAEnE,mBAAO,aAAa,aAAa,iBAAiB;AAAA,UAC9D,CAAW;AAED,eAAK,iBAAiB,cAAc;AACpCT,wBAAAA,MAAY,MAAA,OAAA,0BAAA,2BAA2B,cAAc;AAAA,QACtD;AAAA,MACF,SAAQ,OAAO;AACdA,sBAAAA,MAAA,MAAA,SAAA,0BAAc,wBAAwB,KAAK;AAC3C,cAAM;AAAA,MACP;AAAA,IACF;AAAA;AAAA,IAGD,mBAAmB;AACjB,WAAK,cAAc;AAAA,IACpB;AAAA;AAAA,IAGD,qBAAqB;AACnB,WAAK,gBAAgB,CAAE;AAAA,IACxB;AAAA;AAAA,IAGD,kBAAkB;AAChB,WAAK,aAAa;AAAA,QAChB,SAAS;AAAA,QACT,UAAU;AAAA,QACV,OAAO;AAAA,QACP,YAAY;AAAA,MACb;AAAA,IACF;AAAA;AAAA,IAGD,iBAAiB;AACfA,oBAAAA,MAAA,MAAA,OAAA,0BAAY,sBAAsB;AAClC,WAAK,YAAY,CAAE;AACnB,WAAK,oBAAoB,CAAE;AAG3B,UAAI,OAAO,WAAW,eAAe,OAAO,cAAc;AAExD,eAAO,KAAK,OAAO,YAAY,EAAE,QAAQ,SAAO;AAC9C,cAAI,IAAI,SAAS,iBAAiB,KAAK,IAAI,SAAS,aAAa,GAAG;AAClE,mBAAO,OAAO,aAAa,GAAG;AAC9BA,0BAAAA,MAAY,MAAA,OAAA,0BAAA,wBAAwB,GAAG;AAAA,UACxC;AAAA,QACX,CAAS;AAAA,MACF;AAGD,UAAI,OAAO,WAAW,eAAe,OAAO,gBAAgB,OAAO,aAAa,OAAO;AACrF,YAAI,eAAe;AACnB,mBAAW,CAAC,GAAG,KAAK,OAAO,aAAa,MAAM,WAAW;AACvD,cAAI,IAAI,SAAS,aAAa,GAAG;AAC/B,mBAAO,aAAa,MAAM,OAAO,GAAG;AACpC;AACAA,0BAAAA,MAAY,MAAA,OAAA,0BAAA,uBAAuB,GAAG;AAAA,UACvC;AAAA,QACF;AACDA,sBAAY,MAAA,MAAA,OAAA,0BAAA,oBAAoB,YAAY,SAAS;AAAA,MACtD;AAAA,IACF;AAAA,EACF;AACH,CAAC;;"}