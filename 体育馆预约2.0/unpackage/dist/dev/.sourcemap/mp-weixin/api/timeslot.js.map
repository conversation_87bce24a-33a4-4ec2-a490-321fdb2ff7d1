{"version": 3, "file": "timeslot.js", "sources": ["api/timeslot.js"], "sourcesContent": ["import { get, post, patch } from '@/utils/request.js'\n\n// 获取场馆指定日期的所有时间段\nexport function getVenueTimeSlots(venueId, date, forceRefresh = false) {\n  const options = {\n    cache: !forceRefresh, // 如果forceRefresh为true，则禁用缓存\n    cacheTTL: forceRefresh ? 0 : 60000 // 强制刷新时设置缓存时间为0\n  }\n\n  // 🔧 增强强制刷新功能\n  if (forceRefresh) {\n    // 添加时间戳和随机数避免所有层级的缓存\n    const timestamp = Date.now()\n    const randomId = Math.random().toString(36).substring(7)\n    const params = {\n      _t: timestamp,\n      _r: randomId,\n      _nocache: 1\n    }\n\n    // 设置更强的缓存控制选项\n    options.cache = false\n    options.cacheTTL = 0\n    options.forceRefresh = true\n    options.headers = {\n      'Cache-Control': 'no-cache, no-store, must-revalidate',\n      'Pragma': 'no-cache',\n      'Expires': '0'\n    }\n\n    console.log(`🚫 [API] 强制无缓存获取时间段 - venueId: ${venueId}, date: ${date}, params:`, params)\n    return get(`/timeslots/venue/${venueId}/date/${date}`, params, options)\n  }\n\n  console.log(`[API] getVenueTimeSlots - venueId: ${venueId}, date: ${date}, forceRefresh: ${forceRefresh}, options:`, options)\n  return get(`/timeslots/venue/${venueId}/date/${date}`, {}, options)\n}\n\n// 获取场馆指定日期的可用时间段\nexport function getAvailableTimeSlots(venueId, date) {\n  return get(`/timeslots/venue/${venueId}/date/${date}/available`)\n}\n\n// 检查时间段是否可预约\nexport function checkTimeSlotAvailability(params) {\n  return get('/timeslots/check', params)\n}\n\n// 为场馆生成指定日期的时间段（仅限管理员）\nexport function generateTimeSlots(venueId, date) {\n  return post(`/timeslots/venue/${venueId}/date/${date}/generate`)\n}\n\n// 批量生成未来一周的时间段（仅限管理员）\nexport function generateWeekTimeSlots(venueId) {\n  return post(`/timeslots/venue/${venueId}/generate-week`)\n}\n\n// 更新时间段状态（仅限管理员）\nexport function updateTimeSlotStatus(id, data) {\n  return patch(`/timeslots/${id}/status`, data)\n}"], "names": ["uni", "get", "post"], "mappings": ";;;;AAGO,SAAS,kBAAkB,SAAS,MAAM,eAAe,OAAO;AACrE,QAAM,UAAU;AAAA,IACd,OAAO,CAAC;AAAA;AAAA,IACR,UAAU,eAAe,IAAI;AAAA;AAAA,EAC9B;AAGD,MAAI,cAAc;AAEhB,UAAM,YAAY,KAAK,IAAK;AAC5B,UAAM,WAAW,KAAK,OAAQ,EAAC,SAAS,EAAE,EAAE,UAAU,CAAC;AACvD,UAAM,SAAS;AAAA,MACb,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,UAAU;AAAA,IACX;AAGD,YAAQ,QAAQ;AAChB,YAAQ,WAAW;AACnB,YAAQ,eAAe;AACvB,YAAQ,UAAU;AAAA,MAChB,iBAAiB;AAAA,MACjB,UAAU;AAAA,MACV,WAAW;AAAA,IACZ;AAEDA,kBAAAA,MAAY,MAAA,OAAA,yBAAA,kCAAkC,OAAO,WAAW,IAAI,aAAa,MAAM;AACvF,WAAOC,cAAG,IAAC,oBAAoB,OAAO,SAAS,IAAI,IAAI,QAAQ,OAAO;AAAA,EACvE;AAEDD,gBAAAA,MAAY,MAAA,OAAA,yBAAA,sCAAsC,OAAO,WAAW,IAAI,mBAAmB,YAAY,cAAc,OAAO;AAC5H,SAAOC,cAAG,IAAC,oBAAoB,OAAO,SAAS,IAAI,IAAI,CAAE,GAAE,OAAO;AACpE;AAaO,SAAS,kBAAkB,SAAS,MAAM;AAC/C,SAAOC,cAAAA,KAAK,oBAAoB,OAAO,SAAS,IAAI,WAAW;AACjE;;;"}