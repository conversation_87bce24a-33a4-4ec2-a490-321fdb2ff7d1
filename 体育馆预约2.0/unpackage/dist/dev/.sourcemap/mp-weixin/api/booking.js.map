{"version": 3, "file": "booking.js", "sources": ["api/booking.js"], "sourcesContent": ["import { get, post, put, del } from '@/utils/request.js'\n\n// 创建预约\nexport function createBooking(data) {\n  return post('/bookings', data)\n}\n\n// 获取预约列表\nexport function getBookingList(params) {\n  return get('/bookings', params)\n}\n\n// 获取预约详情\nexport function getBookingDetail(id) {\n  return get(`/bookings/${id}`)\n}\n\n// 取消预约\nexport function cancelBooking(id) {\n  return put(`/bookings/${id}/cancel`)\n}\n\n// 获取场馆可用时间段\nexport function getVenueAvailableSlots(venueId, date) {\n  return get(`/bookings/venues/${venueId}/slots`, { date })\n}\n\n// 创建拼场预约\nexport function createSharedBooking(data) {\n  return post('/bookings/shared', data)\n}\n\n// 申请拼场\nexport function applySharedBooking(orderId, data) {\n  return post(`/bookings/shared/${orderId}/apply`, data)\n}"], "names": ["post", "get", "put"], "mappings": ";;AAGO,SAAS,cAAc,MAAM;AAClC,SAAOA,cAAI,KAAC,aAAa,IAAI;AAC/B;AAQO,SAAS,iBAAiB,IAAI;AACnC,SAAOC,kBAAI,aAAa,EAAE,EAAE;AAC9B;AAGO,SAAS,cAAc,IAAI;AAChC,SAAOC,cAAG,IAAC,aAAa,EAAE,SAAS;AACrC;AAGO,SAAS,uBAAuB,SAAS,MAAM;AACpD,SAAOD,cAAAA,IAAI,oBAAoB,OAAO,UAAU,EAAE,MAAM;AAC1D;AAGO,SAAS,oBAAoB,MAAM;AACxC,SAAOD,cAAI,KAAC,oBAAoB,IAAI;AACtC;AAGO,SAAS,mBAAmB,SAAS,MAAM;AAChD,SAAOA,cAAAA,KAAK,oBAAoB,OAAO,UAAU,IAAI;AACvD;;;;;;;"}