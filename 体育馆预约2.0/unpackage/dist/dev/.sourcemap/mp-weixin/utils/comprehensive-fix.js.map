{"version": 3, "file": "comprehensive-fix.js", "sources": ["utils/comprehensive-fix.js"], "sourcesContent": ["/**\n * 综合问题修复工具\n * 一次性解决价格传递和时间段刷新的所有问题\n */\n\nimport { getVenueTimeSlots } from '@/api/timeslot.js'\n\n// 修复价格计算问题\nexport function fixPriceCalculation(selectedSlots, venue) {\n  console.log('🔧 修复价格计算问题')\n  \n  const fix = {\n    originalSlots: selectedSlots,\n    venue: venue,\n    fixedSlots: [],\n    totalPrice: 0,\n    fixes: []\n  }\n  \n  // 检查场馆价格\n  let venueHourlyPrice = 120 // 默认价格\n  if (venue && venue.price && venue.price > 0) {\n    venueHourlyPrice = parseFloat(venue.price)\n  } else {\n    fix.fixes.push('场馆价格缺失，使用默认价格120元/小时')\n  }\n  \n  const venueHalfHourPrice = venueHourlyPrice / 2\n  \n  // 修复每个时间段的价格\n  selectedSlots.forEach((slot, index) => {\n    const fixedSlot = { ...slot }\n    let slotPrice = 0\n    let priceSource = ''\n    \n    // 尝试获取时间段价格\n    if (slot.price && slot.price > 0) {\n      slotPrice = parseFloat(slot.price)\n      priceSource = 'slot.price'\n    } else if (slot.pricePerHour && slot.pricePerHour > 0) {\n      slotPrice = parseFloat(slot.pricePerHour)\n      priceSource = 'slot.pricePerHour'\n    } else {\n      // 使用场馆价格的一半（30分钟价格）\n      slotPrice = venueHalfHourPrice\n      priceSource = 'venue.price/2'\n      fixedSlot.price = slotPrice // 修复时间段价格\n      fix.fixes.push(`时间段${index + 1}(${slot.startTime}-${slot.endTime})价格缺失，设置为${slotPrice}元`)\n    }\n    \n    fixedSlot.calculatedPrice = slotPrice\n    fixedSlot.priceSource = priceSource\n    fix.fixedSlots.push(fixedSlot)\n    fix.totalPrice += slotPrice\n    \n    console.log(`💰 时间段${index + 1}: ${slot.startTime}-${slot.endTime} = ¥${slotPrice} (${priceSource})`)\n  })\n  \n  console.log(`💰 修复后总价格: ¥${fix.totalPrice}`)\n  console.log('🔧 价格修复详情:', fix.fixes)\n  \n  return fix\n}\n\n// 强力刷新时间段数据\nexport async function forceRefreshTimeSlots(venueId, date, venueStore) {\n  console.log('🔧 强力刷新时间段数据')\n  \n  const refresh = {\n    venueId: venueId,\n    date: date,\n    attempts: 0,\n    maxAttempts: 3,\n    success: false,\n    newData: null,\n    errors: []\n  }\n  \n  // 清除所有可能的缓存\n  console.log('🧹 清除所有缓存...')\n  \n  // 1. 清除VenueStore缓存\n  venueStore.clearTimeSlots()\n  \n  // 2. 清除请求缓存\n  if (typeof window !== 'undefined' && window.cacheManager) {\n    const cacheKeys = Array.from(window.cacheManager.cache.keys())\n    const timeslotKeys = cacheKeys.filter(key => \n      key.includes('timeslots') || \n      key.includes(venueId) || \n      key.includes(date)\n    )\n    \n    timeslotKeys.forEach(key => {\n      window.cacheManager.cache.delete(key)\n    })\n    \n    console.log(`🧹 清除了${timeslotKeys.length}个相关缓存键`)\n  }\n  \n  // 3. 等待缓存清除完成\n  await new Promise(resolve => setTimeout(resolve, 500))\n  \n  // 4. 多次尝试获取新数据\n  while (refresh.attempts < refresh.maxAttempts && !refresh.success) {\n    refresh.attempts++\n    console.log(`📡 第${refresh.attempts}次尝试获取时间段数据...`)\n    \n    try {\n      // 使用强制刷新API\n      const response = await getVenueTimeSlots(venueId, date, true)\n      \n      if (response && response.data && Array.isArray(response.data) && response.data.length > 0) {\n        refresh.newData = response.data\n        refresh.success = true\n        \n        // 更新VenueStore\n        venueStore.setTimeSlots(response.data)\n        \n        console.log(`✅ 第${refresh.attempts}次尝试成功，获取到${response.data.length}个时间段`)\n        \n        // 验证数据质量\n        const validSlots = response.data.filter(slot => \n          slot.id && slot.startTime && slot.endTime && slot.status\n        )\n        \n        if (validSlots.length !== response.data.length) {\n          refresh.errors.push(`数据质量问题：${response.data.length - validSlots.length}个时间段数据不完整`)\n        }\n        \n      } else {\n        const error = `第${refresh.attempts}次尝试获取到空数据或格式错误`\n        refresh.errors.push(error)\n        console.warn(`⚠️ ${error}`)\n        \n        if (refresh.attempts < refresh.maxAttempts) {\n          console.log(`⏳ 等待${refresh.attempts * 1000}ms后重试...`)\n          await new Promise(resolve => setTimeout(resolve, refresh.attempts * 1000))\n        }\n      }\n      \n    } catch (error) {\n      const errorMsg = `第${refresh.attempts}次尝试失败: ${error.message}`\n      refresh.errors.push(errorMsg)\n      console.error(`❌ ${errorMsg}`)\n      \n      if (refresh.attempts < refresh.maxAttempts) {\n        console.log(`⏳ 等待${refresh.attempts * 1000}ms后重试...`)\n        await new Promise(resolve => setTimeout(resolve, refresh.attempts * 1000))\n      }\n    }\n  }\n  \n  if (!refresh.success) {\n    console.error('❌ 所有刷新尝试都失败了')\n  }\n  \n  return refresh\n}\n\n// 综合修复函数\nexport async function comprehensiveFix(selectedSlots, venue, venueId, date, venueStore) {\n  console.log('🚀 开始综合问题修复')\n  \n  const result = {\n    timestamp: new Date().toISOString(),\n    priceFixResult: null,\n    refreshResult: null,\n    finalBookingData: null,\n    success: false,\n    issues: []\n  }\n  \n  try {\n    // 步骤1: 修复价格计算\n    console.log('\\n=== 步骤1: 修复价格计算 ===')\n    result.priceFixResult = fixPriceCalculation(selectedSlots, venue)\n    \n    if (result.priceFixResult.totalPrice <= 0) {\n      result.issues.push('价格修复后仍为0')\n    }\n    \n    // 步骤2: 强力刷新时间段\n    console.log('\\n=== 步骤2: 强力刷新时间段 ===')\n    result.refreshResult = await forceRefreshTimeSlots(venueId, date, venueStore)\n    \n    if (!result.refreshResult.success) {\n      result.issues.push('时间段刷新失败')\n    }\n    \n    // 步骤3: 构建最终预约数据\n    console.log('\\n=== 步骤3: 构建最终预约数据 ===')\n    const firstSlot = selectedSlots[0]\n    const lastSlot = selectedSlots[selectedSlots.length - 1]\n    \n    result.finalBookingData = {\n      venueId: parseInt(venueId),\n      date: date,\n      startTime: firstSlot.startTime,\n      endTime: lastSlot.endTime,\n      slotIds: selectedSlots.map(slot => slot.id),\n      bookingType: 'EXCLUSIVE',\n      description: '',\n      price: result.priceFixResult.totalPrice // 使用修复后的价格\n    }\n    \n    // 验证最终数据\n    if (result.finalBookingData.price > 0 && result.finalBookingData.slotIds.length > 0) {\n      result.success = true\n    } else {\n      if (result.finalBookingData.price <= 0) {\n        result.issues.push('最终价格仍为0')\n      }\n      if (result.finalBookingData.slotIds.length === 0) {\n        result.issues.push('没有选中的时间段')\n      }\n    }\n    \n    console.log('\\n📋 综合修复结果:')\n    console.log(`💰 价格修复: ${result.priceFixResult.fixes.length}个问题已修复`)\n    console.log(`⏰ 时间段刷新: ${result.refreshResult.success ? '成功' : '失败'}`)\n    console.log(`📊 最终价格: ¥${result.finalBookingData.price}`)\n    console.log(`🎯 综合成功: ${result.success ? '是' : '否'}`)\n    \n    if (result.issues.length > 0) {\n      console.log(`❌ 剩余问题: ${result.issues.join(', ')}`)\n    }\n    \n  } catch (error) {\n    console.error('❌ 综合修复过程出错:', error)\n    result.issues.push(`修复过程出错: ${error.message}`)\n  }\n  \n  return result\n}\n\n// 快速问题诊断和修复\nexport async function quickFixAndDiagnose(selectedSlots, venue, venueId, date, venueStore) {\n  console.log('⚡ 快速问题诊断和修复')\n  \n  // 先进行快速诊断\n  const issues = []\n  \n  // 检查基本数据\n  if (!selectedSlots || selectedSlots.length === 0) {\n    issues.push('没有选中的时间段')\n    return { success: false, issues: issues }\n  }\n  \n  if (!venue) {\n    issues.push('场馆数据缺失')\n  } else if (!venue.price || venue.price <= 0) {\n    issues.push('场馆价格缺失或为0')\n  }\n  \n  // 检查时间段价格\n  const slotsWithoutPrice = selectedSlots.filter(slot => !slot.price || slot.price <= 0)\n  if (slotsWithoutPrice.length > 0) {\n    issues.push(`${slotsWithoutPrice.length}个时间段缺少价格`)\n  }\n  \n  console.log('📊 快速诊断结果:', issues)\n  \n  // 如果有问题，执行综合修复\n  if (issues.length > 0) {\n    console.log('🔧 检测到问题，执行综合修复...')\n    return await comprehensiveFix(selectedSlots, venue, venueId, date, venueStore)\n  } else {\n    console.log('✅ 没有检测到问题')\n    return { success: true, issues: [] }\n  }\n}\n\n// 实时修复监控\nexport function startFixMonitoring(venueStore) {\n  console.log('🔍 启动实时修复监控')\n  \n  const monitor = {\n    isActive: false,\n    intervalId: null,\n    fixLog: [],\n    \n    start() {\n      if (this.isActive) return\n      \n      this.isActive = true\n      this.intervalId = setInterval(() => {\n        // 检查VenueStore状态\n        const timeSlots = venueStore.timeSlots || []\n        const venue = venueStore.venueDetail\n        \n        // 检查是否需要修复\n        const needsFix = timeSlots.length === 0 || \n                        !venue || \n                        !venue.price ||\n                        timeSlots.some(slot => !slot.price || slot.price <= 0)\n        \n        if (needsFix) {\n          const fixEntry = {\n            timestamp: new Date().toISOString(),\n            issue: '检测到数据问题',\n            timeSlotsCount: timeSlots.length,\n            venuePrice: venue?.price,\n            action: '需要手动修复'\n          }\n          \n          this.fixLog.push(fixEntry)\n          console.log('🚨 检测到需要修复的问题:', fixEntry)\n        }\n      }, 5000)\n      \n      console.log('✅ 实时修复监控已启动')\n    },\n    \n    stop() {\n      if (!this.isActive) return\n      \n      this.isActive = false\n      if (this.intervalId) {\n        clearInterval(this.intervalId)\n        this.intervalId = null\n      }\n      \n      console.log('🛑 实时修复监控已停止')\n      return this.fixLog\n    }\n  }\n  \n  monitor.start()\n  return monitor\n}\n"], "names": ["uni", "getVenueTimeSlots"], "mappings": ";;;;AAQO,SAAS,oBAAoB,eAAe,OAAO;AACxDA,gBAAAA,MAAA,MAAA,OAAA,oCAAY,aAAa;AAEzB,QAAM,MAAM;AAAA,IACV,eAAe;AAAA,IACf;AAAA,IACA,YAAY,CAAE;AAAA,IACd,YAAY;AAAA,IACZ,OAAO,CAAE;AAAA,EACV;AAGD,MAAI,mBAAmB;AACvB,MAAI,SAAS,MAAM,SAAS,MAAM,QAAQ,GAAG;AAC3C,uBAAmB,WAAW,MAAM,KAAK;AAAA,EAC7C,OAAS;AACL,QAAI,MAAM,KAAK,sBAAsB;AAAA,EACtC;AAED,QAAM,qBAAqB,mBAAmB;AAG9C,gBAAc,QAAQ,CAAC,MAAM,UAAU;AACrC,UAAM,YAAY,EAAE,GAAG,KAAM;AAC7B,QAAI,YAAY;AAChB,QAAI,cAAc;AAGlB,QAAI,KAAK,SAAS,KAAK,QAAQ,GAAG;AAChC,kBAAY,WAAW,KAAK,KAAK;AACjC,oBAAc;AAAA,IACf,WAAU,KAAK,gBAAgB,KAAK,eAAe,GAAG;AACrD,kBAAY,WAAW,KAAK,YAAY;AACxC,oBAAc;AAAA,IACpB,OAAW;AAEL,kBAAY;AACZ,oBAAc;AACd,gBAAU,QAAQ;AAClB,UAAI,MAAM,KAAK,MAAM,QAAQ,CAAC,IAAI,KAAK,SAAS,IAAI,KAAK,OAAO,YAAY,SAAS,GAAG;AAAA,IACzF;AAED,cAAU,kBAAkB;AAC5B,cAAU,cAAc;AACxB,QAAI,WAAW,KAAK,SAAS;AAC7B,QAAI,cAAc;AAElBA,wBAAA,MAAA,OAAA,oCAAY,SAAS,QAAQ,CAAC,KAAK,KAAK,SAAS,IAAI,KAAK,OAAO,OAAO,SAAS,KAAK,WAAW,GAAG;AAAA,EACxG,CAAG;AAEDA,sBAAA,MAAA,OAAA,oCAAY,eAAe,IAAI,UAAU,EAAE;AAC3CA,gBAAY,MAAA,MAAA,OAAA,oCAAA,cAAc,IAAI,KAAK;AAEnC,SAAO;AACT;AAGO,eAAe,sBAAsB,SAAS,MAAM,YAAY;AACrEA,gBAAAA,MAAY,MAAA,OAAA,oCAAA,cAAc;AAE1B,QAAM,UAAU;AAAA,IACd;AAAA,IACA;AAAA,IACA,UAAU;AAAA,IACV,aAAa;AAAA,IACb,SAAS;AAAA,IACT,SAAS;AAAA,IACT,QAAQ,CAAE;AAAA,EACX;AAGDA,gBAAAA,MAAY,MAAA,OAAA,oCAAA,cAAc;AAG1B,aAAW,eAAgB;AAG3B,MAAI,OAAO,WAAW,eAAe,OAAO,cAAc;AACxD,UAAM,YAAY,MAAM,KAAK,OAAO,aAAa,MAAM,MAAM;AAC7D,UAAM,eAAe,UAAU;AAAA,MAAO,SACpC,IAAI,SAAS,WAAW,KACxB,IAAI,SAAS,OAAO,KACpB,IAAI,SAAS,IAAI;AAAA,IAClB;AAED,iBAAa,QAAQ,SAAO;AAC1B,aAAO,aAAa,MAAM,OAAO,GAAG;AAAA,IAC1C,CAAK;AAEDA,yEAAY,SAAS,aAAa,MAAM,QAAQ;AAAA,EACjD;AAGD,QAAM,IAAI,QAAQ,aAAW,WAAW,SAAS,GAAG,CAAC;AAGrD,SAAO,QAAQ,WAAW,QAAQ,eAAe,CAAC,QAAQ,SAAS;AACjE,YAAQ;AACRA,0EAAY,OAAO,QAAQ,QAAQ,eAAe;AAElD,QAAI;AAEF,YAAM,WAAW,MAAMC,aAAAA,kBAAkB,SAAS,MAAM,IAAI;AAE5D,UAAI,YAAY,SAAS,QAAQ,MAAM,QAAQ,SAAS,IAAI,KAAK,SAAS,KAAK,SAAS,GAAG;AACzF,gBAAQ,UAAU,SAAS;AAC3B,gBAAQ,UAAU;AAGlB,mBAAW,aAAa,SAAS,IAAI;AAErCD,sBAAAA,MAAY,MAAA,OAAA,qCAAA,MAAM,QAAQ,QAAQ,YAAY,SAAS,KAAK,MAAM,MAAM;AAGxE,cAAM,aAAa,SAAS,KAAK;AAAA,UAAO,UACtC,KAAK,MAAM,KAAK,aAAa,KAAK,WAAW,KAAK;AAAA,QACnD;AAED,YAAI,WAAW,WAAW,SAAS,KAAK,QAAQ;AAC9C,kBAAQ,OAAO,KAAK,UAAU,SAAS,KAAK,SAAS,WAAW,MAAM,WAAW;AAAA,QAClF;AAAA,MAET,OAAa;AACL,cAAM,QAAQ,IAAI,QAAQ,QAAQ;AAClC,gBAAQ,OAAO,KAAK,KAAK;AACzBA,+EAAa,MAAM,KAAK,EAAE;AAE1B,YAAI,QAAQ,WAAW,QAAQ,aAAa;AAC1CA,8BAAY,MAAA,OAAA,qCAAA,OAAO,QAAQ,WAAW,GAAI,UAAU;AACpD,gBAAM,IAAI,QAAQ,aAAW,WAAW,SAAS,QAAQ,WAAW,GAAI,CAAC;AAAA,QAC1E;AAAA,MACF;AAAA,IAEF,SAAQ,OAAO;AACd,YAAM,WAAW,IAAI,QAAQ,QAAQ,UAAU,MAAM,OAAO;AAC5D,cAAQ,OAAO,KAAK,QAAQ;AAC5BA,oBAAc,MAAA,MAAA,SAAA,qCAAA,KAAK,QAAQ,EAAE;AAE7B,UAAI,QAAQ,WAAW,QAAQ,aAAa;AAC1CA,4BAAY,MAAA,OAAA,qCAAA,OAAO,QAAQ,WAAW,GAAI,UAAU;AACpD,cAAM,IAAI,QAAQ,aAAW,WAAW,SAAS,QAAQ,WAAW,GAAI,CAAC;AAAA,MAC1E;AAAA,IACF;AAAA,EACF;AAED,MAAI,CAAC,QAAQ,SAAS;AACpBA,kBAAAA,0DAAc,cAAc;AAAA,EAC7B;AAED,SAAO;AACT;AAGO,eAAe,iBAAiB,eAAe,OAAO,SAAS,MAAM,YAAY;AACtFA,gBAAAA,MAAA,MAAA,OAAA,qCAAY,aAAa;AAEzB,QAAM,SAAS;AAAA,IACb,YAAW,oBAAI,KAAM,GAAC,YAAa;AAAA,IACnC,gBAAgB;AAAA,IAChB,eAAe;AAAA,IACf,kBAAkB;AAAA,IAClB,SAAS;AAAA,IACT,QAAQ,CAAE;AAAA,EACX;AAED,MAAI;AAEFA,kBAAAA,MAAY,MAAA,OAAA,qCAAA,uBAAuB;AACnC,WAAO,iBAAiB,oBAAoB,eAAe,KAAK;AAEhE,QAAI,OAAO,eAAe,cAAc,GAAG;AACzC,aAAO,OAAO,KAAK,UAAU;AAAA,IAC9B;AAGDA,kBAAAA,MAAA,MAAA,OAAA,qCAAY,wBAAwB;AACpC,WAAO,gBAAgB,MAAM,sBAAsB,SAAS,MAAM,UAAU;AAE5E,QAAI,CAAC,OAAO,cAAc,SAAS;AACjC,aAAO,OAAO,KAAK,SAAS;AAAA,IAC7B;AAGDA,kBAAAA,MAAA,MAAA,OAAA,qCAAY,yBAAyB;AACrC,UAAM,YAAY,cAAc,CAAC;AACjC,UAAM,WAAW,cAAc,cAAc,SAAS,CAAC;AAEvD,WAAO,mBAAmB;AAAA,MACxB,SAAS,SAAS,OAAO;AAAA,MACzB;AAAA,MACA,WAAW,UAAU;AAAA,MACrB,SAAS,SAAS;AAAA,MAClB,SAAS,cAAc,IAAI,UAAQ,KAAK,EAAE;AAAA,MAC1C,aAAa;AAAA,MACb,aAAa;AAAA,MACb,OAAO,OAAO,eAAe;AAAA;AAAA,IAC9B;AAGD,QAAI,OAAO,iBAAiB,QAAQ,KAAK,OAAO,iBAAiB,QAAQ,SAAS,GAAG;AACnF,aAAO,UAAU;AAAA,IACvB,OAAW;AACL,UAAI,OAAO,iBAAiB,SAAS,GAAG;AACtC,eAAO,OAAO,KAAK,SAAS;AAAA,MAC7B;AACD,UAAI,OAAO,iBAAiB,QAAQ,WAAW,GAAG;AAChD,eAAO,OAAO,KAAK,UAAU;AAAA,MAC9B;AAAA,IACF;AAEDA,kBAAAA,wDAAY,cAAc;AAC1BA,kBAAAA,MAAY,MAAA,OAAA,qCAAA,YAAY,OAAO,eAAe,MAAM,MAAM,QAAQ;AAClEA,kBAAAA,MAAA,MAAA,OAAA,qCAAY,YAAY,OAAO,cAAc,UAAU,OAAO,IAAI,EAAE;AACpEA,wBAAA,MAAA,OAAA,qCAAY,aAAa,OAAO,iBAAiB,KAAK,EAAE;AACxDA,kBAAAA,MAAA,MAAA,OAAA,qCAAY,YAAY,OAAO,UAAU,MAAM,GAAG,EAAE;AAEpD,QAAI,OAAO,OAAO,SAAS,GAAG;AAC5BA,oBAAAA,MAAA,MAAA,OAAA,qCAAY,WAAW,OAAO,OAAO,KAAK,IAAI,CAAC,EAAE;AAAA,IAClD;AAAA,EAEF,SAAQ,OAAO;AACdA,kBAAAA,MAAc,MAAA,SAAA,qCAAA,eAAe,KAAK;AAClC,WAAO,OAAO,KAAK,WAAW,MAAM,OAAO,EAAE;AAAA,EAC9C;AAED,SAAO;AACT;;;;"}