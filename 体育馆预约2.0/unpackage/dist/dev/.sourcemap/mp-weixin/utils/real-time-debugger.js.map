{"version": 3, "file": "real-time-debugger.js", "sources": ["utils/real-time-debugger.js"], "sourcesContent": ["/**\n * 实时调试工具\n * 用于监控和调试实际的数据传递和API调用\n */\n\n// 拦截并监控所有API请求\nexport function interceptApiCalls() {\n  console.log('🔍 启动API调用拦截器')\n  \n  // 保存原始的uni.request方法\n  const originalRequest = uni.request\n  \n  // 重写uni.request方法\n  uni.request = function(options) {\n    console.log('🚀 [API拦截] 发起请求:', {\n      url: options.url,\n      method: options.method,\n      data: options.data,\n      header: options.header\n    })\n    \n    // 特别关注预约相关的请求\n    if (options.url && (options.url.includes('/bookings') || options.url.includes('/timeslots'))) {\n      console.log('📋 [预约API] 详细数据:', JSON.stringify(options.data, null, 2))\n      \n      // 检查价格字段\n      if (options.data && options.data.price !== undefined) {\n        console.log('💰 [价格检查] 发送的价格:', {\n          value: options.data.price,\n          type: typeof options.data.price,\n          isNumber: !isNaN(options.data.price),\n          isPositive: options.data.price > 0\n        })\n      }\n    }\n    \n    // 包装success回调\n    const originalSuccess = options.success\n    options.success = function(response) {\n      console.log('✅ [API拦截] 请求成功:', {\n        url: options.url,\n        statusCode: response.statusCode,\n        data: response.data\n      })\n      \n      // 特别关注预约相关的响应\n      if (options.url && (options.url.includes('/bookings') || options.url.includes('/timeslots'))) {\n        console.log('📋 [预约API] 响应详情:', JSON.stringify(response.data, null, 2))\n      }\n      \n      if (originalSuccess) {\n        originalSuccess(response)\n      }\n    }\n    \n    // 包装fail回调\n    const originalFail = options.fail\n    options.fail = function(error) {\n      console.error('❌ [API拦截] 请求失败:', {\n        url: options.url,\n        error: error\n      })\n      \n      if (originalFail) {\n        originalFail(error)\n      }\n    }\n    \n    // 调用原始方法\n    return originalRequest.call(this, options)\n  }\n  \n  console.log('✅ API调用拦截器已启动')\n}\n\n// 监控时间段数据变化\nexport function monitorTimeSlotChanges(venueStore) {\n  console.log('🔍 启动时间段变化监控')\n  \n  let lastTimeSlots = null\n  let changeCount = 0\n  \n  const monitor = setInterval(() => {\n    const currentTimeSlots = venueStore.timeSlots || []\n    const currentSlotsStr = JSON.stringify(currentTimeSlots.map(slot => ({\n      id: slot.id,\n      status: slot.status,\n      time: `${slot.startTime}-${slot.endTime}`\n    })))\n    \n    if (lastTimeSlots !== null && lastTimeSlots !== currentSlotsStr) {\n      changeCount++\n      console.log(`🔄 [时间段监控] 检测到变化 #${changeCount}:`)\n      \n      const lastSlots = JSON.parse(lastTimeSlots)\n      const currentSlots = JSON.parse(currentSlotsStr)\n      \n      // 找出变化的时间段\n      const changes = []\n      for (let i = 0; i < Math.max(lastSlots.length, currentSlots.length); i++) {\n        const lastSlot = lastSlots[i]\n        const currentSlot = currentSlots[i]\n        \n        if (!lastSlot && currentSlot) {\n          changes.push({ type: 'ADDED', slot: currentSlot })\n        } else if (lastSlot && !currentSlot) {\n          changes.push({ type: 'REMOVED', slot: lastSlot })\n        } else if (lastSlot && currentSlot && lastSlot.status !== currentSlot.status) {\n          changes.push({ \n            type: 'STATUS_CHANGED', \n            slot: currentSlot, \n            from: lastSlot.status, \n            to: currentSlot.status \n          })\n        }\n      }\n      \n      console.log('📊 [时间段监控] 变化详情:', changes)\n    }\n    \n    lastTimeSlots = currentSlotsStr\n  }, 1000)\n  \n  // 返回停止监控的函数\n  return () => {\n    clearInterval(monitor)\n    console.log('🛑 时间段变化监控已停止')\n  }\n}\n\n// 检查后端数据库状态\nexport async function checkBackendDatabase(orderId) {\n  console.log('🔍 检查后端数据库状态...')\n  \n  try {\n    // 这里需要调用一个专门的API来检查数据库状态\n    // 由于没有直接的数据库查询API，我们通过订单详情API来检查\n    const response = await uni.request({\n      url: `/api/bookings/${orderId}`,\n      method: 'GET'\n    })\n    \n    if (response.statusCode === 200 && response.data) {\n      console.log('📊 [数据库检查] 订单详情:', response.data)\n      \n      const order = response.data.data || response.data\n      console.log('💰 [数据库检查] 订单价格:', {\n        totalPrice: order.totalPrice,\n        priceType: typeof order.totalPrice,\n        isZero: order.totalPrice === 0\n      })\n      \n      return {\n        success: true,\n        order: order,\n        priceIsZero: order.totalPrice === 0\n      }\n    } else {\n      console.error('❌ [数据库检查] 获取订单详情失败')\n      return {\n        success: false,\n        error: '获取订单详情失败'\n      }\n    }\n  } catch (error) {\n    console.error('❌ [数据库检查] 检查失败:', error)\n    return {\n      success: false,\n      error: error.message\n    }\n  }\n}\n\n// 实时价格传递验证\nexport function validatePriceTransmission(bookingData) {\n  console.log('🔍 实时价格传递验证')\n  \n  const validation = {\n    frontendData: {\n      hasPrice: bookingData.price !== undefined,\n      priceValue: bookingData.price,\n      priceType: typeof bookingData.price,\n      isNumber: !isNaN(bookingData.price),\n      isPositive: bookingData.price > 0\n    },\n    issues: []\n  }\n  \n  console.log('📊 [价格验证] 前端数据:', validation.frontendData)\n  \n  // 检查各种可能的问题\n  if (!validation.frontendData.hasPrice) {\n    validation.issues.push('价格字段缺失')\n  }\n  \n  if (validation.frontendData.hasPrice && !validation.frontendData.isNumber) {\n    validation.issues.push('价格不是有效数字')\n  }\n  \n  if (validation.frontendData.isNumber && !validation.frontendData.isPositive) {\n    validation.issues.push('价格不是正数')\n  }\n  \n  // 检查数据序列化\n  try {\n    const serialized = JSON.stringify(bookingData)\n    const deserialized = JSON.parse(serialized)\n    \n    if (deserialized.price !== bookingData.price) {\n      validation.issues.push('价格在序列化过程中发生变化')\n    }\n    \n    console.log('📊 [价格验证] 序列化测试:', {\n      original: bookingData.price,\n      serialized: serialized.includes('\"price\"'),\n      deserialized: deserialized.price\n    })\n  } catch (error) {\n    validation.issues.push('数据序列化失败')\n  }\n  \n  validation.isValid = validation.issues.length === 0\n  \n  console.log('📊 [价格验证] 结果:', {\n    isValid: validation.isValid,\n    issues: validation.issues\n  })\n  \n  return validation\n}\n\n// 综合实时调试\nexport function startRealTimeDebugging(venueStore) {\n  console.log('🚀 启动综合实时调试')\n  \n  const debugSession = {\n    apiInterceptor: null,\n    timeSlotMonitor: null,\n    isActive: false,\n    logs: []\n  }\n  \n  // 启动API拦截\n  interceptApiCalls()\n  debugSession.apiInterceptor = true\n  \n  // 启动时间段监控\n  debugSession.timeSlotMonitor = monitorTimeSlotChanges(venueStore)\n  \n  debugSession.isActive = true\n  \n  console.log('✅ 综合实时调试已启动')\n  \n  // 返回调试会话控制器\n  return {\n    stop() {\n      if (debugSession.timeSlotMonitor) {\n        debugSession.timeSlotMonitor()\n      }\n      debugSession.isActive = false\n      console.log('🛑 综合实时调试已停止')\n    },\n    \n    isActive() {\n      return debugSession.isActive\n    },\n    \n    getLogs() {\n      return debugSession.logs\n    }\n  }\n}\n\n// 快速问题诊断\nexport async function quickDiagnosis(bookingData, venueStore) {\n  console.log('🔍 开始快速问题诊断...')\n  \n  const diagnosis = {\n    timestamp: new Date().toISOString(),\n    priceIssues: [],\n    timeSlotIssues: [],\n    overallStatus: 'UNKNOWN'\n  }\n  \n  // 1. 价格问题诊断\n  console.log('💰 诊断价格问题...')\n  const priceValidation = validatePriceTransmission(bookingData)\n  if (!priceValidation.isValid) {\n    diagnosis.priceIssues = priceValidation.issues\n  }\n  \n  // 2. 时间段问题诊断\n  console.log('⏰ 诊断时间段问题...')\n  const timeSlots = venueStore.timeSlots || []\n  \n  if (timeSlots.length === 0) {\n    diagnosis.timeSlotIssues.push('没有时间段数据')\n  }\n  \n  const reservedSlots = timeSlots.filter(slot => slot.status === 'RESERVED')\n  if (reservedSlots.length === 0) {\n    diagnosis.timeSlotIssues.push('没有已预约的时间段')\n  }\n  \n  // 3. 综合评估\n  const hasIssues = diagnosis.priceIssues.length > 0 || diagnosis.timeSlotIssues.length > 0\n  diagnosis.overallStatus = hasIssues ? 'ISSUES_FOUND' : 'OK'\n  \n  console.log('📊 快速诊断结果:', diagnosis)\n  \n  return diagnosis\n}\n"], "names": ["uni"], "mappings": ";;AA8KO,SAAS,0BAA0B,aAAa;AACrDA,gBAAAA,MAAA,MAAA,OAAA,sCAAY,aAAa;AAEzB,QAAM,aAAa;AAAA,IACjB,cAAc;AAAA,MACZ,UAAU,YAAY,UAAU;AAAA,MAChC,YAAY,YAAY;AAAA,MACxB,WAAW,OAAO,YAAY;AAAA,MAC9B,UAAU,CAAC,MAAM,YAAY,KAAK;AAAA,MAClC,YAAY,YAAY,QAAQ;AAAA,IACjC;AAAA,IACD,QAAQ,CAAE;AAAA,EACX;AAEDA,yEAAY,mBAAmB,WAAW,YAAY;AAGtD,MAAI,CAAC,WAAW,aAAa,UAAU;AACrC,eAAW,OAAO,KAAK,QAAQ;AAAA,EAChC;AAED,MAAI,WAAW,aAAa,YAAY,CAAC,WAAW,aAAa,UAAU;AACzE,eAAW,OAAO,KAAK,UAAU;AAAA,EAClC;AAED,MAAI,WAAW,aAAa,YAAY,CAAC,WAAW,aAAa,YAAY;AAC3E,eAAW,OAAO,KAAK,QAAQ;AAAA,EAChC;AAGD,MAAI;AACF,UAAM,aAAa,KAAK,UAAU,WAAW;AAC7C,UAAM,eAAe,KAAK,MAAM,UAAU;AAE1C,QAAI,aAAa,UAAU,YAAY,OAAO;AAC5C,iBAAW,OAAO,KAAK,eAAe;AAAA,IACvC;AAEDA,kBAAAA,MAAY,MAAA,OAAA,sCAAA,oBAAoB;AAAA,MAC9B,UAAU,YAAY;AAAA,MACtB,YAAY,WAAW,SAAS,SAAS;AAAA,MACzC,cAAc,aAAa;AAAA,IACjC,CAAK;AAAA,EACF,SAAQ,OAAO;AACd,eAAW,OAAO,KAAK,SAAS;AAAA,EACjC;AAED,aAAW,UAAU,WAAW,OAAO,WAAW;AAElDA,gBAAAA,MAAY,MAAA,OAAA,sCAAA,iBAAiB;AAAA,IAC3B,SAAS,WAAW;AAAA,IACpB,QAAQ,WAAW;AAAA,EACvB,CAAG;AAED,SAAO;AACT;;"}