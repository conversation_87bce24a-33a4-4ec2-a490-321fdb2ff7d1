{"version": 3, "file": "emergency-timeslot-fix.js", "sources": ["utils/emergency-timeslot-fix.js"], "sourcesContent": ["/**\n * 紧急时间段修复工具\n * 解决前端默认时间段与后端数据库不同步的问题\n */\n\nimport { generateTimeSlots } from '@/api/timeslot.js'\n\n// 紧急修复：确保时间段在后端存在\nexport async function ensureTimeSlotsExistInBackend(venueId, date, selectedSlots) {\n  console.log('🚨 紧急修复：确保时间段在后端存在')\n  \n  const fix = {\n    venueId: venueId,\n    date: date,\n    selectedSlots: selectedSlots,\n    backendGenerated: false,\n    backendSlots: null,\n    mappedSlots: [],\n    success: false,\n    error: null\n  }\n  \n  try {\n    // 步骤1: 强制调用后端生成API\n    console.log('📡 强制调用后端生成时间段API...')\n    await generateTimeSlots(venueId, date)\n    fix.backendGenerated = true\n    console.log('✅ 后端时间段生成成功')\n    \n    // 步骤2: 获取后端生成的时间段\n    console.log('📡 获取后端生成的时间段...')\n    const { getVenueTimeSlots } = await import('@/api/timeslot.js')\n    const response = await getVenueTimeSlots(venueId, date, true)\n    \n    if (response && response.data && response.data.length > 0) {\n      fix.backendSlots = response.data\n      console.log('✅ 获取到后端时间段:', response.data.length, '个')\n      \n      // 步骤3: 将前端选中的时间段映射到后端时间段\n      fix.mappedSlots = mapFrontendSlotsToBackend(selectedSlots, response.data)\n      console.log('✅ 时间段映射完成:', fix.mappedSlots.length, '个')\n      \n      fix.success = fix.mappedSlots.length > 0\n    } else {\n      throw new Error('后端生成时间段后仍然获取不到数据')\n    }\n    \n  } catch (error) {\n    console.error('❌ 紧急修复失败:', error)\n    fix.error = error.message\n  }\n  \n  console.log('🚨 紧急修复结果:', fix)\n  return fix\n}\n\n// 将前端时间段映射到后端时间段\nfunction mapFrontendSlotsToBackend(frontendSlots, backendSlots) {\n  console.log('🔄 映射前端时间段到后端时间段')\n  \n  const mappedSlots = []\n  \n  frontendSlots.forEach(frontendSlot => {\n    // 根据时间范围查找对应的后端时间段\n    const matchingBackendSlot = backendSlots.find(backendSlot => \n      backendSlot.startTime === frontendSlot.startTime && \n      backendSlot.endTime === frontendSlot.endTime\n    )\n    \n    if (matchingBackendSlot) {\n      mappedSlots.push({\n        frontendId: frontendSlot.id,\n        backendId: matchingBackendSlot.id,\n        startTime: frontendSlot.startTime,\n        endTime: frontendSlot.endTime,\n        price: matchingBackendSlot.price || frontendSlot.price\n      })\n      console.log(`✅ 映射成功: ${frontendSlot.startTime}-${frontendSlot.endTime} -> ID ${matchingBackendSlot.id}`)\n    } else {\n      console.warn(`⚠️ 未找到匹配的后端时间段: ${frontendSlot.startTime}-${frontendSlot.endTime}`)\n    }\n  })\n  \n  console.log('🔄 映射完成，成功映射', mappedSlots.length, '个时间段')\n  return mappedSlots\n}\n\n// 修复预约数据中的时间段ID\nexport function fixBookingDataSlotIds(bookingData, mappedSlots) {\n  console.log('🔧 修复预约数据中的时间段ID')\n  \n  const fixedData = { ...bookingData }\n  \n  // 将前端的字符串ID替换为后端的数字ID\n  fixedData.slotIds = mappedSlots.map(slot => slot.backendId)\n  \n  console.log('🔧 原始slotIds:', bookingData.slotIds)\n  console.log('🔧 修复后slotIds:', fixedData.slotIds)\n  \n  return fixedData\n}\n\n// 综合紧急修复流程\nexport async function emergencyBookingFix(venueId, date, selectedSlots, bookingData) {\n  console.log('🚨 启动综合紧急修复流程')\n  \n  const emergencyFix = {\n    timestamp: new Date().toISOString(),\n    originalBookingData: bookingData,\n    fixedBookingData: null,\n    timeslotFix: null,\n    success: false,\n    error: null\n  }\n  \n  try {\n    // 步骤1: 确保时间段在后端存在\n    emergencyFix.timeslotFix = await ensureTimeSlotsExistInBackend(venueId, date, selectedSlots)\n    \n    if (emergencyFix.timeslotFix.success) {\n      // 步骤2: 修复预约数据中的时间段ID\n      emergencyFix.fixedBookingData = fixBookingDataSlotIds(\n        bookingData, \n        emergencyFix.timeslotFix.mappedSlots\n      )\n      \n      emergencyFix.success = true\n      console.log('✅ 综合紧急修复成功')\n    } else {\n      throw new Error('时间段修复失败')\n    }\n    \n  } catch (error) {\n    console.error('❌ 综合紧急修复失败:', error)\n    emergencyFix.error = error.message\n  }\n  \n  console.log('🚨 综合紧急修复结果:', emergencyFix)\n  return emergencyFix\n}\n\n// 快速检查时间段同步状态\nexport async function quickCheckTimeslotSync(venueId, date, selectedSlots) {\n  console.log('⚡ 快速检查时间段同步状态')\n  \n  const check = {\n    venueId: venueId,\n    date: date,\n    frontendSlots: selectedSlots.length,\n    backendSlots: 0,\n    needsSync: false,\n    hasDefaultIds: false,\n    issues: []\n  }\n  \n  try {\n    // 检查前端时间段ID格式\n    const defaultIdPattern = /^default_/\n    check.hasDefaultIds = selectedSlots.some(slot => defaultIdPattern.test(slot.id))\n    \n    if (check.hasDefaultIds) {\n      check.issues.push('前端使用默认生成的时间段ID')\n      check.needsSync = true\n    }\n    \n    // 检查后端时间段\n    const { getVenueTimeSlots } = await import('@/api/timeslot.js')\n    const response = await getVenueTimeSlots(venueId, date, true)\n    \n    if (response && response.data) {\n      check.backendSlots = response.data.length\n    }\n    \n    if (check.backendSlots === 0) {\n      check.issues.push('后端没有时间段数据')\n      check.needsSync = true\n    }\n    \n    console.log('⚡ 时间段同步状态检查结果:', check)\n    \n  } catch (error) {\n    console.error('❌ 检查时间段同步状态失败:', error)\n    check.issues.push(`检查失败: ${error.message}`)\n  }\n  \n  return check\n}\n\n// 智能预约数据修复\nexport async function smartBookingDataFix(venueId, date, selectedSlots, bookingData) {\n  console.log('🧠 智能预约数据修复')\n  \n  const smartFix = {\n    timestamp: new Date().toISOString(),\n    originalData: bookingData,\n    fixedData: null,\n    appliedFixes: [],\n    success: false\n  }\n  \n  try {\n    // 步骤1: 快速检查同步状态\n    const syncCheck = await quickCheckTimeslotSync(venueId, date, selectedSlots)\n    \n    if (syncCheck.needsSync) {\n      console.log('🔧 检测到需要同步，执行紧急修复...')\n      \n      // 步骤2: 执行紧急修复\n      const emergencyResult = await emergencyBookingFix(venueId, date, selectedSlots, bookingData)\n      \n      if (emergencyResult.success) {\n        smartFix.fixedData = emergencyResult.fixedBookingData\n        smartFix.appliedFixes.push('时间段ID映射修复')\n        smartFix.appliedFixes.push('后端时间段生成')\n        smartFix.success = true\n      } else {\n        throw new Error('紧急修复失败')\n      }\n    } else {\n      console.log('✅ 时间段同步状态正常，无需修复')\n      smartFix.fixedData = bookingData\n      smartFix.success = true\n    }\n    \n  } catch (error) {\n    console.error('❌ 智能修复失败:', error)\n    // 如果智能修复失败，至少返回原始数据\n    smartFix.fixedData = bookingData\n    smartFix.appliedFixes.push('修复失败，使用原始数据')\n  }\n  \n  console.log('🧠 智能修复结果:', smartFix)\n  return smartFix\n}\n\n// 导出所有功能\nexport default {\n  ensureTimeSlotsExistInBackend,\n  mapFrontendSlotsToBackend,\n  fixBookingDataSlotIds,\n  emergencyBookingFix,\n  quickCheckTimeslotSync,\n  smartBookingDataFix\n}\n"], "names": ["uni", "generateTimeSlots"], "mappings": ";;;;AAQO,eAAe,8BAA8B,SAAS,MAAM,eAAe;AAChFA,gBAAAA,4DAAY,oBAAoB;AAEhC,QAAM,MAAM;AAAA,IACV;AAAA,IACA;AAAA,IACA;AAAA,IACA,kBAAkB;AAAA,IAClB,cAAc;AAAA,IACd,aAAa,CAAE;AAAA,IACf,SAAS;AAAA,IACT,OAAO;AAAA,EACR;AAED,MAAI;AAEFA,kBAAAA,MAAY,MAAA,OAAA,yCAAA,sBAAsB;AAClC,UAAMC,aAAiB,kBAAC,SAAS,IAAI;AACrC,QAAI,mBAAmB;AACvBD,kBAAAA,MAAY,MAAA,OAAA,yCAAA,aAAa;AAGzBA,kBAAAA,MAAA,MAAA,OAAA,yCAAY,kBAAkB;AAC9B,UAAM,EAAE,kBAAiB,IAAK,MAAa;AAC3C,UAAM,WAAW,MAAM,kBAAkB,SAAS,MAAM,IAAI;AAE5D,QAAI,YAAY,SAAS,QAAQ,SAAS,KAAK,SAAS,GAAG;AACzD,UAAI,eAAe,SAAS;AAC5BA,0BAAY,MAAA,OAAA,yCAAA,eAAe,SAAS,KAAK,QAAQ,GAAG;AAGpD,UAAI,cAAc,0BAA0B,eAAe,SAAS,IAAI;AACxEA,0BAAY,MAAA,OAAA,yCAAA,cAAc,IAAI,YAAY,QAAQ,GAAG;AAErD,UAAI,UAAU,IAAI,YAAY,SAAS;AAAA,IAC7C,OAAW;AACL,YAAM,IAAI,MAAM,kBAAkB;AAAA,IACnC;AAAA,EAEF,SAAQ,OAAO;AACdA,kBAAAA,MAAA,MAAA,SAAA,yCAAc,aAAa,KAAK;AAChC,QAAI,QAAQ,MAAM;AAAA,EACnB;AAEDA,gBAAAA,MAAY,MAAA,OAAA,yCAAA,cAAc,GAAG;AAC7B,SAAO;AACT;AAGA,SAAS,0BAA0B,eAAe,cAAc;AAC9DA,gBAAAA,4DAAY,kBAAkB;AAE9B,QAAM,cAAc,CAAE;AAEtB,gBAAc,QAAQ,kBAAgB;AAEpC,UAAM,sBAAsB,aAAa;AAAA,MAAK,iBAC5C,YAAY,cAAc,aAAa,aACvC,YAAY,YAAY,aAAa;AAAA,IACtC;AAED,QAAI,qBAAqB;AACvB,kBAAY,KAAK;AAAA,QACf,YAAY,aAAa;AAAA,QACzB,WAAW,oBAAoB;AAAA,QAC/B,WAAW,aAAa;AAAA,QACxB,SAAS,aAAa;AAAA,QACtB,OAAO,oBAAoB,SAAS,aAAa;AAAA,MACzD,CAAO;AACDA,oBAAY,MAAA,MAAA,OAAA,yCAAA,WAAW,aAAa,SAAS,IAAI,aAAa,OAAO,UAAU,oBAAoB,EAAE,EAAE;AAAA,IAC7G,OAAW;AACLA,oBAAAA,MAAa,MAAA,QAAA,yCAAA,mBAAmB,aAAa,SAAS,IAAI,aAAa,OAAO,EAAE;AAAA,IACjF;AAAA,EACL,CAAG;AAEDA,4EAAY,gBAAgB,YAAY,QAAQ,MAAM;AACtD,SAAO;AACT;AAGO,SAAS,sBAAsB,aAAa,aAAa;AAC9DA,gBAAAA,4DAAY,kBAAkB;AAE9B,QAAM,YAAY,EAAE,GAAG,YAAa;AAGpC,YAAU,UAAU,YAAY,IAAI,UAAQ,KAAK,SAAS;AAE1DA,gBAAY,MAAA,MAAA,OAAA,yCAAA,iBAAiB,YAAY,OAAO;AAChDA,gBAAY,MAAA,MAAA,OAAA,yCAAA,kBAAkB,UAAU,OAAO;AAE/C,SAAO;AACT;AAGO,eAAe,oBAAoB,SAAS,MAAM,eAAe,aAAa;AACnFA,gBAAAA,MAAY,MAAA,OAAA,0CAAA,eAAe;AAE3B,QAAM,eAAe;AAAA,IACnB,YAAW,oBAAI,KAAM,GAAC,YAAa;AAAA,IACnC,qBAAqB;AAAA,IACrB,kBAAkB;AAAA,IAClB,aAAa;AAAA,IACb,SAAS;AAAA,IACT,OAAO;AAAA,EACR;AAED,MAAI;AAEF,iBAAa,cAAc,MAAM,8BAA8B,SAAS,MAAM,aAAa;AAE3F,QAAI,aAAa,YAAY,SAAS;AAEpC,mBAAa,mBAAmB;AAAA,QAC9B;AAAA,QACA,aAAa,YAAY;AAAA,MAC1B;AAED,mBAAa,UAAU;AACvBA,oBAAAA,MAAA,MAAA,OAAA,0CAAY,YAAY;AAAA,IAC9B,OAAW;AACL,YAAM,IAAI,MAAM,SAAS;AAAA,IAC1B;AAAA,EAEF,SAAQ,OAAO;AACdA,kBAAAA,MAAc,MAAA,SAAA,0CAAA,eAAe,KAAK;AAClC,iBAAa,QAAQ,MAAM;AAAA,EAC5B;AAEDA,gBAAAA,MAAA,MAAA,OAAA,0CAAY,gBAAgB,YAAY;AACxC,SAAO;AACT;AAGO,eAAe,uBAAuB,SAAS,MAAM,eAAe;AACzEA,gBAAAA,MAAY,MAAA,OAAA,0CAAA,eAAe;AAE3B,QAAM,QAAQ;AAAA,IACZ;AAAA,IACA;AAAA,IACA,eAAe,cAAc;AAAA,IAC7B,cAAc;AAAA,IACd,WAAW;AAAA,IACX,eAAe;AAAA,IACf,QAAQ,CAAE;AAAA,EACX;AAED,MAAI;AAEF,UAAM,mBAAmB;AACzB,UAAM,gBAAgB,cAAc,KAAK,UAAQ,iBAAiB,KAAK,KAAK,EAAE,CAAC;AAE/E,QAAI,MAAM,eAAe;AACvB,YAAM,OAAO,KAAK,gBAAgB;AAClC,YAAM,YAAY;AAAA,IACnB;AAGD,UAAM,EAAE,kBAAiB,IAAK,MAAa;AAC3C,UAAM,WAAW,MAAM,kBAAkB,SAAS,MAAM,IAAI;AAE5D,QAAI,YAAY,SAAS,MAAM;AAC7B,YAAM,eAAe,SAAS,KAAK;AAAA,IACpC;AAED,QAAI,MAAM,iBAAiB,GAAG;AAC5B,YAAM,OAAO,KAAK,WAAW;AAC7B,YAAM,YAAY;AAAA,IACnB;AAEDA,kBAAAA,MAAY,MAAA,OAAA,0CAAA,kBAAkB,KAAK;AAAA,EAEpC,SAAQ,OAAO;AACdA,kBAAAA,MAAc,MAAA,SAAA,0CAAA,kBAAkB,KAAK;AACrC,UAAM,OAAO,KAAK,SAAS,MAAM,OAAO,EAAE;AAAA,EAC3C;AAED,SAAO;AACT;AAGO,eAAe,oBAAoB,SAAS,MAAM,eAAe,aAAa;AACnFA,gBAAAA,MAAA,MAAA,OAAA,0CAAY,aAAa;AAEzB,QAAM,WAAW;AAAA,IACf,YAAW,oBAAI,KAAM,GAAC,YAAa;AAAA,IACnC,cAAc;AAAA,IACd,WAAW;AAAA,IACX,cAAc,CAAE;AAAA,IAChB,SAAS;AAAA,EACV;AAED,MAAI;AAEF,UAAM,YAAY,MAAM,uBAAuB,SAAS,MAAM,aAAa;AAE3E,QAAI,UAAU,WAAW;AACvBA,oBAAAA,MAAA,MAAA,OAAA,0CAAY,sBAAsB;AAGlC,YAAM,kBAAkB,MAAM,oBAAoB,SAAS,MAAM,eAAe,WAAW;AAE3F,UAAI,gBAAgB,SAAS;AAC3B,iBAAS,YAAY,gBAAgB;AACrC,iBAAS,aAAa,KAAK,WAAW;AACtC,iBAAS,aAAa,KAAK,SAAS;AACpC,iBAAS,UAAU;AAAA,MAC3B,OAAa;AACL,cAAM,IAAI,MAAM,QAAQ;AAAA,MACzB;AAAA,IACP,OAAW;AACLA,oBAAAA,6DAAY,kBAAkB;AAC9B,eAAS,YAAY;AACrB,eAAS,UAAU;AAAA,IACpB;AAAA,EAEF,SAAQ,OAAO;AACdA,kBAAAA,MAAA,MAAA,SAAA,0CAAc,aAAa,KAAK;AAEhC,aAAS,YAAY;AACrB,aAAS,aAAa,KAAK,aAAa;AAAA,EACzC;AAEDA,gBAAAA,MAAY,MAAA,OAAA,0CAAA,cAAc,QAAQ;AAClC,SAAO;AACT;AAGA,MAAe,uBAAA;AAAA,EACb;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;;;;;;;"}