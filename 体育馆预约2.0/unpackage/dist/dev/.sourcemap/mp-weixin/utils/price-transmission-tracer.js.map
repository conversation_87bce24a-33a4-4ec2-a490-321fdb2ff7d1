{"version": 3, "file": "price-transmission-tracer.js", "sources": ["utils/price-transmission-tracer.js"], "sourcesContent": ["/**\n * 价格传递追踪器\n * 专门用于追踪价格从前端计算到后端接收的完整过程\n */\n\n// 全局价格追踪日志\nlet priceTraceLog = []\n\n// 清除追踪日志\nexport function clearPriceTrace() {\n  priceTraceLog = []\n  console.log('🧹 价格追踪日志已清除')\n}\n\n// 获取追踪日志\nexport function getPriceTrace() {\n  return priceTraceLog\n}\n\n// 添加追踪记录\nfunction addTrace(step, data) {\n  const trace = {\n    timestamp: new Date().toISOString(),\n    step: step,\n    data: data\n  }\n  priceTraceLog.push(trace)\n  console.log(`📊 [价格追踪] ${step}:`, data)\n}\n\n// 追踪时间段价格计算\nexport function traceSlotPriceCalculation(slots, venue) {\n  addTrace('时间段价格计算开始', {\n    slotsCount: slots.length,\n    venuePrice: venue?.price\n  })\n  \n  let totalPrice = 0\n  const slotDetails = []\n  \n  slots.forEach((slot, index) => {\n    const slotData = {\n      index: index + 1,\n      timeRange: `${slot.startTime}-${slot.endTime}`,\n      slotPrice: slot.price,\n      slotPriceType: typeof slot.price,\n      venuePrice: venue?.price,\n      venuePriceType: typeof venue?.price\n    }\n    \n    // 模拟getSlotPrice逻辑\n    let calculatedPrice = 0\n    let method = ''\n    \n    if (slot.price && slot.price > 0) {\n      calculatedPrice = parseFloat(slot.price)\n      method = 'slot.price'\n    } else if (slot.pricePerHour && slot.pricePerHour > 0) {\n      calculatedPrice = parseFloat(slot.pricePerHour)\n      method = 'slot.pricePerHour'\n    } else if (venue?.price && venue.price > 0) {\n      calculatedPrice = parseFloat(venue.price) / 2\n      method = 'venue.price/2'\n    } else {\n      calculatedPrice = 60\n      method = 'default'\n    }\n    \n    slotData.calculatedPrice = calculatedPrice\n    slotData.method = method\n    totalPrice += calculatedPrice\n    \n    slotDetails.push(slotData)\n  })\n  \n  addTrace('时间段价格计算完成', {\n    slotDetails: slotDetails,\n    totalPrice: totalPrice\n  })\n  \n  return totalPrice\n}\n\n// 追踪预约数据构建\nexport function traceBookingDataConstruction(bookingData) {\n  addTrace('预约数据构建', {\n    hasPrice: bookingData.price !== undefined,\n    priceValue: bookingData.price,\n    priceType: typeof bookingData.price,\n    isNumber: !isNaN(bookingData.price),\n    isPositive: bookingData.price > 0,\n    bookingType: bookingData.bookingType,\n    venueId: bookingData.venueId,\n    date: bookingData.date,\n    startTime: bookingData.startTime,\n    endTime: bookingData.endTime\n  })\n  \n  // 检查数据序列化\n  try {\n    const serialized = JSON.stringify(bookingData)\n    const deserialized = JSON.parse(serialized)\n    \n    addTrace('数据序列化测试', {\n      originalPrice: bookingData.price,\n      serializedContainsPrice: serialized.includes('\"price\"'),\n      deserializedPrice: deserialized.price,\n      pricePreserved: deserialized.price === bookingData.price\n    })\n  } catch (error) {\n    addTrace('数据序列化失败', { error: error.message })\n  }\n}\n\n// 追踪API请求\nexport function traceApiRequest(url, method, data) {\n  addTrace('API请求发送', {\n    url: url,\n    method: method,\n    hasData: data !== undefined,\n    dataType: typeof data,\n    hasPrice: data && data.price !== undefined,\n    priceValue: data?.price,\n    priceType: typeof data?.price\n  })\n  \n  // 检查uni.request的实际行为\n  const originalRequest = uni.request\n  \n  uni.request = function(options) {\n    addTrace('uni.request实际调用', {\n      url: options.url,\n      method: options.method,\n      dataType: typeof options.data,\n      hasPrice: options.data && options.data.price !== undefined,\n      priceValue: options.data?.price,\n      actualDataSent: options.data\n    })\n    \n    // 恢复原始方法\n    uni.request = originalRequest\n    \n    // 调用原始方法\n    return originalRequest.call(this, options)\n  }\n}\n\n// 追踪API响应\nexport function traceApiResponse(response) {\n  addTrace('API响应接收', {\n    statusCode: response.statusCode,\n    hasData: response.data !== undefined,\n    dataType: typeof response.data,\n    responseData: response.data\n  })\n}\n\n// 完整的价格传递追踪\nexport async function traceCompletePriceTransmission(selectedSlots, venue, bookingForm) {\n  console.log('🚀 开始完整价格传递追踪')\n  clearPriceTrace()\n  \n  try {\n    // 步骤1: 追踪价格计算\n    const calculatedPrice = traceSlotPriceCalculation(selectedSlots, venue)\n    \n    // 步骤2: 构建预约数据\n    const bookingData = {\n      venueId: venue?.id || 25,\n      date: '2025-07-19',\n      startTime: selectedSlots[0]?.startTime || '18:00',\n      endTime: selectedSlots[selectedSlots.length - 1]?.endTime || '20:00',\n      slotIds: selectedSlots.map(slot => slot.id),\n      bookingType: bookingForm?.bookingType || 'EXCLUSIVE',\n      description: bookingForm?.description || '',\n      price: calculatedPrice\n    }\n    \n    // 步骤3: 追踪数据构建\n    traceBookingDataConstruction(bookingData)\n    \n    // 步骤4: 模拟API调用\n    traceApiRequest('/bookings', 'POST', bookingData)\n    \n    // 返回追踪结果\n    const trace = getPriceTrace()\n    \n    console.log('📋 完整价格传递追踪结果:')\n    trace.forEach((entry, index) => {\n      console.log(`${index + 1}. ${entry.step}:`, entry.data)\n    })\n    \n    return {\n      success: true,\n      calculatedPrice: calculatedPrice,\n      finalBookingData: bookingData,\n      trace: trace,\n      issues: trace.filter(entry => \n        entry.data.error || \n        (entry.step.includes('价格') && entry.data.calculatedPrice === 0) ||\n        (entry.step.includes('数据') && !entry.data.pricePreserved)\n      )\n    }\n    \n  } catch (error) {\n    addTrace('追踪过程出错', { error: error.message })\n    return {\n      success: false,\n      error: error.message,\n      trace: getPriceTrace()\n    }\n  }\n}\n\n// 实时价格监控\nexport function startPriceMonitoring() {\n  console.log('🔍 启动实时价格监控')\n  \n  const monitor = {\n    isActive: false,\n    interceptors: [],\n    \n    start() {\n      if (this.isActive) {\n        console.log('⚠️ 价格监控已在运行')\n        return\n      }\n      \n      this.isActive = true\n      \n      // 拦截uni.request\n      const originalRequest = uni.request\n      uni.request = function(options) {\n        if (options.url && options.url.includes('/bookings')) {\n          addTrace('监控到预约请求', {\n            url: options.url,\n            method: options.method,\n            hasPrice: options.data && options.data.price !== undefined,\n            priceValue: options.data?.price,\n            priceType: typeof options.data?.price,\n            fullData: options.data\n          })\n        }\n        \n        return originalRequest.call(this, options)\n      }\n      \n      this.interceptors.push(() => {\n        uni.request = originalRequest\n      })\n      \n      console.log('✅ 实时价格监控已启动')\n    },\n    \n    stop() {\n      if (!this.isActive) {\n        console.log('⚠️ 价格监控未在运行')\n        return\n      }\n      \n      this.isActive = false\n      \n      // 恢复所有拦截器\n      this.interceptors.forEach(restore => restore())\n      this.interceptors = []\n      \n      console.log('🛑 实时价格监控已停止')\n      \n      // 返回监控日志\n      return getPriceTrace()\n    }\n  }\n  \n  monitor.start()\n  return monitor\n}\n\n// 快速价格问题诊断\nexport function quickPriceDiagnosis(selectedSlots, venue) {\n  console.log('⚡ 快速价格问题诊断')\n  \n  const diagnosis = {\n    timestamp: new Date().toISOString(),\n    issues: [],\n    warnings: [],\n    summary: ''\n  }\n  \n  // 检查时间段数据\n  if (!selectedSlots || selectedSlots.length === 0) {\n    diagnosis.issues.push('没有选中的时间段')\n  } else {\n    selectedSlots.forEach((slot, index) => {\n      if (!slot.price && !slot.pricePerHour) {\n        diagnosis.warnings.push(`时间段${index + 1}缺少价格信息`)\n      }\n      if (slot.price === 0) {\n        diagnosis.issues.push(`时间段${index + 1}价格为0`)\n      }\n    })\n  }\n  \n  // 检查场馆数据\n  if (!venue) {\n    diagnosis.issues.push('场馆数据缺失')\n  } else if (!venue.price || venue.price === 0) {\n    diagnosis.warnings.push('场馆价格缺失或为0')\n  }\n  \n  // 模拟价格计算\n  let totalPrice = 0\n  if (selectedSlots && selectedSlots.length > 0) {\n    selectedSlots.forEach(slot => {\n      if (slot.price && slot.price > 0) {\n        totalPrice += parseFloat(slot.price)\n      } else if (venue?.price && venue.price > 0) {\n        totalPrice += parseFloat(venue.price) / 2\n      } else {\n        totalPrice += 60 // 默认价格\n      }\n    })\n  }\n  \n  if (totalPrice === 0) {\n    diagnosis.issues.push('计算的总价格为0')\n  }\n  \n  // 生成总结\n  const issueCount = diagnosis.issues.length\n  const warningCount = diagnosis.warnings.length\n  \n  if (issueCount === 0 && warningCount === 0) {\n    diagnosis.summary = '✅ 价格计算正常'\n  } else if (issueCount === 0) {\n    diagnosis.summary = `⚠️ 有${warningCount}个警告`\n  } else {\n    diagnosis.summary = `❌ 有${issueCount}个问题，${warningCount}个警告`\n  }\n  \n  diagnosis.calculatedPrice = totalPrice\n  \n  console.log('⚡ 快速诊断结果:', diagnosis)\n  return diagnosis\n}\n"], "names": ["uni"], "mappings": ";;AAsRO,SAAS,oBAAoB,eAAe,OAAO;AACxDA,gBAAAA,MAAA,MAAA,OAAA,6CAAY,YAAY;AAExB,QAAM,YAAY;AAAA,IAChB,YAAW,oBAAI,KAAM,GAAC,YAAa;AAAA,IACnC,QAAQ,CAAE;AAAA,IACV,UAAU,CAAE;AAAA,IACZ,SAAS;AAAA,EACV;AAGD,MAAI,CAAC,iBAAiB,cAAc,WAAW,GAAG;AAChD,cAAU,OAAO,KAAK,UAAU;AAAA,EACpC,OAAS;AACL,kBAAc,QAAQ,CAAC,MAAM,UAAU;AACrC,UAAI,CAAC,KAAK,SAAS,CAAC,KAAK,cAAc;AACrC,kBAAU,SAAS,KAAK,MAAM,QAAQ,CAAC,QAAQ;AAAA,MAChD;AACD,UAAI,KAAK,UAAU,GAAG;AACpB,kBAAU,OAAO,KAAK,MAAM,QAAQ,CAAC,MAAM;AAAA,MAC5C;AAAA,IACP,CAAK;AAAA,EACF;AAGD,MAAI,CAAC,OAAO;AACV,cAAU,OAAO,KAAK,QAAQ;AAAA,EAClC,WAAa,CAAC,MAAM,SAAS,MAAM,UAAU,GAAG;AAC5C,cAAU,SAAS,KAAK,WAAW;AAAA,EACpC;AAGD,MAAI,aAAa;AACjB,MAAI,iBAAiB,cAAc,SAAS,GAAG;AAC7C,kBAAc,QAAQ,UAAQ;AAC5B,UAAI,KAAK,SAAS,KAAK,QAAQ,GAAG;AAChC,sBAAc,WAAW,KAAK,KAAK;AAAA,MACpC,YAAU,+BAAO,UAAS,MAAM,QAAQ,GAAG;AAC1C,sBAAc,WAAW,MAAM,KAAK,IAAI;AAAA,MAChD,OAAa;AACL,sBAAc;AAAA,MACf;AAAA,IACP,CAAK;AAAA,EACF;AAED,MAAI,eAAe,GAAG;AACpB,cAAU,OAAO,KAAK,UAAU;AAAA,EACjC;AAGD,QAAM,aAAa,UAAU,OAAO;AACpC,QAAM,eAAe,UAAU,SAAS;AAExC,MAAI,eAAe,KAAK,iBAAiB,GAAG;AAC1C,cAAU,UAAU;AAAA,EACxB,WAAa,eAAe,GAAG;AAC3B,cAAU,UAAU,OAAO,YAAY;AAAA,EAC3C,OAAS;AACL,cAAU,UAAU,MAAM,UAAU,OAAO,YAAY;AAAA,EACxD;AAED,YAAU,kBAAkB;AAE5BA,gBAAAA,MAAY,MAAA,OAAA,6CAAA,aAAa,SAAS;AAClC,SAAO;AACT;;"}