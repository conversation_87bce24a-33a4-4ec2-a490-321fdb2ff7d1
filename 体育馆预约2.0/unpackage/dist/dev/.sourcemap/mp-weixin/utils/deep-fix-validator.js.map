{"version": 3, "file": "deep-fix-validator.js", "sources": ["utils/deep-fix-validator.js"], "sourcesContent": ["/**\n * 深度修复验证工具\n * 用于验证后端价格传递和时间段刷新的深度修复效果\n */\n\n// 验证后端价格接收\nexport async function validateBackendPriceReceiving(bookingData) {\n  console.log('🔍 开始验证后端价格接收...')\n  \n  const validation = {\n    frontendPrice: null,\n    backendReceived: null,\n    priceTransmitted: false,\n    issues: []\n  }\n  \n  try {\n    // 检查前端发送的价格\n    validation.frontendPrice = parseFloat(bookingData.price) || 0\n    console.log('💰 前端发送的价格:', validation.frontendPrice)\n    \n    if (validation.frontendPrice <= 0) {\n      validation.issues.push('前端发送的价格为0或无效')\n    }\n    \n    // 模拟检查后端接收情况\n    // 这里需要实际的API调用来验证\n    console.log('📡 检查后端是否正确接收价格...')\n    \n    // 检查价格传递的关键点\n    const priceChecks = {\n      hasPrice: bookingData.price !== undefined && bookingData.price !== null,\n      priceIsNumber: !isNaN(parseFloat(bookingData.price)),\n      priceIsPositive: parseFloat(bookingData.price) > 0,\n      priceIsReasonable: parseFloat(bookingData.price) >= 50 && parseFloat(bookingData.price) <= 1000\n    }\n    \n    console.log('🔍 价格检查结果:', priceChecks)\n    \n    validation.priceTransmitted = Object.values(priceChecks).every(check => check)\n    \n    if (!priceChecks.hasPrice) {\n      validation.issues.push('价格字段缺失')\n    }\n    if (!priceChecks.priceIsNumber) {\n      validation.issues.push('价格不是有效数字')\n    }\n    if (!priceChecks.priceIsPositive) {\n      validation.issues.push('价格不是正数')\n    }\n    if (!priceChecks.priceIsReasonable) {\n      validation.issues.push('价格不在合理范围内')\n    }\n    \n  } catch (error) {\n    console.error('❌ 验证后端价格接收失败:', error)\n    validation.issues.push(`验证失败: ${error.message}`)\n  }\n  \n  console.log('📊 后端价格接收验证结果:', validation)\n  return validation\n}\n\n// 验证时间段刷新的深度问题\nexport async function validateTimeSlotRefreshDeep(venueId, date, venueStore) {\n  console.log('🔍 开始深度验证时间段刷新...')\n  \n  const validation = {\n    beforeRefresh: null,\n    afterRefresh: null,\n    cacheCleared: false,\n    dataChanged: false,\n    refreshSuccess: false,\n    issues: []\n  }\n  \n  try {\n    // 步骤1：记录刷新前状态\n    validation.beforeRefresh = {\n      slotsCount: venueStore.timeSlots ? venueStore.timeSlots.length : 0,\n      slotsData: venueStore.timeSlots ? [...venueStore.timeSlots] : [],\n      timestamp: Date.now()\n    }\n    \n    console.log('📊 刷新前状态:', validation.beforeRefresh)\n    \n    // 步骤2：检查缓存清除\n    const cacheKeys = []\n    if (typeof window !== 'undefined' && window.cacheManager) {\n      for (const key of window.cacheManager.cache.keys()) {\n        if (key.includes('timeslots') || key.includes(venueId)) {\n          cacheKeys.push(key)\n        }\n      }\n    }\n    \n    console.log('🔍 刷新前缓存键:', cacheKeys)\n    \n    // 步骤3：执行强力刷新\n    console.log('🚀 执行强力刷新...')\n    const refreshResult = await venueStore.forceRefreshTimeSlots(venueId, date)\n    \n    // 步骤4：检查缓存是否被清除\n    const cacheKeysAfter = []\n    if (typeof window !== 'undefined' && window.cacheManager) {\n      for (const key of window.cacheManager.cache.keys()) {\n        if (key.includes('timeslots') || key.includes(venueId)) {\n          cacheKeysAfter.push(key)\n        }\n      }\n    }\n    \n    validation.cacheCleared = cacheKeysAfter.length < cacheKeys.length\n    console.log('🧹 缓存清除检查:', {\n      before: cacheKeys.length,\n      after: cacheKeysAfter.length,\n      cleared: validation.cacheCleared\n    })\n    \n    // 步骤5：记录刷新后状态\n    validation.afterRefresh = {\n      slotsCount: venueStore.timeSlots ? venueStore.timeSlots.length : 0,\n      slotsData: venueStore.timeSlots ? [...venueStore.timeSlots] : [],\n      timestamp: Date.now()\n    }\n    \n    console.log('📊 刷新后状态:', validation.afterRefresh)\n    \n    // 步骤6：检查数据是否真的改变了\n    const beforeDataStr = JSON.stringify(validation.beforeRefresh.slotsData)\n    const afterDataStr = JSON.stringify(validation.afterRefresh.slotsData)\n    validation.dataChanged = beforeDataStr !== afterDataStr\n    \n    console.log('🔄 数据变化检查:', {\n      dataChanged: validation.dataChanged,\n      beforeHash: beforeDataStr.substring(0, 50) + '...',\n      afterHash: afterDataStr.substring(0, 50) + '...'\n    })\n    \n    // 步骤7：综合评估\n    validation.refreshSuccess = refreshResult.success && validation.afterRefresh.slotsCount > 0\n    \n    // 收集问题\n    if (!refreshResult.success) {\n      validation.issues.push('刷新操作失败')\n    }\n    if (!validation.cacheCleared) {\n      validation.issues.push('缓存未被正确清除')\n    }\n    if (validation.afterRefresh.slotsCount === 0) {\n      validation.issues.push('刷新后没有获取到时间段数据')\n    }\n    if (!validation.dataChanged && validation.beforeRefresh.slotsCount > 0) {\n      validation.issues.push('数据没有发生变化，可能仍在使用缓存')\n    }\n    \n  } catch (error) {\n    console.error('❌ 深度验证时间段刷新失败:', error)\n    validation.issues.push(`验证失败: ${error.message}`)\n  }\n  \n  console.log('📊 时间段刷新深度验证结果:', validation)\n  return validation\n}\n\n// 综合深度验证\nexport async function runDeepValidation(bookingData, venueId, date, venueStore) {\n  console.log('🚀 开始综合深度验证...')\n  \n  const results = {\n    priceValidation: null,\n    timeSlotValidation: null,\n    overallSuccess: false,\n    summary: ''\n  }\n  \n  try {\n    // 验证1：后端价格接收\n    console.log('\\n=== 验证1: 后端价格接收 ===')\n    results.priceValidation = await validateBackendPriceReceiving(bookingData)\n    \n    // 验证2：时间段刷新\n    console.log('\\n=== 验证2: 时间段刷新 ===')\n    results.timeSlotValidation = await validateTimeSlotRefreshDeep(venueId, date, venueStore)\n    \n    // 综合评估\n    const priceOk = results.priceValidation.priceTransmitted && results.priceValidation.issues.length === 0\n    const timeSlotOk = results.timeSlotValidation.refreshSuccess && results.timeSlotValidation.issues.length === 0\n    \n    results.overallSuccess = priceOk && timeSlotOk\n    \n    // 生成总结\n    const priceStatus = priceOk ? '✅ 通过' : '❌ 失败'\n    const timeSlotStatus = timeSlotOk ? '✅ 通过' : '❌ 失败'\n    \n    results.summary = `价格传递: ${priceStatus}, 时间段刷新: ${timeSlotStatus}`\n    \n    console.log('\\n📋 综合深度验证结果:')\n    console.log(`${priceOk ? '✅' : '❌'} 价格传递验证: ${results.priceValidation.issues.length === 0 ? '无问题' : results.priceValidation.issues.join(', ')}`)\n    console.log(`${timeSlotOk ? '✅' : '❌'} 时间段刷新验证: ${results.timeSlotValidation.issues.length === 0 ? '无问题' : results.timeSlotValidation.issues.join(', ')}`)\n    console.log(`${results.overallSuccess ? '🎉' : '⚠️'} 总体评估: ${results.overallSuccess ? '全部修复成功' : '仍有问题需要解决'}`)\n    \n  } catch (error) {\n    console.error('❌ 综合深度验证失败:', error)\n    results.error = error.message\n  }\n  \n  return results\n}\n\n// 实时监控工具\nexport function createRealTimeMonitor(venueId, date, venueStore) {\n  console.log('📡 创建实时监控器...')\n  \n  const monitor = {\n    isMonitoring: false,\n    intervalId: null,\n    lastState: null,\n    changeLog: [],\n    \n    start() {\n      if (this.isMonitoring) {\n        console.log('⚠️ 监控器已在运行')\n        return\n      }\n      \n      console.log('🚀 启动实时监控')\n      this.isMonitoring = true\n      this.lastState = this.getCurrentState()\n      \n      this.intervalId = setInterval(() => {\n        const currentState = this.getCurrentState()\n        \n        if (JSON.stringify(currentState) !== JSON.stringify(this.lastState)) {\n          const change = {\n            timestamp: new Date().toLocaleTimeString(),\n            before: this.lastState,\n            after: currentState,\n            type: this.detectChangeType(this.lastState, currentState)\n          }\n          \n          this.changeLog.push(change)\n          console.log('🔄 检测到时间段状态变化:', change)\n          \n          this.lastState = currentState\n        }\n      }, 1000) // 每秒检查一次\n    },\n    \n    stop() {\n      if (!this.isMonitoring) {\n        console.log('⚠️ 监控器未在运行')\n        return\n      }\n      \n      console.log('🛑 停止实时监控')\n      this.isMonitoring = false\n      \n      if (this.intervalId) {\n        clearInterval(this.intervalId)\n        this.intervalId = null\n      }\n      \n      console.log('📊 监控日志:', this.changeLog)\n      return this.changeLog\n    },\n    \n    getCurrentState() {\n      const slots = venueStore.timeSlots || []\n      return {\n        count: slots.length,\n        statuses: slots.map(slot => ({\n          id: slot.id,\n          status: slot.status,\n          time: `${slot.startTime}-${slot.endTime}`\n        }))\n      }\n    },\n    \n    detectChangeType(before, after) {\n      if (before.count !== after.count) {\n        return 'COUNT_CHANGE'\n      }\n      \n      // 检查状态变化\n      for (let i = 0; i < Math.min(before.statuses.length, after.statuses.length); i++) {\n        if (before.statuses[i].status !== after.statuses[i].status) {\n          return 'STATUS_CHANGE'\n        }\n      }\n      \n      return 'UNKNOWN_CHANGE'\n    }\n  }\n  \n  return monitor\n}\n"], "names": ["uni"], "mappings": ";;AAMO,eAAe,8BAA8B,aAAa;AAC/DA,gBAAAA,uDAAY,kBAAkB;AAE9B,QAAM,aAAa;AAAA,IACjB,eAAe;AAAA,IACf,iBAAiB;AAAA,IACjB,kBAAkB;AAAA,IAClB,QAAQ,CAAE;AAAA,EACX;AAED,MAAI;AAEF,eAAW,gBAAgB,WAAW,YAAY,KAAK,KAAK;AAC5DA,0EAAY,eAAe,WAAW,aAAa;AAEnD,QAAI,WAAW,iBAAiB,GAAG;AACjC,iBAAW,OAAO,KAAK,cAAc;AAAA,IACtC;AAIDA,kBAAAA,MAAY,MAAA,OAAA,qCAAA,oBAAoB;AAGhC,UAAM,cAAc;AAAA,MAClB,UAAU,YAAY,UAAU,UAAa,YAAY,UAAU;AAAA,MACnE,eAAe,CAAC,MAAM,WAAW,YAAY,KAAK,CAAC;AAAA,MACnD,iBAAiB,WAAW,YAAY,KAAK,IAAI;AAAA,MACjD,mBAAmB,WAAW,YAAY,KAAK,KAAK,MAAM,WAAW,YAAY,KAAK,KAAK;AAAA,IAC5F;AAEDA,kBAAAA,MAAA,MAAA,OAAA,qCAAY,cAAc,WAAW;AAErC,eAAW,mBAAmB,OAAO,OAAO,WAAW,EAAE,MAAM,WAAS,KAAK;AAE7E,QAAI,CAAC,YAAY,UAAU;AACzB,iBAAW,OAAO,KAAK,QAAQ;AAAA,IAChC;AACD,QAAI,CAAC,YAAY,eAAe;AAC9B,iBAAW,OAAO,KAAK,UAAU;AAAA,IAClC;AACD,QAAI,CAAC,YAAY,iBAAiB;AAChC,iBAAW,OAAO,KAAK,QAAQ;AAAA,IAChC;AACD,QAAI,CAAC,YAAY,mBAAmB;AAClC,iBAAW,OAAO,KAAK,WAAW;AAAA,IACnC;AAAA,EAEF,SAAQ,OAAO;AACdA,kBAAAA,MAAc,MAAA,SAAA,qCAAA,iBAAiB,KAAK;AACpC,eAAW,OAAO,KAAK,SAAS,MAAM,OAAO,EAAE;AAAA,EAChD;AAEDA,gBAAAA,MAAA,MAAA,OAAA,qCAAY,kBAAkB,UAAU;AACxC,SAAO;AACT;AAGO,eAAe,4BAA4B,SAAS,MAAM,YAAY;AAC3EA,gBAAAA,wDAAY,mBAAmB;AAE/B,QAAM,aAAa;AAAA,IACjB,eAAe;AAAA,IACf,cAAc;AAAA,IACd,cAAc;AAAA,IACd,aAAa;AAAA,IACb,gBAAgB;AAAA,IAChB,QAAQ,CAAE;AAAA,EACX;AAED,MAAI;AAEF,eAAW,gBAAgB;AAAA,MACzB,YAAY,WAAW,YAAY,WAAW,UAAU,SAAS;AAAA,MACjE,WAAW,WAAW,YAAY,CAAC,GAAG,WAAW,SAAS,IAAI,CAAE;AAAA,MAChE,WAAW,KAAK,IAAK;AAAA,IACtB;AAEDA,0EAAY,aAAa,WAAW,aAAa;AAGjD,UAAM,YAAY,CAAE;AACpB,QAAI,OAAO,WAAW,eAAe,OAAO,cAAc;AACxD,iBAAW,OAAO,OAAO,aAAa,MAAM,KAAI,GAAI;AAClD,YAAI,IAAI,SAAS,WAAW,KAAK,IAAI,SAAS,OAAO,GAAG;AACtD,oBAAU,KAAK,GAAG;AAAA,QACnB;AAAA,MACF;AAAA,IACF;AAEDA,kBAAAA,MAAY,MAAA,OAAA,qCAAA,cAAc,SAAS;AAGnCA,kBAAAA,yDAAY,cAAc;AAC1B,UAAM,gBAAgB,MAAM,WAAW,sBAAsB,SAAS,IAAI;AAG1E,UAAM,iBAAiB,CAAE;AACzB,QAAI,OAAO,WAAW,eAAe,OAAO,cAAc;AACxD,iBAAW,OAAO,OAAO,aAAa,MAAM,KAAI,GAAI;AAClD,YAAI,IAAI,SAAS,WAAW,KAAK,IAAI,SAAS,OAAO,GAAG;AACtD,yBAAe,KAAK,GAAG;AAAA,QACxB;AAAA,MACF;AAAA,IACF;AAED,eAAW,eAAe,eAAe,SAAS,UAAU;AAC5DA,kBAAAA,yDAAY,cAAc;AAAA,MACxB,QAAQ,UAAU;AAAA,MAClB,OAAO,eAAe;AAAA,MACtB,SAAS,WAAW;AAAA,IAC1B,CAAK;AAGD,eAAW,eAAe;AAAA,MACxB,YAAY,WAAW,YAAY,WAAW,UAAU,SAAS;AAAA,MACjE,WAAW,WAAW,YAAY,CAAC,GAAG,WAAW,SAAS,IAAI,CAAE;AAAA,MAChE,WAAW,KAAK,IAAK;AAAA,IACtB;AAEDA,2EAAY,aAAa,WAAW,YAAY;AAGhD,UAAM,gBAAgB,KAAK,UAAU,WAAW,cAAc,SAAS;AACvE,UAAM,eAAe,KAAK,UAAU,WAAW,aAAa,SAAS;AACrE,eAAW,cAAc,kBAAkB;AAE3CA,kBAAAA,yDAAY,cAAc;AAAA,MACxB,aAAa,WAAW;AAAA,MACxB,YAAY,cAAc,UAAU,GAAG,EAAE,IAAI;AAAA,MAC7C,WAAW,aAAa,UAAU,GAAG,EAAE,IAAI;AAAA,IACjD,CAAK;AAGD,eAAW,iBAAiB,cAAc,WAAW,WAAW,aAAa,aAAa;AAG1F,QAAI,CAAC,cAAc,SAAS;AAC1B,iBAAW,OAAO,KAAK,QAAQ;AAAA,IAChC;AACD,QAAI,CAAC,WAAW,cAAc;AAC5B,iBAAW,OAAO,KAAK,UAAU;AAAA,IAClC;AACD,QAAI,WAAW,aAAa,eAAe,GAAG;AAC5C,iBAAW,OAAO,KAAK,eAAe;AAAA,IACvC;AACD,QAAI,CAAC,WAAW,eAAe,WAAW,cAAc,aAAa,GAAG;AACtE,iBAAW,OAAO,KAAK,mBAAmB;AAAA,IAC3C;AAAA,EAEF,SAAQ,OAAO;AACdA,kBAAAA,MAAc,MAAA,SAAA,sCAAA,kBAAkB,KAAK;AACrC,eAAW,OAAO,KAAK,SAAS,MAAM,OAAO,EAAE;AAAA,EAChD;AAEDA,gBAAAA,MAAA,MAAA,OAAA,sCAAY,mBAAmB,UAAU;AACzC,SAAO;AACT;AAGO,eAAe,kBAAkB,aAAa,SAAS,MAAM,YAAY;AAC9EA,gBAAAA,MAAY,MAAA,OAAA,sCAAA,gBAAgB;AAE5B,QAAM,UAAU;AAAA,IACd,iBAAiB;AAAA,IACjB,oBAAoB;AAAA,IACpB,gBAAgB;AAAA,IAChB,SAAS;AAAA,EACV;AAED,MAAI;AAEFA,kBAAAA,MAAY,MAAA,OAAA,sCAAA,uBAAuB;AACnC,YAAQ,kBAAkB,MAAM,8BAA8B,WAAW;AAGzEA,kBAAAA,MAAY,MAAA,OAAA,sCAAA,sBAAsB;AAClC,YAAQ,qBAAqB,MAAM,4BAA4B,SAAS,MAAM,UAAU;AAGxF,UAAM,UAAU,QAAQ,gBAAgB,oBAAoB,QAAQ,gBAAgB,OAAO,WAAW;AACtG,UAAM,aAAa,QAAQ,mBAAmB,kBAAkB,QAAQ,mBAAmB,OAAO,WAAW;AAE7G,YAAQ,iBAAiB,WAAW;AAGpC,UAAM,cAAc,UAAU,SAAS;AACvC,UAAM,iBAAiB,aAAa,SAAS;AAE7C,YAAQ,UAAU,SAAS,WAAW,YAAY,cAAc;AAEhEA,kBAAAA,MAAA,MAAA,OAAA,sCAAY,gBAAgB;AAC5BA,wBAAA,MAAA,OAAA,sCAAY,GAAG,UAAU,MAAM,GAAG,YAAY,QAAQ,gBAAgB,OAAO,WAAW,IAAI,QAAQ,QAAQ,gBAAgB,OAAO,KAAK,IAAI,CAAC,EAAE;AAC/IA,wBAAA,MAAA,OAAA,sCAAY,GAAG,aAAa,MAAM,GAAG,aAAa,QAAQ,mBAAmB,OAAO,WAAW,IAAI,QAAQ,QAAQ,mBAAmB,OAAO,KAAK,IAAI,CAAC,EAAE;AACzJA,kBAAA,MAAA,MAAA,OAAA,sCAAY,GAAG,QAAQ,iBAAiB,OAAO,IAAI,UAAU,QAAQ,iBAAiB,WAAW,UAAU,EAAE;AAAA,EAE9G,SAAQ,OAAO;AACdA,kBAAAA,MAAc,MAAA,SAAA,sCAAA,eAAe,KAAK;AAClC,YAAQ,QAAQ,MAAM;AAAA,EACvB;AAED,SAAO;AACT;;"}