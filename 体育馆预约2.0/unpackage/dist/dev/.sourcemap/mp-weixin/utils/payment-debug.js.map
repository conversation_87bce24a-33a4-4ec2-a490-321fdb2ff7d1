{"version": 3, "file": "payment-debug.js", "sources": ["utils/payment-debug.js"], "sourcesContent": ["/**\n * 支付页面调试工具\n * 用于诊断和修复订单金额显示问题\n */\n\n// 订单金额计算调试\nexport function debugOrderAmount(orderInfo) {\n  console.log('🔍 [支付调试] 开始分析订单金额问题')\n  console.log('📋 订单信息:', orderInfo)\n  \n  if (!orderInfo) {\n    console.log('❌ 订单信息为空')\n    return { success: false, error: '订单信息为空' }\n  }\n\n  // 检查各种价格字段\n  const priceFields = {\n    totalPrice: orderInfo.totalPrice,\n    paymentAmount: orderInfo.paymentAmount,\n    price: orderInfo.price,\n    amount: orderInfo.amount\n  }\n  \n  console.log('💰 价格字段检查:', priceFields)\n  \n  // 检查时间字段\n  const timeFields = {\n    startTime: orderInfo.startTime,\n    endTime: orderInfo.endTime,\n    bookingTime: orderInfo.bookingTime,\n    bookingDate: orderInfo.bookingDate\n  }\n  \n  console.log('⏰ 时间字段检查:', timeFields)\n  \n  // 检查订单类型\n  const typeFields = {\n    bookingType: orderInfo.bookingType,\n    isVirtualOrder: orderInfo.isVirtualOrder,\n    orderType: orderInfo.orderType\n  }\n  \n  console.log('🏷️ 类型字段检查:', typeFields)\n  \n  // 尝试计算正确的价格\n  let calculatedPrice = 0\n  let calculationMethod = ''\n  \n  // 方法1：使用现有价格字段\n  if (orderInfo.totalPrice && orderInfo.totalPrice > 0) {\n    calculatedPrice = orderInfo.totalPrice\n    calculationMethod = 'totalPrice字段'\n  } else if (orderInfo.paymentAmount && orderInfo.paymentAmount > 0) {\n    calculatedPrice = orderInfo.paymentAmount\n    calculationMethod = 'paymentAmount字段'\n  } else if (orderInfo.price && orderInfo.price > 0) {\n    calculatedPrice = orderInfo.price\n    calculationMethod = 'price字段'\n  }\n  \n  // 方法2：根据时间段计算\n  if (calculatedPrice === 0 && orderInfo.startTime && orderInfo.endTime) {\n    try {\n      const startHour = parseInt(orderInfo.startTime.split(':')[0])\n      const startMinute = parseInt(orderInfo.startTime.split(':')[1])\n      const endHour = parseInt(orderInfo.endTime.split(':')[0])\n      const endMinute = parseInt(orderInfo.endTime.split(':')[1])\n      \n      const duration = (endHour + endMinute / 60) - (startHour + startMinute / 60)\n      const hourlyRate = 120 // 标准费率\n      \n      calculatedPrice = duration * hourlyRate\n      calculationMethod = `时间段计算 (${duration}小时 × ${hourlyRate}元/小时)`\n      \n      console.log(`⏱️ 时间段计算: ${orderInfo.startTime}-${orderInfo.endTime} = ${duration}小时`)\n    } catch (error) {\n      console.error('时间段计算失败:', error)\n    }\n  }\n  \n  // 方法3：根据订单类型使用默认价格\n  if (calculatedPrice === 0) {\n    if (orderInfo.bookingType === 'SHARED' || orderInfo.isVirtualOrder) {\n      calculatedPrice = 120 // 拼场默认价格\n      calculationMethod = '拼场默认价格'\n    } else {\n      calculatedPrice = 240 // 独享默认价格（2小时）\n      calculationMethod = '独享默认价格'\n    }\n  }\n  \n  const result = {\n    success: true,\n    originalPrice: orderInfo.totalPrice || 0,\n    calculatedPrice: calculatedPrice,\n    calculationMethod: calculationMethod,\n    recommendation: calculatedPrice > 0 ? '使用计算价格' : '需要检查后端数据'\n  }\n  \n  console.log('✅ [支付调试] 分析结果:', result)\n  return result\n}\n\n// 时间段刷新调试\nexport function debugTimeSlotRefresh(venueId, date, venueStore) {\n  console.log('🔄 [时间段调试] 开始分析时间段刷新问题')\n  console.log('📍 参数:', { venueId, date })\n  \n  if (!venueStore) {\n    console.log('❌ VenueStore未提供')\n    return { success: false, error: 'VenueStore未提供' }\n  }\n  \n  // 检查当前时间段状态\n  const currentTimeSlots = venueStore.timeSlots || []\n  console.log('📊 当前时间段数量:', currentTimeSlots.length)\n  console.log('📊 当前时间段状态:', currentTimeSlots.map(slot => ({\n    id: slot.id,\n    time: `${slot.startTime}-${slot.endTime}`,\n    status: slot.status\n  })))\n  \n  return {\n    success: true,\n    currentSlotsCount: currentTimeSlots.length,\n    currentSlots: currentTimeSlots,\n    recommendation: '执行强制刷新'\n  }\n}\n\n// 执行时间段强制刷新\nexport async function forceRefreshTimeSlots(venueId, date, venueStore) {\n  console.log('🚀 [时间段调试] 执行强制刷新')\n\n  try {\n    // 步骤1：清除所有相关缓存\n    console.log('🧹 步骤1: 清除所有相关缓存')\n    venueStore.clearTimeSlots()\n\n    // 清除请求缓存\n    if (typeof window !== 'undefined' && window.cacheManager) {\n      // 清除时间段相关的所有缓存\n      window.cacheManager.clearUrl(`/timeslots/venue/${venueId}`)\n      window.cacheManager.clearUrl(`/timeslots`)\n      console.log('🧹 已清除请求缓存')\n    }\n\n    // 步骤2：等待缓存清除\n    console.log('⏳ 步骤2: 等待缓存清除')\n    await new Promise(resolve => setTimeout(resolve, 300))\n\n    // 步骤3：强制获取新数据（多次尝试）\n    console.log('📡 步骤3: 强制获取新数据')\n    let attempts = 0\n    let success = false\n    let newTimeSlots = []\n\n    while (attempts < 3 && !success) {\n      attempts++\n      console.log(`📡 尝试第${attempts}次获取数据...`)\n\n      try {\n        await venueStore.getTimeSlots(venueId, date, true)\n        newTimeSlots = venueStore.timeSlots || []\n\n        if (newTimeSlots.length > 0) {\n          success = true\n          console.log(`✅ 第${attempts}次尝试成功`)\n        } else {\n          console.log(`⚠️ 第${attempts}次尝试获取到空数据`)\n          if (attempts < 3) {\n            await new Promise(resolve => setTimeout(resolve, 500))\n          }\n        }\n      } catch (error) {\n        console.error(`❌ 第${attempts}次尝试失败:`, error)\n        if (attempts < 3) {\n          await new Promise(resolve => setTimeout(resolve, 500))\n        }\n      }\n    }\n\n    // 步骤4：验证结果\n    console.log('✅ 步骤4: 验证结果')\n    console.log('📊 刷新后时间段数量:', newTimeSlots.length)\n    console.log('📊 刷新后时间段状态:', newTimeSlots.map(slot => ({\n      id: slot.id,\n      time: `${slot.startTime}-${slot.endTime}`,\n      status: slot.status\n    })))\n\n    return {\n      success: success,\n      newSlotsCount: newTimeSlots.length,\n      newSlots: newTimeSlots,\n      attempts: attempts\n    }\n  } catch (error) {\n    console.error('❌ 强制刷新失败:', error)\n    return {\n      success: false,\n      error: error.message\n    }\n  }\n}\n\n// 综合调试工具\nexport function runPaymentDiagnostics(orderInfo, venueId, date, venueStore) {\n  console.log('🔍 [综合调试] 开始支付相关问题诊断')\n  \n  const results = {\n    orderAmount: debugOrderAmount(orderInfo),\n    timeSlotRefresh: debugTimeSlotRefresh(venueId, date, venueStore)\n  }\n  \n  console.log('📋 [综合调试] 诊断结果:', results)\n  return results\n}\n"], "names": ["uni"], "mappings": ";;AAMO,SAAS,iBAAiB,WAAW;AAC1CA,gBAAAA,MAAY,MAAA,OAAA,+BAAA,sBAAsB;AAClCA,gBAAAA,kDAAY,YAAY,SAAS;AAEjC,MAAI,CAAC,WAAW;AACdA,kBAAAA,MAAA,MAAA,OAAA,gCAAY,UAAU;AACtB,WAAO,EAAE,SAAS,OAAO,OAAO,SAAU;AAAA,EAC3C;AAGD,QAAM,cAAc;AAAA,IAClB,YAAY,UAAU;AAAA,IACtB,eAAe,UAAU;AAAA,IACzB,OAAO,UAAU;AAAA,IACjB,QAAQ,UAAU;AAAA,EACnB;AAEDA,gBAAAA,MAAY,MAAA,OAAA,gCAAA,cAAc,WAAW;AAGrC,QAAM,aAAa;AAAA,IACjB,WAAW,UAAU;AAAA,IACrB,SAAS,UAAU;AAAA,IACnB,aAAa,UAAU;AAAA,IACvB,aAAa,UAAU;AAAA,EACxB;AAEDA,gBAAAA,MAAY,MAAA,OAAA,gCAAA,aAAa,UAAU;AAGnC,QAAM,aAAa;AAAA,IACjB,aAAa,UAAU;AAAA,IACvB,gBAAgB,UAAU;AAAA,IAC1B,WAAW,UAAU;AAAA,EACtB;AAEDA,gBAAAA,MAAY,MAAA,OAAA,gCAAA,eAAe,UAAU;AAGrC,MAAI,kBAAkB;AACtB,MAAI,oBAAoB;AAGxB,MAAI,UAAU,cAAc,UAAU,aAAa,GAAG;AACpD,sBAAkB,UAAU;AAC5B,wBAAoB;AAAA,EACrB,WAAU,UAAU,iBAAiB,UAAU,gBAAgB,GAAG;AACjE,sBAAkB,UAAU;AAC5B,wBAAoB;AAAA,EACrB,WAAU,UAAU,SAAS,UAAU,QAAQ,GAAG;AACjD,sBAAkB,UAAU;AAC5B,wBAAoB;AAAA,EACrB;AAGD,MAAI,oBAAoB,KAAK,UAAU,aAAa,UAAU,SAAS;AACrE,QAAI;AACF,YAAM,YAAY,SAAS,UAAU,UAAU,MAAM,GAAG,EAAE,CAAC,CAAC;AAC5D,YAAM,cAAc,SAAS,UAAU,UAAU,MAAM,GAAG,EAAE,CAAC,CAAC;AAC9D,YAAM,UAAU,SAAS,UAAU,QAAQ,MAAM,GAAG,EAAE,CAAC,CAAC;AACxD,YAAM,YAAY,SAAS,UAAU,QAAQ,MAAM,GAAG,EAAE,CAAC,CAAC;AAE1D,YAAM,WAAY,UAAU,YAAY,MAAO,YAAY,cAAc;AACzE,YAAM,aAAa;AAEnB,wBAAkB,WAAW;AAC7B,0BAAoB,UAAU,QAAQ,QAAQ,UAAU;AAExDA,oBAAAA,mDAAY,aAAa,UAAU,SAAS,IAAI,UAAU,OAAO,MAAM,QAAQ,IAAI;AAAA,IACpF,SAAQ,OAAO;AACdA,oBAAAA,MAAc,MAAA,SAAA,gCAAA,YAAY,KAAK;AAAA,IAChC;AAAA,EACF;AAGD,MAAI,oBAAoB,GAAG;AACzB,QAAI,UAAU,gBAAgB,YAAY,UAAU,gBAAgB;AAClE,wBAAkB;AAClB,0BAAoB;AAAA,IAC1B,OAAW;AACL,wBAAkB;AAClB,0BAAoB;AAAA,IACrB;AAAA,EACF;AAED,QAAM,SAAS;AAAA,IACb,SAAS;AAAA,IACT,eAAe,UAAU,cAAc;AAAA,IACvC;AAAA,IACA;AAAA,IACA,gBAAgB,kBAAkB,IAAI,WAAW;AAAA,EAClD;AAEDA,gBAAAA,MAAY,MAAA,OAAA,iCAAA,kBAAkB,MAAM;AACpC,SAAO;AACT;AAGO,SAAS,qBAAqB,SAAS,MAAM,YAAY;AAC9DA,gBAAAA,MAAY,MAAA,OAAA,iCAAA,wBAAwB;AACpCA,gBAAA,MAAA,MAAA,OAAA,iCAAY,UAAU,EAAE,SAAS,MAAM;AAEvC,MAAI,CAAC,YAAY;AACfA,kBAAAA,MAAA,MAAA,OAAA,iCAAY,iBAAiB;AAC7B,WAAO,EAAE,SAAS,OAAO,OAAO,gBAAiB;AAAA,EAClD;AAGD,QAAM,mBAAmB,WAAW,aAAa,CAAE;AACnDA,gBAAY,MAAA,MAAA,OAAA,iCAAA,eAAe,iBAAiB,MAAM;AAClDA,oEAAY,eAAe,iBAAiB,IAAI,WAAS;AAAA,IACvD,IAAI,KAAK;AAAA,IACT,MAAM,GAAG,KAAK,SAAS,IAAI,KAAK,OAAO;AAAA,IACvC,QAAQ,KAAK;AAAA,EACd,EAAC,CAAC;AAEH,SAAO;AAAA,IACL,SAAS;AAAA,IACT,mBAAmB,iBAAiB;AAAA,IACpC,cAAc;AAAA,IACd,gBAAgB;AAAA,EACjB;AACH;AAGO,eAAe,sBAAsB,SAAS,MAAM,YAAY;AACrEA,gBAAAA,oDAAY,mBAAmB;AAE/B,MAAI;AAEFA,kBAAAA,MAAA,MAAA,OAAA,iCAAY,kBAAkB;AAC9B,eAAW,eAAgB;AAG3B,QAAI,OAAO,WAAW,eAAe,OAAO,cAAc;AAExD,aAAO,aAAa,SAAS,oBAAoB,OAAO,EAAE;AAC1D,aAAO,aAAa,SAAS,YAAY;AACzCA,oBAAAA,MAAA,MAAA,OAAA,iCAAY,YAAY;AAAA,IACzB;AAGDA,kBAAAA,oDAAY,eAAe;AAC3B,UAAM,IAAI,QAAQ,aAAW,WAAW,SAAS,GAAG,CAAC;AAGrDA,kBAAAA,MAAA,MAAA,OAAA,iCAAY,iBAAiB;AAC7B,QAAI,WAAW;AACf,QAAI,UAAU;AACd,QAAI,eAAe,CAAE;AAErB,WAAO,WAAW,KAAK,CAAC,SAAS;AAC/B;AACAA,oBAAY,MAAA,MAAA,OAAA,iCAAA,SAAS,QAAQ,UAAU;AAEvC,UAAI;AACF,cAAM,WAAW,aAAa,SAAS,MAAM,IAAI;AACjD,uBAAe,WAAW,aAAa,CAAE;AAEzC,YAAI,aAAa,SAAS,GAAG;AAC3B,oBAAU;AACVA,wBAAA,MAAA,MAAA,OAAA,iCAAY,MAAM,QAAQ,OAAO;AAAA,QAC3C,OAAe;AACLA,wBAAA,MAAA,MAAA,OAAA,iCAAY,OAAO,QAAQ,WAAW;AACtC,cAAI,WAAW,GAAG;AAChB,kBAAM,IAAI,QAAQ,aAAW,WAAW,SAAS,GAAG,CAAC;AAAA,UACtD;AAAA,QACF;AAAA,MACF,SAAQ,OAAO;AACdA,4BAAA,MAAA,SAAA,iCAAc,MAAM,QAAQ,UAAU,KAAK;AAC3C,YAAI,WAAW,GAAG;AAChB,gBAAM,IAAI,QAAQ,aAAW,WAAW,SAAS,GAAG,CAAC;AAAA,QACtD;AAAA,MACF;AAAA,IACF;AAGDA,kBAAAA,MAAY,MAAA,OAAA,iCAAA,aAAa;AACzBA,kBAAY,MAAA,MAAA,OAAA,iCAAA,gBAAgB,aAAa,MAAM;AAC/CA,kBAAA,MAAA,MAAA,OAAA,iCAAY,gBAAgB,aAAa,IAAI,WAAS;AAAA,MACpD,IAAI,KAAK;AAAA,MACT,MAAM,GAAG,KAAK,SAAS,IAAI,KAAK,OAAO;AAAA,MACvC,QAAQ,KAAK;AAAA,IACd,EAAC,CAAC;AAEH,WAAO;AAAA,MACL;AAAA,MACA,eAAe,aAAa;AAAA,MAC5B,UAAU;AAAA,MACV;AAAA,IACD;AAAA,EACF,SAAQ,OAAO;AACdA,kBAAAA,MAAA,MAAA,SAAA,iCAAc,aAAa,KAAK;AAChC,WAAO;AAAA,MACL,SAAS;AAAA,MACT,OAAO,MAAM;AAAA,IACd;AAAA,EACF;AACH;;;;"}