{"version": 3, "file": "backend-price-debug.js", "sources": ["utils/backend-price-debug.js"], "sourcesContent": ["/**\n * 后端价格调试工具\n * 用于调试前端价格传递到后端的问题\n */\n\n// 调试预约数据发送\nexport function debugBookingDataSend(bookingData) {\n  console.log('🔍 调试预约数据发送')\n  \n  const debug = {\n    timestamp: new Date().toISOString(),\n    originalData: bookingData,\n    priceAnalysis: {\n      hasPrice: bookingData.hasOwnProperty('price'),\n      priceValue: bookingData.price,\n      priceType: typeof bookingData.price,\n      isNumber: !isNaN(parseFloat(bookingData.price)),\n      isPositive: parseFloat(bookingData.price) > 0\n    },\n    dataStructure: {\n      venueId: {\n        value: bookingData.venueId,\n        type: typeof bookingData.venueId\n      },\n      date: {\n        value: bookingData.date,\n        type: typeof bookingData.date\n      },\n      startTime: {\n        value: bookingData.startTime,\n        type: typeof bookingData.startTime\n      },\n      endTime: {\n        value: bookingData.endTime,\n        type: typeof bookingData.endTime\n      },\n      price: {\n        value: bookingData.price,\n        type: typeof bookingData.price\n      },\n      slotIds: {\n        value: bookingData.slotIds,\n        type: typeof bookingData.slotIds,\n        length: Array.isArray(bookingData.slotIds) ? bookingData.slotIds.length : 'N/A'\n      }\n    },\n    serialization: null,\n    issues: []\n  }\n  \n  // 检查价格问题\n  if (!debug.priceAnalysis.hasPrice) {\n    debug.issues.push('缺少price字段')\n  } else if (!debug.priceAnalysis.isNumber) {\n    debug.issues.push('price不是有效数字')\n  } else if (!debug.priceAnalysis.isPositive) {\n    debug.issues.push('price不是正数')\n  }\n  \n  // 检查venueId类型\n  if (typeof bookingData.venueId === 'string') {\n    debug.issues.push('venueId是字符串类型，后端可能需要数字类型')\n  }\n  \n  // 测试序列化\n  try {\n    const serialized = JSON.stringify(bookingData)\n    const deserialized = JSON.parse(serialized)\n    debug.serialization = {\n      success: true,\n      serializedLength: serialized.length,\n      deserializedPrice: deserialized.price,\n      pricePreserved: deserialized.price === bookingData.price\n    }\n  } catch (error) {\n    debug.serialization = {\n      success: false,\n      error: error.message\n    }\n    debug.issues.push('数据序列化失败')\n  }\n  \n  console.log('🔍 预约数据发送调试结果:', debug)\n  return debug\n}\n\n// 调试后端响应\nexport function debugBackendResponse(response) {\n  console.log('🔍 调试后端响应')\n  \n  const debug = {\n    timestamp: new Date().toISOString(),\n    originalResponse: response,\n    responseAnalysis: {\n      hasData: response.hasOwnProperty('data'),\n      hasOrderId: response.hasOwnProperty('orderId'),\n      success: response.success,\n      message: response.message\n    },\n    priceAnalysis: null,\n    issues: []\n  }\n  \n  // 分析返回的订单数据中的价格\n  if (response.data) {\n    debug.priceAnalysis = {\n      hasTotalPrice: response.data.hasOwnProperty('totalPrice'),\n      totalPriceValue: response.data.totalPrice,\n      totalPriceType: typeof response.data.totalPrice,\n      isZero: response.data.totalPrice === 0,\n      orderFields: Object.keys(response.data)\n    }\n    \n    if (debug.priceAnalysis.isZero) {\n      debug.issues.push('后端返回的totalPrice为0')\n    }\n  } else {\n    debug.issues.push('响应中没有data字段')\n  }\n  \n  console.log('🔍 后端响应调试结果:', debug)\n  return debug\n}\n\n// 比较前端发送和后端返回的价格\nexport function compareFrontendBackendPrice(sentData, receivedResponse) {\n  console.log('🔍 比较前端发送和后端返回的价格')\n  \n  const comparison = {\n    timestamp: new Date().toISOString(),\n    frontend: {\n      sentPrice: sentData.price,\n      sentPriceType: typeof sentData.price\n    },\n    backend: {\n      receivedPrice: receivedResponse.data?.totalPrice,\n      receivedPriceType: typeof receivedResponse.data?.totalPrice\n    },\n    match: false,\n    priceLoss: 0,\n    issues: []\n  }\n  \n  const frontendPrice = parseFloat(sentData.price)\n  const backendPrice = parseFloat(receivedResponse.data?.totalPrice || 0)\n  \n  comparison.match = frontendPrice === backendPrice\n  comparison.priceLoss = frontendPrice - backendPrice\n  \n  if (!comparison.match) {\n    comparison.issues.push(`价格不匹配: 发送${frontendPrice}，接收${backendPrice}`)\n  }\n  \n  if (backendPrice === 0 && frontendPrice > 0) {\n    comparison.issues.push('后端价格被重置为0')\n  }\n  \n  console.log('🔍 价格比较结果:', comparison)\n  return comparison\n}\n\n// 生成价格传递报告\nexport function generatePriceTransmissionReport(sentData, receivedResponse) {\n  console.log('📊 生成价格传递报告')\n  \n  const sendDebug = debugBookingDataSend(sentData)\n  const responseDebug = debugBackendResponse(receivedResponse)\n  const priceComparison = compareFrontendBackendPrice(sentData, receivedResponse)\n  \n  const report = {\n    timestamp: new Date().toISOString(),\n    title: '价格传递调试报告',\n    summary: '',\n    details: {\n      sendDebug: sendDebug,\n      responseDebug: responseDebug,\n      priceComparison: priceComparison\n    },\n    overallIssues: [],\n    recommendations: []\n  }\n  \n  // 收集所有问题\n  report.overallIssues.push(...sendDebug.issues)\n  report.overallIssues.push(...responseDebug.issues)\n  report.overallIssues.push(...priceComparison.issues)\n  \n  // 生成建议\n  if (priceComparison.priceLoss > 0) {\n    report.recommendations.push('检查后端是否正确接收和处理price字段')\n    report.recommendations.push('验证后端Order模型的totalPrice设置逻辑')\n  }\n  \n  if (sendDebug.issues.includes('venueId是字符串类型，后端可能需要数字类型')) {\n    report.recommendations.push('考虑将venueId转换为数字类型')\n  }\n  \n  if (responseDebug.issues.includes('后端返回的totalPrice为0')) {\n    report.recommendations.push('检查后端是否有重置价格的逻辑')\n    report.recommendations.push('验证后端价格计算和保存流程')\n  }\n  \n  // 生成总结\n  if (report.overallIssues.length === 0) {\n    report.summary = '✅ 价格传递正常'\n  } else {\n    report.summary = `❌ 发现${report.overallIssues.length}个问题`\n  }\n  \n  console.log('📊 价格传递报告:', report)\n  return report\n}\n\n// 实时价格传递监控\nexport function startPriceTransmissionMonitor() {\n  console.log('🔍 启动价格传递监控')\n  \n  const monitor = {\n    isActive: false,\n    transmissions: [],\n    \n    start() {\n      this.isActive = true\n      console.log('✅ 价格传递监控已启动')\n    },\n    \n    recordTransmission(sentData, receivedResponse) {\n      if (!this.isActive) return\n      \n      const transmission = {\n        timestamp: new Date().toISOString(),\n        sentPrice: sentData.price,\n        receivedPrice: receivedResponse.data?.totalPrice,\n        success: sentData.price === receivedResponse.data?.totalPrice,\n        report: generatePriceTransmissionReport(sentData, receivedResponse)\n      }\n      \n      this.transmissions.push(transmission)\n      \n      // 只保留最近10次传输记录\n      if (this.transmissions.length > 10) {\n        this.transmissions.shift()\n      }\n      \n      console.log('📊 记录价格传递:', transmission)\n      \n      if (!transmission.success) {\n        console.warn('⚠️ 价格传递失败:', transmission.report.overallIssues)\n      }\n    },\n    \n    stop() {\n      this.isActive = false\n      console.log('🛑 价格传递监控已停止')\n      return this.transmissions\n    },\n    \n    getReport() {\n      const successCount = this.transmissions.filter(t => t.success).length\n      const failureCount = this.transmissions.length - successCount\n      \n      return {\n        totalTransmissions: this.transmissions.length,\n        successCount: successCount,\n        failureCount: failureCount,\n        successRate: this.transmissions.length > 0 ? \n          Math.round((successCount / this.transmissions.length) * 100) : 0,\n        recentTransmissions: this.transmissions.slice(-5)\n      }\n    }\n  }\n  \n  monitor.start()\n  return monitor\n}\n\n// 快速价格传递测试\nexport async function quickPriceTransmissionTest(bookingStore, testData) {\n  console.log('⚡ 快速价格传递测试')\n  \n  const test = {\n    timestamp: new Date().toISOString(),\n    testData: testData,\n    result: null,\n    success: false,\n    error: null\n  }\n  \n  try {\n    // 启动监控\n    const monitor = startPriceTransmissionMonitor()\n    \n    // 发送测试数据\n    const response = await bookingStore.createBooking(testData)\n    \n    // 记录传输\n    monitor.recordTransmission(testData, response)\n    \n    // 停止监控并获取结果\n    const monitorReport = monitor.getReport()\n    \n    test.result = {\n      response: response,\n      monitorReport: monitorReport\n    }\n    \n    test.success = testData.price === response.data?.totalPrice\n    \n  } catch (error) {\n    console.error('❌ 价格传递测试失败:', error)\n    test.error = error.message\n  }\n  \n  console.log('⚡ 价格传递测试结果:', test)\n  return test\n}\n"], "names": ["uni"], "mappings": ";;;AAMO,SAAS,qBAAqB,aAAa;AAChDA,gBAAAA,MAAA,MAAA,OAAA,qCAAY,aAAa;AAEzB,QAAM,QAAQ;AAAA,IACZ,YAAW,oBAAI,KAAM,GAAC,YAAa;AAAA,IACnC,cAAc;AAAA,IACd,eAAe;AAAA,MACb,UAAU,YAAY,eAAe,OAAO;AAAA,MAC5C,YAAY,YAAY;AAAA,MACxB,WAAW,OAAO,YAAY;AAAA,MAC9B,UAAU,CAAC,MAAM,WAAW,YAAY,KAAK,CAAC;AAAA,MAC9C,YAAY,WAAW,YAAY,KAAK,IAAI;AAAA,IAC7C;AAAA,IACD,eAAe;AAAA,MACb,SAAS;AAAA,QACP,OAAO,YAAY;AAAA,QACnB,MAAM,OAAO,YAAY;AAAA,MAC1B;AAAA,MACD,MAAM;AAAA,QACJ,OAAO,YAAY;AAAA,QACnB,MAAM,OAAO,YAAY;AAAA,MAC1B;AAAA,MACD,WAAW;AAAA,QACT,OAAO,YAAY;AAAA,QACnB,MAAM,OAAO,YAAY;AAAA,MAC1B;AAAA,MACD,SAAS;AAAA,QACP,OAAO,YAAY;AAAA,QACnB,MAAM,OAAO,YAAY;AAAA,MAC1B;AAAA,MACD,OAAO;AAAA,QACL,OAAO,YAAY;AAAA,QACnB,MAAM,OAAO,YAAY;AAAA,MAC1B;AAAA,MACD,SAAS;AAAA,QACP,OAAO,YAAY;AAAA,QACnB,MAAM,OAAO,YAAY;AAAA,QACzB,QAAQ,MAAM,QAAQ,YAAY,OAAO,IAAI,YAAY,QAAQ,SAAS;AAAA,MAC3E;AAAA,IACF;AAAA,IACD,eAAe;AAAA,IACf,QAAQ,CAAE;AAAA,EACX;AAGD,MAAI,CAAC,MAAM,cAAc,UAAU;AACjC,UAAM,OAAO,KAAK,WAAW;AAAA,EAC9B,WAAU,CAAC,MAAM,cAAc,UAAU;AACxC,UAAM,OAAO,KAAK,aAAa;AAAA,EAChC,WAAU,CAAC,MAAM,cAAc,YAAY;AAC1C,UAAM,OAAO,KAAK,WAAW;AAAA,EAC9B;AAGD,MAAI,OAAO,YAAY,YAAY,UAAU;AAC3C,UAAM,OAAO,KAAK,0BAA0B;AAAA,EAC7C;AAGD,MAAI;AACF,UAAM,aAAa,KAAK,UAAU,WAAW;AAC7C,UAAM,eAAe,KAAK,MAAM,UAAU;AAC1C,UAAM,gBAAgB;AAAA,MACpB,SAAS;AAAA,MACT,kBAAkB,WAAW;AAAA,MAC7B,mBAAmB,aAAa;AAAA,MAChC,gBAAgB,aAAa,UAAU,YAAY;AAAA,IACpD;AAAA,EACF,SAAQ,OAAO;AACd,UAAM,gBAAgB;AAAA,MACpB,SAAS;AAAA,MACT,OAAO,MAAM;AAAA,IACd;AACD,UAAM,OAAO,KAAK,SAAS;AAAA,EAC5B;AAEDA,gBAAAA,MAAY,MAAA,OAAA,sCAAA,kBAAkB,KAAK;AACnC,SAAO;AACT;AAGO,SAAS,qBAAqB,UAAU;AAC7CA,gBAAAA,MAAA,MAAA,OAAA,sCAAY,WAAW;AAEvB,QAAM,QAAQ;AAAA,IACZ,YAAW,oBAAI,KAAM,GAAC,YAAa;AAAA,IACnC,kBAAkB;AAAA,IAClB,kBAAkB;AAAA,MAChB,SAAS,SAAS,eAAe,MAAM;AAAA,MACvC,YAAY,SAAS,eAAe,SAAS;AAAA,MAC7C,SAAS,SAAS;AAAA,MAClB,SAAS,SAAS;AAAA,IACnB;AAAA,IACD,eAAe;AAAA,IACf,QAAQ,CAAE;AAAA,EACX;AAGD,MAAI,SAAS,MAAM;AACjB,UAAM,gBAAgB;AAAA,MACpB,eAAe,SAAS,KAAK,eAAe,YAAY;AAAA,MACxD,iBAAiB,SAAS,KAAK;AAAA,MAC/B,gBAAgB,OAAO,SAAS,KAAK;AAAA,MACrC,QAAQ,SAAS,KAAK,eAAe;AAAA,MACrC,aAAa,OAAO,KAAK,SAAS,IAAI;AAAA,IACvC;AAED,QAAI,MAAM,cAAc,QAAQ;AAC9B,YAAM,OAAO,KAAK,mBAAmB;AAAA,IACtC;AAAA,EACL,OAAS;AACL,UAAM,OAAO,KAAK,aAAa;AAAA,EAChC;AAEDA,gBAAAA,0DAAY,gBAAgB,KAAK;AACjC,SAAO;AACT;AAGO,SAAS,4BAA4B,UAAU,kBAAkB;;AACtEA,gBAAAA,0DAAY,mBAAmB;AAE/B,QAAM,aAAa;AAAA,IACjB,YAAW,oBAAI,KAAM,GAAC,YAAa;AAAA,IACnC,UAAU;AAAA,MACR,WAAW,SAAS;AAAA,MACpB,eAAe,OAAO,SAAS;AAAA,IAChC;AAAA,IACD,SAAS;AAAA,MACP,gBAAe,sBAAiB,SAAjB,mBAAuB;AAAA,MACtC,mBAAmB,SAAO,sBAAiB,SAAjB,mBAAuB;AAAA,IAClD;AAAA,IACD,OAAO;AAAA,IACP,WAAW;AAAA,IACX,QAAQ,CAAE;AAAA,EACX;AAED,QAAM,gBAAgB,WAAW,SAAS,KAAK;AAC/C,QAAM,eAAe,aAAW,sBAAiB,SAAjB,mBAAuB,eAAc,CAAC;AAEtE,aAAW,QAAQ,kBAAkB;AACrC,aAAW,YAAY,gBAAgB;AAEvC,MAAI,CAAC,WAAW,OAAO;AACrB,eAAW,OAAO,KAAK,YAAY,aAAa,MAAM,YAAY,EAAE;AAAA,EACrE;AAED,MAAI,iBAAiB,KAAK,gBAAgB,GAAG;AAC3C,eAAW,OAAO,KAAK,WAAW;AAAA,EACnC;AAEDA,gBAAAA,MAAY,MAAA,OAAA,uCAAA,cAAc,UAAU;AACpC,SAAO;AACT;AAGO,SAAS,gCAAgC,UAAU,kBAAkB;AAC1EA,gBAAAA,MAAA,MAAA,OAAA,uCAAY,aAAa;AAEzB,QAAM,YAAY,qBAAqB,QAAQ;AAC/C,QAAM,gBAAgB,qBAAqB,gBAAgB;AAC3D,QAAM,kBAAkB,4BAA4B,UAAU,gBAAgB;AAE9E,QAAM,SAAS;AAAA,IACb,YAAW,oBAAI,KAAM,GAAC,YAAa;AAAA,IACnC,OAAO;AAAA,IACP,SAAS;AAAA,IACT,SAAS;AAAA,MACP;AAAA,MACA;AAAA,MACA;AAAA,IACD;AAAA,IACD,eAAe,CAAE;AAAA,IACjB,iBAAiB,CAAE;AAAA,EACpB;AAGD,SAAO,cAAc,KAAK,GAAG,UAAU,MAAM;AAC7C,SAAO,cAAc,KAAK,GAAG,cAAc,MAAM;AACjD,SAAO,cAAc,KAAK,GAAG,gBAAgB,MAAM;AAGnD,MAAI,gBAAgB,YAAY,GAAG;AACjC,WAAO,gBAAgB,KAAK,sBAAsB;AAClD,WAAO,gBAAgB,KAAK,4BAA4B;AAAA,EACzD;AAED,MAAI,UAAU,OAAO,SAAS,0BAA0B,GAAG;AACzD,WAAO,gBAAgB,KAAK,mBAAmB;AAAA,EAChD;AAED,MAAI,cAAc,OAAO,SAAS,mBAAmB,GAAG;AACtD,WAAO,gBAAgB,KAAK,gBAAgB;AAC5C,WAAO,gBAAgB,KAAK,eAAe;AAAA,EAC5C;AAGD,MAAI,OAAO,cAAc,WAAW,GAAG;AACrC,WAAO,UAAU;AAAA,EACrB,OAAS;AACL,WAAO,UAAU,OAAO,OAAO,cAAc,MAAM;AAAA,EACpD;AAEDA,gBAAAA,0DAAY,cAAc,MAAM;AAChC,SAAO;AACT;AAGO,SAAS,gCAAgC;AAC9CA,gBAAAA,MAAA,MAAA,OAAA,uCAAY,aAAa;AAEzB,QAAM,UAAU;AAAA,IACd,UAAU;AAAA,IACV,eAAe,CAAE;AAAA,IAEjB,QAAQ;AACN,WAAK,WAAW;AAChBA,oBAAAA,MAAA,MAAA,OAAA,uCAAY,aAAa;AAAA,IAC1B;AAAA,IAED,mBAAmB,UAAU,kBAAkB;;AAC7C,UAAI,CAAC,KAAK;AAAU;AAEpB,YAAM,eAAe;AAAA,QACnB,YAAW,oBAAI,KAAM,GAAC,YAAa;AAAA,QACnC,WAAW,SAAS;AAAA,QACpB,gBAAe,sBAAiB,SAAjB,mBAAuB;AAAA,QACtC,SAAS,SAAS,YAAU,sBAAiB,SAAjB,mBAAuB;AAAA,QACnD,QAAQ,gCAAgC,UAAU,gBAAgB;AAAA,MACnE;AAED,WAAK,cAAc,KAAK,YAAY;AAGpC,UAAI,KAAK,cAAc,SAAS,IAAI;AAClC,aAAK,cAAc,MAAO;AAAA,MAC3B;AAEDA,oBAAAA,MAAY,MAAA,OAAA,uCAAA,cAAc,YAAY;AAEtC,UAAI,CAAC,aAAa,SAAS;AACzBA,sBAAA,MAAA,MAAA,QAAA,uCAAa,cAAc,aAAa,OAAO,aAAa;AAAA,MAC7D;AAAA,IACF;AAAA,IAED,OAAO;AACL,WAAK,WAAW;AAChBA,oBAAAA,MAAY,MAAA,OAAA,uCAAA,cAAc;AAC1B,aAAO,KAAK;AAAA,IACb;AAAA,IAED,YAAY;AACV,YAAM,eAAe,KAAK,cAAc,OAAO,OAAK,EAAE,OAAO,EAAE;AAC/D,YAAM,eAAe,KAAK,cAAc,SAAS;AAEjD,aAAO;AAAA,QACL,oBAAoB,KAAK,cAAc;AAAA,QACvC;AAAA,QACA;AAAA,QACA,aAAa,KAAK,cAAc,SAAS,IACvC,KAAK,MAAO,eAAe,KAAK,cAAc,SAAU,GAAG,IAAI;AAAA,QACjE,qBAAqB,KAAK,cAAc,MAAM,EAAE;AAAA,MACjD;AAAA,IACF;AAAA,EACF;AAED,UAAQ,MAAO;AACf,SAAO;AACT;AAGO,eAAe,2BAA2B,cAAc,UAAU;;AACvEA,gBAAAA,MAAA,MAAA,OAAA,uCAAY,YAAY;AAExB,QAAM,OAAO;AAAA,IACX,YAAW,oBAAI,KAAM,GAAC,YAAa;AAAA,IACnC;AAAA,IACA,QAAQ;AAAA,IACR,SAAS;AAAA,IACT,OAAO;AAAA,EACR;AAED,MAAI;AAEF,UAAM,UAAU,8BAA+B;AAG/C,UAAM,WAAW,MAAM,aAAa,cAAc,QAAQ;AAG1D,YAAQ,mBAAmB,UAAU,QAAQ;AAG7C,UAAM,gBAAgB,QAAQ,UAAW;AAEzC,SAAK,SAAS;AAAA,MACZ;AAAA,MACA;AAAA,IACD;AAED,SAAK,UAAU,SAAS,YAAU,cAAS,SAAT,mBAAe;AAAA,EAElD,SAAQ,OAAO;AACdA,kBAAAA,MAAc,MAAA,SAAA,uCAAA,eAAe,KAAK;AAClC,SAAK,QAAQ,MAAM;AAAA,EACpB;AAEDA,gBAAAA,0DAAY,eAAe,IAAI;AAC/B,SAAO;AACT;;;;;;;"}