{"version": 3, "file": "sharing-fix-diagnosis.js", "sources": ["utils/sharing-fix-diagnosis.js"], "sourcesContent": ["/**\n * 拼场问题诊断和修复工具\n * 专门用于诊断和修复拼场相关的复杂问题\n */\n\n// 拼场价格计算诊断\nexport function diagnoseSharingPriceCalculation(selectedSlots, venue, bookingForm) {\n  console.log('🔍 拼场价格计算诊断')\n  \n  const diagnosis = {\n    timestamp: new Date().toISOString(),\n    selectedSlots: selectedSlots,\n    venue: venue,\n    bookingForm: bookingForm,\n    priceCalculation: {\n      totalOriginalPrice: 0,\n      pricePerTeam: 0,\n      discountAmount: 0,\n      discountPercentage: 50\n    },\n    issues: [],\n    recommendations: []\n  }\n  \n  try {\n    // 计算原始总价\n    let totalPrice = 0\n    selectedSlots.forEach((slot, index) => {\n      let slotPrice = 0\n      \n      if (slot.price && slot.price > 0) {\n        slotPrice = parseFloat(slot.price)\n      } else if (venue && venue.price && venue.price > 0) {\n        slotPrice = parseFloat(venue.price) / 2 // 30分钟价格\n      } else {\n        slotPrice = 60 // 默认价格\n        diagnosis.issues.push(`时间段${index + 1}使用默认价格`)\n      }\n      \n      totalPrice += slotPrice\n      console.log(`💰 拼场时间段${index + 1}: ${slot.startTime}-${slot.endTime} = ¥${slotPrice}`)\n    })\n    \n    diagnosis.priceCalculation.totalOriginalPrice = totalPrice\n    diagnosis.priceCalculation.pricePerTeam = totalPrice / 2 // 拼场每队支付一半\n    diagnosis.priceCalculation.discountAmount = totalPrice / 2\n    \n    console.log('💰 拼场价格计算结果:', diagnosis.priceCalculation)\n    \n    // 验证价格合理性\n    if (totalPrice <= 0) {\n      diagnosis.issues.push('总价格为0或负数')\n    }\n    \n    if (diagnosis.priceCalculation.pricePerTeam < 30) {\n      diagnosis.issues.push('每队价格过低，可能计算有误')\n    }\n    \n    if (diagnosis.priceCalculation.pricePerTeam > 500) {\n      diagnosis.issues.push('每队价格过高，请检查计算逻辑')\n    }\n    \n    // 生成建议\n    if (diagnosis.issues.length === 0) {\n      diagnosis.recommendations.push('价格计算正常')\n    } else {\n      diagnosis.recommendations.push('需要检查价格计算逻辑')\n      diagnosis.recommendations.push('确保时间段价格数据完整')\n    }\n    \n  } catch (error) {\n    console.error('❌ 拼场价格计算诊断失败:', error)\n    diagnosis.issues.push(`诊断过程出错: ${error.message}`)\n  }\n  \n  console.log('🔍 拼场价格诊断结果:', diagnosis)\n  return diagnosis\n}\n\n// 拼场数据结构诊断\nexport function diagnoseSharingDataStructure(bookingData) {\n  console.log('🔍 拼场数据结构诊断')\n  \n  const diagnosis = {\n    timestamp: new Date().toISOString(),\n    bookingData: bookingData,\n    requiredFields: {\n      venueId: false,\n      date: false,\n      startTime: false,\n      teamName: false,\n      contactInfo: false,\n      maxParticipants: false,\n      price: false\n    },\n    dataTypes: {},\n    issues: [],\n    recommendations: []\n  }\n  \n  try {\n    // 检查必需字段\n    const requiredFieldsList = Object.keys(diagnosis.requiredFields)\n    \n    requiredFieldsList.forEach(field => {\n      const hasField = bookingData.hasOwnProperty(field) && \n                      bookingData[field] !== null && \n                      bookingData[field] !== undefined\n      \n      diagnosis.requiredFields[field] = hasField\n      diagnosis.dataTypes[field] = {\n        value: bookingData[field],\n        type: typeof bookingData[field],\n        hasValue: hasField\n      }\n      \n      if (!hasField) {\n        diagnosis.issues.push(`缺少必需字段: ${field}`)\n      }\n    })\n    \n    // 检查数据类型\n    if (bookingData.venueId && typeof bookingData.venueId !== 'number') {\n      diagnosis.issues.push('venueId应该是数字类型')\n    }\n    \n    if (bookingData.price && typeof bookingData.price !== 'number') {\n      diagnosis.issues.push('price应该是数字类型')\n    }\n    \n    if (bookingData.maxParticipants && bookingData.maxParticipants !== 2) {\n      diagnosis.issues.push('拼场maxParticipants应该固定为2')\n    }\n    \n    // 检查拼场特有字段\n    if (!bookingData.teamName || bookingData.teamName.trim() === '') {\n      diagnosis.issues.push('拼场必须提供队伍名称')\n    }\n    \n    if (!bookingData.contactInfo || bookingData.contactInfo.trim() === '') {\n      diagnosis.issues.push('拼场必须提供联系方式')\n    }\n    \n    // 生成建议\n    if (diagnosis.issues.length === 0) {\n      diagnosis.recommendations.push('数据结构完整')\n    } else {\n      diagnosis.recommendations.push('需要补充缺失的字段')\n      diagnosis.recommendations.push('检查数据类型是否正确')\n    }\n    \n  } catch (error) {\n    console.error('❌ 拼场数据结构诊断失败:', error)\n    diagnosis.issues.push(`诊断过程出错: ${error.message}`)\n  }\n  \n  console.log('🔍 拼场数据结构诊断结果:', diagnosis)\n  return diagnosis\n}\n\n// 拼场API路径诊断\nexport function diagnoseSharingApiPath() {\n  console.log('🔍 拼场API路径诊断')\n  \n  const diagnosis = {\n    timestamp: new Date().toISOString(),\n    availableApis: {\n      createSharedBooking: '/bookings/shared',\n      createSharingOrder: '/sharing-orders',\n      applySharingOrder: '/sharing-orders/{id}/apply-join',\n      joinSharingOrder: '/bookings/shared/{id}/apply'\n    },\n    recommendations: [],\n    preferredFlow: []\n  }\n  \n  // 分析API使用建议\n  diagnosis.recommendations.push('建议统一使用 /bookings/shared 系列API')\n  diagnosis.recommendations.push('避免混用 /sharing-orders 和 /bookings/shared')\n  diagnosis.recommendations.push('确保前后端API路径一致')\n  \n  // 推荐的拼场流程\n  diagnosis.preferredFlow = [\n    '1. 创建拼场: POST /bookings/shared',\n    '2. 申请加入: POST /bookings/shared/{id}/apply', \n    '3. 处理申请: PUT /bookings/shared/requests/{requestId}',\n    '4. 支付完成: 更新申请状态为PAID'\n  ]\n  \n  console.log('🔍 拼场API路径诊断结果:', diagnosis)\n  return diagnosis\n}\n\n// 拼场时间段同步诊断\nexport async function diagnoseSharingTimeSlotSync(venueId, date, selectedSlots) {\n  console.log('🔍 拼场时间段同步诊断')\n  \n  const diagnosis = {\n    timestamp: new Date().toISOString(),\n    venueId: venueId,\n    date: date,\n    selectedSlots: selectedSlots,\n    syncStatus: {\n      frontendSlots: selectedSlots.length,\n      backendSlots: 0,\n      hasDefaultIds: false,\n      needsSync: false\n    },\n    issues: [],\n    recommendations: []\n  }\n  \n  try {\n    // 检查前端时间段ID格式\n    const defaultIdPattern = /^default_/\n    diagnosis.syncStatus.hasDefaultIds = selectedSlots.some(slot => \n      defaultIdPattern.test(slot.id)\n    )\n    \n    if (diagnosis.syncStatus.hasDefaultIds) {\n      diagnosis.issues.push('使用前端生成的默认时间段ID')\n      diagnosis.syncStatus.needsSync = true\n    }\n    \n    // 检查后端时间段（这里需要实际API调用）\n    // 由于是诊断工具，我们先记录需要检查的项目\n    diagnosis.recommendations.push('需要检查后端是否有对应的时间段数据')\n    diagnosis.recommendations.push('确保时间段ID能被后端正确识别')\n    \n    if (diagnosis.syncStatus.needsSync) {\n      diagnosis.recommendations.push('建议在创建拼场前先同步时间段到后端')\n    }\n    \n  } catch (error) {\n    console.error('❌ 拼场时间段同步诊断失败:', error)\n    diagnosis.issues.push(`诊断过程出错: ${error.message}`)\n  }\n  \n  console.log('🔍 拼场时间段同步诊断结果:', diagnosis)\n  return diagnosis\n}\n\n// 综合拼场问题诊断\nexport async function comprehensiveSharingDiagnosis(selectedSlots, venue, bookingForm, venueId, date) {\n  console.log('🚀 开始综合拼场问题诊断')\n  \n  const diagnosis = {\n    timestamp: new Date().toISOString(),\n    overallStatus: 'UNKNOWN',\n    priceCalculationDiagnosis: null,\n    dataStructureDiagnosis: null,\n    apiPathDiagnosis: null,\n    timeSlotSyncDiagnosis: null,\n    allIssues: [],\n    allRecommendations: [],\n    summary: ''\n  }\n  \n  try {\n    // 1. 价格计算诊断\n    console.log('\\n=== 拼场价格计算诊断 ===')\n    diagnosis.priceCalculationDiagnosis = diagnoseSharingPriceCalculation(selectedSlots, venue, bookingForm)\n    \n    // 2. 构建预约数据进行结构诊断\n    const mockBookingData = {\n      venueId: parseInt(venueId),\n      date: date,\n      startTime: selectedSlots[0]?.startTime,\n      teamName: bookingForm.teamName,\n      contactInfo: bookingForm.contactInfo,\n      maxParticipants: 2,\n      price: diagnosis.priceCalculationDiagnosis.priceCalculation.pricePerTeam,\n      description: bookingForm.description\n    }\n    \n    console.log('\\n=== 拼场数据结构诊断 ===')\n    diagnosis.dataStructureDiagnosis = diagnoseSharingDataStructure(mockBookingData)\n    \n    // 3. API路径诊断\n    console.log('\\n=== 拼场API路径诊断 ===')\n    diagnosis.apiPathDiagnosis = diagnoseSharingApiPath()\n    \n    // 4. 时间段同步诊断\n    console.log('\\n=== 拼场时间段同步诊断 ===')\n    diagnosis.timeSlotSyncDiagnosis = await diagnoseSharingTimeSlotSync(venueId, date, selectedSlots)\n    \n    // 5. 收集所有问题和建议\n    const allDiagnoses = [\n      diagnosis.priceCalculationDiagnosis,\n      diagnosis.dataStructureDiagnosis,\n      diagnosis.timeSlotSyncDiagnosis\n    ]\n    \n    allDiagnoses.forEach(d => {\n      if (d && d.issues) {\n        diagnosis.allIssues.push(...d.issues)\n      }\n      if (d && d.recommendations) {\n        diagnosis.allRecommendations.push(...d.recommendations)\n      }\n    })\n    \n    // 添加API建议\n    if (diagnosis.apiPathDiagnosis && diagnosis.apiPathDiagnosis.recommendations) {\n      diagnosis.allRecommendations.push(...diagnosis.apiPathDiagnosis.recommendations)\n    }\n    \n    // 6. 确定总体状态\n    if (diagnosis.allIssues.length === 0) {\n      diagnosis.overallStatus = 'HEALTHY'\n      diagnosis.summary = '✅ 拼场系统状态良好'\n    } else if (diagnosis.allIssues.length <= 3) {\n      diagnosis.overallStatus = 'WARNING'\n      diagnosis.summary = `⚠️ 发现${diagnosis.allIssues.length}个问题，需要注意`\n    } else {\n      diagnosis.overallStatus = 'ERROR'\n      diagnosis.summary = `❌ 发现${diagnosis.allIssues.length}个问题，需要修复`\n    }\n    \n    console.log('\\n📋 综合拼场诊断结果:')\n    console.log(`📊 总体状态: ${diagnosis.overallStatus}`)\n    console.log(`📊 发现问题: ${diagnosis.allIssues.length}个`)\n    console.log(`📊 修复建议: ${diagnosis.allRecommendations.length}个`)\n    console.log(`📊 总结: ${diagnosis.summary}`)\n    \n    if (diagnosis.allIssues.length > 0) {\n      console.log('\\n❌ 发现的问题:')\n      diagnosis.allIssues.forEach((issue, index) => {\n        console.log(`   ${index + 1}. ${issue}`)\n      })\n    }\n    \n    if (diagnosis.allRecommendations.length > 0) {\n      console.log('\\n💡 修复建议:')\n      diagnosis.allRecommendations.forEach((rec, index) => {\n        console.log(`   ${index + 1}. ${rec}`)\n      })\n    }\n    \n  } catch (error) {\n    console.error('❌ 综合拼场诊断失败:', error)\n    diagnosis.overallStatus = 'ERROR'\n    diagnosis.summary = `❌ 诊断过程出错: ${error.message}`\n  }\n  \n  return diagnosis\n}\n\n// 拼场问题快速修复\nexport function quickSharingFix(selectedSlots, venue, bookingForm, venueId, date) {\n  console.log('🔧 拼场问题快速修复')\n  \n  const fix = {\n    timestamp: new Date().toISOString(),\n    originalData: {\n      selectedSlots: selectedSlots,\n      venue: venue,\n      bookingForm: bookingForm\n    },\n    fixedData: null,\n    appliedFixes: [],\n    success: false\n  }\n  \n  try {\n    // 修复价格计算\n    let totalPrice = 0\n    selectedSlots.forEach(slot => {\n      let slotPrice = 0\n      \n      if (slot.price && slot.price > 0) {\n        slotPrice = parseFloat(slot.price)\n      } else if (venue && venue.price && venue.price > 0) {\n        slotPrice = parseFloat(venue.price) / 2\n        fix.appliedFixes.push(`修复时间段${slot.startTime}-${slot.endTime}价格`)\n      } else {\n        slotPrice = 60\n        fix.appliedFixes.push(`设置时间段${slot.startTime}-${slot.endTime}默认价格`)\n      }\n      \n      totalPrice += slotPrice\n    })\n    \n    const pricePerTeam = totalPrice / 2\n    \n    // 构建修复后的预约数据\n    fix.fixedData = {\n      venueId: parseInt(venueId),\n      date: date,\n      startTime: selectedSlots[0].startTime,\n      endTime: selectedSlots[selectedSlots.length - 1].endTime,\n      teamName: bookingForm.teamName || '',\n      contactInfo: bookingForm.contactInfo || '',\n      maxParticipants: 2,\n      description: bookingForm.description || '',\n      price: pricePerTeam, // 每队支付的价格\n      totalOriginalPrice: totalPrice, // 原始总价\n      slotIds: selectedSlots.map(slot => slot.id)\n    }\n    \n    // 验证修复结果\n    if (fix.fixedData.price > 0 && \n        fix.fixedData.teamName.trim() !== '' && \n        fix.fixedData.contactInfo.trim() !== '') {\n      fix.success = true\n      fix.appliedFixes.push('数据验证通过')\n    } else {\n      fix.appliedFixes.push('数据验证失败')\n    }\n    \n    console.log('🔧 拼场快速修复结果:', fix)\n    \n  } catch (error) {\n    console.error('❌ 拼场快速修复失败:', error)\n    fix.appliedFixes.push(`修复失败: ${error.message}`)\n  }\n  \n  return fix\n}\n\n// 导出所有功能\nexport default {\n  diagnoseSharingPriceCalculation,\n  diagnoseSharingDataStructure,\n  diagnoseSharingApiPath,\n  diagnoseSharingTimeSlotSync,\n  comprehensiveSharingDiagnosis,\n  quickSharingFix\n}\n"], "names": ["uni"], "mappings": ";;;AAMO,SAAS,gCAAgC,eAAe,OAAO,aAAa;AACjFA,gBAAAA,MAAA,MAAA,OAAA,uCAAY,aAAa;AAEzB,QAAM,YAAY;AAAA,IAChB,YAAW,oBAAI,KAAM,GAAC,YAAa;AAAA,IACnC;AAAA,IACA;AAAA,IACA;AAAA,IACA,kBAAkB;AAAA,MAChB,oBAAoB;AAAA,MACpB,cAAc;AAAA,MACd,gBAAgB;AAAA,MAChB,oBAAoB;AAAA,IACrB;AAAA,IACD,QAAQ,CAAE;AAAA,IACV,iBAAiB,CAAE;AAAA,EACpB;AAED,MAAI;AAEF,QAAI,aAAa;AACjB,kBAAc,QAAQ,CAAC,MAAM,UAAU;AACrC,UAAI,YAAY;AAEhB,UAAI,KAAK,SAAS,KAAK,QAAQ,GAAG;AAChC,oBAAY,WAAW,KAAK,KAAK;AAAA,MACzC,WAAiB,SAAS,MAAM,SAAS,MAAM,QAAQ,GAAG;AAClD,oBAAY,WAAW,MAAM,KAAK,IAAI;AAAA,MAC9C,OAAa;AACL,oBAAY;AACZ,kBAAU,OAAO,KAAK,MAAM,QAAQ,CAAC,QAAQ;AAAA,MAC9C;AAED,oBAAc;AACdA,+EAAY,WAAW,QAAQ,CAAC,KAAK,KAAK,SAAS,IAAI,KAAK,OAAO,OAAO,SAAS,EAAE;AAAA,IAC3F,CAAK;AAED,cAAU,iBAAiB,qBAAqB;AAChD,cAAU,iBAAiB,eAAe,aAAa;AACvD,cAAU,iBAAiB,iBAAiB,aAAa;AAEzDA,kBAAA,MAAA,MAAA,OAAA,wCAAY,gBAAgB,UAAU,gBAAgB;AAGtD,QAAI,cAAc,GAAG;AACnB,gBAAU,OAAO,KAAK,UAAU;AAAA,IACjC;AAED,QAAI,UAAU,iBAAiB,eAAe,IAAI;AAChD,gBAAU,OAAO,KAAK,eAAe;AAAA,IACtC;AAED,QAAI,UAAU,iBAAiB,eAAe,KAAK;AACjD,gBAAU,OAAO,KAAK,gBAAgB;AAAA,IACvC;AAGD,QAAI,UAAU,OAAO,WAAW,GAAG;AACjC,gBAAU,gBAAgB,KAAK,QAAQ;AAAA,IAC7C,OAAW;AACL,gBAAU,gBAAgB,KAAK,YAAY;AAC3C,gBAAU,gBAAgB,KAAK,aAAa;AAAA,IAC7C;AAAA,EAEF,SAAQ,OAAO;AACdA,kBAAAA,MAAc,MAAA,SAAA,wCAAA,iBAAiB,KAAK;AACpC,cAAU,OAAO,KAAK,WAAW,MAAM,OAAO,EAAE;AAAA,EACjD;AAEDA,gBAAAA,MAAY,MAAA,OAAA,wCAAA,gBAAgB,SAAS;AACrC,SAAO;AACT;AAGO,SAAS,6BAA6B,aAAa;AACxDA,gBAAAA,MAAA,MAAA,OAAA,wCAAY,aAAa;AAEzB,QAAM,YAAY;AAAA,IAChB,YAAW,oBAAI,KAAM,GAAC,YAAa;AAAA,IACnC;AAAA,IACA,gBAAgB;AAAA,MACd,SAAS;AAAA,MACT,MAAM;AAAA,MACN,WAAW;AAAA,MACX,UAAU;AAAA,MACV,aAAa;AAAA,MACb,iBAAiB;AAAA,MACjB,OAAO;AAAA,IACR;AAAA,IACD,WAAW,CAAE;AAAA,IACb,QAAQ,CAAE;AAAA,IACV,iBAAiB,CAAE;AAAA,EACpB;AAED,MAAI;AAEF,UAAM,qBAAqB,OAAO,KAAK,UAAU,cAAc;AAE/D,uBAAmB,QAAQ,WAAS;AAClC,YAAM,WAAW,YAAY,eAAe,KAAK,KACjC,YAAY,KAAK,MAAM,QACvB,YAAY,KAAK,MAAM;AAEvC,gBAAU,eAAe,KAAK,IAAI;AAClC,gBAAU,UAAU,KAAK,IAAI;AAAA,QAC3B,OAAO,YAAY,KAAK;AAAA,QACxB,MAAM,OAAO,YAAY,KAAK;AAAA,QAC9B,UAAU;AAAA,MACX;AAED,UAAI,CAAC,UAAU;AACb,kBAAU,OAAO,KAAK,WAAW,KAAK,EAAE;AAAA,MACzC;AAAA,IACP,CAAK;AAGD,QAAI,YAAY,WAAW,OAAO,YAAY,YAAY,UAAU;AAClE,gBAAU,OAAO,KAAK,gBAAgB;AAAA,IACvC;AAED,QAAI,YAAY,SAAS,OAAO,YAAY,UAAU,UAAU;AAC9D,gBAAU,OAAO,KAAK,cAAc;AAAA,IACrC;AAED,QAAI,YAAY,mBAAmB,YAAY,oBAAoB,GAAG;AACpE,gBAAU,OAAO,KAAK,yBAAyB;AAAA,IAChD;AAGD,QAAI,CAAC,YAAY,YAAY,YAAY,SAAS,KAAM,MAAK,IAAI;AAC/D,gBAAU,OAAO,KAAK,YAAY;AAAA,IACnC;AAED,QAAI,CAAC,YAAY,eAAe,YAAY,YAAY,KAAM,MAAK,IAAI;AACrE,gBAAU,OAAO,KAAK,YAAY;AAAA,IACnC;AAGD,QAAI,UAAU,OAAO,WAAW,GAAG;AACjC,gBAAU,gBAAgB,KAAK,QAAQ;AAAA,IAC7C,OAAW;AACL,gBAAU,gBAAgB,KAAK,WAAW;AAC1C,gBAAU,gBAAgB,KAAK,YAAY;AAAA,IAC5C;AAAA,EAEF,SAAQ,OAAO;AACdA,kBAAAA,MAAc,MAAA,SAAA,yCAAA,iBAAiB,KAAK;AACpC,cAAU,OAAO,KAAK,WAAW,MAAM,OAAO,EAAE;AAAA,EACjD;AAEDA,gBAAAA,MAAA,MAAA,OAAA,yCAAY,kBAAkB,SAAS;AACvC,SAAO;AACT;AAGO,SAAS,yBAAyB;AACvCA,gBAAAA,MAAY,MAAA,OAAA,yCAAA,cAAc;AAE1B,QAAM,YAAY;AAAA,IAChB,YAAW,oBAAI,KAAM,GAAC,YAAa;AAAA,IACnC,eAAe;AAAA,MACb,qBAAqB;AAAA,MACrB,oBAAoB;AAAA,MACpB,mBAAmB;AAAA,MACnB,kBAAkB;AAAA,IACnB;AAAA,IACD,iBAAiB,CAAE;AAAA,IACnB,eAAe,CAAE;AAAA,EAClB;AAGD,YAAU,gBAAgB,KAAK,+BAA+B;AAC9D,YAAU,gBAAgB,KAAK,yCAAyC;AACxE,YAAU,gBAAgB,KAAK,cAAc;AAG7C,YAAU,gBAAgB;AAAA,IACxB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACD;AAEDA,gBAAAA,MAAA,MAAA,OAAA,yCAAY,mBAAmB,SAAS;AACxC,SAAO;AACT;AAGO,eAAe,4BAA4B,SAAS,MAAM,eAAe;AAC9EA,gBAAAA,MAAY,MAAA,OAAA,yCAAA,cAAc;AAE1B,QAAM,YAAY;AAAA,IAChB,YAAW,oBAAI,KAAM,GAAC,YAAa;AAAA,IACnC;AAAA,IACA;AAAA,IACA;AAAA,IACA,YAAY;AAAA,MACV,eAAe,cAAc;AAAA,MAC7B,cAAc;AAAA,MACd,eAAe;AAAA,MACf,WAAW;AAAA,IACZ;AAAA,IACD,QAAQ,CAAE;AAAA,IACV,iBAAiB,CAAE;AAAA,EACpB;AAED,MAAI;AAEF,UAAM,mBAAmB;AACzB,cAAU,WAAW,gBAAgB,cAAc;AAAA,MAAK,UACtD,iBAAiB,KAAK,KAAK,EAAE;AAAA,IAC9B;AAED,QAAI,UAAU,WAAW,eAAe;AACtC,gBAAU,OAAO,KAAK,gBAAgB;AACtC,gBAAU,WAAW,YAAY;AAAA,IAClC;AAID,cAAU,gBAAgB,KAAK,mBAAmB;AAClD,cAAU,gBAAgB,KAAK,iBAAiB;AAEhD,QAAI,UAAU,WAAW,WAAW;AAClC,gBAAU,gBAAgB,KAAK,mBAAmB;AAAA,IACnD;AAAA,EAEF,SAAQ,OAAO;AACdA,kBAAAA,MAAc,MAAA,SAAA,yCAAA,kBAAkB,KAAK;AACrC,cAAU,OAAO,KAAK,WAAW,MAAM,OAAO,EAAE;AAAA,EACjD;AAEDA,gBAAAA,MAAA,MAAA,OAAA,yCAAY,mBAAmB,SAAS;AACxC,SAAO;AACT;AAGO,eAAe,8BAA8B,eAAe,OAAO,aAAa,SAAS,MAAM;;AACpGA,gBAAAA,MAAY,MAAA,OAAA,yCAAA,eAAe;AAE3B,QAAM,YAAY;AAAA,IAChB,YAAW,oBAAI,KAAM,GAAC,YAAa;AAAA,IACnC,eAAe;AAAA,IACf,2BAA2B;AAAA,IAC3B,wBAAwB;AAAA,IACxB,kBAAkB;AAAA,IAClB,uBAAuB;AAAA,IACvB,WAAW,CAAE;AAAA,IACb,oBAAoB,CAAE;AAAA,IACtB,SAAS;AAAA,EACV;AAED,MAAI;AAEFA,kBAAAA,MAAY,MAAA,OAAA,yCAAA,oBAAoB;AAChC,cAAU,4BAA4B,gCAAgC,eAAe,OAAO,WAAW;AAGvG,UAAM,kBAAkB;AAAA,MACtB,SAAS,SAAS,OAAO;AAAA,MACzB;AAAA,MACA,YAAW,mBAAc,CAAC,MAAf,mBAAkB;AAAA,MAC7B,UAAU,YAAY;AAAA,MACtB,aAAa,YAAY;AAAA,MACzB,iBAAiB;AAAA,MACjB,OAAO,UAAU,0BAA0B,iBAAiB;AAAA,MAC5D,aAAa,YAAY;AAAA,IAC1B;AAEDA,kBAAAA,MAAY,MAAA,OAAA,yCAAA,oBAAoB;AAChC,cAAU,yBAAyB,6BAA6B,eAAe;AAG/EA,kBAAAA,MAAY,MAAA,OAAA,yCAAA,qBAAqB;AACjC,cAAU,mBAAmB,uBAAwB;AAGrDA,kBAAAA,MAAY,MAAA,OAAA,yCAAA,qBAAqB;AACjC,cAAU,wBAAwB,MAAM,4BAA4B,SAAS,MAAM,aAAa;AAGhG,UAAM,eAAe;AAAA,MACnB,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,IACX;AAED,iBAAa,QAAQ,OAAK;AACxB,UAAI,KAAK,EAAE,QAAQ;AACjB,kBAAU,UAAU,KAAK,GAAG,EAAE,MAAM;AAAA,MACrC;AACD,UAAI,KAAK,EAAE,iBAAiB;AAC1B,kBAAU,mBAAmB,KAAK,GAAG,EAAE,eAAe;AAAA,MACvD;AAAA,IACP,CAAK;AAGD,QAAI,UAAU,oBAAoB,UAAU,iBAAiB,iBAAiB;AAC5E,gBAAU,mBAAmB,KAAK,GAAG,UAAU,iBAAiB,eAAe;AAAA,IAChF;AAGD,QAAI,UAAU,UAAU,WAAW,GAAG;AACpC,gBAAU,gBAAgB;AAC1B,gBAAU,UAAU;AAAA,IACrB,WAAU,UAAU,UAAU,UAAU,GAAG;AAC1C,gBAAU,gBAAgB;AAC1B,gBAAU,UAAU,QAAQ,UAAU,UAAU,MAAM;AAAA,IAC5D,OAAW;AACL,gBAAU,gBAAgB;AAC1B,gBAAU,UAAU,OAAO,UAAU,UAAU,MAAM;AAAA,IACtD;AAEDA,kBAAAA,MAAA,MAAA,OAAA,yCAAY,gBAAgB;AAC5BA,8EAAY,YAAY,UAAU,aAAa,EAAE;AACjDA,wBAAA,MAAA,OAAA,yCAAY,YAAY,UAAU,UAAU,MAAM,GAAG;AACrDA,wBAAY,MAAA,OAAA,yCAAA,YAAY,UAAU,mBAAmB,MAAM,GAAG;AAC9DA,wBAAA,MAAA,OAAA,yCAAY,UAAU,UAAU,OAAO,EAAE;AAEzC,QAAI,UAAU,UAAU,SAAS,GAAG;AAClCA,oBAAAA,MAAA,MAAA,OAAA,yCAAY,YAAY;AACxB,gBAAU,UAAU,QAAQ,CAAC,OAAO,UAAU;AAC5CA,sBAAAA,MAAY,MAAA,OAAA,yCAAA,MAAM,QAAQ,CAAC,KAAK,KAAK,EAAE;AAAA,MAC/C,CAAO;AAAA,IACF;AAED,QAAI,UAAU,mBAAmB,SAAS,GAAG;AAC3CA,oBAAAA,MAAA,MAAA,OAAA,yCAAY,YAAY;AACxB,gBAAU,mBAAmB,QAAQ,CAAC,KAAK,UAAU;AACnDA,sBAAAA,MAAY,MAAA,OAAA,yCAAA,MAAM,QAAQ,CAAC,KAAK,GAAG,EAAE;AAAA,MAC7C,CAAO;AAAA,IACF;AAAA,EAEF,SAAQ,OAAO;AACdA,kBAAAA,MAAc,MAAA,SAAA,yCAAA,eAAe,KAAK;AAClC,cAAU,gBAAgB;AAC1B,cAAU,UAAU,aAAa,MAAM,OAAO;AAAA,EAC/C;AAED,SAAO;AACT;AAGO,SAAS,gBAAgB,eAAe,OAAO,aAAa,SAAS,MAAM;AAChFA,gBAAAA,MAAA,MAAA,OAAA,yCAAY,aAAa;AAEzB,QAAM,MAAM;AAAA,IACV,YAAW,oBAAI,KAAM,GAAC,YAAa;AAAA,IACnC,cAAc;AAAA,MACZ;AAAA,MACA;AAAA,MACA;AAAA,IACD;AAAA,IACD,WAAW;AAAA,IACX,cAAc,CAAE;AAAA,IAChB,SAAS;AAAA,EACV;AAED,MAAI;AAEF,QAAI,aAAa;AACjB,kBAAc,QAAQ,UAAQ;AAC5B,UAAI,YAAY;AAEhB,UAAI,KAAK,SAAS,KAAK,QAAQ,GAAG;AAChC,oBAAY,WAAW,KAAK,KAAK;AAAA,MACzC,WAAiB,SAAS,MAAM,SAAS,MAAM,QAAQ,GAAG;AAClD,oBAAY,WAAW,MAAM,KAAK,IAAI;AACtC,YAAI,aAAa,KAAK,QAAQ,KAAK,SAAS,IAAI,KAAK,OAAO,IAAI;AAAA,MACxE,OAAa;AACL,oBAAY;AACZ,YAAI,aAAa,KAAK,QAAQ,KAAK,SAAS,IAAI,KAAK,OAAO,MAAM;AAAA,MACnE;AAED,oBAAc;AAAA,IACpB,CAAK;AAED,UAAM,eAAe,aAAa;AAGlC,QAAI,YAAY;AAAA,MACd,SAAS,SAAS,OAAO;AAAA,MACzB;AAAA,MACA,WAAW,cAAc,CAAC,EAAE;AAAA,MAC5B,SAAS,cAAc,cAAc,SAAS,CAAC,EAAE;AAAA,MACjD,UAAU,YAAY,YAAY;AAAA,MAClC,aAAa,YAAY,eAAe;AAAA,MACxC,iBAAiB;AAAA,MACjB,aAAa,YAAY,eAAe;AAAA,MACxC,OAAO;AAAA;AAAA,MACP,oBAAoB;AAAA;AAAA,MACpB,SAAS,cAAc,IAAI,UAAQ,KAAK,EAAE;AAAA,IAC3C;AAGD,QAAI,IAAI,UAAU,QAAQ,KACtB,IAAI,UAAU,SAAS,KAAM,MAAK,MAClC,IAAI,UAAU,YAAY,KAAI,MAAO,IAAI;AAC3C,UAAI,UAAU;AACd,UAAI,aAAa,KAAK,QAAQ;AAAA,IACpC,OAAW;AACL,UAAI,aAAa,KAAK,QAAQ;AAAA,IAC/B;AAEDA,kBAAAA,MAAA,MAAA,OAAA,yCAAY,gBAAgB,GAAG;AAAA,EAEhC,SAAQ,OAAO;AACdA,kBAAAA,MAAc,MAAA,SAAA,yCAAA,eAAe,KAAK;AAClC,QAAI,aAAa,KAAK,SAAS,MAAM,OAAO,EAAE;AAAA,EAC/C;AAED,SAAO;AACT;AAGA,MAAe,sBAAA;AAAA,EACb;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;;;;;;;;"}