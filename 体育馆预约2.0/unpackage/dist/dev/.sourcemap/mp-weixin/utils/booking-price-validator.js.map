{"version": 3, "file": "booking-price-validator.js", "sources": ["utils/booking-price-validator.js"], "sourcesContent": ["/**\n * 预约价格验证工具\n * 用于验证预约创建时的价格计算和数据传递\n */\n\n// 验证时间段价格计算\nexport function validateSlotPricing(slots, venue) {\n  console.log('🔍 开始验证时间段价格计算...')\n  \n  const results = {\n    totalSlots: slots.length,\n    validSlots: 0,\n    invalidSlots: 0,\n    totalPrice: 0,\n    details: [],\n    issues: []\n  }\n  \n  slots.forEach((slot, index) => {\n    const slotResult = {\n      index: index + 1,\n      timeRange: `${slot.startTime}-${slot.endTime}`,\n      slotPrice: slot.price,\n      slotPricePerHour: slot.pricePerHour,\n      venuePrice: venue?.price,\n      calculatedPrice: 0,\n      method: '',\n      valid: false\n    }\n    \n    // 计算价格逻辑（与前端保持一致）\n    if (slot.price && slot.price > 0) {\n      slotResult.calculatedPrice = parseFloat(slot.price)\n      slotResult.method = 'slot.price'\n      slotResult.valid = true\n    } else if (slot.pricePerHour && slot.pricePerHour > 0) {\n      slotResult.calculatedPrice = parseFloat(slot.pricePerHour)\n      slotResult.method = 'slot.pricePerHour'\n      slotResult.valid = true\n    } else if (venue?.price && venue.price > 0) {\n      slotResult.calculatedPrice = parseFloat(venue.price) / 2 // 半小时价格\n      slotResult.method = 'venue.price/2'\n      slotResult.valid = true\n    } else {\n      slotResult.calculatedPrice = 60 // 默认价格\n      slotResult.method = 'default'\n      slotResult.valid = false\n      results.issues.push(`时间段${index + 1}使用默认价格`)\n    }\n    \n    if (slotResult.valid) {\n      results.validSlots++\n    } else {\n      results.invalidSlots++\n    }\n    \n    results.totalPrice += slotResult.calculatedPrice\n    results.details.push(slotResult)\n    \n    console.log(`💰 时间段${index + 1}: ${slotResult.timeRange} = ¥${slotResult.calculatedPrice} (${slotResult.method})`)\n  })\n  \n  console.log(`📊 价格验证结果: 总计¥${results.totalPrice}, 有效${results.validSlots}个, 无效${results.invalidSlots}个`)\n  \n  return results\n}\n\n// 验证预约数据完整性\nexport function validateBookingData(bookingData) {\n  console.log('🔍 开始验证预约数据完整性...')\n  \n  const validation = {\n    valid: true,\n    errors: [],\n    warnings: [],\n    data: bookingData\n  }\n  \n  // 必需字段检查\n  const requiredFields = ['venueId', 'date', 'startTime', 'endTime', 'price']\n  \n  requiredFields.forEach(field => {\n    if (!bookingData[field]) {\n      validation.errors.push(`缺少必需字段: ${field}`)\n      validation.valid = false\n    }\n  })\n  \n  // 价格检查\n  if (bookingData.price !== undefined) {\n    const price = parseFloat(bookingData.price)\n    if (isNaN(price) || price <= 0) {\n      validation.errors.push(`价格无效: ${bookingData.price}`)\n      validation.valid = false\n    } else if (price < 50) {\n      validation.warnings.push(`价格可能过低: ¥${price}`)\n    } else if (price > 1000) {\n      validation.warnings.push(`价格可能过高: ¥${price}`)\n    }\n  }\n  \n  // 时间格式检查\n  if (bookingData.startTime && bookingData.endTime) {\n    const timeRegex = /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/\n    \n    if (!timeRegex.test(bookingData.startTime)) {\n      validation.errors.push(`开始时间格式错误: ${bookingData.startTime}`)\n      validation.valid = false\n    }\n    \n    if (!timeRegex.test(bookingData.endTime)) {\n      validation.errors.push(`结束时间格式错误: ${bookingData.endTime}`)\n      validation.valid = false\n    }\n    \n    // 时间逻辑检查\n    if (timeRegex.test(bookingData.startTime) && timeRegex.test(bookingData.endTime)) {\n      const [startHour, startMin] = bookingData.startTime.split(':').map(Number)\n      const [endHour, endMin] = bookingData.endTime.split(':').map(Number)\n      \n      const startMinutes = startHour * 60 + startMin\n      const endMinutes = endHour * 60 + endMin\n      \n      if (endMinutes <= startMinutes) {\n        validation.errors.push('结束时间必须晚于开始时间')\n        validation.valid = false\n      }\n    }\n  }\n  \n  // 日期格式检查\n  if (bookingData.date) {\n    const dateRegex = /^\\d{4}-\\d{2}-\\d{2}$/\n    if (!dateRegex.test(bookingData.date)) {\n      validation.errors.push(`日期格式错误: ${bookingData.date}`)\n      validation.valid = false\n    }\n  }\n  \n  console.log(`✅ 数据验证${validation.valid ? '通过' : '失败'}`)\n  if (validation.errors.length > 0) {\n    console.log('❌ 错误:', validation.errors)\n  }\n  if (validation.warnings.length > 0) {\n    console.log('⚠️ 警告:', validation.warnings)\n  }\n  \n  return validation\n}\n\n// 模拟后端价格计算\nexport function simulateBackendPricing(bookingData, venue) {\n  console.log('🔍 模拟后端价格计算...')\n  \n  const frontendPrice = parseFloat(bookingData.price) || 0\n  const venuePrice = parseFloat(venue?.price) || 120 // 默认120元/小时\n  \n  // 模拟后端逻辑\n  const finalPrice = frontendPrice > 0 ? frontendPrice : venuePrice\n  \n  console.log(`💰 前端价格: ¥${frontendPrice}`)\n  console.log(`💰 场馆价格: ¥${venuePrice}`)\n  console.log(`💰 后端最终价格: ¥${finalPrice}`)\n  \n  return {\n    frontendPrice,\n    venuePrice,\n    finalPrice,\n    usedFrontendPrice: frontendPrice > 0\n  }\n}\n\n// 综合验证函数\nexport function validateCompleteBookingFlow(slots, venue, bookingForm) {\n  console.log('🚀 开始完整预约流程验证...')\n  \n  const results = {\n    slotPricing: null,\n    bookingData: null,\n    backendSimulation: null,\n    overall: false\n  }\n  \n  try {\n    // 1. 验证时间段价格\n    results.slotPricing = validateSlotPricing(slots, venue)\n    \n    // 2. 构建预约数据\n    const firstSlot = slots[0]\n    const lastSlot = slots[slots.length - 1]\n    \n    const mockBookingData = {\n      venueId: venue?.id || 25,\n      date: '2025-07-19',\n      startTime: firstSlot?.startTime || '18:00',\n      endTime: lastSlot?.endTime || '20:00',\n      slotIds: slots.map(slot => slot.id),\n      bookingType: bookingForm?.bookingType || 'EXCLUSIVE',\n      description: bookingForm?.description || '',\n      price: results.slotPricing.totalPrice\n    }\n    \n    // 3. 验证预约数据\n    results.bookingData = validateBookingData(mockBookingData)\n    \n    // 4. 模拟后端处理\n    results.backendSimulation = simulateBackendPricing(mockBookingData, venue)\n    \n    // 5. 综合评估\n    results.overall = results.slotPricing.invalidSlots === 0 && \n                     results.bookingData.valid && \n                     results.backendSimulation.finalPrice > 0\n    \n    console.log(`🎯 综合验证${results.overall ? '通过' : '失败'}`)\n    \n  } catch (error) {\n    console.error('❌ 验证过程出错:', error)\n    results.error = error.message\n  }\n  \n  return results\n}\n\n// 快速价格检查\nexport function quickPriceCheck(price) {\n  const numPrice = parseFloat(price)\n  \n  if (isNaN(numPrice)) {\n    return { valid: false, message: '价格不是有效数字' }\n  }\n  \n  if (numPrice <= 0) {\n    return { valid: false, message: '价格必须大于0' }\n  }\n  \n  if (numPrice < 30) {\n    return { valid: true, message: '价格偏低，请确认', level: 'warning' }\n  }\n  \n  if (numPrice > 500) {\n    return { valid: true, message: '价格偏高，请确认', level: 'warning' }\n  }\n  \n  return { valid: true, message: '价格正常', level: 'success' }\n}\n"], "names": ["uni"], "mappings": ";;AAoEO,SAAS,oBAAoB,aAAa;AAC/CA,gBAAAA,6DAAY,mBAAmB;AAE/B,QAAM,aAAa;AAAA,IACjB,OAAO;AAAA,IACP,QAAQ,CAAE;AAAA,IACV,UAAU,CAAE;AAAA,IACZ,MAAM;AAAA,EACP;AAGD,QAAM,iBAAiB,CAAC,WAAW,QAAQ,aAAa,WAAW,OAAO;AAE1E,iBAAe,QAAQ,WAAS;AAC9B,QAAI,CAAC,YAAY,KAAK,GAAG;AACvB,iBAAW,OAAO,KAAK,WAAW,KAAK,EAAE;AACzC,iBAAW,QAAQ;AAAA,IACpB;AAAA,EACL,CAAG;AAGD,MAAI,YAAY,UAAU,QAAW;AACnC,UAAM,QAAQ,WAAW,YAAY,KAAK;AAC1C,QAAI,MAAM,KAAK,KAAK,SAAS,GAAG;AAC9B,iBAAW,OAAO,KAAK,SAAS,YAAY,KAAK,EAAE;AACnD,iBAAW,QAAQ;AAAA,IACzB,WAAe,QAAQ,IAAI;AACrB,iBAAW,SAAS,KAAK,YAAY,KAAK,EAAE;AAAA,IAClD,WAAe,QAAQ,KAAM;AACvB,iBAAW,SAAS,KAAK,YAAY,KAAK,EAAE;AAAA,IAC7C;AAAA,EACF;AAGD,MAAI,YAAY,aAAa,YAAY,SAAS;AAChD,UAAM,YAAY;AAElB,QAAI,CAAC,UAAU,KAAK,YAAY,SAAS,GAAG;AAC1C,iBAAW,OAAO,KAAK,aAAa,YAAY,SAAS,EAAE;AAC3D,iBAAW,QAAQ;AAAA,IACpB;AAED,QAAI,CAAC,UAAU,KAAK,YAAY,OAAO,GAAG;AACxC,iBAAW,OAAO,KAAK,aAAa,YAAY,OAAO,EAAE;AACzD,iBAAW,QAAQ;AAAA,IACpB;AAGD,QAAI,UAAU,KAAK,YAAY,SAAS,KAAK,UAAU,KAAK,YAAY,OAAO,GAAG;AAChF,YAAM,CAAC,WAAW,QAAQ,IAAI,YAAY,UAAU,MAAM,GAAG,EAAE,IAAI,MAAM;AACzE,YAAM,CAAC,SAAS,MAAM,IAAI,YAAY,QAAQ,MAAM,GAAG,EAAE,IAAI,MAAM;AAEnE,YAAM,eAAe,YAAY,KAAK;AACtC,YAAM,aAAa,UAAU,KAAK;AAElC,UAAI,cAAc,cAAc;AAC9B,mBAAW,OAAO,KAAK,cAAc;AACrC,mBAAW,QAAQ;AAAA,MACpB;AAAA,IACF;AAAA,EACF;AAGD,MAAI,YAAY,MAAM;AACpB,UAAM,YAAY;AAClB,QAAI,CAAC,UAAU,KAAK,YAAY,IAAI,GAAG;AACrC,iBAAW,OAAO,KAAK,WAAW,YAAY,IAAI,EAAE;AACpD,iBAAW,QAAQ;AAAA,IACpB;AAAA,EACF;AAEDA,gBAAAA,MAAY,MAAA,OAAA,2CAAA,SAAS,WAAW,QAAQ,OAAO,IAAI,EAAE;AACrD,MAAI,WAAW,OAAO,SAAS,GAAG;AAChCA,kBAAA,MAAA,MAAA,OAAA,2CAAY,SAAS,WAAW,MAAM;AAAA,EACvC;AACD,MAAI,WAAW,SAAS,SAAS,GAAG;AAClCA,kBAAA,MAAA,MAAA,OAAA,2CAAY,UAAU,WAAW,QAAQ;AAAA,EAC1C;AAED,SAAO;AACT;AA4EO,SAAS,gBAAgB,OAAO;AACrC,QAAM,WAAW,WAAW,KAAK;AAEjC,MAAI,MAAM,QAAQ,GAAG;AACnB,WAAO,EAAE,OAAO,OAAO,SAAS,WAAY;AAAA,EAC7C;AAED,MAAI,YAAY,GAAG;AACjB,WAAO,EAAE,OAAO,OAAO,SAAS,UAAW;AAAA,EAC5C;AAED,MAAI,WAAW,IAAI;AACjB,WAAO,EAAE,OAAO,MAAM,SAAS,YAAY,OAAO,UAAW;AAAA,EAC9D;AAED,MAAI,WAAW,KAAK;AAClB,WAAO,EAAE,OAAO,MAAM,SAAS,YAAY,OAAO,UAAW;AAAA,EAC9D;AAED,SAAO,EAAE,OAAO,MAAM,SAAS,QAAQ,OAAO,UAAW;AAC3D;;;"}