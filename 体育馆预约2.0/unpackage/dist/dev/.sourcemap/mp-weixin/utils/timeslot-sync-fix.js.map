{"version": 3, "file": "timeslot-sync-fix.js", "sources": ["utils/timeslot-sync-fix.js"], "sourcesContent": ["/**\n * 时间段同步修复工具\n * 恢复前端生成时间段并同步到后端的功能\n */\n\nimport { generateTimeSlots } from '@/api/timeslot.js'\n\n// 修复时间段生成和同步逻辑\nexport async function fixTimeSlotGeneration(venueId, date, venueStore) {\n  console.log('🔧 修复时间段生成和同步逻辑')\n  \n  const fix = {\n    venueId: venueId,\n    date: date,\n    steps: [],\n    success: false,\n    generatedSlots: null,\n    syncedToBackend: false,\n    error: null\n  }\n  \n  try {\n    // 步骤1: 检查当前时间段状态\n    fix.steps.push('检查当前时间段状态')\n    const currentSlots = venueStore.timeSlots || []\n    console.log('📊 当前时间段数量:', currentSlots.length)\n    \n    // 步骤2: 尝试后端生成\n    fix.steps.push('尝试后端生成时间段')\n    try {\n      console.log('📡 调用后端生成API...')\n      const generateResponse = await generateTimeSlots(venueId, date)\n      console.log('✅ 后端生成API调用成功:', generateResponse)\n      \n      // 步骤3: 重新获取后端生成的时间段\n      fix.steps.push('获取后端生成的时间段')\n      const { getVenueTimeSlots } = await import('@/api/timeslot.js')\n      const fetchResponse = await getVenueTimeSlots(venueId, date, true)\n      \n      if (fetchResponse && fetchResponse.data && fetchResponse.data.length > 0) {\n        fix.generatedSlots = fetchResponse.data\n        fix.syncedToBackend = true\n        fix.success = true\n        \n        // 更新VenueStore\n        venueStore.setTimeSlots(fetchResponse.data)\n        console.log('✅ 后端生成并同步成功:', fetchResponse.data.length, '个时间段')\n        \n        fix.steps.push(`成功生成${fetchResponse.data.length}个时间段`)\n      } else {\n        console.warn('⚠️ 后端生成API成功但返回空数据')\n        throw new Error('后端生成返回空数据')\n      }\n      \n    } catch (backendError) {\n      console.warn('⚠️ 后端生成失败，使用前端生成:', backendError)\n      fix.steps.push('后端生成失败，使用前端生成')\n      \n      // 步骤4: 前端生成时间段\n      const frontendSlots = generateFrontendTimeSlots(venueId, date, venueStore.venueDetail)\n      fix.generatedSlots = frontendSlots\n      \n      // 更新VenueStore\n      venueStore.setTimeSlots(frontendSlots)\n      console.log('✅ 前端生成成功:', frontendSlots.length, '个时间段')\n      \n      // 步骤5: 尝试同步到后端（可选）\n      fix.steps.push('尝试同步前端生成的时间段到后端')\n      try {\n        await syncFrontendSlotsToBackend(frontendSlots, venueId, date)\n        fix.syncedToBackend = true\n        console.log('✅ 前端时间段同步到后端成功')\n        fix.steps.push('同步到后端成功')\n      } catch (syncError) {\n        console.warn('⚠️ 同步到后端失败:', syncError)\n        fix.steps.push('同步到后端失败，但前端可用')\n      }\n      \n      fix.success = true\n    }\n    \n  } catch (error) {\n    console.error('❌ 时间段生成修复失败:', error)\n    fix.error = error.message\n    fix.steps.push(`修复失败: ${error.message}`)\n  }\n  \n  console.log('🔧 时间段生成修复结果:', fix)\n  return fix\n}\n\n// 前端生成时间段\nfunction generateFrontendTimeSlots(venueId, date, venue) {\n  console.log('🏗️ 前端生成时间段')\n  \n  // 获取场馆价格信息\n  const venueHourPrice = venue?.price || 120 // 默认120元/小时\n  const venueHalfHourPrice = Math.round(venueHourPrice / 2) // 半小时价格\n  \n  console.log('💰 场馆小时价格:', venueHourPrice)\n  console.log('💰 场馆半小时价格:', venueHalfHourPrice)\n  \n  const slots = []\n  const startHour = 9\n  const endHour = 22\n  \n  // 生成半小时间隔的时间段\n  for (let hour = startHour; hour < endHour; hour++) {\n    for (let minute = 0; minute < 60; minute += 30) {\n      const startTime = `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`\n      const endMinute = minute + 30\n      const endHour = endMinute >= 60 ? hour + 1 : hour\n      const actualEndMinute = endMinute >= 60 ? 0 : endMinute\n      const endTime = `${endHour.toString().padStart(2, '0')}:${actualEndMinute.toString().padStart(2, '0')}`\n      \n      // 如果结束时间超过营业时间，跳出循环\n      if (endHour >= endHour && actualEndMinute > 0 && endHour >= endHour) {\n        break\n      }\n      \n      const slot = {\n        id: `default_${venueId}_${date}_${hour}_${minute}`,\n        venueId: parseInt(venueId),\n        date: date,\n        startTime: startTime,\n        endTime: endTime,\n        status: 'AVAILABLE',\n        price: venueHalfHourPrice,\n        isDefault: true // 标记为前端生成的默认时间段\n      }\n      \n      slots.push(slot)\n    }\n  }\n  \n  console.log('🏗️ 前端生成完成:', slots.length, '个时间段')\n  return slots\n}\n\n// 同步前端生成的时间段到后端\nasync function syncFrontendSlotsToBackend(slots, venueId, date) {\n  console.log('🔄 同步前端时间段到后端')\n  \n  // 这里可以实现批量创建时间段的API调用\n  // 由于当前后端只有生成API，我们先尝试调用生成API\n  try {\n    await generateTimeSlots(venueId, date)\n    console.log('✅ 通过后端生成API同步成功')\n  } catch (error) {\n    console.warn('⚠️ 后端同步失败:', error)\n    throw error\n  }\n}\n\n// 验证时间段数据完整性\nexport function validateTimeSlots(timeSlots) {\n  console.log('🔍 验证时间段数据完整性')\n  \n  const validation = {\n    totalSlots: timeSlots.length,\n    validSlots: 0,\n    invalidSlots: 0,\n    issues: [],\n    priceIssues: 0,\n    timeIssues: 0\n  }\n  \n  timeSlots.forEach((slot, index) => {\n    let isValid = true\n    \n    // 检查必需字段\n    if (!slot.id || !slot.startTime || !slot.endTime || !slot.status) {\n      validation.issues.push(`时间段${index + 1}缺少必需字段`)\n      isValid = false\n    }\n    \n    // 检查价格\n    if (!slot.price || slot.price <= 0) {\n      validation.priceIssues++\n      validation.issues.push(`时间段${index + 1}价格无效`)\n      isValid = false\n    }\n    \n    // 检查时间格式\n    const timeRegex = /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/\n    if (!timeRegex.test(slot.startTime) || !timeRegex.test(slot.endTime)) {\n      validation.timeIssues++\n      validation.issues.push(`时间段${index + 1}时间格式错误`)\n      isValid = false\n    }\n    \n    if (isValid) {\n      validation.validSlots++\n    } else {\n      validation.invalidSlots++\n    }\n  })\n  \n  console.log('🔍 时间段验证结果:', validation)\n  return validation\n}\n\n// 强制重新生成时间段\nexport async function forceRegenerateTimeSlots(venueId, date, venueStore) {\n  console.log('🔄 强制重新生成时间段')\n  \n  const regenerate = {\n    success: false,\n    method: 'unknown',\n    slotsCount: 0,\n    error: null\n  }\n  \n  try {\n    // 清除现有时间段\n    venueStore.clearTimeSlots()\n    \n    // 等待清除完成\n    await new Promise(resolve => setTimeout(resolve, 200))\n    \n    // 执行修复\n    const fixResult = await fixTimeSlotGeneration(venueId, date, venueStore)\n    \n    if (fixResult.success) {\n      regenerate.success = true\n      regenerate.method = fixResult.syncedToBackend ? 'backend' : 'frontend'\n      regenerate.slotsCount = fixResult.generatedSlots?.length || 0\n      console.log('✅ 强制重新生成成功')\n    } else {\n      regenerate.error = fixResult.error\n      console.error('❌ 强制重新生成失败')\n    }\n    \n  } catch (error) {\n    console.error('❌ 强制重新生成过程出错:', error)\n    regenerate.error = error.message\n  }\n  \n  return regenerate\n}\n\n// 检查时间段同步状态\nexport async function checkTimeSlotSyncStatus(venueId, date, venueStore) {\n  console.log('🔍 检查时间段同步状态')\n  \n  const status = {\n    frontendSlots: 0,\n    backendSlots: 0,\n    synced: false,\n    needsSync: false,\n    issues: []\n  }\n  \n  try {\n    // 检查前端时间段\n    const frontendSlots = venueStore.timeSlots || []\n    status.frontendSlots = frontendSlots.length\n    \n    // 检查后端时间段\n    const { getVenueTimeSlots } = await import('@/api/timeslot.js')\n    const backendResponse = await getVenueTimeSlots(venueId, date, true)\n    const backendSlots = backendResponse?.data || []\n    status.backendSlots = backendSlots.length\n    \n    // 判断同步状态\n    if (status.frontendSlots > 0 && status.backendSlots > 0) {\n      status.synced = true\n    } else if (status.frontendSlots > 0 && status.backendSlots === 0) {\n      status.needsSync = true\n      status.issues.push('前端有时间段但后端没有')\n    } else if (status.frontendSlots === 0 && status.backendSlots === 0) {\n      status.needsSync = true\n      status.issues.push('前后端都没有时间段数据')\n    }\n    \n  } catch (error) {\n    console.error('❌ 检查同步状态失败:', error)\n    status.issues.push(`检查失败: ${error.message}`)\n  }\n  \n  console.log('🔍 时间段同步状态:', status)\n  return status\n}\n\n// 自动修复时间段问题\nexport async function autoFixTimeSlotIssues(venueId, date, venueStore) {\n  console.log('🤖 自动修复时间段问题')\n  \n  const autoFix = {\n    steps: [],\n    success: false,\n    finalSlotsCount: 0\n  }\n  \n  try {\n    // 步骤1: 检查同步状态\n    autoFix.steps.push('检查同步状态')\n    const syncStatus = await checkTimeSlotSyncStatus(venueId, date, venueStore)\n    \n    // 步骤2: 根据状态决定修复策略\n    if (syncStatus.needsSync) {\n      autoFix.steps.push('需要同步，执行时间段生成修复')\n      const fixResult = await fixTimeSlotGeneration(venueId, date, venueStore)\n      \n      if (fixResult.success) {\n        autoFix.success = true\n        autoFix.finalSlotsCount = fixResult.generatedSlots?.length || 0\n        autoFix.steps.push(`修复成功，生成${autoFix.finalSlotsCount}个时间段`)\n      } else {\n        autoFix.steps.push('修复失败')\n      }\n    } else if (syncStatus.synced) {\n      autoFix.success = true\n      autoFix.finalSlotsCount = syncStatus.frontendSlots\n      autoFix.steps.push('时间段已同步，无需修复')\n    }\n    \n  } catch (error) {\n    console.error('❌ 自动修复失败:', error)\n    autoFix.steps.push(`自动修复失败: ${error.message}`)\n  }\n  \n  console.log('🤖 自动修复结果:', autoFix)\n  return autoFix\n}\n"], "names": ["uni", "generateTimeSlots", "endHour"], "mappings": ";;;;AAQO,eAAe,sBAAsB,SAAS,MAAM,YAAY;AACrEA,gBAAAA,MAAY,MAAA,OAAA,oCAAA,iBAAiB;AAE7B,QAAM,MAAM;AAAA,IACV;AAAA,IACA;AAAA,IACA,OAAO,CAAE;AAAA,IACT,SAAS;AAAA,IACT,gBAAgB;AAAA,IAChB,iBAAiB;AAAA,IACjB,OAAO;AAAA,EACR;AAED,MAAI;AAEF,QAAI,MAAM,KAAK,WAAW;AAC1B,UAAM,eAAe,WAAW,aAAa,CAAE;AAC/CA,kBAAY,MAAA,MAAA,OAAA,oCAAA,eAAe,aAAa,MAAM;AAG9C,QAAI,MAAM,KAAK,WAAW;AAC1B,QAAI;AACFA,oBAAAA,MAAY,MAAA,OAAA,oCAAA,iBAAiB;AAC7B,YAAM,mBAAmB,MAAMC,+BAAkB,SAAS,IAAI;AAC9DD,oBAAAA,MAAA,MAAA,OAAA,oCAAY,kBAAkB,gBAAgB;AAG9C,UAAI,MAAM,KAAK,YAAY;AAC3B,YAAM,EAAE,kBAAiB,IAAK,MAAa;AAC3C,YAAM,gBAAgB,MAAM,kBAAkB,SAAS,MAAM,IAAI;AAEjE,UAAI,iBAAiB,cAAc,QAAQ,cAAc,KAAK,SAAS,GAAG;AACxE,YAAI,iBAAiB,cAAc;AACnC,YAAI,kBAAkB;AACtB,YAAI,UAAU;AAGd,mBAAW,aAAa,cAAc,IAAI;AAC1CA,4BAAY,MAAA,OAAA,oCAAA,gBAAgB,cAAc,KAAK,QAAQ,MAAM;AAE7D,YAAI,MAAM,KAAK,OAAO,cAAc,KAAK,MAAM,MAAM;AAAA,MAC7D,OAAa;AACLA,sBAAAA,MAAA,MAAA,QAAA,oCAAa,oBAAoB;AACjC,cAAM,IAAI,MAAM,WAAW;AAAA,MAC5B;AAAA,IAEF,SAAQ,cAAc;AACrBA,oBAAAA,wDAAa,qBAAqB,YAAY;AAC9C,UAAI,MAAM,KAAK,eAAe;AAG9B,YAAM,gBAAgB,0BAA0B,SAAS,MAAM,WAAW,WAAW;AACrF,UAAI,iBAAiB;AAGrB,iBAAW,aAAa,aAAa;AACrCA,oBAAY,MAAA,MAAA,OAAA,oCAAA,aAAa,cAAc,QAAQ,MAAM;AAGrD,UAAI,MAAM,KAAK,iBAAiB;AAChC,UAAI;AACF,cAAM,2BAA2B,eAAe,SAAS,IAAI;AAC7D,YAAI,kBAAkB;AACtBA,sBAAAA,MAAA,MAAA,OAAA,oCAAY,gBAAgB;AAC5B,YAAI,MAAM,KAAK,SAAS;AAAA,MACzB,SAAQ,WAAW;AAClBA,sBAAAA,MAAa,MAAA,QAAA,oCAAA,eAAe,SAAS;AACrC,YAAI,MAAM,KAAK,eAAe;AAAA,MAC/B;AAED,UAAI,UAAU;AAAA,IACf;AAAA,EAEF,SAAQ,OAAO;AACdA,kBAAAA,MAAc,MAAA,SAAA,oCAAA,gBAAgB,KAAK;AACnC,QAAI,QAAQ,MAAM;AAClB,QAAI,MAAM,KAAK,SAAS,MAAM,OAAO,EAAE;AAAA,EACxC;AAEDA,gBAAAA,uDAAY,iBAAiB,GAAG;AAChC,SAAO;AACT;AAGA,SAAS,0BAA0B,SAAS,MAAM,OAAO;AACvDA,gBAAAA,MAAA,MAAA,OAAA,oCAAY,aAAa;AAGzB,QAAM,kBAAiB,+BAAO,UAAS;AACvC,QAAM,qBAAqB,KAAK,MAAM,iBAAiB,CAAC;AAExDA,gBAAAA,MAAA,MAAA,OAAA,qCAAY,cAAc,cAAc;AACxCA,gBAAAA,MAAA,MAAA,OAAA,qCAAY,eAAe,kBAAkB;AAE7C,QAAM,QAAQ,CAAE;AAChB,QAAM,YAAY;AAClB,QAAM,UAAU;AAGhB,WAAS,OAAO,WAAW,OAAO,SAAS,QAAQ;AACjD,aAAS,SAAS,GAAG,SAAS,IAAI,UAAU,IAAI;AAC9C,YAAM,YAAY,GAAG,KAAK,SAAU,EAAC,SAAS,GAAG,GAAG,CAAC,IAAI,OAAO,SAAU,EAAC,SAAS,GAAG,GAAG,CAAC;AAC3F,YAAM,YAAY,SAAS;AAC3B,YAAME,WAAU,aAAa,KAAK,OAAO,IAAI;AAC7C,YAAM,kBAAkB,aAAa,KAAK,IAAI;AAC9C,YAAM,UAAU,GAAGA,SAAQ,SAAU,EAAC,SAAS,GAAG,GAAG,CAAC,IAAI,gBAAgB,SAAU,EAAC,SAAS,GAAG,GAAG,CAAC;AAGrG,UAAIA,YAAWA,YAAW,kBAAkB,KAAKA,YAAWA,UAAS;AACnE;AAAA,MACD;AAED,YAAM,OAAO;AAAA,QACX,IAAI,WAAW,OAAO,IAAI,IAAI,IAAI,IAAI,IAAI,MAAM;AAAA,QAChD,SAAS,SAAS,OAAO;AAAA,QACzB;AAAA,QACA;AAAA,QACA;AAAA,QACA,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,WAAW;AAAA;AAAA,MACZ;AAED,YAAM,KAAK,IAAI;AAAA,IAChB;AAAA,EACF;AAEDF,gBAAY,MAAA,MAAA,OAAA,qCAAA,eAAe,MAAM,QAAQ,MAAM;AAC/C,SAAO;AACT;AAGA,eAAe,2BAA2B,OAAO,SAAS,MAAM;AAC9DA,gBAAAA,MAAY,MAAA,OAAA,qCAAA,eAAe;AAI3B,MAAI;AACF,UAAMC,aAAiB,kBAAC,SAAS,IAAI;AACrCD,kBAAAA,MAAA,MAAA,OAAA,qCAAY,iBAAiB;AAAA,EAC9B,SAAQ,OAAO;AACdA,kBAAAA,MAAA,MAAA,QAAA,qCAAa,cAAc,KAAK;AAChC,UAAM;AAAA,EACP;AACH;AAmDO,eAAe,yBAAyB,SAAS,MAAM,YAAY;;AACxEA,gBAAAA,MAAY,MAAA,OAAA,qCAAA,cAAc;AAE1B,QAAM,aAAa;AAAA,IACjB,SAAS;AAAA,IACT,QAAQ;AAAA,IACR,YAAY;AAAA,IACZ,OAAO;AAAA,EACR;AAED,MAAI;AAEF,eAAW,eAAgB;AAG3B,UAAM,IAAI,QAAQ,aAAW,WAAW,SAAS,GAAG,CAAC;AAGrD,UAAM,YAAY,MAAM,sBAAsB,SAAS,MAAM,UAAU;AAEvE,QAAI,UAAU,SAAS;AACrB,iBAAW,UAAU;AACrB,iBAAW,SAAS,UAAU,kBAAkB,YAAY;AAC5D,iBAAW,eAAa,eAAU,mBAAV,mBAA0B,WAAU;AAC5DA,oBAAAA,MAAA,MAAA,OAAA,qCAAY,YAAY;AAAA,IAC9B,OAAW;AACL,iBAAW,QAAQ,UAAU;AAC7BA,oBAAAA,MAAA,MAAA,SAAA,qCAAc,YAAY;AAAA,IAC3B;AAAA,EAEF,SAAQ,OAAO;AACdA,kBAAAA,MAAc,MAAA,SAAA,qCAAA,iBAAiB,KAAK;AACpC,eAAW,QAAQ,MAAM;AAAA,EAC1B;AAED,SAAO;AACT;AAGO,eAAe,wBAAwB,SAAS,MAAM,YAAY;AACvEA,gBAAAA,MAAY,MAAA,OAAA,qCAAA,cAAc;AAE1B,QAAM,SAAS;AAAA,IACb,eAAe;AAAA,IACf,cAAc;AAAA,IACd,QAAQ;AAAA,IACR,WAAW;AAAA,IACX,QAAQ,CAAE;AAAA,EACX;AAED,MAAI;AAEF,UAAM,gBAAgB,WAAW,aAAa,CAAE;AAChD,WAAO,gBAAgB,cAAc;AAGrC,UAAM,EAAE,kBAAiB,IAAK,MAAa;AAC3C,UAAM,kBAAkB,MAAM,kBAAkB,SAAS,MAAM,IAAI;AACnE,UAAM,gBAAe,mDAAiB,SAAQ,CAAE;AAChD,WAAO,eAAe,aAAa;AAGnC,QAAI,OAAO,gBAAgB,KAAK,OAAO,eAAe,GAAG;AACvD,aAAO,SAAS;AAAA,IACtB,WAAe,OAAO,gBAAgB,KAAK,OAAO,iBAAiB,GAAG;AAChE,aAAO,YAAY;AACnB,aAAO,OAAO,KAAK,aAAa;AAAA,IACtC,WAAe,OAAO,kBAAkB,KAAK,OAAO,iBAAiB,GAAG;AAClE,aAAO,YAAY;AACnB,aAAO,OAAO,KAAK,aAAa;AAAA,IACjC;AAAA,EAEF,SAAQ,OAAO;AACdA,kBAAAA,MAAc,MAAA,SAAA,qCAAA,eAAe,KAAK;AAClC,WAAO,OAAO,KAAK,SAAS,MAAM,OAAO,EAAE;AAAA,EAC5C;AAEDA,gBAAAA,wDAAY,eAAe,MAAM;AACjC,SAAO;AACT;AAGO,eAAe,sBAAsB,SAAS,MAAM,YAAY;;AACrEA,gBAAAA,MAAY,MAAA,OAAA,qCAAA,cAAc;AAE1B,QAAM,UAAU;AAAA,IACd,OAAO,CAAE;AAAA,IACT,SAAS;AAAA,IACT,iBAAiB;AAAA,EAClB;AAED,MAAI;AAEF,YAAQ,MAAM,KAAK,QAAQ;AAC3B,UAAM,aAAa,MAAM,wBAAwB,SAAS,MAAM,UAAU;AAG1E,QAAI,WAAW,WAAW;AACxB,cAAQ,MAAM,KAAK,gBAAgB;AACnC,YAAM,YAAY,MAAM,sBAAsB,SAAS,MAAM,UAAU;AAEvE,UAAI,UAAU,SAAS;AACrB,gBAAQ,UAAU;AAClB,gBAAQ,oBAAkB,eAAU,mBAAV,mBAA0B,WAAU;AAC9D,gBAAQ,MAAM,KAAK,UAAU,QAAQ,eAAe,MAAM;AAAA,MAClE,OAAa;AACL,gBAAQ,MAAM,KAAK,MAAM;AAAA,MAC1B;AAAA,IACP,WAAe,WAAW,QAAQ;AAC5B,cAAQ,UAAU;AAClB,cAAQ,kBAAkB,WAAW;AACrC,cAAQ,MAAM,KAAK,aAAa;AAAA,IACjC;AAAA,EAEF,SAAQ,OAAO;AACdA,kBAAAA,MAAA,MAAA,SAAA,qCAAc,aAAa,KAAK;AAChC,YAAQ,MAAM,KAAK,WAAW,MAAM,OAAO,EAAE;AAAA,EAC9C;AAEDA,gBAAAA,wDAAY,cAAc,OAAO;AACjC,SAAO;AACT;;;;;"}