{"version": 3, "file": "manage.js", "sources": ["pages/sharing/manage.vue", "pages/sharing/manage.vue?type=page"], "sourcesContent": ["<template>\n  <view class=\"container\">\n    <!-- 导航栏 -->\n    <view class=\"navbar\">\n      <view class=\"nav-left\" @click=\"goBack\">\n        <text class=\"nav-icon\">‹</text>\n      </view>\n      <text class=\"nav-title\">管理拼场</text>\n      <view class=\"nav-right\"></view>\n    </view>\n    \n    <!-- 加载状态 -->\n    <view v-if=\"loading\" class=\"loading-state\">\n      <text>加载中...</text>\n    </view>\n    \n    <!-- 错误状态 -->\n    <view v-else-if=\"error\" class=\"error-state\">\n      <text class=\"error-icon\">⚠️</text>\n      <text class=\"error-text\">{{ error }}</text>\n      <button class=\"retry-btn\" @click=\"loadSharingDetail\">\n        重新加载\n      </button>\n    </view>\n    \n    <!-- 拼场详情 -->\n    <view v-else-if=\"sharingDetail\" class=\"content\">\n      <!-- 拼场基本信息 -->\n      <view class=\"info-section\">\n        <view class=\"venue-header\">\n          <text class=\"venue-name\">{{ sharingDetail.venueName }}</text>\n          <view class=\"status-badge\" :class=\"getStatusClass(sharingDetail.status)\">\n            <text class=\"status-text\">{{ getStatusText(sharingDetail.status) }}</text>\n          </view>\n        </view>\n        \n        <!-- 队伍信息 -->\n        <view class=\"team-info\">\n          <view class=\"team-header\">\n            <text class=\"team-name\">{{ sharingDetail.teamName }}</text>\n            <text class=\"creator-label\">队长</text>\n          </view>\n          \n          <!-- 参与人数进度 -->\n          <view class=\"participants-progress\">\n            <view class=\"progress-info\">\n              <text class=\"progress-text\">\n                参与人数：{{ sharingDetail.currentParticipants }}/{{ sharingDetail.maxParticipants }}人\n              </text>\n              <text class=\"progress-percent\">\n                {{ getProgressPercent(sharingDetail.currentParticipants, sharingDetail.maxParticipants) }}%\n              </text>\n            </view>\n            <view class=\"progress-bar\">\n              <view \n                class=\"progress-fill\"\n                :style=\"{ width: getProgressPercent(sharingDetail.currentParticipants, sharingDetail.maxParticipants) + '%' }\"\n              ></view>\n            </view>\n          </view>\n        </view>\n        \n        <!-- 活动信息 -->\n        <view class=\"activity-info\">\n          <view class=\"info-row\">\n            <text class=\"info-label\">活动时间</text>\n            <text class=\"info-value\">{{ formatActivityTime(sharingDetail) }}</text>\n          </view>\n          <view class=\"info-row\">\n            <text class=\"info-label\">每队费用</text>\n            <text class=\"info-value price\">¥{{ getPerTeamPrice() }}</text>\n          </view>\n          <view class=\"info-row\">\n            <text class=\"info-label\">订单号</text>\n            <text class=\"info-value order-no\">{{ sharingDetail.orderNo }}</text>\n          </view>\n          <view class=\"info-row\">\n            <text class=\"info-label\">创建时间</text>\n            <text class=\"info-value\">{{ formatDateTime(sharingDetail.createdAt) }}</text>\n          </view>\n        </view>\n        \n        <!-- 活动描述 -->\n        <view v-if=\"sharingDetail.description\" class=\"description\">\n          <text class=\"description-label\">活动描述</text>\n          <text class=\"description-text\">{{ sharingDetail.description }}</text>\n        </view>\n      </view>\n      \n      <!-- 参与者管理 -->\n      <view class=\"participants-section\">\n        <view class=\"section-title\">\n          <text class=\"title-text\">队伍管理</text>\n          <text class=\"count-text\">({{ participants.length }}支)</text>\n        </view>\n        \n        <view class=\"participants-list\">\n          <view \n            v-for=\"participant in participants\" \n            :key=\"participant.id\"\n            class=\"participant-item\"\n          >\n            <view class=\"participant-info\">\n              <image \n                class=\"participant-avatar\" \n                :src=\"participant.avatar || '/static/images/default-avatar.png'\"\n                mode=\"aspectFill\"\n              />\n              <view class=\"participant-details\">\n                <text class=\"participant-name\">{{ participant.nickname || participant.username }}</text>\n                <text class=\"participant-role\">{{ participant.isCreator ? '队长' : '队员' }}</text>\n              </view>\n            </view>\n            \n            <!-- 移除按钮（仅对队员显示） -->\n            <view \n              v-if=\"!participant.isCreator && canManage\"\n              class=\"remove-btn\"\n              @click=\"showRemoveConfirm(participant)\"\n            >\n              <text class=\"remove-text\">移除</text>\n            </view>\n          </view>\n        </view>\n        \n        <view v-if=\"participants.length === 0\" class=\"empty-participants\">\n          <text class=\"empty-icon\">👥</text>\n          <text class=\"empty-text\">暂无参与者</text>\n        </view>\n      </view>\n      \n      <!-- 拼场设置 -->\n      <view class=\"settings-section\">\n        <view class=\"section-title\">\n          <text class=\"title-text\">拼场设置</text>\n        </view>\n        \n\n\n        <view class=\"settings-list\">\n          <!-- 自动通过申请 -->\n          <view class=\"setting-item\">\n            <view class=\"setting-info\">\n              <text class=\"setting-label\">自动通过申请</text>\n              <text class=\"setting-desc\">\n                {{ canManage ? '开启后，其他用户申请加入时将自动通过' : '请先完成支付后才能设置自动通过' }}\n              </text>\n            </view>\n            <switch\n              :checked=\"settings.autoApprove\"\n              @change=\"onAutoApproveChange\"\n              :disabled=\"!canManage\"\n              color=\"#ff6b35\"\n            />\n          </view>\n          \n          <!-- 允许中途退出 -->\n          <view class=\"setting-item\">\n            <view class=\"setting-info\">\n              <text class=\"setting-label\">允许中途退出</text>\n              <text class=\"setting-desc\">开启后，参与者可以在活动开始前退出</text>\n            </view>\n            <switch\n              :checked=\"settings.allowExit\"\n              @change=\"onAllowExitChange\"\n              :disabled=\"!canManage\"\n              color=\"#ff6b35\"\n            />\n          </view>\n        </view>\n      </view>\n      \n      <!-- 拼场申请 -->\n      <view v-if=\"requests.length > 0\" class=\"requests-section\">\n        <view class=\"section-title\">\n          <text class=\"title-text\">拼场申请</text>\n          <text class=\"count-text\">({{ pendingRequests.length }}条待处理)</text>\n        </view>\n        \n        <view class=\"requests-list\">\n          <view \n            v-for=\"request in requests\" \n            :key=\"request.id\"\n            class=\"request-item\"\n          >\n            <view class=\"request-info\">\n              <image \n                class=\"request-avatar\" \n                :src=\"request.userAvatar || '/static/images/default-avatar.png'\"\n                mode=\"aspectFill\"\n              />\n              <view class=\"request-details\">\n                <text class=\"request-name\">{{ request.userNickname || request.username }}</text>\n                <text class=\"request-time\">{{ formatDateTime(request.createdAt) }}</text>\n              </view>\n            </view>\n            \n            <view class=\"request-actions\">\n              <view v-if=\"request.status === 'PENDING'\" class=\"action-buttons\">\n                <button \n                  class=\"action-btn reject-btn\"\n                  @click=\"handleRequest(request.id, 'REJECTED')\"\n                >\n                  拒绝\n                </button>\n                <button \n                  class=\"action-btn approve-btn\"\n                  @click=\"handleRequest(request.id, 'APPROVED')\"\n                >\n                  同意\n                </button>\n              </view>\n              \n              <view v-else class=\"request-status\">\n                <text class=\"status-text\" :class=\"getRequestStatusClass(request.status)\">\n                  {{ getRequestStatusText(request.status) }}\n                </text>\n              </view>\n            </view>\n          </view>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 底部操作栏 -->\n    <view v-if=\"sharingDetail && canManage\" class=\"bottom-actions\">\n      <button\n        v-if=\"canConfirm\"\n        class=\"action-btn confirm-btn\"\n        @click=\"handleConfirmSharing\"\n      >\n        确认拼场\n      </button>\n\n      <button\n        v-if=\"canCancel\"\n        class=\"action-btn cancel-btn\"\n        @click=\"handleCancelSharing\"\n      >\n        取消拼场\n      </button>\n    </view>\n    \n    <!-- 移除参与者确认弹窗 -->\n    <uni-popup ref=\"removePopup\" type=\"dialog\">\n      <uni-popup-dialog \n        type=\"warn\"\n        title=\"移除参与者\"\n        :content=\"`确定要移除 ${removeTarget?.nickname || removeTarget?.username} 吗？`\"\n        @confirm=\"confirmRemove\"\n        @close=\"cancelRemove\"\n      ></uni-popup-dialog>\n    </uni-popup>\n    \n    <!-- 取消拼场确认弹窗 -->\n    <uni-popup ref=\"cancelPopup\" type=\"dialog\">\n      <uni-popup-dialog \n        type=\"warn\"\n        title=\"取消拼场\"\n        content=\"确定要取消这个拼场吗？取消后将无法恢复。\"\n        @confirm=\"confirmCancel\"\n        @close=\"() => {}\"\n      ></uni-popup-dialog>\n    </uni-popup>\n  </view>\n</template>\n\n<script>\nimport { useSharingStore } from '@/stores/sharing.js'\nimport { useUserStore } from '@/stores/user.js'\nimport { formatDate, formatDateTime } from '@/utils/helpers.js'\n\nexport default {\n  name: 'SharingManage',\n\n  setup() {\n    const sharingStore = useSharingStore()\n    const userStore = useUserStore()\n    return {\n      sharingStore,\n      userStore\n    }\n  },\n\n  data() {\n    return {\n      sharingId: '',\n      error: '',\n      removeTarget: null,\n      pendingSettings: {}, // 暂存未支付用户的设置\n      settings: {\n        autoApprove: true,\n        allowExit: true\n      },\n      participants: [],\n      requests: []\n    }\n  },\n  \n  computed: {\n    // 使用Pinia store的getters（修复：使用新的getter名称）\n    sharingOrderDetail() {\n      return this.sharingStore.sharingOrderDetailGetter\n    },\n    loading() {\n      return this.sharingStore.isLoading\n    },\n    userInfo() {\n      return this.userStore.getUserInfo\n    },\n    \n    // 拼场详情\n    sharingDetail() {\n      return this.sharingOrderDetail\n    },\n    \n    // 检查发起者是否已支付\n    isCreatorPaid() {\n      if (!this.sharingDetail) return false\n\n      // 需要检查主订单(Order)的状态，而不是拼场订单(SharingOrder)的状态\n      // sharingDetail.status 是 SharingOrder 的状态\n      // sharingDetail.orderStatus 是主订单状态\n\n      // 如果有主订单状态字段，优先使用\n      const mainOrderStatus = this.sharingDetail.orderStatus\n      if (mainOrderStatus) {\n        // 已支付的状态包括：PAID, OPEN, APPROVED_PENDING_PAYMENT, SHARING_SUCCESS, CONFIRMED, VERIFIED, COMPLETED\n        const paidStatuses = [\n          'PAID',                    // 已支付（普通订单）\n          'OPEN',                    // 开放中（拼场订单发起者已支付）\n          'APPROVED_PENDING_PAYMENT', // 已批准待支付（发起者已支付，等待申请者支付）\n          'SHARING_SUCCESS',         // 拼场成功（双方都已支付）\n          'CONFIRMED',               // 已确认\n          'VERIFIED',                // 已核销\n          'COMPLETED'                // 已完成\n        ]\n        const isPaid = paidStatuses.includes(mainOrderStatus)\n\n        console.log('=== isCreatorPaid 调试信息 ===')\n        console.log('主订单状态:', mainOrderStatus)\n        console.log('是否已支付:', isPaid)\n        console.log('支付状态列表:', paidStatuses)\n\n        return isPaid\n      }\n\n      // 如果没有主订单状态，通过拼场订单状态推断\n      // SharingOrder.OPEN 状态通常表示发起者已支付\n      const fallbackPaid = this.sharingDetail.status === 'OPEN'\n      console.log('=== isCreatorPaid 备用逻辑 ===')\n      console.log('拼场订单状态:', this.sharingDetail.status)\n      console.log('备用判断结果:', fallbackPaid)\n\n      return fallbackPaid\n    },\n\n    // 是否可以管理（基础权限检查，不包括支付状态）\n    canManage() {\n      const hasSharingDetail = !!this.sharingDetail\n      const hasUserInfo = !!this.userInfo\n      const creatorMatch = this.sharingDetail?.creatorUsername === this.userInfo?.username\n      const statusCheck = this.sharingDetail?.status === 'ACTIVE' || this.sharingDetail?.status === 'RECRUITING' || this.sharingDetail?.status === 'OPEN'\n\n      // 基础权限检查（不包括支付状态）\n      const result = hasSharingDetail && hasUserInfo && creatorMatch && statusCheck\n\n      // 添加调试信息\n      console.log('=== canManage 调试信息 ===')\n      console.log('hasSharingDetail:', hasSharingDetail)\n      console.log('hasUserInfo:', hasUserInfo)\n      console.log('creatorMatch:', creatorMatch)\n      console.log('statusCheck:', statusCheck)\n      console.log('sharingDetail.status:', this.sharingDetail?.status)\n      console.log('creatorPaid:', this.isCreatorPaid)\n      console.log('sharingDetail.orderStatus:', this.sharingDetail?.orderStatus)\n      console.log('canManage result:', result)\n\n      return result\n    },\n    \n    // 是否可以确认\n    canConfirm() {\n      return this.sharingDetail?.status === 'OPEN' && \n             this.sharingDetail?.currentParticipants >= 2\n    },\n    \n    // 是否可以取消\n    canCancel() {\n      return ['OPEN', 'FULL'].includes(this.sharingDetail?.status)\n    },\n    \n    // 待处理申请\n    pendingRequests() {\n      return this.requests.filter(request => request.status === 'PENDING')\n    }\n  },\n  \n  onLoad(options) {\n    console.log('=== 拼场管理页面 onLoad ===')\n    console.log('接收到的参数:', options)\n\n    if (options.id) {\n      this.sharingId = options.id\n      console.log('设置拼场ID:', this.sharingId)\n      this.loadSharingDetail()\n    } else {\n      console.error('缺少拼场ID参数')\n      this.error = '缺少拼场ID参数'\n    }\n\n    // 监听支付成功事件\n    uni.$on('paymentSuccess', this.onPaymentSuccess)\n  },\n  \n  onShow() {\n    console.log('=== 拼场管理页面 onShow 触发 ===')\n    // 页面显示时强制刷新数据\n    if (this.sharingId) {\n      // 先加载暂存设置\n      this.loadPendingSettings()\n\n      // 强制刷新数据，不使用缓存\n      this.forceRefreshData().then(() => {\n        // 检查是否有暂存的设置需要应用\n        this.applyPendingSettings()\n      })\n    }\n  },\n  \n  onPullDownRefresh() {\n    this.loadSharingDetail().finally(() => {\n      uni.stopPullDownRefresh()\n    })\n  },\n\n  onUnload() {\n    // 移除事件监听\n    uni.$off('paymentSuccess', this.onPaymentSuccess)\n  },\n  \n  methods: {\n    // 使用Pinia store的actions\n    async getSharingOrderDetail(orderId, forceRefresh = false) {\n      return await this.sharingStore.getOrderDetail(orderId, forceRefresh)\n    },\n    async updateSharingSettings(data) {\n      return await this.sharingStore.updateSharingSettings(data)\n    },\n    async removeSharingParticipant(data) {\n      // 这个方法需要在Pinia store中实现\n      console.warn('removeSharingParticipant 方法需要在Pinia store中实现')\n    },\n    async confirmSharingOrder(orderId) {\n      // 这个方法需要在Pinia store中实现\n      console.warn('confirmSharingOrder 方法需要在Pinia store中实现')\n    },\n    async cancelSharingOrder(orderId) {\n      // 这个方法需要在Pinia store中实现\n      console.warn('cancelSharingOrder 方法需要在Pinia store中实现')\n    },\n    async processSharingRequest(data) {\n      return await this.sharingStore.processSharingRequest(data)\n    },\n    async getSharingRequests(params) {\n      return await this.sharingStore.getReceivedRequestsList(params)\n    },\n    \n    // 返回上一页\n    goBack() {\n      uni.navigateBack()\n    },\n    \n    // 加载拼场详情\n    async loadSharingDetail() {\n      if (!this.sharingId) return\n      \n      try {\n        this.error = ''\n        console.log('拼场管理页面：开始加载拼场详情:', this.sharingId)\n        \n        // 加载拼场详情\n        await this.getSharingOrderDetail(this.sharingId)\n\n        if (this.sharingDetail) {\n          console.log('=== 拼场详情加载完成 ===')\n          console.log('拼场订单状态:', this.sharingDetail.status)\n          console.log('主订单状态:', this.sharingDetail.orderStatus)\n          console.log('发起者是否已支付:', this.isCreatorPaid)\n\n          // 初始化设置\n          this.settings = {\n            autoApprove: this.sharingDetail.autoApprove || false,\n            allowExit: this.sharingDetail.allowExit || false\n          }\n          \n          // 模拟参与者数据（实际应该从API获取）\n          this.participants = [\n            {\n              id: 'creator',\n              username: this.sharingDetail.creatorUsername,\n              nickname: this.sharingDetail.creatorUsername,\n              avatar: '',\n              isCreator: true\n            }\n          ]\n          \n          // 添加其他参与者（模拟数据）\n          for (let i = 1; i < this.sharingDetail.currentParticipants; i++) {\n            this.participants.push({\n              id: `participant_${i}`,\n              username: `user_${i}`,\n              nickname: `用户${i}`,\n              avatar: '',\n              isCreator: false\n            })\n          }\n          \n          // 加载拼场申请\n          await this.loadSharingRequests()\n          \n          console.log('拼场管理页面：加载拼场详情成功:', this.sharingDetail)\n        } else {\n          this.error = '拼场不存在或已被删除'\n        }\n        \n      } catch (error) {\n        console.error('拼场管理页面：加载拼场详情失败:', error)\n        this.error = error.message || '加载失败，请重试'\n      }\n    },\n    \n    // 加载拼场申请\n    async loadSharingRequests() {\n      try {\n        const requests = await this.getSharingRequests(this.sharingId)\n        this.requests = requests || []\n        console.log('拼场管理页面：加载拼场申请成功:', this.requests)\n      } catch (error) {\n        console.error('拼场管理页面：加载拼场申请失败:', error)\n        this.requests = []\n      }\n    },\n\n    // 自动通过申请开关变化\n    async onAutoApproveChange(e) {\n      const newValue = e.detail.value\n\n      // 如果要开启自动通过，检查发起者是否已支付\n      if (newValue && !this.isCreatorPaid) {\n        // 暂存设置，等待支付后应用\n        this.pendingSettings = {\n          ...this.pendingSettings,\n          autoApprove: newValue\n        }\n\n        // 保存暂存设置到本地存储\n        uni.setStorageSync(`pendingSettings_${this.sharingId}`, this.pendingSettings)\n\n        uni.showModal({\n          title: '需要先支付',\n          content: '支付完成后，自动通过申请功能将自动开启。',\n          showCancel: true,\n          cancelText: '取消',\n          confirmText: '去支付',\n          success: (res) => {\n            if (res.confirm) {\n              // 跳转到支付页面，带上来源标识\n              uni.navigateTo({\n                url: `/pages/payment/index?orderId=${this.sharingDetail.orderId}&type=sharing&from=sharing-manage`\n              })\n            } else {\n              // 用户取消，清除暂存设置并恢复开关状态\n              this.pendingSettings = {}\n              uni.removeStorageSync(`pendingSettings_${this.sharingId}`)\n              // 强制刷新页面数据以恢复开关状态\n              this.$forceUpdate()\n            }\n          }\n        })\n        return\n      }\n\n      try {\n        await this.updateSharingSettings({\n          sharingId: this.sharingId,\n          settings: {\n            autoApprove: newValue,\n            allowExit: this.settings.allowExit\n          }\n        })\n\n        this.settings.autoApprove = newValue\n\n        uni.showToast({\n          title: newValue ? '已开启自动通过' : '已关闭自动通过',\n          icon: 'success'\n        })\n\n      } catch (error) {\n        console.error('拼场管理页面：更新自动通过设置失败:', error)\n        uni.showToast({\n          title: '设置失败',\n          icon: 'error'\n        })\n      }\n    },\n    \n    // 允许中途退出开关变化\n    async onAllowExitChange(e) {\n      const newValue = e.detail.value\n\n      try {\n        await this.updateSharingSettings({\n          sharingId: this.sharingId,\n          settings: {\n            autoApprove: this.settings.autoApprove,\n            allowExit: newValue\n          }\n        })\n        \n        this.settings.allowExit = newValue\n        \n        uni.showToast({\n          title: newValue ? '已允许中途退出' : '已禁止中途退出',\n          icon: 'success'\n        })\n        \n      } catch (error) {\n        console.error('拼场管理页面：更新退出设置失败:', error)\n        uni.showToast({\n          title: '设置失败',\n          icon: 'error'\n        })\n      }\n    },\n    \n    // 显示移除确认弹窗\n    showRemoveConfirm(participant) {\n      this.removeTarget = participant\n      this.$refs.removePopup.open()\n    },\n    \n    // 确认移除参与者\n    async confirmRemove() {\n      if (!this.removeTarget) return\n      \n      try {\n        await this.removeSharingParticipant({\n          sharingId: this.sharingId,\n          participantId: this.removeTarget.id\n        })\n        \n        // 从本地列表中移除\n        const index = this.participants.findIndex(p => p.id === this.removeTarget.id)\n        if (index > -1) {\n          this.participants.splice(index, 1)\n        }\n        \n        // 更新拼场详情中的参与人数\n        if (this.sharingDetail) {\n          this.sharingDetail.currentParticipants--\n        }\n        \n        uni.showToast({\n          title: '移除成功',\n          icon: 'success'\n        })\n        \n        this.removeTarget = null\n        \n      } catch (error) {\n        console.error('拼场管理页面：移除参与者失败:', error)\n        uni.showToast({\n          title: error.message || '移除失败',\n          icon: 'error'\n        })\n      }\n    },\n    \n    // 取消移除\n    cancelRemove() {\n      this.removeTarget = null\n    },\n    \n    // 处理拼场申请\n    async handleRequest(requestId, action) {\n      try {\n        await this.processSharingRequest({\n          requestId,\n          action: action === 'APPROVED' ? 'approve' : 'reject'\n        })\n        \n        // 更新本地申请状态\n        const request = this.requests.find(r => r.id === requestId)\n        if (request) {\n          request.status = action\n        }\n        \n        // 如果是同意申请，更新参与者列表和人数\n        if (action === 'APPROVED') {\n          if (request) {\n            this.participants.push({\n              id: request.userId,\n              username: request.username,\n              nickname: request.userNickname,\n              avatar: request.userAvatar,\n              isCreator: false\n            })\n          }\n          \n          if (this.sharingDetail) {\n            this.sharingDetail.currentParticipants++\n          }\n        }\n        \n        uni.showToast({\n          title: action === 'APPROVED' ? '已同意申请' : '已拒绝申请',\n          icon: 'success'\n        })\n\n        // 通知其他页面刷新数据\n        uni.$emit('sharingDataChanged', {\n          orderId: this.sharingId,\n          action: action,\n          currentParticipants: this.sharingDetail?.currentParticipants || 0\n        })\n\n      } catch (error) {\n        console.error('拼场管理页面：处理申请失败:', error)\n\n        // 检查是否需要跳转到支付页面\n        if (error.needPayment && error.orderId) {\n          console.log('发起者需要先支付，跳转到支付页面，订单ID:', error.orderId)\n\n          // 显示提示信息\n          uni.showModal({\n            title: '需要先支付',\n            content: error.message || '发起者尚未支付，无法批准申请。请先完成支付后再处理申请。',\n            showCancel: true,\n            cancelText: '取消',\n            confirmText: '去支付',\n            success: (res) => {\n              if (res.confirm) {\n                // 跳转到支付页面\n                uni.navigateTo({\n                  url: `/pages/payment/index?orderId=${error.orderId}&from=sharing-manage`\n                })\n              }\n            }\n          })\n        } else {\n          // 普通错误提示\n          uni.showToast({\n            title: error.message || '操作失败',\n            icon: 'error'\n          })\n        }\n      }\n    },\n    \n    // 处理确认拼场点击\n    handleConfirmSharing() {\n      if (!this.isCreatorPaid) {\n        this.showPaymentPrompt()\n        return\n      }\n      this.confirmSharing()\n    },\n\n    // 处理取消拼场点击\n    handleCancelSharing() {\n      if (!this.isCreatorPaid) {\n        this.showPaymentPrompt()\n        return\n      }\n      this.showCancelConfirm()\n    },\n\n    // 显示支付提示\n    showPaymentPrompt() {\n      uni.showModal({\n        title: '需要先支付',\n        content: '请先完成订单支付后再管理拼场',\n        confirmText: '去支付',\n        cancelText: '取消',\n        success: (res) => {\n          if (res.confirm) {\n            // 跳转到支付页面\n            uni.navigateTo({\n              url: `/pages/payment/index?orderId=${this.sharingDetail.orderId}&type=sharing`\n            })\n          }\n        }\n      })\n    },\n\n    // 强制刷新数据（不使用缓存）\n    async forceRefreshData() {\n      try {\n        console.log('=== 强制刷新拼场数据 ===')\n\n        // 清除错误状态\n        this.error = ''\n\n        // 清除store中的缓存数据\n        this.sharingStore.setSharingOrderDetail(null)\n\n        // 强制重新加载数据\n        await this.getSharingOrderDetail(this.sharingId, true)\n\n        // 检查数据获取结果\n        if (this.sharingDetail) {\n          // 重新初始化设置\n          this.settings = {\n            autoApprove: this.sharingDetail.autoApprove || false,\n            allowExit: this.sharingDetail.allowExit || false\n          }\n\n          // 重新加载拼场申请\n          await this.loadSharingRequests()\n\n          console.log('=== 数据刷新完成 ===')\n          console.log('当前拼场状态:', this.sharingDetail?.status)\n          console.log('当前主订单状态:', this.sharingDetail?.orderStatus)\n          console.log('发起者是否已支付:', this.isCreatorPaid)\n          console.log('当前设置状态:', this.settings)\n        } else {\n          console.warn('=== 数据刷新失败：拼场详情为空 ===')\n          this.error = '拼场不存在或已被删除'\n        }\n\n      } catch (error) {\n        console.error('强制刷新数据失败:', error)\n        uni.showToast({\n          title: '数据刷新失败',\n          icon: 'error'\n        })\n      }\n    },\n\n    // 处理支付成功事件\n    async onPaymentSuccess(eventData) {\n      console.log('=== 收到支付成功事件 ===')\n      console.log('事件数据:', eventData)\n\n      if (eventData.type === 'sharing' && eventData.fromPage === 'sharing-manage') {\n        console.log('拼场支付成功，重新查找拼场订单')\n\n        try {\n          // 支付的是主订单ID，需要通过主订单ID找到对应的拼场订单\n          const mainOrderId = eventData.orderId\n          console.log('支付的主订单ID:', mainOrderId)\n\n          // 延迟一下再查找，确保后端数据已经更新\n          setTimeout(async () => {\n            try {\n              // 通过主订单ID查找拼场订单\n              await this.findSharingOrderByMainOrderId(mainOrderId)\n            } catch (error) {\n              console.error('通过主订单ID查找拼场订单失败:', error)\n              // 如果查找失败，尝试刷新当前拼场订单\n              this.forceRefreshData()\n            }\n          }, 1000)\n\n        } catch (error) {\n          console.error('处理支付成功事件失败:', error)\n          // 降级处理：直接刷新当前数据\n          setTimeout(() => {\n            this.forceRefreshData()\n          }, 1000)\n        }\n      }\n    },\n\n    // 通过主订单ID查找拼场订单\n    async findSharingOrderByMainOrderId(mainOrderId) {\n      try {\n        console.log('=== 通过主订单ID查找拼场订单 ===')\n        console.log('主订单ID:', mainOrderId)\n\n        // 调用store方法\n        const newSharingOrderId = await this.sharingStore.getOrderDetailByMainOrderId(mainOrderId)\n\n        if (newSharingOrderId) {\n          console.log('找到新的拼场订单ID:', newSharingOrderId)\n          // 更新当前页面的拼场订单ID\n          this.sharingId = newSharingOrderId.toString()\n          console.log('更新页面拼场ID为:', this.sharingId)\n\n          // 清除错误状态\n          this.error = ''\n\n          // 重新初始化设置\n          if (this.sharingDetail) {\n            this.settings = {\n              autoApprove: this.sharingDetail.autoApprove || false,\n              allowExit: this.sharingDetail.allowExit || false\n            }\n\n            // 重新加载拼场申请\n            await this.loadSharingRequests()\n          }\n\n          console.log('=== 拼场订单查找和更新完成 ===')\n        } else {\n          throw new Error('未找到对应的拼场订单')\n        }\n\n      } catch (error) {\n        console.error('通过主订单ID查找拼场订单失败:', error)\n        throw error\n      }\n    },\n\n    // 加载暂存设置\n    loadPendingSettings() {\n      try {\n        const savedSettings = uni.getStorageSync(`pendingSettings_${this.sharingId}`)\n        if (savedSettings && typeof savedSettings === 'object') {\n          this.pendingSettings = savedSettings\n          console.log('加载暂存设置:', this.pendingSettings)\n        }\n      } catch (error) {\n        console.error('加载暂存设置失败:', error)\n        this.pendingSettings = {}\n      }\n    },\n\n    // 应用暂存的设置\n    async applyPendingSettings() {\n      if (Object.keys(this.pendingSettings).length === 0) {\n        return // 没有暂存设置\n      }\n\n      if (!this.isCreatorPaid) {\n        return // 仍未支付，不应用设置\n      }\n\n      try {\n        console.log('应用暂存设置:', this.pendingSettings)\n\n        // 合并当前设置和暂存设置\n        const newSettings = {\n          autoApprove: this.pendingSettings.autoApprove !== undefined ? this.pendingSettings.autoApprove : this.settings.autoApprove,\n          allowExit: this.pendingSettings.allowExit !== undefined ? this.pendingSettings.allowExit : this.settings.allowExit\n        }\n\n        await this.updateSharingSettings({\n          sharingId: this.sharingId,\n          settings: newSettings\n        })\n\n        // 更新本地设置\n        this.settings = { ...newSettings }\n\n        // 清空暂存设置\n        this.pendingSettings = {}\n        uni.removeStorageSync(`pendingSettings_${this.sharingId}`)\n\n        uni.showToast({\n          title: '设置已自动应用',\n          icon: 'success'\n        })\n\n      } catch (error) {\n        console.error('应用暂存设置失败:', error)\n        // 保留暂存设置，下次再试\n      }\n    },\n\n    // 确认拼场\n    async confirmSharing() {\n      try {\n        uni.showLoading({ title: '确认中...' })\n\n        await this.confirmSharingOrder(this.sharingId)\n\n        uni.hideLoading()\n\n        uni.showToast({\n          title: '确认成功',\n          icon: 'success'\n        })\n\n        // 刷新页面数据\n        setTimeout(() => {\n          this.loadSharingDetail()\n        }, 1500)\n\n      } catch (error) {\n        uni.hideLoading()\n        console.error('拼场管理页面：确认拼场失败:', error)\n        uni.showToast({\n          title: error.message || '确认失败',\n          icon: 'error'\n        })\n      }\n    },\n    \n    // 显示取消确认弹窗\n    showCancelConfirm() {\n      this.$refs.cancelPopup.open()\n    },\n    \n    // 确认取消拼场\n    async confirmCancel() {\n      try {\n        uni.showLoading({ title: '取消中...' })\n        \n        await this.cancelSharingOrder(this.sharingId)\n        \n        uni.hideLoading()\n        \n        uni.showToast({\n          title: '取消成功',\n          icon: 'success'\n        })\n        \n        // 返回上一页\n        setTimeout(() => {\n          uni.navigateBack()\n        }, 1500)\n        \n      } catch (error) {\n        uni.hideLoading()\n        console.error('拼场管理页面：取消拼场失败:', error)\n        uni.showToast({\n          title: error.message || '取消失败',\n          icon: 'error'\n        })\n      }\n    },\n    \n    // 获取进度百分比\n    getProgressPercent(current, max) {\n      if (!max || max === 0) return 0\n      return Math.round((current / max) * 100)\n    },\n    \n    // 格式化活动时间（参考预约订单的实现）\n    formatActivityTime(sharing) {\n      if (!sharing) return '--'\n      \n      const bookingDate = sharing.bookingDate || sharing.date\n      const startTime = sharing.startTime || sharing.bookingStartTime\n      const endTime = sharing.endTime || sharing.bookingEndTime\n      const timeSlotCount = sharing.timeSlotCount || 1\n      \n      if (!bookingDate) {\n        return '时间未知'\n      }\n      \n      const date = this.formatDate(bookingDate)\n      \n      if (!startTime || !endTime) {\n        if (timeSlotCount && timeSlotCount > 0) {\n          return `${date} (${timeSlotCount}个时段)`\n        }\n        return `${date} 时间待定`\n      }\n      \n      // 格式化时间显示（去掉秒数）\n      const formatTime = (timeStr) => {\n        if (!timeStr) return ''\n        if (timeStr.length > 5 && timeStr.includes(':')) {\n          return timeStr.substring(0, 5)\n        }\n        return timeStr\n      }\n      \n      const formattedStart = formatTime(startTime)\n      const formattedEnd = formatTime(endTime)\n      \n      // 如果有多个时间段，显示时间段数量\n      if (timeSlotCount > 1) {\n        return `${date} ${formattedStart}-${formattedEnd} (${timeSlotCount}个时段)`\n      }\n      \n      return `${date} ${formattedStart}-${formattedEnd}`\n    },\n    \n    // 格式化日期\n    formatDate(date) {\n      if (!date) return '--'\n      try {\n        const dateObj = new Date(date)\n        if (isNaN(dateObj.getTime())) return '--'\n        const year = dateObj.getFullYear()\n        const month = String(dateObj.getMonth() + 1).padStart(2, '0')\n        const day = String(dateObj.getDate()).padStart(2, '0')\n        return `${year}-${month}-${day}`\n      } catch (error) {\n        console.error('日期格式化错误:', error)\n        return '--'\n      }\n    },\n    \n    // 格式化日期时间\n    formatDateTime(datetime) {\n      if (!datetime) return '--'\n      try {\n        // 处理 iOS 兼容性问题：将 \"2025-07-13 23:34:22\" 格式转换为 \"2025/07/13 23:34:22\"\n        let dateStr = datetime\n        if (typeof datetime === 'string' && datetime.includes(' ') && datetime.includes('-')) {\n          dateStr = datetime.replace(/-/g, '/')\n        }\n\n        const date = new Date(dateStr)\n        if (isNaN(date.getTime())) return '--'\n\n        const year = date.getFullYear()\n        const month = String(date.getMonth() + 1).padStart(2, '0')\n        const day = String(date.getDate()).padStart(2, '0')\n        const hour = String(date.getHours()).padStart(2, '0')\n        const minute = String(date.getMinutes()).padStart(2, '0')\n        return `${year}-${month}-${day} ${hour}:${minute}`\n      } catch (error) {\n        console.error('时间格式化错误:', error)\n        return '--'\n      }\n    },\n    \n    // 格式化时间段\n    formatTimeSlot(startTime, endTime) {\n      if (!startTime && !endTime) {\n        return '时间未指定'\n      }\n      if (startTime && !endTime) {\n        return startTime\n      }\n      if (!startTime && endTime) {\n        return endTime\n      }\n      \n      // 格式化时间显示（去掉秒数）\n      const formatTime = (timeStr) => {\n        if (!timeStr) return ''\n        if (timeStr.length > 5 && timeStr.includes(':')) {\n          return timeStr.substring(0, 5)\n        }\n        return timeStr\n      }\n      \n      const formattedStart = formatTime(startTime)\n      const formattedEnd = formatTime(endTime)\n      \n      return `${formattedStart}-${formattedEnd}`\n    },\n    \n    // 格式化价格显示\n    formatPrice(price) {\n      if (!price && price !== 0) return '0.00'\n      const numPrice = Number(price)\n      if (isNaN(numPrice)) return '0.00'\n      return numPrice.toFixed(2)\n    },\n\n    // 获取每队费用\n    getPerTeamPrice() {\n      if (!this.sharingDetail) return '0.00'\n      \n      // 如果有明确的每队费用，直接使用\n      if (this.sharingDetail.pricePerPerson) {\n        return this.formatPrice(this.sharingDetail.pricePerPerson)\n      }\n      \n      // 否则根据总费用和队伍数量计算\n      const totalPrice = this.sharingDetail.totalPrice || this.sharingDetail.price || 0\n      const maxParticipants = this.sharingDetail.maxParticipants || 2\n      const perTeamPrice = totalPrice / maxParticipants\n      \n      return this.formatPrice(perTeamPrice)\n    },\n    \n    // 获取状态文本\n    getStatusText(status) {\n      const statusMap = {\n        'OPEN': '招募中',\n        'FULL': '已满员',\n        'CONFIRMED': '已确认',\n        'CANCELLED': '已取消',\n        'EXPIRED': '已过期'\n      }\n      return statusMap[status] || '未知状态'\n    },\n    \n    // 获取状态样式类\n    getStatusClass(status) {\n      const classMap = {\n        'OPEN': 'status-open',\n        'FULL': 'status-full',\n        'CONFIRMED': 'status-confirmed',\n        'CANCELLED': 'status-cancelled',\n        'EXPIRED': 'status-expired'\n      }\n      return classMap[status] || 'status-unknown'\n    },\n    \n    // 获取申请状态文本\n    getRequestStatusText(status) {\n      const statusMap = {\n        'PENDING': '待处理',\n        'APPROVED': '已同意',\n        'REJECTED': '已拒绝'\n      }\n      return statusMap[status] || '未知状态'\n    },\n    \n    // 获取申请状态样式类\n    getRequestStatusClass(status) {\n      const classMap = {\n        'PENDING': 'request-pending',\n        'APPROVED': 'request-approved',\n        'REJECTED': 'request-rejected'\n      }\n      return classMap[status] || 'request-unknown'\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.container {\n  background-color: #f5f5f5;\n  min-height: 100vh;\n  padding-bottom: 120rpx;\n}\n\n// 导航栏\n.navbar {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: 20rpx 30rpx;\n  background-color: #ffffff;\n  border-bottom: 1rpx solid #f0f0f0;\n  \n  .nav-left {\n    width: 60rpx;\n    \n    .nav-icon {\n      font-size: 40rpx;\n      color: #333333;\n    }\n  }\n  \n  .nav-title {\n    font-size: 32rpx;\n    font-weight: bold;\n    color: #333333;\n  }\n  \n  .nav-right {\n    width: 60rpx;\n  }\n}\n\n// 加载状态\n.loading-state {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  padding: 200rpx 0;\n  \n  text {\n    font-size: 28rpx;\n    color: #999999;\n  }\n}\n\n// 错误状态\n.error-state {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  padding: 200rpx 60rpx;\n  \n  .error-icon {\n    font-size: 120rpx;\n    margin-bottom: 30rpx;\n  }\n  \n  .error-text {\n    font-size: 28rpx;\n    color: #333333;\n    text-align: center;\n    margin-bottom: 40rpx;\n    line-height: 1.4;\n  }\n  \n  .retry-btn {\n    width: 200rpx;\n    height: 70rpx;\n    background-color: #ff6b35;\n    color: #ffffff;\n    border: none;\n    border-radius: 12rpx;\n    font-size: 26rpx;\n  }\n}\n\n// 内容区域\n.content {\n  padding: 20rpx;\n}\n\n// 信息区块\n.info-section {\n  background-color: #ffffff;\n  border-radius: 16rpx;\n  padding: 30rpx;\n  margin-bottom: 20rpx;\n  \n  .venue-header {\n    display: flex;\n    align-items: center;\n    justify-content: space-between;\n    margin-bottom: 24rpx;\n    \n    .venue-name {\n      font-size: 36rpx;\n      font-weight: bold;\n      color: #333333;\n    }\n    \n    .status-badge {\n      padding: 8rpx 16rpx;\n      border-radius: 20rpx;\n      \n      .status-text {\n        font-size: 24rpx;\n        font-weight: bold;\n      }\n      \n      &.status-open {\n        background-color: #e6f7ff;\n        .status-text { color: #1890ff; }\n      }\n      \n      &.status-full {\n        background-color: #fff7e6;\n        .status-text { color: #fa8c16; }\n      }\n      \n      &.status-confirmed {\n        background-color: #f6ffed;\n        .status-text { color: #52c41a; }\n      }\n      \n      &.status-cancelled {\n        background-color: #fff2f0;\n        .status-text { color: #ff4d4f; }\n      }\n      \n      &.status-expired {\n        background-color: #f5f5f5;\n        .status-text { color: #999999; }\n      }\n    }\n  }\n  \n  .team-info {\n    margin-bottom: 24rpx;\n    \n    .team-header {\n      display: flex;\n      align-items: center;\n      justify-content: space-between;\n      margin-bottom: 16rpx;\n      \n      .team-name {\n        font-size: 28rpx;\n        font-weight: bold;\n        color: #333333;\n      }\n      \n      .creator-label {\n        font-size: 22rpx;\n        color: #ff6b35;\n        background-color: #fff7f0;\n        padding: 4rpx 12rpx;\n        border-radius: 12rpx;\n      }\n    }\n    \n    .participants-progress {\n      .progress-info {\n        display: flex;\n        justify-content: space-between;\n        align-items: center;\n        margin-bottom: 12rpx;\n        \n        .progress-text {\n          font-size: 26rpx;\n          color: #666666;\n        }\n        \n        .progress-percent {\n          font-size: 24rpx;\n          color: #ff6b35;\n          font-weight: bold;\n        }\n      }\n      \n      .progress-bar {\n        height: 8rpx;\n        background-color: #f0f0f0;\n        border-radius: 4rpx;\n        overflow: hidden;\n        \n        .progress-fill {\n          height: 100%;\n          background-color: #ff6b35;\n          transition: width 0.3s ease;\n        }\n      }\n    }\n  }\n  \n  .activity-info {\n    margin-bottom: 24rpx;\n    \n    .info-row {\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      padding: 12rpx 0;\n      border-bottom: 1rpx solid #f0f0f0;\n      \n      &:last-child {\n        border-bottom: none;\n      }\n      \n      .info-label {\n        font-size: 26rpx;\n        color: #666666;\n      }\n      \n      .info-value {\n        font-size: 26rpx;\n        color: #333333;\n        \n        &.price {\n          color: #ff6b35;\n          font-weight: bold;\n        }\n        \n        &.order-no {\n          font-family: monospace;\n          font-size: 22rpx;\n        }\n      }\n    }\n  }\n  \n  .description {\n    .description-label {\n      font-size: 26rpx;\n      color: #666666;\n      display: block;\n      margin-bottom: 12rpx;\n    }\n    \n    .description-text {\n      font-size: 26rpx;\n      color: #333333;\n      line-height: 1.5;\n    }\n  }\n}\n\n// 区块标题\n.section-title {\n  display: flex;\n  align-items: center;\n  margin-bottom: 20rpx;\n  \n  .title-text {\n    font-size: 32rpx;\n    font-weight: bold;\n    color: #333333;\n  }\n  \n  .count-text {\n    font-size: 24rpx;\n    color: #999999;\n    margin-left: 12rpx;\n  }\n}\n\n// 参与者区块\n.participants-section {\n  background-color: #ffffff;\n  border-radius: 16rpx;\n  padding: 30rpx;\n  margin-bottom: 20rpx;\n  \n  .participants-list {\n    .participant-item {\n      display: flex;\n      align-items: center;\n      justify-content: space-between;\n      padding: 20rpx 0;\n      border-bottom: 1rpx solid #f0f0f0;\n      \n      &:last-child {\n        border-bottom: none;\n      }\n      \n      .participant-info {\n        display: flex;\n        align-items: center;\n        flex: 1;\n        \n        .participant-avatar {\n          width: 80rpx;\n          height: 80rpx;\n          border-radius: 50%;\n          margin-right: 20rpx;\n        }\n        \n        .participant-details {\n          .participant-name {\n            font-size: 28rpx;\n            color: #333333;\n            display: block;\n            margin-bottom: 6rpx;\n          }\n          \n          .participant-role {\n            font-size: 22rpx;\n            color: #999999;\n          }\n        }\n      }\n      \n      .remove-btn {\n        padding: 12rpx 24rpx;\n        background-color: #fff2f0;\n        border-radius: 20rpx;\n        \n        .remove-text {\n          font-size: 24rpx;\n          color: #ff4d4f;\n        }\n      }\n    }\n  }\n  \n  .empty-participants {\n    display: flex;\n    flex-direction: column;\n    align-items: center;\n    padding: 80rpx 0;\n    \n    .empty-icon {\n      font-size: 120rpx;\n      margin-bottom: 20rpx;\n    }\n    \n    .empty-text {\n      font-size: 26rpx;\n      color: #999999;\n    }\n  }\n}\n\n// 设置区块\n.settings-section {\n  background-color: #ffffff;\n  border-radius: 16rpx;\n  padding: 30rpx;\n  margin-bottom: 20rpx;\n  \n  .settings-list {\n    .setting-item {\n      display: flex;\n      align-items: flex-start;\n      justify-content: space-between;\n      padding: 24rpx 0;\n      border-bottom: 1rpx solid #f0f0f0;\n      \n      &:last-child {\n        border-bottom: none;\n      }\n      \n      .setting-info {\n        flex: 1;\n        margin-right: 20rpx;\n        \n        .setting-label {\n          font-size: 28rpx;\n          color: #333333;\n          display: block;\n          margin-bottom: 8rpx;\n        }\n        \n        .setting-desc {\n          font-size: 24rpx;\n          color: #999999;\n          line-height: 1.4;\n        }\n      }\n    }\n  }\n}\n\n// 申请区块\n.requests-section {\n  background-color: #ffffff;\n  border-radius: 16rpx;\n  padding: 30rpx;\n  margin-bottom: 20rpx;\n  \n  .requests-list {\n    .request-item {\n      display: flex;\n      align-items: center;\n      justify-content: space-between;\n      padding: 20rpx 0;\n      border-bottom: 1rpx solid #f0f0f0;\n      \n      &:last-child {\n        border-bottom: none;\n      }\n      \n      .request-info {\n        display: flex;\n        align-items: center;\n        flex: 1;\n        \n        .request-avatar {\n          width: 80rpx;\n          height: 80rpx;\n          border-radius: 50%;\n          margin-right: 20rpx;\n        }\n        \n        .request-details {\n          .request-name {\n            font-size: 28rpx;\n            color: #333333;\n            display: block;\n            margin-bottom: 6rpx;\n          }\n          \n          .request-time {\n            font-size: 22rpx;\n            color: #999999;\n          }\n        }\n      }\n      \n      .request-actions {\n        .action-buttons {\n          display: flex;\n          gap: 12rpx;\n          \n          .action-btn {\n            padding: 12rpx 20rpx;\n            border: none;\n            border-radius: 20rpx;\n            font-size: 24rpx;\n            \n            &.reject-btn {\n              background-color: #f5f5f5;\n              color: #666666;\n            }\n            \n            &.approve-btn {\n              background-color: #ff6b35;\n              color: #ffffff;\n            }\n          }\n        }\n        \n        .request-status {\n          .status-text {\n            font-size: 24rpx;\n            padding: 8rpx 16rpx;\n            border-radius: 12rpx;\n            \n            &.request-pending {\n              background-color: #fff7e6;\n              color: #fa8c16;\n            }\n            \n            &.request-approved {\n              background-color: #f6ffed;\n              color: #52c41a;\n            }\n            \n            &.request-rejected {\n              background-color: #fff2f0;\n              color: #ff4d4f;\n            }\n          }\n        }\n      }\n    }\n  }\n}\n\n// 底部操作栏\n.bottom-actions {\n  position: fixed;\n  bottom: 0;\n  left: 0;\n  right: 0;\n  background-color: #ffffff;\n  padding: 20rpx 30rpx;\n  border-top: 1rpx solid #f0f0f0;\n  display: flex;\n  gap: 20rpx;\n  \n  .action-btn {\n    flex: 1;\n    height: 80rpx;\n    border: none;\n    border-radius: 12rpx;\n    font-size: 28rpx;\n    font-weight: bold;\n    \n    &.confirm-btn {\n      background-color: #ff6b35;\n      color: #ffffff;\n    }\n    \n    &.cancel-btn {\n      background-color: #f5f5f5;\n      color: #666666;\n    }\n  }\n}\n</style>", "import MiniProgramPage from '/Users/<USER>/Desktop/体育馆预约 2/体育馆预约2.0/pages/sharing/manage.vue'\nwx.createPage(MiniProgramPage)"], "names": ["useSharingStore", "useUserStore", "uni"], "mappings": ";;;;AAgRA,MAAK,YAAU;AAAA,EACb,MAAM;AAAA,EAEN,QAAQ;AACN,UAAM,eAAeA,eAAAA,gBAAgB;AACrC,UAAM,YAAYC,YAAAA,aAAa;AAC/B,WAAO;AAAA,MACL;AAAA,MACA;AAAA,IACF;AAAA,EACD;AAAA,EAED,OAAO;AACL,WAAO;AAAA,MACL,WAAW;AAAA,MACX,OAAO;AAAA,MACP,cAAc;AAAA,MACd,iBAAiB,CAAE;AAAA;AAAA,MACnB,UAAU;AAAA,QACR,aAAa;AAAA,QACb,WAAW;AAAA,MACZ;AAAA,MACD,cAAc,CAAE;AAAA,MAChB,UAAU,CAAC;AAAA,IACb;AAAA,EACD;AAAA,EAED,UAAU;AAAA;AAAA,IAER,qBAAqB;AACnB,aAAO,KAAK,aAAa;AAAA,IAC1B;AAAA,IACD,UAAU;AACR,aAAO,KAAK,aAAa;AAAA,IAC1B;AAAA,IACD,WAAW;AACT,aAAO,KAAK,UAAU;AAAA,IACvB;AAAA;AAAA,IAGD,gBAAgB;AACd,aAAO,KAAK;AAAA,IACb;AAAA;AAAA,IAGD,gBAAgB;AACd,UAAI,CAAC,KAAK;AAAe,eAAO;AAOhC,YAAM,kBAAkB,KAAK,cAAc;AAC3C,UAAI,iBAAiB;AAEnB,cAAM,eAAe;AAAA,UACnB;AAAA;AAAA,UACA;AAAA;AAAA,UACA;AAAA;AAAA,UACA;AAAA;AAAA,UACA;AAAA;AAAA,UACA;AAAA;AAAA,UACA;AAAA;AAAA,QACF;AACA,cAAM,SAAS,aAAa,SAAS,eAAe;AAEpDC,sBAAAA,MAAA,MAAA,OAAA,mCAAY,4BAA4B;AACxCA,sBAAAA,MAAY,MAAA,OAAA,mCAAA,UAAU,eAAe;AACrCA,sBAAAA,MAAA,MAAA,OAAA,mCAAY,UAAU,MAAM;AAC5BA,sBAAAA,MAAA,MAAA,OAAA,mCAAY,WAAW,YAAY;AAEnC,eAAO;AAAA,MACT;AAIA,YAAM,eAAe,KAAK,cAAc,WAAW;AACnDA,oBAAAA,MAAY,MAAA,OAAA,mCAAA,4BAA4B;AACxCA,oBAAA,MAAA,MAAA,OAAA,mCAAY,WAAW,KAAK,cAAc,MAAM;AAChDA,oBAAAA,MAAA,MAAA,OAAA,mCAAY,WAAW,YAAY;AAEnC,aAAO;AAAA,IACR;AAAA;AAAA,IAGD,YAAY;;AACV,YAAM,mBAAmB,CAAC,CAAC,KAAK;AAChC,YAAM,cAAc,CAAC,CAAC,KAAK;AAC3B,YAAM,iBAAe,UAAK,kBAAL,mBAAoB,uBAAoB,UAAK,aAAL,mBAAe;AAC5E,YAAM,gBAAc,UAAK,kBAAL,mBAAoB,YAAW,cAAY,UAAK,kBAAL,mBAAoB,YAAW,kBAAgB,UAAK,kBAAL,mBAAoB,YAAW;AAG7I,YAAM,SAAS,oBAAoB,eAAe,gBAAgB;AAGlEA,oBAAAA,MAAA,MAAA,OAAA,mCAAY,wBAAwB;AACpCA,oBAAAA,MAAA,MAAA,OAAA,mCAAY,qBAAqB,gBAAgB;AACjDA,oBAAAA,MAAY,MAAA,OAAA,mCAAA,gBAAgB,WAAW;AACvCA,oBAAAA,MAAY,MAAA,OAAA,mCAAA,iBAAiB,YAAY;AACzCA,oBAAAA,MAAY,MAAA,OAAA,mCAAA,gBAAgB,WAAW;AACvCA,oBAAY,MAAA,MAAA,OAAA,mCAAA,0BAAyB,UAAK,kBAAL,mBAAoB,MAAM;AAC/DA,oBAAA,MAAA,MAAA,OAAA,mCAAY,gBAAgB,KAAK,aAAa;AAC9CA,oBAAA,MAAA,MAAA,OAAA,mCAAY,+BAA8B,UAAK,kBAAL,mBAAoB,WAAW;AACzEA,oBAAAA,MAAY,MAAA,OAAA,mCAAA,qBAAqB,MAAM;AAEvC,aAAO;AAAA,IACR;AAAA;AAAA,IAGD,aAAa;;AACX,eAAO,UAAK,kBAAL,mBAAoB,YAAW,YAC/B,UAAK,kBAAL,mBAAoB,wBAAuB;AAAA,IACnD;AAAA;AAAA,IAGD,YAAY;;AACV,aAAO,CAAC,QAAQ,MAAM,EAAE,UAAS,UAAK,kBAAL,mBAAoB,MAAM;AAAA,IAC5D;AAAA;AAAA,IAGD,kBAAkB;AAChB,aAAO,KAAK,SAAS,OAAO,aAAW,QAAQ,WAAW,SAAS;AAAA,IACrE;AAAA,EACD;AAAA,EAED,OAAO,SAAS;AACdA,kBAAAA,MAAY,MAAA,OAAA,mCAAA,uBAAuB;AACnCA,kBAAAA,MAAA,MAAA,OAAA,mCAAY,WAAW,OAAO;AAE9B,QAAI,QAAQ,IAAI;AACd,WAAK,YAAY,QAAQ;AACzBA,oBAAA,MAAA,MAAA,OAAA,mCAAY,WAAW,KAAK,SAAS;AACrC,WAAK,kBAAkB;AAAA,WAClB;AACLA,oBAAAA,wDAAc,UAAU;AACxB,WAAK,QAAQ;AAAA,IACf;AAGAA,kBAAAA,MAAI,IAAI,kBAAkB,KAAK,gBAAgB;AAAA,EAChD;AAAA,EAED,SAAS;AACPA,kBAAAA,MAAA,MAAA,OAAA,mCAAY,0BAA0B;AAEtC,QAAI,KAAK,WAAW;AAElB,WAAK,oBAAoB;AAGzB,WAAK,mBAAmB,KAAK,MAAM;AAEjC,aAAK,qBAAqB;AAAA,OAC3B;AAAA,IACH;AAAA,EACD;AAAA,EAED,oBAAoB;AAClB,SAAK,oBAAoB,QAAQ,MAAM;AACrCA,oBAAAA,MAAI,oBAAoB;AAAA,KACzB;AAAA,EACF;AAAA,EAED,WAAW;AAETA,kBAAAA,MAAI,KAAK,kBAAkB,KAAK,gBAAgB;AAAA,EACjD;AAAA,EAED,SAAS;AAAA;AAAA,IAEP,MAAM,sBAAsB,SAAS,eAAe,OAAO;AACzD,aAAO,MAAM,KAAK,aAAa,eAAe,SAAS,YAAY;AAAA,IACpE;AAAA,IACD,MAAM,sBAAsB,MAAM;AAChC,aAAO,MAAM,KAAK,aAAa,sBAAsB,IAAI;AAAA,IAC1D;AAAA,IACD,MAAM,yBAAyB,MAAM;AAEnCA,oBAAAA,uDAAa,8CAA8C;AAAA,IAC5D;AAAA,IACD,MAAM,oBAAoB,SAAS;AAEjCA,oBAAAA,MAAa,MAAA,QAAA,mCAAA,yCAAyC;AAAA,IACvD;AAAA,IACD,MAAM,mBAAmB,SAAS;AAEhCA,oBAAAA,MAAa,MAAA,QAAA,mCAAA,wCAAwC;AAAA,IACtD;AAAA,IACD,MAAM,sBAAsB,MAAM;AAChC,aAAO,MAAM,KAAK,aAAa,sBAAsB,IAAI;AAAA,IAC1D;AAAA,IACD,MAAM,mBAAmB,QAAQ;AAC/B,aAAO,MAAM,KAAK,aAAa,wBAAwB,MAAM;AAAA,IAC9D;AAAA;AAAA,IAGD,SAAS;AACPA,oBAAAA,MAAI,aAAa;AAAA,IAClB;AAAA;AAAA,IAGD,MAAM,oBAAoB;AACxB,UAAI,CAAC,KAAK;AAAW;AAErB,UAAI;AACF,aAAK,QAAQ;AACbA,sBAAY,MAAA,MAAA,OAAA,mCAAA,oBAAoB,KAAK,SAAS;AAG9C,cAAM,KAAK,sBAAsB,KAAK,SAAS;AAE/C,YAAI,KAAK,eAAe;AACtBA,wBAAAA,MAAA,MAAA,OAAA,mCAAY,kBAAkB;AAC9BA,wBAAA,MAAA,MAAA,OAAA,mCAAY,WAAW,KAAK,cAAc,MAAM;AAChDA,wBAAY,MAAA,MAAA,OAAA,mCAAA,UAAU,KAAK,cAAc,WAAW;AACpDA,wBAAY,MAAA,MAAA,OAAA,mCAAA,aAAa,KAAK,aAAa;AAG3C,eAAK,WAAW;AAAA,YACd,aAAa,KAAK,cAAc,eAAe;AAAA,YAC/C,WAAW,KAAK,cAAc,aAAa;AAAA,UAC7C;AAGA,eAAK,eAAe;AAAA,YAClB;AAAA,cACE,IAAI;AAAA,cACJ,UAAU,KAAK,cAAc;AAAA,cAC7B,UAAU,KAAK,cAAc;AAAA,cAC7B,QAAQ;AAAA,cACR,WAAW;AAAA,YACb;AAAA,UACF;AAGA,mBAAS,IAAI,GAAG,IAAI,KAAK,cAAc,qBAAqB,KAAK;AAC/D,iBAAK,aAAa,KAAK;AAAA,cACrB,IAAI,eAAe,CAAC;AAAA,cACpB,UAAU,QAAQ,CAAC;AAAA,cACnB,UAAU,KAAK,CAAC;AAAA,cAChB,QAAQ;AAAA,cACR,WAAW;AAAA,aACZ;AAAA,UACH;AAGA,gBAAM,KAAK,oBAAoB;AAE/BA,wBAAY,MAAA,MAAA,OAAA,mCAAA,oBAAoB,KAAK,aAAa;AAAA,eAC7C;AACL,eAAK,QAAQ;AAAA,QACf;AAAA,MAEA,SAAO,OAAO;AACdA,sBAAAA,MAAc,MAAA,SAAA,mCAAA,oBAAoB,KAAK;AACvC,aAAK,QAAQ,MAAM,WAAW;AAAA,MAChC;AAAA,IACD;AAAA;AAAA,IAGD,MAAM,sBAAsB;AAC1B,UAAI;AACF,cAAM,WAAW,MAAM,KAAK,mBAAmB,KAAK,SAAS;AAC7D,aAAK,WAAW,YAAY,CAAC;AAC7BA,sBAAY,MAAA,MAAA,OAAA,mCAAA,oBAAoB,KAAK,QAAQ;AAAA,MAC7C,SAAO,OAAO;AACdA,sBAAAA,MAAc,MAAA,SAAA,mCAAA,oBAAoB,KAAK;AACvC,aAAK,WAAW,CAAC;AAAA,MACnB;AAAA,IACD;AAAA;AAAA,IAGD,MAAM,oBAAoB,GAAG;AAC3B,YAAM,WAAW,EAAE,OAAO;AAG1B,UAAI,YAAY,CAAC,KAAK,eAAe;AAEnC,aAAK,kBAAkB;AAAA,UACrB,GAAG,KAAK;AAAA,UACR,aAAa;AAAA,QACf;AAGAA,4BAAI,eAAe,mBAAmB,KAAK,SAAS,IAAI,KAAK,eAAe;AAE5EA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,SAAS;AAAA,UACT,YAAY;AAAA,UACZ,YAAY;AAAA,UACZ,aAAa;AAAA,UACb,SAAS,CAAC,QAAQ;AAChB,gBAAI,IAAI,SAAS;AAEfA,4BAAAA,MAAI,WAAW;AAAA,gBACb,KAAK,gCAAgC,KAAK,cAAc,OAAO;AAAA,eAChE;AAAA,mBACI;AAEL,mBAAK,kBAAkB,CAAC;AACxBA,4BAAG,MAAC,kBAAkB,mBAAmB,KAAK,SAAS,EAAE;AAEzD,mBAAK,aAAa;AAAA,YACpB;AAAA,UACF;AAAA,SACD;AACD;AAAA,MACF;AAEA,UAAI;AACF,cAAM,KAAK,sBAAsB;AAAA,UAC/B,WAAW,KAAK;AAAA,UAChB,UAAU;AAAA,YACR,aAAa;AAAA,YACb,WAAW,KAAK,SAAS;AAAA,UAC3B;AAAA,SACD;AAED,aAAK,SAAS,cAAc;AAE5BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO,WAAW,YAAY;AAAA,UAC9B,MAAM;AAAA,SACP;AAAA,MAED,SAAO,OAAO;AACdA,sBAAAA,MAAc,MAAA,SAAA,mCAAA,sBAAsB,KAAK;AACzCA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,SACP;AAAA,MACH;AAAA,IACD;AAAA;AAAA,IAGD,MAAM,kBAAkB,GAAG;AACzB,YAAM,WAAW,EAAE,OAAO;AAE1B,UAAI;AACF,cAAM,KAAK,sBAAsB;AAAA,UAC/B,WAAW,KAAK;AAAA,UAChB,UAAU;AAAA,YACR,aAAa,KAAK,SAAS;AAAA,YAC3B,WAAW;AAAA,UACb;AAAA,SACD;AAED,aAAK,SAAS,YAAY;AAE1BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO,WAAW,YAAY;AAAA,UAC9B,MAAM;AAAA,SACP;AAAA,MAED,SAAO,OAAO;AACdA,sBAAAA,MAAc,MAAA,SAAA,mCAAA,oBAAoB,KAAK;AACvCA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,SACP;AAAA,MACH;AAAA,IACD;AAAA;AAAA,IAGD,kBAAkB,aAAa;AAC7B,WAAK,eAAe;AACpB,WAAK,MAAM,YAAY,KAAK;AAAA,IAC7B;AAAA;AAAA,IAGD,MAAM,gBAAgB;AACpB,UAAI,CAAC,KAAK;AAAc;AAExB,UAAI;AACF,cAAM,KAAK,yBAAyB;AAAA,UAClC,WAAW,KAAK;AAAA,UAChB,eAAe,KAAK,aAAa;AAAA,SAClC;AAGD,cAAM,QAAQ,KAAK,aAAa,UAAU,OAAK,EAAE,OAAO,KAAK,aAAa,EAAE;AAC5E,YAAI,QAAQ,IAAI;AACd,eAAK,aAAa,OAAO,OAAO,CAAC;AAAA,QACnC;AAGA,YAAI,KAAK,eAAe;AACtB,eAAK,cAAc;AAAA,QACrB;AAEAA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,SACP;AAED,aAAK,eAAe;AAAA,MAEpB,SAAO,OAAO;AACdA,sBAAAA,MAAc,MAAA,SAAA,mCAAA,mBAAmB,KAAK;AACtCA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO,MAAM,WAAW;AAAA,UACxB,MAAM;AAAA,SACP;AAAA,MACH;AAAA,IACD;AAAA;AAAA,IAGD,eAAe;AACb,WAAK,eAAe;AAAA,IACrB;AAAA;AAAA,IAGD,MAAM,cAAc,WAAW,QAAQ;;AACrC,UAAI;AACF,cAAM,KAAK,sBAAsB;AAAA,UAC/B;AAAA,UACA,QAAQ,WAAW,aAAa,YAAY;AAAA,SAC7C;AAGD,cAAM,UAAU,KAAK,SAAS,KAAK,OAAK,EAAE,OAAO,SAAS;AAC1D,YAAI,SAAS;AACX,kBAAQ,SAAS;AAAA,QACnB;AAGA,YAAI,WAAW,YAAY;AACzB,cAAI,SAAS;AACX,iBAAK,aAAa,KAAK;AAAA,cACrB,IAAI,QAAQ;AAAA,cACZ,UAAU,QAAQ;AAAA,cAClB,UAAU,QAAQ;AAAA,cAClB,QAAQ,QAAQ;AAAA,cAChB,WAAW;AAAA,aACZ;AAAA,UACH;AAEA,cAAI,KAAK,eAAe;AACtB,iBAAK,cAAc;AAAA,UACrB;AAAA,QACF;AAEAA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO,WAAW,aAAa,UAAU;AAAA,UACzC,MAAM;AAAA,SACP;AAGDA,sBAAG,MAAC,MAAM,sBAAsB;AAAA,UAC9B,SAAS,KAAK;AAAA,UACd;AAAA,UACA,uBAAqB,UAAK,kBAAL,mBAAoB,wBAAuB;AAAA,SACjE;AAAA,MAED,SAAO,OAAO;AACdA,sBAAAA,MAAA,MAAA,SAAA,mCAAc,kBAAkB,KAAK;AAGrC,YAAI,MAAM,eAAe,MAAM,SAAS;AACtCA,wBAAY,MAAA,MAAA,OAAA,mCAAA,0BAA0B,MAAM,OAAO;AAGnDA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,SAAS,MAAM,WAAW;AAAA,YAC1B,YAAY;AAAA,YACZ,YAAY;AAAA,YACZ,aAAa;AAAA,YACb,SAAS,CAAC,QAAQ;AAChB,kBAAI,IAAI,SAAS;AAEfA,8BAAAA,MAAI,WAAW;AAAA,kBACb,KAAK,gCAAgC,MAAM,OAAO;AAAA,iBACnD;AAAA,cACH;AAAA,YACF;AAAA,WACD;AAAA,eACI;AAELA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO,MAAM,WAAW;AAAA,YACxB,MAAM;AAAA,WACP;AAAA,QACH;AAAA,MACF;AAAA,IACD;AAAA;AAAA,IAGD,uBAAuB;AACrB,UAAI,CAAC,KAAK,eAAe;AACvB,aAAK,kBAAkB;AACvB;AAAA,MACF;AACA,WAAK,eAAe;AAAA,IACrB;AAAA;AAAA,IAGD,sBAAsB;AACpB,UAAI,CAAC,KAAK,eAAe;AACvB,aAAK,kBAAkB;AACvB;AAAA,MACF;AACA,WAAK,kBAAkB;AAAA,IACxB;AAAA;AAAA,IAGD,oBAAoB;AAClBA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS;AAAA,QACT,aAAa;AAAA,QACb,YAAY;AAAA,QACZ,SAAS,CAAC,QAAQ;AAChB,cAAI,IAAI,SAAS;AAEfA,0BAAAA,MAAI,WAAW;AAAA,cACb,KAAK,gCAAgC,KAAK,cAAc,OAAO;AAAA,aAChE;AAAA,UACH;AAAA,QACF;AAAA,OACD;AAAA,IACF;AAAA;AAAA,IAGD,MAAM,mBAAmB;;AACvB,UAAI;AACFA,sBAAAA,MAAY,MAAA,OAAA,mCAAA,kBAAkB;AAG9B,aAAK,QAAQ;AAGb,aAAK,aAAa,sBAAsB,IAAI;AAG5C,cAAM,KAAK,sBAAsB,KAAK,WAAW,IAAI;AAGrD,YAAI,KAAK,eAAe;AAEtB,eAAK,WAAW;AAAA,YACd,aAAa,KAAK,cAAc,eAAe;AAAA,YAC/C,WAAW,KAAK,cAAc,aAAa;AAAA,UAC7C;AAGA,gBAAM,KAAK,oBAAoB;AAE/BA,wBAAAA,MAAY,MAAA,OAAA,mCAAA,gBAAgB;AAC5BA,wBAAA,MAAA,MAAA,OAAA,mCAAY,YAAW,UAAK,kBAAL,mBAAoB,MAAM;AACjDA,wBAAA,MAAA,MAAA,OAAA,mCAAY,aAAY,UAAK,kBAAL,mBAAoB,WAAW;AACvDA,wBAAY,MAAA,MAAA,OAAA,mCAAA,aAAa,KAAK,aAAa;AAC3CA,8EAAY,WAAW,KAAK,QAAQ;AAAA,eAC/B;AACLA,wBAAAA,MAAa,MAAA,QAAA,mCAAA,uBAAuB;AACpC,eAAK,QAAQ;AAAA,QACf;AAAA,MAEA,SAAO,OAAO;AACdA,sBAAAA,MAAc,MAAA,SAAA,mCAAA,aAAa,KAAK;AAChCA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,SACP;AAAA,MACH;AAAA,IACD;AAAA;AAAA,IAGD,MAAM,iBAAiB,WAAW;AAChCA,oBAAAA,sDAAY,kBAAkB;AAC9BA,oBAAAA,sDAAY,SAAS,SAAS;AAE9B,UAAI,UAAU,SAAS,aAAa,UAAU,aAAa,kBAAkB;AAC3EA,sBAAAA,MAAA,MAAA,OAAA,mCAAY,iBAAiB;AAE7B,YAAI;AAEF,gBAAM,cAAc,UAAU;AAC9BA,wBAAAA,sDAAY,aAAa,WAAW;AAGpC,qBAAW,YAAY;AACrB,gBAAI;AAEF,oBAAM,KAAK,8BAA8B,WAAW;AAAA,YACpD,SAAO,OAAO;AACdA,4BAAAA,wDAAc,oBAAoB,KAAK;AAEvC,mBAAK,iBAAiB;AAAA,YACxB;AAAA,UACD,GAAE,GAAI;AAAA,QAEP,SAAO,OAAO;AACdA,wBAAAA,MAAA,MAAA,SAAA,mCAAc,eAAe,KAAK;AAElC,qBAAW,MAAM;AACf,iBAAK,iBAAiB;AAAA,UACvB,GAAE,GAAI;AAAA,QACT;AAAA,MACF;AAAA,IACD;AAAA;AAAA,IAGD,MAAM,8BAA8B,aAAa;AAC/C,UAAI;AACFA,sBAAAA,MAAA,MAAA,OAAA,mCAAY,uBAAuB;AACnCA,sBAAAA,MAAA,MAAA,OAAA,mCAAY,UAAU,WAAW;AAGjC,cAAM,oBAAoB,MAAM,KAAK,aAAa,4BAA4B,WAAW;AAEzF,YAAI,mBAAmB;AACrBA,wBAAAA,sDAAY,eAAe,iBAAiB;AAE5C,eAAK,YAAY,kBAAkB,SAAS;AAC5CA,wBAAA,MAAA,MAAA,OAAA,mCAAY,cAAc,KAAK,SAAS;AAGxC,eAAK,QAAQ;AAGb,cAAI,KAAK,eAAe;AACtB,iBAAK,WAAW;AAAA,cACd,aAAa,KAAK,cAAc,eAAe;AAAA,cAC/C,WAAW,KAAK,cAAc,aAAa;AAAA,YAC7C;AAGA,kBAAM,KAAK,oBAAoB;AAAA,UACjC;AAEAA,wBAAAA,MAAA,MAAA,OAAA,mCAAY,qBAAqB;AAAA,eAC5B;AACL,gBAAM,IAAI,MAAM,YAAY;AAAA,QAC9B;AAAA,MAEA,SAAO,OAAO;AACdA,sBAAAA,MAAc,MAAA,SAAA,mCAAA,oBAAoB,KAAK;AACvC,cAAM;AAAA,MACR;AAAA,IACD;AAAA;AAAA,IAGD,sBAAsB;AACpB,UAAI;AACF,cAAM,gBAAgBA,cAAAA,MAAI,eAAe,mBAAmB,KAAK,SAAS,EAAE;AAC5E,YAAI,iBAAiB,OAAO,kBAAkB,UAAU;AACtD,eAAK,kBAAkB;AACvBA,wBAAY,MAAA,MAAA,OAAA,mCAAA,WAAW,KAAK,eAAe;AAAA,QAC7C;AAAA,MACA,SAAO,OAAO;AACdA,sBAAAA,MAAc,MAAA,SAAA,mCAAA,aAAa,KAAK;AAChC,aAAK,kBAAkB,CAAC;AAAA,MAC1B;AAAA,IACD;AAAA;AAAA,IAGD,MAAM,uBAAuB;AAC3B,UAAI,OAAO,KAAK,KAAK,eAAe,EAAE,WAAW,GAAG;AAClD;AAAA,MACF;AAEA,UAAI,CAAC,KAAK,eAAe;AACvB;AAAA,MACF;AAEA,UAAI;AACFA,sBAAA,MAAA,MAAA,OAAA,mCAAY,WAAW,KAAK,eAAe;AAG3C,cAAM,cAAc;AAAA,UAClB,aAAa,KAAK,gBAAgB,gBAAgB,SAAY,KAAK,gBAAgB,cAAc,KAAK,SAAS;AAAA,UAC/G,WAAW,KAAK,gBAAgB,cAAc,SAAY,KAAK,gBAAgB,YAAY,KAAK,SAAS;AAAA,QAC3G;AAEA,cAAM,KAAK,sBAAsB;AAAA,UAC/B,WAAW,KAAK;AAAA,UAChB,UAAU;AAAA,SACX;AAGD,aAAK,WAAW,EAAE,GAAG,YAAY;AAGjC,aAAK,kBAAkB,CAAC;AACxBA,sBAAG,MAAC,kBAAkB,mBAAmB,KAAK,SAAS,EAAE;AAEzDA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,SACP;AAAA,MAED,SAAO,OAAO;AACdA,sBAAAA,MAAc,MAAA,SAAA,mCAAA,aAAa,KAAK;AAAA,MAElC;AAAA,IACD;AAAA;AAAA,IAGD,MAAM,iBAAiB;AACrB,UAAI;AACFA,sBAAAA,MAAI,YAAY,EAAE,OAAO,UAAU;AAEnC,cAAM,KAAK,oBAAoB,KAAK,SAAS;AAE7CA,sBAAAA,MAAI,YAAY;AAEhBA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,SACP;AAGD,mBAAW,MAAM;AACf,eAAK,kBAAkB;AAAA,QACxB,GAAE,IAAI;AAAA,MAEP,SAAO,OAAO;AACdA,sBAAAA,MAAI,YAAY;AAChBA,sBAAAA,MAAA,MAAA,SAAA,mCAAc,kBAAkB,KAAK;AACrCA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO,MAAM,WAAW;AAAA,UACxB,MAAM;AAAA,SACP;AAAA,MACH;AAAA,IACD;AAAA;AAAA,IAGD,oBAAoB;AAClB,WAAK,MAAM,YAAY,KAAK;AAAA,IAC7B;AAAA;AAAA,IAGD,MAAM,gBAAgB;AACpB,UAAI;AACFA,sBAAAA,MAAI,YAAY,EAAE,OAAO,UAAU;AAEnC,cAAM,KAAK,mBAAmB,KAAK,SAAS;AAE5CA,sBAAAA,MAAI,YAAY;AAEhBA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,SACP;AAGD,mBAAW,MAAM;AACfA,wBAAAA,MAAI,aAAa;AAAA,QAClB,GAAE,IAAI;AAAA,MAEP,SAAO,OAAO;AACdA,sBAAAA,MAAI,YAAY;AAChBA,sBAAAA,MAAA,MAAA,SAAA,oCAAc,kBAAkB,KAAK;AACrCA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO,MAAM,WAAW;AAAA,UACxB,MAAM;AAAA,SACP;AAAA,MACH;AAAA,IACD;AAAA;AAAA,IAGD,mBAAmB,SAAS,KAAK;AAC/B,UAAI,CAAC,OAAO,QAAQ;AAAG,eAAO;AAC9B,aAAO,KAAK,MAAO,UAAU,MAAO,GAAG;AAAA,IACxC;AAAA;AAAA,IAGD,mBAAmB,SAAS;AAC1B,UAAI,CAAC;AAAS,eAAO;AAErB,YAAM,cAAc,QAAQ,eAAe,QAAQ;AACnD,YAAM,YAAY,QAAQ,aAAa,QAAQ;AAC/C,YAAM,UAAU,QAAQ,WAAW,QAAQ;AAC3C,YAAM,gBAAgB,QAAQ,iBAAiB;AAE/C,UAAI,CAAC,aAAa;AAChB,eAAO;AAAA,MACT;AAEA,YAAM,OAAO,KAAK,WAAW,WAAW;AAExC,UAAI,CAAC,aAAa,CAAC,SAAS;AAC1B,YAAI,iBAAiB,gBAAgB,GAAG;AACtC,iBAAO,GAAG,IAAI,KAAK,aAAa;AAAA,QAClC;AACA,eAAO,GAAG,IAAI;AAAA,MAChB;AAGA,YAAM,aAAa,CAAC,YAAY;AAC9B,YAAI,CAAC;AAAS,iBAAO;AACrB,YAAI,QAAQ,SAAS,KAAK,QAAQ,SAAS,GAAG,GAAG;AAC/C,iBAAO,QAAQ,UAAU,GAAG,CAAC;AAAA,QAC/B;AACA,eAAO;AAAA,MACT;AAEA,YAAM,iBAAiB,WAAW,SAAS;AAC3C,YAAM,eAAe,WAAW,OAAO;AAGvC,UAAI,gBAAgB,GAAG;AACrB,eAAO,GAAG,IAAI,IAAI,cAAc,IAAI,YAAY,KAAK,aAAa;AAAA,MACpE;AAEA,aAAO,GAAG,IAAI,IAAI,cAAc,IAAI,YAAY;AAAA,IACjD;AAAA;AAAA,IAGD,WAAW,MAAM;AACf,UAAI,CAAC;AAAM,eAAO;AAClB,UAAI;AACF,cAAM,UAAU,IAAI,KAAK,IAAI;AAC7B,YAAI,MAAM,QAAQ,QAAS,CAAA;AAAG,iBAAO;AACrC,cAAM,OAAO,QAAQ,YAAY;AACjC,cAAM,QAAQ,OAAO,QAAQ,SAAQ,IAAK,CAAC,EAAE,SAAS,GAAG,GAAG;AAC5D,cAAM,MAAM,OAAO,QAAQ,QAAS,CAAA,EAAE,SAAS,GAAG,GAAG;AACrD,eAAO,GAAG,IAAI,IAAI,KAAK,IAAI,GAAG;AAAA,MAC9B,SAAO,OAAO;AACdA,sBAAAA,MAAA,MAAA,SAAA,oCAAc,YAAY,KAAK;AAC/B,eAAO;AAAA,MACT;AAAA,IACD;AAAA;AAAA,IAGD,eAAe,UAAU;AACvB,UAAI,CAAC;AAAU,eAAO;AACtB,UAAI;AAEF,YAAI,UAAU;AACd,YAAI,OAAO,aAAa,YAAY,SAAS,SAAS,GAAG,KAAK,SAAS,SAAS,GAAG,GAAG;AACpF,oBAAU,SAAS,QAAQ,MAAM,GAAG;AAAA,QACtC;AAEA,cAAM,OAAO,IAAI,KAAK,OAAO;AAC7B,YAAI,MAAM,KAAK,QAAS,CAAA;AAAG,iBAAO;AAElC,cAAM,OAAO,KAAK,YAAY;AAC9B,cAAM,QAAQ,OAAO,KAAK,SAAQ,IAAK,CAAC,EAAE,SAAS,GAAG,GAAG;AACzD,cAAM,MAAM,OAAO,KAAK,QAAS,CAAA,EAAE,SAAS,GAAG,GAAG;AAClD,cAAM,OAAO,OAAO,KAAK,SAAU,CAAA,EAAE,SAAS,GAAG,GAAG;AACpD,cAAM,SAAS,OAAO,KAAK,WAAY,CAAA,EAAE,SAAS,GAAG,GAAG;AACxD,eAAO,GAAG,IAAI,IAAI,KAAK,IAAI,GAAG,IAAI,IAAI,IAAI,MAAM;AAAA,MAChD,SAAO,OAAO;AACdA,sBAAAA,MAAA,MAAA,SAAA,oCAAc,YAAY,KAAK;AAC/B,eAAO;AAAA,MACT;AAAA,IACD;AAAA;AAAA,IAGD,eAAe,WAAW,SAAS;AACjC,UAAI,CAAC,aAAa,CAAC,SAAS;AAC1B,eAAO;AAAA,MACT;AACA,UAAI,aAAa,CAAC,SAAS;AACzB,eAAO;AAAA,MACT;AACA,UAAI,CAAC,aAAa,SAAS;AACzB,eAAO;AAAA,MACT;AAGA,YAAM,aAAa,CAAC,YAAY;AAC9B,YAAI,CAAC;AAAS,iBAAO;AACrB,YAAI,QAAQ,SAAS,KAAK,QAAQ,SAAS,GAAG,GAAG;AAC/C,iBAAO,QAAQ,UAAU,GAAG,CAAC;AAAA,QAC/B;AACA,eAAO;AAAA,MACT;AAEA,YAAM,iBAAiB,WAAW,SAAS;AAC3C,YAAM,eAAe,WAAW,OAAO;AAEvC,aAAO,GAAG,cAAc,IAAI,YAAY;AAAA,IACzC;AAAA;AAAA,IAGD,YAAY,OAAO;AACjB,UAAI,CAAC,SAAS,UAAU;AAAG,eAAO;AAClC,YAAM,WAAW,OAAO,KAAK;AAC7B,UAAI,MAAM,QAAQ;AAAG,eAAO;AAC5B,aAAO,SAAS,QAAQ,CAAC;AAAA,IAC1B;AAAA;AAAA,IAGD,kBAAkB;AAChB,UAAI,CAAC,KAAK;AAAe,eAAO;AAGhC,UAAI,KAAK,cAAc,gBAAgB;AACrC,eAAO,KAAK,YAAY,KAAK,cAAc,cAAc;AAAA,MAC3D;AAGA,YAAM,aAAa,KAAK,cAAc,cAAc,KAAK,cAAc,SAAS;AAChF,YAAM,kBAAkB,KAAK,cAAc,mBAAmB;AAC9D,YAAM,eAAe,aAAa;AAElC,aAAO,KAAK,YAAY,YAAY;AAAA,IACrC;AAAA;AAAA,IAGD,cAAc,QAAQ;AACpB,YAAM,YAAY;AAAA,QAChB,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,aAAa;AAAA,QACb,aAAa;AAAA,QACb,WAAW;AAAA,MACb;AACA,aAAO,UAAU,MAAM,KAAK;AAAA,IAC7B;AAAA;AAAA,IAGD,eAAe,QAAQ;AACrB,YAAM,WAAW;AAAA,QACf,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,aAAa;AAAA,QACb,aAAa;AAAA,QACb,WAAW;AAAA,MACb;AACA,aAAO,SAAS,MAAM,KAAK;AAAA,IAC5B;AAAA;AAAA,IAGD,qBAAqB,QAAQ;AAC3B,YAAM,YAAY;AAAA,QAChB,WAAW;AAAA,QACX,YAAY;AAAA,QACZ,YAAY;AAAA,MACd;AACA,aAAO,UAAU,MAAM,KAAK;AAAA,IAC7B;AAAA;AAAA,IAGD,sBAAsB,QAAQ;AAC5B,YAAM,WAAW;AAAA,QACf,WAAW;AAAA,QACX,YAAY;AAAA,QACZ,YAAY;AAAA,MACd;AACA,aAAO,SAAS,MAAM,KAAK;AAAA,IAC7B;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACnsCA,GAAG,WAAW,eAAe;"}