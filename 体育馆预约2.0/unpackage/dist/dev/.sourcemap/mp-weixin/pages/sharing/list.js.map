{"version": 3, "file": "list.js", "sources": ["pages/sharing/list.vue", "pages/sharing/list.vue?type=page"], "sourcesContent": ["<template>\n  <view class=\"container\">\n    <!-- 显示模式切换 -->\n    <view class=\"mode-switch\">\n      <view \n        class=\"mode-item\"\n        :class=\"{ active: showMode === 'joinable' }\"\n        @click=\"switchMode('joinable')\"\n      >\n        可参与\n      </view>\n      <view \n        class=\"mode-item\"\n        :class=\"{ active: showMode === 'all' }\"\n        @click=\"switchMode('all')\"\n      >\n        全部\n      </view>\n    </view>\n    \n    <!-- 筛选栏 -->\n    <view class=\"filter-section\">\n      <scroll-view class=\"filter-scroll\" scroll-x>\n        <view class=\"filter-item\" \n              :class=\"{ active: selectedStatus === '' }\" \n              @click=\"selectStatus('')\">\n          全部\n        </view>\n        <view \n          v-for=\"status in statusOptions\" \n          :key=\"status.value\" \n          class=\"filter-item\"\n          :class=\"{ active: selectedStatus === status.value }\"\n          @click=\"selectStatus(status.value)\"\n        >\n          {{ status.label }}\n        </view>\n      </scroll-view>\n      <view class=\"filter-more\" @click=\"showFilterModal\">\n        <text>筛选</text>\n      </view>\n    </view>\n    \n    <!-- 调试信息 -->\n    <view v-if=\"true\" class=\"debug-info\" style=\"background: #f0f0f0; padding: 10px; margin: 10px; border-radius: 5px;\">\n      <view style=\"margin-bottom: 5px;\"><text style=\"font-size: 12px; color: #666;\">调试信息：</text></view>\n      <view style=\"margin-bottom: 3px;\"><text style=\"font-size: 12px; color: #666;\">原始数据数量: {{ sharingOrders?.length || 0 }}</text></view>\n      <view style=\"margin-bottom: 3px;\"><text style=\"font-size: 12px; color: #666;\">筛选后数量: {{ filteredSharingOrders?.length || 0 }}</text></view>\n      <view style=\"margin-bottom: 3px;\"><text style=\"font-size: 12px; color: #666;\">加载状态: {{ loading ? '加载中' : '已完成' }}</text></view>\n      <view style=\"margin-bottom: 3px;\"><text style=\"font-size: 12px; color: #666;\">选中状态: {{ selectedStatus || '全部' }}</text></view>\n      <view style=\"margin-bottom: 3px;\"><text style=\"font-size: 12px; color: #666;\">Store状态: {{ sharingStore ? '已连接' : '未连接' }}</text></view>\n      <view v-if=\"sharingOrders && sharingOrders.length > 0\" style=\"margin-bottom: 3px;\">\n        <text style=\"font-size: 12px; color: #666;\">第一条数据: {{ JSON.stringify(sharingOrders[0]) }}</text>\n      </view>\n    </view>\n    \n    <!-- 拼场列表 -->\n    <view class=\"sharing-list\">\n      <view \n        v-for=\"sharing in filteredSharingOrders\" \n        :key=\"sharing.id\" \n        class=\"sharing-card\"\n        :class=\"{ 'full-card': sharing.status === 'FULL' || sharing.currentParticipants >= sharing.maxParticipants }\"\n        @click=\"navigateToDetail(sharing.id)\"\n      >\n        <view class=\"card-header\">\n          <view class=\"venue-info\">\n            <text class=\"venue-name\">{{ sharing.venueName || '未知场馆' }}</text>\n            <text class=\"venue-location\">📍 {{ sharing.venueLocation || '位置未知' }}</text>\n          </view>\n          <!-- 自己的拼场标识 -->\n          <view v-if=\"isMySharing(sharing)\" class=\"my-sharing-badge\">\n            <text class=\"badge-text\">我的</text>\n          </view>\n          <!-- 已满标签 -->\n          <view v-if=\"sharing.status === 'FULL' || sharing.currentParticipants >= sharing.maxParticipants\" class=\"full-badge\">\n            <text class=\"badge-text\">已满</text>\n          </view>\n          <view class=\"sharing-status\" :class=\"getStatusClass(sharing.status)\">\n            {{ getStatusText(sharing.status) }}\n          </view>\n        </view>\n        \n        <view class=\"card-content\">\n          <view class=\"time-info\">\n            <text class=\"time-icon\">🕐</text>\n            <text class=\"time-text\">{{ formatTimeRange(sharing) }}</text>\n          </view>\n          \n\n          \n          <view class=\"team-info\">\n            <text class=\"team-icon\">👥</text>\n            <text class=\"team-name\">{{ sharing.teamName || '未命名队伍' }}</text>\n          </view>\n          \n          <view class=\"participants-info\">\n            <text class=\"participants-text\">参与球队：{{ sharing.currentParticipants || 0 }}/{{ sharing.maxParticipants || 2 }}支</text>\n            <view class=\"progress-bar\">\n              <view\n                class=\"progress-fill\"\n                :style=\"{ width: getProgressWidth(sharing) + '%' }\"\n              ></view>\n            </view>\n          </view>\n\n          <!-- 倒计时显示 -->\n          <CountdownTimer\n            v-if=\"shouldShowCountdown(sharing)\"\n            :order=\"sharing\"\n            label=\"自动取消\"\n            :short=\"true\"\n            class=\"simple\"\n            @expired=\"onCountdownExpired\"\n          />\n          \n          <view class=\"price-info\">\n            <text class=\"price-label\">费用：</text>\n            <text class=\"price-value\">¥{{ formatPrice(sharing.pricePerTeam || sharing.perTeamPrice || sharing.pricePerPerson || 0) }}</text>\n            <text class=\"price-note\">（每队费用）</text>\n          </view>\n          \n          <view class=\"creator-info\">\n            <text class=\"creator-label\">发起人：</text>\n            <text class=\"creator-value\">{{ sharing.creatorUsername || '未知' }}</text>\n          </view>\n          \n          <view class=\"create-info\">\n            <text class=\"create-label\">创建时间：</text>\n            <text class=\"create-value\">{{ formatCreateTime(sharing.createdAt) }}</text>\n          </view>\n          \n          <view v-if=\"sharing.description\" class=\"description\">\n            <text>{{ sharing.description }}</text>\n          </view>\n        </view>\n        \n        <view class=\"card-actions\">\n          <view class=\"organizer-info\">\n            <text class=\"organizer-name\">{{ sharing.creatorUsername || '未知用户' }}</text>\n          </view>\n          \n          <button \n            v-if=\"canJoinSharing(sharing)\"\n            class=\"join-btn\"\n            @click.stop=\"joinSharing(sharing.id)\"\n          >\n            申请拼场\n          </button>\n          <view \n            v-else \n            class=\"join-disabled\"\n            :class=\"{ 'applied': hasAppliedToSharing(sharing) }\"\n          >\n            {{ getJoinButtonText(sharing) }}\n          </view>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 空状态 -->\n    <view v-if=\"filteredSharingOrders.length === 0 && !loading\" class=\"empty-state\">\n      <text class=\"empty-icon\">🏀</text>\n      <text class=\"empty-text\">暂无拼场订单</text>\n    </view>\n    \n    <!-- 加载状态 -->\n    <view v-if=\"loading\" class=\"loading-state\">\n      <text>加载中...</text>\n    </view>\n    \n    <!-- 加载更多 -->\n    <view v-if=\"hasMore && filteredSharingOrders.length > 0\" class=\"load-more\" @click=\"loadMore\">\n      <text>{{ loading ? '加载中...' : '加载更多' }}</text>\n    </view>\n    \n    <!-- 筛选弹窗 -->\n    <uni-popup ref=\"filterPopup\" type=\"bottom\">\n      <view class=\"filter-modal\">\n        <view class=\"modal-header\">\n          <text class=\"modal-title\">筛选条件</text>\n          <text class=\"modal-close\" @click=\"closeFilterModal\">✕</text>\n        </view>\n        \n        <view class=\"filter-content\">\n          <!-- 日期筛选 -->\n          <view class=\"filter-group\">\n            <text class=\"group-title\">活动日期</text>\n            <view class=\"date-options\">\n              <view \n                v-for=\"date in dateOptions\" \n                :key=\"date.value\" \n                class=\"date-item\"\n                :class=\"{ active: filterOptions.date === date.value }\"\n                @click=\"selectDate(date.value)\"\n              >\n                {{ date.label }}\n              </view>\n            </view>\n          </view>\n          \n          <!-- 价格筛选 -->\n          <view class=\"filter-group\">\n            <text class=\"group-title\">价格范围</text>\n            <view class=\"price-range\">\n              <input \n                v-model=\"filterOptions.minPrice\" \n                type=\"number\" \n                placeholder=\"最低价格\" \n                class=\"price-input\"\n              />\n              <text class=\"price-separator\">-</text>\n              <input \n                v-model=\"filterOptions.maxPrice\" \n                type=\"number\" \n                placeholder=\"最高价格\" \n                class=\"price-input\"\n              />\n            </view>\n          </view>\n          \n          <!-- 人数筛选 -->\n          <view class=\"filter-group\">\n            <text class=\"group-title\">参与人数</text>\n            <view class=\"participants-options\">\n              <view \n                v-for=\"participants in participantsOptions\" \n                :key=\"participants.value\" \n                class=\"participants-item\"\n                :class=\"{ active: filterOptions.participants === participants.value }\"\n                @click=\"selectParticipants(participants.value)\"\n              >\n                {{ participants.label }}\n              </view>\n            </view>\n          </view>\n        </view>\n        \n        <view class=\"modal-footer\">\n          <button class=\"reset-btn\" @click=\"resetFilter\">重置</button>\n          <button class=\"confirm-btn\" @click=\"applyFilter\">确定</button>\n        </view>\n      </view>\n    </uni-popup>\n    \n    <!-- 申请拼场弹窗 -->\n    <uni-popup ref=\"joinPopup\" type=\"bottom\">\n      <view class=\"apply-modal\">\n        <view class=\"modal-header\">\n          <text class=\"modal-title\">申请加入拼场</text>\n          <text class=\"close-btn\" @click=\"closeJoinModal\">✕</text>\n        </view>\n        \n        <view class=\"modal-content\">\n          <view class=\"form-item\">\n            <text class=\"form-label\">队伍名称</text>\n            <input \n              v-model=\"applyForm.teamName\"\n              class=\"form-input\"\n              placeholder=\"请输入队伍名称（可选）\"\n              maxlength=\"20\"\n            />\n          </view>\n          \n          <view class=\"form-item\">\n            <text class=\"form-label\">联系方式 <text class=\"required\">*</text></text>\n            <input \n              v-model=\"applyForm.contactInfo\"\n              class=\"form-input\"\n              placeholder=\"请输入手机号或微信号\"\n              maxlength=\"50\"\n            />\n          </view>\n          \n          <view class=\"form-item\">\n            <text class=\"form-label\">申请说明</text>\n            <text class=\"form-hint\">您将代表一支球队申请加入此拼场</text>\n          </view>\n          \n          <view class=\"form-item\">\n            <text class=\"form-label\">申请留言</text>\n            <textarea \n              v-model=\"applyForm.message\"\n              class=\"form-textarea\"\n              placeholder=\"请输入申请留言（可选）\"\n              maxlength=\"200\"\n            ></textarea>\n            <text class=\"char-count\">{{ applyForm.message.length }}/200</text>\n          </view>\n        </view>\n        \n        <view class=\"modal-actions\">\n          <button class=\"modal-btn cancel-btn\" @click=\"closeJoinModal\">\n            取消\n          </button>\n          <button \n            class=\"modal-btn confirm-btn\" \n            :disabled=\"!canSubmitApplication\"\n            @click=\"submitApplication\"\n          >\n            提交申请\n          </button>\n        </view>\n      </view>\n    </uni-popup>\n    \n    <!-- 悬浮按钮 -->\n    <view class=\"floating-btn\" @click=\"goToMyOrders\">\n      <text class=\"floating-btn-text\">我的拼场</text>\n    </view>\n  </view>\n</template>\n\n<script>\nimport { useSharingStore } from '@/stores/sharing.js'\nimport { useUserStore } from '@/stores/user.js'\nimport CountdownTimer from '@/components/CountdownTimer.vue'\nimport { shouldShowCountdown } from '@/utils/countdown.js'\nimport { formatDate, formatDateTime } from '@/utils/helpers.js'\n\nexport default {\n  name: 'SharingList',\n\n  components: {\n    CountdownTimer\n  },\n  \n  data() {\n    return {\n      sharingStore: null,\n      userStore: null,\n      selectedStatus: '',\n      currentSharing: null,\n      showMode: 'joinable', // 'joinable' 可参与的, 'all' 全部\n      userApplications: [], // 用户的申请记录\n      \n      // 申请表单数据\n      applyForm: {\n        teamName: '',\n        contactInfo: '',\n        message: ''\n      },\n      \n      // 状态选项\n      statusOptions: [\n        { label: '开放中', value: 'OPEN' },\n        { label: '等待对方支付', value: 'APPROVED_PENDING_PAYMENT' },\n        { label: '拼场成功', value: 'SHARING_SUCCESS' },\n        { label: '已确认', value: 'CONFIRMED' },\n        { label: '已取消', value: 'CANCELLED' },\n        { label: '已过期', value: 'EXPIRED' }\n      ],\n      \n      // 筛选选项\n      filterOptions: {\n        date: '',\n        minPrice: '',\n        maxPrice: '',\n        participants: ''\n      },\n      \n      // 日期选项\n      dateOptions: [\n        { label: '今天', value: 'today' },\n        { label: '明天', value: 'tomorrow' },\n        { label: '本周', value: 'week' },\n        { label: '本月', value: 'month' }\n      ],\n      \n      // 人数选项\n      participantsOptions: [\n        { label: '2人', value: '2' },\n        { label: '4人', value: '4' },\n        { label: '6人', value: '6' },\n        { label: '8人及以上', value: '8+' }\n      ]\n    }\n  },\n  \n  computed: {\n    sharingOrders() {\n      return this.sharingStore?.sharingOrdersGetter || []\n    },\n\n    loading() {\n      return this.sharingStore?.isLoading || false\n    },\n\n    pagination() {\n      return this.sharingStore?.getPagination || { current: 0, totalPages: 0 }\n    },\n\n    userInfo() {\n      return this.userStore?.getUserInfo || {}\n    },\n\n    filteredSharingOrders() {\n      let orders = this.sharingOrders || []\n      \n      // 根据显示模式筛选\n      if (this.showMode === 'joinable') {\n        orders = orders.filter(order => {\n          // 显示开放状态的拼场，包括：\n          // 1. 还有空位且不是自己创建的拼场（可以申请）\n          // 2. 自己已经申请过的拼场（显示状态，不能重复申请）\n          const isOpenAndAvailable = order.status === 'OPEN' && \n                                   order.currentParticipants < order.maxParticipants &&\n                                   !this.isMySharing(order)\n          \n          // 显示所有开放状态的拼场，让用户看到申请后的状态变化\n          return order.status === 'OPEN' || order.status === 'FULL'\n        })\n      }\n      // 'all' 模式显示所有状态的拼场订单，包括已满员、已确认等\n      \n      if (this.selectedStatus) {\n        orders = orders.filter(order => order.status === this.selectedStatus)\n      }\n      \n      // 应用筛选条件\n      if (this.filterOptions.minPrice) {\n        orders = orders.filter(order => (order.totalPrice || 0) >= parseFloat(this.filterOptions.minPrice))\n      }\n      \n      if (this.filterOptions.maxPrice) {\n        orders = orders.filter(order => (order.totalPrice || 0) <= parseFloat(this.filterOptions.maxPrice))\n      }\n      \n      if (this.filterOptions.participants) {\n        const targetParticipants = parseInt(this.filterOptions.participants)\n        if (this.filterOptions.participants === '8+') {\n          orders = orders.filter(order => (order.maxParticipants || 0) >= 8)\n        } else {\n          orders = orders.filter(order => (order.maxParticipants || 0) === targetParticipants)\n        }\n      }\n      \n      return orders\n    },\n    \n    hasMore() {\n      return this.pagination.current < Math.ceil(this.pagination.total / this.pagination.pageSize)\n    },\n    \n    // 是否可以提交申请\n    canSubmitApplication() {\n      return this.applyForm.contactInfo.trim().length > 0\n    }\n  },\n  \n  onLoad() {\n    // 初始化Pinia stores\n    this.sharingStore = useSharingStore()\n    this.userStore = useUserStore()\n\n    // 监听拼场数据变化\n    uni.$on('sharingDataChanged', this.onSharingDataChanged)\n    // 监听订单取消事件\n    uni.$on('orderCancelled', this.onOrderCancelled)\n    this.initData()\n  },\n\n  onUnload() {\n    // 移除监听器\n    uni.$off('sharingDataChanged', this.onSharingDataChanged)\n    uni.$off('orderCancelled', this.onOrderCancelled)\n  },\n\n  async onShow() {\n    // 强制刷新数据，确保从其他页面返回时数据是最新的\n    await this.refreshData()\n    // 加载用户申请记录\n    await this.loadUserApplications()\n  },\n  \n  onPullDownRefresh() {\n    this.refreshData()\n  },\n  \n  onReachBottom() {\n    if (this.hasMore && !this.loading) {\n      this.loadMore()\n    }\n  },\n  \n  methods: {\n    \n    // 初始化数据\n    async initData() {\n      try {\n        console.log('拼场列表页面：开始初始化数据')\n        console.log('拼场列表页面：Store状态:', this.sharingStore)\n        console.log('拼场列表页面：当前显示模式:', this.showMode)\n        \n        // 根据显示模式选择不同的API\n        const apiMethod = this.showMode === 'all' ? this.sharingStore.getAllSharingOrders : this.sharingStore.getJoinableSharingOrders\n        const result = await apiMethod({ page: 1, pageSize: 10 })\n        console.log('拼场列表页面：API返回结果:', result)\n        console.log('拼场列表页面：Store中的数据:', this.sharingOrders)\n        console.log('拼场列表页面：初始化数据完成，订单数量:', this.sharingOrders?.length || 0)\n\n        // 调试：检查第一个分享订单的数据结构\n        if (this.sharingOrders && this.sharingOrders.length > 0) {\n          console.log('第一个分享订单的完整数据结构:', this.sharingOrders[0])\n          console.log('第一个分享订单的价格字段:', {\n            pricePerTeam: this.sharingOrders[0].pricePerTeam,\n            perTeamPrice: this.sharingOrders[0].perTeamPrice,\n            pricePerPerson: this.sharingOrders[0].pricePerPerson,\n            price: this.sharingOrders[0].price,\n            totalPrice: this.sharingOrders[0].totalPrice,\n            cost: this.sharingOrders[0].cost\n          })\n          console.log('第一个分享订单的参与者信息:', {\n            currentParticipants: this.sharingOrders[0].currentParticipants,\n            maxParticipants: this.sharingOrders[0].maxParticipants,\n            participants: this.sharingOrders[0].participants,\n            participantCount: this.sharingOrders[0].participantCount\n          })\n        }\n        \n        // 强制更新视图\n        this.$forceUpdate()\n      } catch (error) {\n        console.error('拼场列表页面：初始化数据失败:', error)\n        uni.showToast({\n          title: '获取拼场数据失败',\n          icon: 'none'\n        })\n      }\n    },\n    \n    // 刷新数据\n    async refreshData() {\n      // 防止重复调用\n      if (this.loading) {\n        console.log('拼场列表页面：正在加载中，跳过重复调用')\n        return\n      }\n\n      try {\n        console.log('拼场列表页面：开始刷新数据，当前显示模式:', this.showMode)\n\n        // 根据显示模式选择不同的API\n        const apiMethod = this.showMode === 'all' ? this.sharingStore.getAllSharingOrders : this.sharingStore.getJoinableSharingOrders\n\n        // 添加时间戳参数，确保不使用缓存\n        const result = await apiMethod({\n          page: 1,\n          pageSize: 10,\n          refresh: true,\n          _t: Date.now() // 添加时间戳，防止缓存\n        })\n\n        console.log('拼场列表页面：API调用结果:', result)\n        console.log('拼场列表页面：刷新数据完成，订单数量:', this.sharingOrders?.length || 0)\n        console.log('拼场列表页面：Store中的数据:', this.sharingOrders)\n\n        // 强制更新视图\n        this.$forceUpdate()\n        uni.stopPullDownRefresh()\n      } catch (error) {\n        uni.stopPullDownRefresh()\n        console.error('拼场列表页面：刷新数据失败:', error)\n        uni.showToast({\n          title: '刷新数据失败',\n          icon: 'none'\n        })\n      }\n    },\n    \n    // 加载更多\n    async loadMore() {\n      if (this.loading || !this.hasMore) return\n      \n      try {\n        console.log('拼场列表页面：开始加载更多，当前页码:', this.pagination.current, '显示模式:', this.showMode)\n        const nextPage = this.pagination.current + 1\n        // 根据显示模式选择不同的API\n        const apiMethod = this.showMode === 'all' ? this.sharingStore.getAllSharingOrders : this.sharingStore.getJoinableSharingOrders\n        await apiMethod({ \n          page: nextPage, \n          pageSize: 10,\n          status: this.selectedStatus,\n          ...this.filterOptions\n        })\n        console.log('拼场列表页面：加载更多完成，订单数量:', this.sharingOrders?.length || 0)\n      } catch (error) {\n        console.error('拼场列表页面：加载更多失败:', error)\n        uni.showToast({\n          title: '加载更多失败',\n          icon: 'none'\n        })\n      }\n    },\n    \n    // 选择状态\n    async selectStatus(status) {\n      this.selectedStatus = status\n      try {\n        // 根据显示模式选择不同的API\n        const apiMethod = this.showMode === 'all' ? this.sharingStore.getAllSharingOrders : this.sharingStore.getJoinableSharingOrders\n        await apiMethod({ \n          page: 1, \n          pageSize: 10, \n          status: status,\n          refresh: true,\n          ...this.filterOptions\n        })\n      } catch (error) {\n        console.error('筛选失败:', error)\n      }\n    },\n    \n    // 切换显示模式\n    async switchMode(mode) {\n      if (this.showMode === mode) {\n        console.log('拼场列表页面：模式未改变，跳过切换')\n        return\n      }\n\n      console.log('拼场列表页面：切换显示模式从', this.showMode, '到', mode)\n      this.showMode = mode\n      this.selectedStatus = '' // 重置状态筛选\n\n      try {\n        // 切换模式时重新加载数据\n        await this.refreshData()\n      } catch (error) {\n        console.error('切换模式失败:', error)\n        uni.showToast({\n          title: '切换模式失败，请重试',\n          icon: 'error'\n        })\n        // 恢复之前的模式\n        this.showMode = mode === 'all' ? 'joinable' : 'all'\n      }\n    },\n    \n    // 跳转到详情页\n    navigateToDetail(sharingId) {\n      uni.navigateTo({\n        url: `/pages/sharing/detail?id=${sharingId}`\n      })\n    },\n    \n    // 判断是否可以加入拼场\n    canJoinSharing(sharing) {\n      // 如果是自己创建的拼场，不能申请\n      if (this.userInfo && sharing.creatorUsername === this.userInfo.username) {\n        return false\n      }\n      \n      // 如果已经申请过，不能重复申请\n      if (this.hasAppliedToSharing(sharing.id)) {\n        return false\n      }\n      \n      return sharing.status === 'OPEN' && \n             (sharing.currentParticipants || 0) < (sharing.maxParticipants || 0)\n    },\n    \n    // 判断是否为自己的拼场\n    isMySharing(sharing) {\n      return this.userInfo && sharing.creatorUsername === this.userInfo.username\n    },\n    \n    // 判断是否已申请过该拼场\n    hasAppliedToSharing(sharingId) {\n      return this.userApplications.some(app => \n        app.sharingOrder && app.sharingOrder.id === sharingId\n      )\n    },\n    \n    // 加载用户申请记录\n    async loadUserApplications() {\n      try {\n        const applications = await this.sharingStore.getMySharingRequests()\n        this.userApplications = applications || []\n      } catch (error) {\n        console.error('加载用户申请记录失败:', error)\n        this.userApplications = []\n      }\n    },\n    \n    // 加入拼场\n    joinSharing(sharingId) {\n      this.currentSharing = this.sharingOrders.find(s => s.id === sharingId)\n      // 重置表单\n      this.resetApplyForm()\n      this.$refs.joinPopup.open()\n    },\n    \n    // 重置申请表单\n    resetApplyForm() {\n      this.applyForm = {\n        teamName: '', // 队名默认为空，让用户自己填写\n        contactInfo: this.userInfo?.phone || this.userInfo?.mobile || '', // 联系方式默认为手机号\n        message: ''\n      }\n    },\n    \n    // 关闭加入弹窗\n    closeJoinModal() {\n      this.$refs.joinPopup.close()\n      this.currentSharing = null\n      this.resetApplyForm()\n    },\n    \n    // 提交申请\n    async submitApplication() {\n      if (!this.canSubmitApplication) {\n        uni.showToast({\n          title: '请填写联系方式',\n          icon: 'none'\n        })\n        return\n      }\n      \n      try {\n        uni.showLoading({ title: '提交中...' })\n        \n        const applicationData = {\n          teamName: this.applyForm.teamName.trim(),\n          contactInfo: this.applyForm.contactInfo.trim(),\n          message: this.applyForm.message.trim()\n        }\n        \n        const response = await this.sharingStore.applySharingOrder({\n          orderId: this.currentSharing.id,\n          data: applicationData\n        })\n\n        uni.hideLoading()\n        this.closeJoinModal()\n\n        // 检查申请是否被自动通过（需要支付）\n        if (response && response.data && response.data.status === 'APPROVED_PENDING_PAYMENT') {\n          // 自动通过，提示用户并引导支付\n          uni.showModal({\n            title: '申请已通过',\n            content: '您的拼场申请已自动通过！请在30分钟内完成支付以确认参与。',\n            showCancel: false,\n            confirmText: '去支付',\n            success: () => {\n              // 跳转到支付页面，使用虚拟订单ID\n              uni.navigateTo({\n                url: `/pages/payment/index?orderId=${-response.data.id}&type=sharing&from=sharing-list`\n              })\n            }\n          })\n        } else if (response && response.data && response.data.status === 'APPROVED') {\n          // 旧的自动通过逻辑（兼容性）\n          uni.showModal({\n            title: '申请已通过',\n            content: '您的拼场申请已自动通过！请完成支付以确认参与。',\n            showCancel: false,\n            confirmText: '去支付',\n            success: () => {\n              // 跳转到支付页面，使用虚拟订单ID\n              uni.navigateTo({\n                url: `/pages/payment/index?orderId=${-response.data.id}&type=sharing&from=sharing-list`\n              })\n            }\n          })\n        } else {\n          // 普通提交，显示等待审核提示\n          uni.showToast({\n            title: response?.message || '申请提交成功，等待审核',\n            icon: 'success',\n            duration: 2000\n          })\n        }\n\n        // 刷新列表和用户申请记录\n        await this.refreshData()\n        await this.loadUserApplications()\n        \n      } catch (error) {\n        uni.hideLoading()\n        console.error('加入拼场失败:', error)\n        uni.showToast({\n          title: error.message || '加入失败',\n          icon: 'error'\n        })\n      }\n    },\n    \n    // 显示筛选弹窗\n    showFilterModal() {\n      this.$refs.filterPopup.open()\n    },\n    \n    // 关闭筛选弹窗\n    closeFilterModal() {\n      this.$refs.filterPopup.close()\n    },\n    \n    // 选择日期\n    selectDate(date) {\n      this.filterOptions.date = date\n    },\n    \n    // 选择人数\n    selectParticipants(participants) {\n      this.filterOptions.participants = participants\n    },\n    \n    // 重置筛选\n    resetFilter() {\n      this.filterOptions = {\n        date: '',\n        minPrice: '',\n        maxPrice: '',\n        participants: ''\n      }\n    },\n    \n    // 应用筛选\n    async applyFilter() {\n      this.closeFilterModal()\n      try {\n        await this.sharingStore.getJoinableSharingOrders({\n          page: 1, \n          pageSize: 10,\n          status: this.selectedStatus,\n          refresh: true,\n          ...this.filterOptions\n        })\n      } catch (error) {\n        console.error('应用筛选失败:', error)\n      }\n    },\n    \n    // 格式化日期\n    formatDate(date) {\n      if (!date) return '--'\n      return formatDate(date, 'MM-DD')\n    },\n    \n    // 格式化时间\n    formatDateTime(datetime) {\n      if (!datetime) return '--'\n      return formatDateTime(datetime)\n    },\n    \n    // 格式化时间段\n    formatTimeSlot(startTime, endTime) {\n      if (!startTime && !endTime) {\n        return '时间未指定'\n      }\n      if (startTime && !endTime) {\n        return startTime\n      }\n      if (!startTime && endTime) {\n        return endTime\n      }\n      return `${startTime}-${endTime}`\n    },\n    \n    // 格式化时间范围显示（参考booking/list）\n    formatTimeRange(sharing) {\n      const startTime = sharing.startTime || sharing.bookingStartTime\n      const endTime = sharing.endTime || sharing.bookingEndTime\n      const timeSlotCount = sharing.timeSlotCount || 1\n      \n      if (!startTime || !endTime) {\n        return '时间待定'\n      }\n      \n      // 格式化时间显示（去掉秒数）\n      const formatTime = (timeStr) => {\n        if (!timeStr) return ''\n        // 如果是完整的时间格式（HH:mm:ss），只取前5位\n        if (timeStr.length > 5 && timeStr.includes(':')) {\n          return timeStr.substring(0, 5)\n        }\n        return timeStr\n      }\n      \n      const formattedStart = formatTime(startTime)\n      const formattedEnd = formatTime(endTime)\n      \n      // 添加日期信息\n      const dateStr = this.formatDate(sharing.bookingDate)\n      \n      // 如果有多个时间段，显示时间段数量\n      if (timeSlotCount > 1) {\n        return `${dateStr} ${formattedStart} - ${formattedEnd} (${timeSlotCount}个时段)`\n      }\n      \n      return `${dateStr} ${formattedStart} - ${formattedEnd}`\n    },\n    \n    // 格式化创建时间（参考booking/list）\n    formatCreateTime(datetime) {\n      if (!datetime) return '--'\n      try {\n        // 处理iOS兼容性问题：将空格分隔的日期时间格式转换为T分隔的ISO格式\n        let dateStr = datetime\n        if (typeof dateStr === 'string' && dateStr.includes(' ') && !dateStr.includes('T')) {\n          dateStr = dateStr.replace(' ', 'T')\n        }\n        \n        const date = new Date(dateStr)\n        if (isNaN(date.getTime())) return '--'\n        const year = date.getFullYear()\n        const month = String(date.getMonth() + 1).padStart(2, '0')\n        const day = String(date.getDate()).padStart(2, '0')\n        const hour = String(date.getHours()).padStart(2, '0')\n        const minute = String(date.getMinutes()).padStart(2, '0')\n        return `${year}-${month}-${day} ${hour}:${minute}`\n      } catch (error) {\n        console.error('时间格式化错误:', error)\n        return '--'\n      }\n    },\n    \n    // 格式化加入时间\n    formatJoinTime() {\n      if (!this.currentSharing) return ''\n      return `${this.formatDate(this.currentSharing.bookingDate)} ${this.formatTimeSlot(this.currentSharing.startTime, this.currentSharing.endTime)}`\n    },\n    \n    // 格式化价格显示\n    formatPrice(price) {\n      if (!price && price !== 0) return '0'\n      const numPrice = Number(price)\n      if (isNaN(numPrice)) return '0'\n      return numPrice.toFixed(2)\n    },\n    \n    // 获取进度条宽度\n    getProgressWidth(sharing) {\n      const current = sharing.currentParticipants || 0\n      const max = sharing.maxParticipants || 2\n      return Math.min((current / max) * 100, 100)\n    },\n\n    // 判断是否显示倒计时\n    shouldShowCountdown(order) {\n      return shouldShowCountdown(order)\n    },\n\n    // 倒计时过期处理\n    onCountdownExpired(order) {\n      console.log('拼场订单倒计时过期:', order.orderNo)\n      // 刷新数据，更新订单状态\n      this.refreshData()\n    },\n    \n    // 获取状态样式类\n    getStatusClass(status) {\n      const statusMap = {\n        'OPEN': 'status-open',\n        'FULL': 'status-full',\n        'CONFIRMED': 'status-confirmed',\n        'CANCELLED': 'status-cancelled',\n        'EXPIRED': 'status-expired'\n      }\n      return statusMap[status] || 'status-open'\n    },\n    \n    // 获取状态文本\n    getStatusText(status) {\n      const statusMap = {\n        'OPEN': '开放中(1/2)',\n        'APPROVED_PENDING_PAYMENT': '等待对方支付',\n        'SHARING_SUCCESS': '拼场成功(2人)',\n        'CONFIRMED': '已确认',\n        'CANCELLED': '已取消',\n        'EXPIRED': '已过期'\n      }\n      return statusMap[status] || '开放中'\n    },\n\n    // 处理拼场数据变化\n    onSharingDataChanged(data) {\n      console.log('拼场列表页面：收到数据变化通知:', data)\n\n      // 查找对应的订单并更新\n      if (this.sharingOrders && data.orderId) {\n        const order = this.sharingOrders.find(o => o.id == data.orderId)\n        if (order) {\n          // 更新参与人数\n          if (data.currentParticipants !== undefined) {\n            order.currentParticipants = data.currentParticipants\n          }\n\n          // 如果是批准申请，可能需要更新状态\n          if (data.action === 'APPROVED' && order.currentParticipants >= 2) {\n            order.status = 'SHARING_SUCCESS'\n          }\n\n          console.log('拼场列表页面：已更新订单数据:', order)\n        }\n      }\n\n      // 强制刷新数据以确保一致性\n      setTimeout(() => {\n        this.refreshData()\n      }, 1000)\n    },\n\n    // 处理订单取消事件\n    onOrderCancelled(data) {\n      console.log('拼场列表页面：收到订单取消通知:', data)\n\n      if (data.type === 'booking' && data.orderId) {\n        console.log('检测到预约订单取消，刷新拼场大厅数据')\n\n        // 延迟刷新数据，确保后端状态已同步\n        setTimeout(() => {\n          console.log('开始刷新拼场大厅数据...')\n          this.refreshData()\n        }, 1500) // 稍微延长延迟时间，确保后端处理完成\n      }\n    },\n\n    // 获取加入按钮文本\n    getJoinButtonText(sharing) {\n      // 如果是自己的拼场\n      if (this.isMySharing(sharing)) {\n        return '我的拼场'\n      }\n      // 如果已申请过该拼场\n      if (this.hasAppliedToSharing(sharing)) {\n        return '已申请'\n      }\n      if (sharing.status === 'FULL') {\n        return '已满员'\n      }\n      if (sharing.status === 'CONFIRMED') {\n        return '已确认'\n      }\n      if (sharing.status === 'CANCELLED') {\n        return '已取消'\n      }\n      if (sharing.status === 'EXPIRED') {\n        return '已过期'\n      }\n      return '申请拼场'\n    },\n    \n    // 导航到我的拼场页面\n    goToMyOrders() {\n      uni.navigateTo({\n        url: '/pages/sharing/my-orders'\n      })\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.container {\n  background-color: #f5f5f5;\n  min-height: 100vh;\n}\n\n// 显示模式切换\n.mode-switch {\n  display: flex;\n  background-color: #ffffff;\n  margin: 20rpx 30rpx;\n  border-radius: 12rpx;\n  padding: 8rpx;\n  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);\n  \n  .mode-item {\n    flex: 1;\n    text-align: center;\n    padding: 16rpx 0;\n    border-radius: 8rpx;\n    font-size: 28rpx;\n    color: #666666;\n    transition: all 0.3s ease;\n    \n    &.active {\n      background-color: #007aff;\n      color: #ffffff;\n      font-weight: bold;\n    }\n  }\n }\n\n.container {\n  padding-bottom: 120rpx;\n}\n\n// 悬浮按钮\n.floating-btn {\n  position: fixed;\n  bottom: 120rpx;\n  right: 30rpx;\n  width: 120rpx;\n  height: 120rpx;\n  background-color: #ff6b35;\n  border-radius: 60rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  box-shadow: 0 8rpx 20rpx rgba(255, 107, 53, 0.3);\n  z-index: 999;\n  \n  .floating-btn-text {\n    font-size: 24rpx;\n    color: #ffffff;\n    font-weight: 500;\n    text-align: center;\n    line-height: 1.2;\n  }\n}\n\n// 筛选栏\n.filter-section {\n  display: flex;\n  background-color: #ffffff;\n  padding: 20rpx 30rpx;\n  border-bottom: 1rpx solid #f0f0f0;\n  \n  .filter-scroll {\n    flex: 1;\n    white-space: nowrap;\n    \n    .filter-item {\n      display: inline-block;\n      padding: 12rpx 24rpx;\n      margin-right: 20rpx;\n      background-color: #f5f5f5;\n      border-radius: 30rpx;\n      font-size: 24rpx;\n      color: #666666;\n      \n      &.active {\n        background-color: #ff6b35;\n        color: #ffffff;\n      }\n    }\n  }\n  \n  .filter-more {\n    padding: 12rpx 24rpx;\n    background-color: #f5f5f5;\n    border-radius: 30rpx;\n    font-size: 24rpx;\n    color: #666666;\n  }\n}\n\n// 拼场列表\n.sharing-list {\n  padding: 20rpx 30rpx;\n  \n  .sharing-card {\n    background-color: #ffffff;\n    border-radius: 16rpx;\n    padding: 30rpx;\n    margin-bottom: 20rpx;\n    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);\n    \n    &.full-card {\n      background-color: #f8f8f8;\n      opacity: 0.7;\n      \n      .venue-name {\n        color: #999999 !important;\n      }\n      \n      .time-text, .team-name {\n        color: #999999 !important;\n      }\n    }\n    \n    .card-header {\n      display: flex;\n      justify-content: space-between;\n      align-items: flex-start;\n      margin-bottom: 20rpx;\n      \n      .venue-info {\n        flex: 1;\n        \n        .venue-name {\n          font-size: 32rpx;\n          font-weight: bold;\n          color: #333333;\n          display: block;\n          margin-bottom: 8rpx;\n        }\n        \n        .venue-location {\n          font-size: 24rpx;\n          color: #999999;\n        }\n      }\n      \n      .my-sharing-badge {\n        padding: 6rpx 12rpx;\n        background-color: #ff6b35;\n        border-radius: 16rpx;\n        margin-right: 12rpx;\n        \n        .badge-text {\n          font-size: 20rpx;\n          color: #ffffff;\n          font-weight: bold;\n        }\n      }\n      \n      .full-badge {\n        padding: 6rpx 12rpx;\n        background-color: #999999;\n        border-radius: 16rpx;\n        margin-right: 12rpx;\n        \n        .badge-text {\n          font-size: 20rpx;\n          color: #ffffff;\n          font-weight: bold;\n        }\n      }\n      \n      .sharing-status {\n        padding: 8rpx 16rpx;\n        border-radius: 20rpx;\n        font-size: 22rpx;\n        \n        &.status-open {\n          background-color: #e8f5e8;\n          color: #52c41a;\n        }\n        \n        &.status-full {\n          background-color: #fff2e8;\n          color: #fa8c16;\n        }\n        \n        &.status-confirmed {\n          background-color: #e6f7ff;\n          color: #1890ff;\n        }\n        \n        &.status-cancelled {\n          background-color: #fff1f0;\n          color: #ff4d4f;\n        }\n        \n        &.status-expired {\n          background-color: #f6f6f6;\n          color: #999999;\n        }\n      }\n    }\n    \n    .card-content {\n      .time-info, .team-info {\n        display: flex;\n        align-items: center;\n        margin-bottom: 16rpx;\n        \n        .time-icon, .team-icon {\n          font-size: 28rpx;\n          margin-right: 12rpx;\n        }\n        \n        .time-text, .team-name {\n          font-size: 28rpx;\n          color: #333333;\n        }\n      }\n      \n      .participants-info {\n        margin-bottom: 16rpx;\n        \n        .participants-text {\n          font-size: 26rpx;\n          color: #666666;\n          margin-bottom: 8rpx;\n        }\n        \n        .progress-bar {\n          height: 8rpx;\n          background-color: #f0f0f0;\n          border-radius: 4rpx;\n          overflow: hidden;\n          \n          .progress-fill {\n            height: 100%;\n            background-color: #ff6b35;\n            transition: width 0.3s ease;\n          }\n        }\n      }\n      \n      .price-info, .creator-info, .create-info {\n        display: flex;\n        align-items: center;\n        margin-bottom: 12rpx;\n        \n        .price-label, .creator-label, .create-label {\n          font-size: 24rpx;\n          color: #999999;\n          margin-right: 8rpx;\n        }\n        \n        .price-value {\n          font-size: 28rpx;\n          font-weight: bold;\n          color: #ff6b35;\n        }\n        \n        .price-note {\n          font-size: 20rpx;\n          color: #999999;\n          margin-left: 8rpx;\n        }\n        \n        .creator-value, .create-value {\n          font-size: 24rpx;\n          color: #666666;\n        }\n      }\n      \n      .description {\n        margin-top: 16rpx;\n        padding: 16rpx;\n        background-color: #f8f8f8;\n        border-radius: 8rpx;\n        \n        text {\n          font-size: 24rpx;\n          color: #666666;\n          line-height: 1.5;\n        }\n      }\n    }\n    \n    .card-actions {\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      margin-top: 20rpx;\n      padding-top: 20rpx;\n      border-top: 1rpx solid #f0f0f0;\n      \n      .organizer-info {\n        display: flex;\n        align-items: center;\n        \n        .organizer-name {\n          font-size: 24rpx;\n          color: #666666;\n        }\n      }\n      \n      .join-btn {\n        padding: 12rpx 24rpx;\n        background-color: #ff6b35;\n        color: #ffffff;\n        border-radius: 24rpx;\n        font-size: 24rpx;\n        border: none;\n      }\n      \n      .join-disabled {\n        padding: 12rpx 24rpx;\n        background-color: #f0f0f0;\n        color: #999999;\n        border-radius: 24rpx;\n        font-size: 24rpx;\n        \n        &.applied {\n          background-color: #e8f4fd;\n          color: #1890ff;\n          border: 1rpx solid #91d5ff;\n        }\n      }\n    }\n\n    // 倒计时样式\n    .countdown-container.simple {\n      margin-top: 12rpx;\n      padding: 6rpx 10rpx;\n      font-size: 20rpx;\n\n      .countdown-icon {\n        font-size: 22rpx;\n      }\n\n      .countdown-content {\n        .countdown-time {\n          font-size: 20rpx;\n        }\n      }\n    }\n  }\n}\n\n// 空状态\n.empty-state {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 120rpx 60rpx;\n  \n  .empty-icon {\n    font-size: 120rpx;\n    margin-bottom: 30rpx;\n  }\n  \n  .empty-text {\n    font-size: 28rpx;\n    color: #999999;\n  }\n}\n\n// 加载状态\n.loading-state {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  padding: 60rpx;\n  \n  text {\n    font-size: 28rpx;\n    color: #999999;\n  }\n}\n\n// 加载更多\n.load-more {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  padding: 40rpx;\n  \n  text {\n    font-size: 28rpx;\n    color: #999999;\n  }\n}\n\n// 筛选弹窗\n.filter-modal {\n  background-color: #ffffff;\n  border-radius: 24rpx 24rpx 0 0;\n  padding: 40rpx 30rpx;\n  max-height: 80vh;\n  \n  .modal-header {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    margin-bottom: 40rpx;\n    \n    .modal-title {\n      font-size: 32rpx;\n      font-weight: bold;\n      color: #333333;\n    }\n    \n    .modal-close {\n      font-size: 40rpx;\n      color: #999999;\n    }\n  }\n  \n  .filter-content {\n    .filter-group {\n      margin-bottom: 40rpx;\n      \n      .group-title {\n        font-size: 28rpx;\n        font-weight: bold;\n        color: #333333;\n        margin-bottom: 20rpx;\n      }\n      \n      .date-options, .participants-options {\n        display: flex;\n        flex-wrap: wrap;\n        gap: 16rpx;\n        \n        .date-item, .participants-item {\n          padding: 16rpx 24rpx;\n          background-color: #f5f5f5;\n          border-radius: 24rpx;\n          font-size: 24rpx;\n          color: #666666;\n          \n          &.active {\n            background-color: #ff6b35;\n            color: #ffffff;\n          }\n        }\n      }\n      \n      .price-range {\n        display: flex;\n        align-items: center;\n        gap: 16rpx;\n        \n        .price-input {\n          flex: 1;\n          padding: 16rpx;\n          border: 1rpx solid #e0e0e0;\n          border-radius: 8rpx;\n          font-size: 24rpx;\n        }\n        \n        .price-separator {\n          font-size: 24rpx;\n          color: #999999;\n        }\n      }\n    }\n  }\n  \n  .modal-footer {\n    display: flex;\n    gap: 20rpx;\n    margin-top: 40rpx;\n    \n    .reset-btn, .confirm-btn {\n      flex: 1;\n      padding: 24rpx;\n      border-radius: 12rpx;\n      font-size: 28rpx;\n      border: none;\n    }\n    \n    .reset-btn {\n      background-color: #f5f5f5;\n      color: #666666;\n    }\n    \n    .confirm-btn {\n      background-color: #ff6b35;\n      color: #ffffff;\n    }\n  }\n}\n\n// 申请拼场弹窗\n.apply-modal {\n  background-color: #ffffff;\n  border-radius: 24rpx 24rpx 0 0;\n  padding: 0;\n  max-height: 80vh;\n  \n  .modal-header {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    padding: 32rpx 32rpx 0;\n    \n    .modal-title {\n      font-size: 32rpx;\n      font-weight: 600;\n      color: #333333;\n    }\n    \n    .close-btn {\n      font-size: 32rpx;\n      color: #999999;\n      padding: 8rpx;\n    }\n  }\n  \n  .modal-content {\n    padding: 32rpx;\n    max-height: 60vh;\n    overflow-y: auto;\n    \n    .form-item {\n      margin-bottom: 32rpx;\n      \n      .form-label {\n        display: block;\n        font-size: 28rpx;\n        color: #333333;\n        margin-bottom: 16rpx;\n        \n        .required {\n          color: #ff4d4f;\n        }\n      }\n      \n      .form-hint {\n        font-size: 24rpx;\n        color: #999999;\n        line-height: 1.5;\n      }\n      \n      .form-input {\n        width: 100%;\n        padding: 24rpx;\n        border: 1rpx solid #e0e0e0;\n        border-radius: 12rpx;\n        font-size: 28rpx;\n        background-color: #fafafa;\n        \n        &:focus {\n          border-color: #ff6b35;\n          background-color: #ffffff;\n        }\n      }\n      \n      .form-textarea {\n        width: 100%;\n        min-height: 120rpx;\n        padding: 24rpx;\n        border: 1rpx solid #e0e0e0;\n        border-radius: 12rpx;\n        font-size: 28rpx;\n        background-color: #fafafa;\n        resize: none;\n        \n        &:focus {\n          border-color: #ff6b35;\n          background-color: #ffffff;\n        }\n      }\n      \n      .char-count {\n        display: block;\n        text-align: right;\n        font-size: 24rpx;\n        color: #999999;\n        margin-top: 8rpx;\n      }\n    }\n  }\n  \n  .modal-actions {\n    display: flex;\n    padding: 24rpx 32rpx 32rpx;\n    gap: 24rpx;\n    \n    .modal-btn {\n      flex: 1;\n      padding: 28rpx;\n      border-radius: 12rpx;\n      font-size: 28rpx;\n      border: none;\n      \n      &.cancel-btn {\n        background-color: #f5f5f5;\n        color: #666666;\n      }\n      \n      &.confirm-btn {\n        background-color: #ff6b35;\n        color: #ffffff;\n        \n        &:disabled {\n          background-color: #cccccc;\n          color: #999999;\n        }\n      }\n    }\n  }\n}\n</style>", "import MiniProgramPage from '/Users/<USER>/Desktop/体育馆预约 2/体育馆预约2.0/pages/sharing/list.vue'\nwx.createPage(MiniProgramPage)"], "names": ["useSharingStore", "useUserStore", "uni", "formatDate", "formatDateTime", "shouldShowCountdown"], "mappings": ";;;;;;AA4TA,MAAK,iBAAkB,MAAW;AAIlC,MAAK,YAAU;AAAA,EACb,MAAM;AAAA,EAEN,YAAY;AAAA,IACV;AAAA,EACD;AAAA,EAED,OAAO;AACL,WAAO;AAAA,MACL,cAAc;AAAA,MACd,WAAW;AAAA,MACX,gBAAgB;AAAA,MAChB,gBAAgB;AAAA,MAChB,UAAU;AAAA;AAAA,MACV,kBAAkB,CAAE;AAAA;AAAA;AAAA,MAGpB,WAAW;AAAA,QACT,UAAU;AAAA,QACV,aAAa;AAAA,QACb,SAAS;AAAA,MACV;AAAA;AAAA,MAGD,eAAe;AAAA,QACb,EAAE,OAAO,OAAO,OAAO,OAAQ;AAAA,QAC/B,EAAE,OAAO,UAAU,OAAO,2BAA4B;AAAA,QACtD,EAAE,OAAO,QAAQ,OAAO,kBAAmB;AAAA,QAC3C,EAAE,OAAO,OAAO,OAAO,YAAa;AAAA,QACpC,EAAE,OAAO,OAAO,OAAO,YAAa;AAAA,QACpC,EAAE,OAAO,OAAO,OAAO,UAAU;AAAA,MAClC;AAAA;AAAA,MAGD,eAAe;AAAA,QACb,MAAM;AAAA,QACN,UAAU;AAAA,QACV,UAAU;AAAA,QACV,cAAc;AAAA,MACf;AAAA;AAAA,MAGD,aAAa;AAAA,QACX,EAAE,OAAO,MAAM,OAAO,QAAS;AAAA,QAC/B,EAAE,OAAO,MAAM,OAAO,WAAY;AAAA,QAClC,EAAE,OAAO,MAAM,OAAO,OAAQ;AAAA,QAC9B,EAAE,OAAO,MAAM,OAAO,QAAQ;AAAA,MAC/B;AAAA;AAAA,MAGD,qBAAqB;AAAA,QACnB,EAAE,OAAO,MAAM,OAAO,IAAK;AAAA,QAC3B,EAAE,OAAO,MAAM,OAAO,IAAK;AAAA,QAC3B,EAAE,OAAO,MAAM,OAAO,IAAK;AAAA,QAC3B,EAAE,OAAO,SAAS,OAAO,KAAK;AAAA,MAChC;AAAA,IACF;AAAA,EACD;AAAA,EAED,UAAU;AAAA,IACR,gBAAgB;;AACd,eAAO,UAAK,iBAAL,mBAAmB,wBAAuB,CAAC;AAAA,IACnD;AAAA,IAED,UAAU;;AACR,eAAO,UAAK,iBAAL,mBAAmB,cAAa;AAAA,IACxC;AAAA,IAED,aAAa;;AACX,eAAO,UAAK,iBAAL,mBAAmB,kBAAiB,EAAE,SAAS,GAAG,YAAY,EAAE;AAAA,IACxE;AAAA,IAED,WAAW;;AACT,eAAO,UAAK,cAAL,mBAAgB,gBAAe,CAAC;AAAA,IACxC;AAAA,IAED,wBAAwB;AACtB,UAAI,SAAS,KAAK,iBAAiB,CAAC;AAGpC,UAAI,KAAK,aAAa,YAAY;AAChC,iBAAS,OAAO,OAAO,WAAS;AAIH,gBAAM,WAAW,UACnB,MAAM,sBAAsB,MAAM,mBAClC,CAAC,KAAK,YAAY,KAAK;AAGhD,iBAAO,MAAM,WAAW,UAAU,MAAM,WAAW;AAAA,SACpD;AAAA,MACH;AAGA,UAAI,KAAK,gBAAgB;AACvB,iBAAS,OAAO,OAAO,WAAS,MAAM,WAAW,KAAK,cAAc;AAAA,MACtE;AAGA,UAAI,KAAK,cAAc,UAAU;AAC/B,iBAAS,OAAO,OAAO,YAAU,MAAM,cAAc,MAAM,WAAW,KAAK,cAAc,QAAQ,CAAC;AAAA,MACpG;AAEA,UAAI,KAAK,cAAc,UAAU;AAC/B,iBAAS,OAAO,OAAO,YAAU,MAAM,cAAc,MAAM,WAAW,KAAK,cAAc,QAAQ,CAAC;AAAA,MACpG;AAEA,UAAI,KAAK,cAAc,cAAc;AACnC,cAAM,qBAAqB,SAAS,KAAK,cAAc,YAAY;AACnE,YAAI,KAAK,cAAc,iBAAiB,MAAM;AAC5C,mBAAS,OAAO,OAAO,YAAU,MAAM,mBAAmB,MAAM,CAAC;AAAA,eAC5D;AACL,mBAAS,OAAO,OAAO,YAAU,MAAM,mBAAmB,OAAO,kBAAkB;AAAA,QACrF;AAAA,MACF;AAEA,aAAO;AAAA,IACR;AAAA,IAED,UAAU;AACR,aAAO,KAAK,WAAW,UAAU,KAAK,KAAK,KAAK,WAAW,QAAQ,KAAK,WAAW,QAAQ;AAAA,IAC5F;AAAA;AAAA,IAGD,uBAAuB;AACrB,aAAO,KAAK,UAAU,YAAY,KAAM,EAAC,SAAS;AAAA,IACpD;AAAA,EACD;AAAA,EAED,SAAS;AAEP,SAAK,eAAeA,+BAAgB;AACpC,SAAK,YAAYC,yBAAa;AAG9BC,kBAAAA,MAAI,IAAI,sBAAsB,KAAK,oBAAoB;AAEvDA,kBAAAA,MAAI,IAAI,kBAAkB,KAAK,gBAAgB;AAC/C,SAAK,SAAS;AAAA,EACf;AAAA,EAED,WAAW;AAETA,kBAAAA,MAAI,KAAK,sBAAsB,KAAK,oBAAoB;AACxDA,kBAAAA,MAAI,KAAK,kBAAkB,KAAK,gBAAgB;AAAA,EACjD;AAAA,EAED,MAAM,SAAS;AAEb,UAAM,KAAK,YAAY;AAEvB,UAAM,KAAK,qBAAqB;AAAA,EACjC;AAAA,EAED,oBAAoB;AAClB,SAAK,YAAY;AAAA,EAClB;AAAA,EAED,gBAAgB;AACd,QAAI,KAAK,WAAW,CAAC,KAAK,SAAS;AACjC,WAAK,SAAS;AAAA,IAChB;AAAA,EACD;AAAA,EAED,SAAS;AAAA;AAAA,IAGP,MAAM,WAAW;;AACf,UAAI;AACFA,sBAAAA,MAAA,MAAA,OAAA,iCAAY,gBAAgB;AAC5BA,0EAAY,mBAAmB,KAAK,YAAY;AAChDA,sBAAA,MAAA,MAAA,OAAA,iCAAY,kBAAkB,KAAK,QAAQ;AAG3C,cAAM,YAAY,KAAK,aAAa,QAAQ,KAAK,aAAa,sBAAsB,KAAK,aAAa;AACtG,cAAM,SAAS,MAAM,UAAU,EAAE,MAAM,GAAG,UAAU,IAAI;AACxDA,sBAAAA,MAAY,MAAA,OAAA,iCAAA,mBAAmB,MAAM;AACrCA,0EAAY,qBAAqB,KAAK,aAAa;AACnDA,4BAAA,MAAA,OAAA,iCAAY,0BAAwB,UAAK,kBAAL,mBAAoB,WAAU,CAAC;AAGnE,YAAI,KAAK,iBAAiB,KAAK,cAAc,SAAS,GAAG;AACvDA,8BAAY,MAAA,OAAA,iCAAA,mBAAmB,KAAK,cAAc,CAAC,CAAC;AACpDA,wBAAAA,MAAY,MAAA,OAAA,iCAAA,iBAAiB;AAAA,YAC3B,cAAc,KAAK,cAAc,CAAC,EAAE;AAAA,YACpC,cAAc,KAAK,cAAc,CAAC,EAAE;AAAA,YACpC,gBAAgB,KAAK,cAAc,CAAC,EAAE;AAAA,YACtC,OAAO,KAAK,cAAc,CAAC,EAAE;AAAA,YAC7B,YAAY,KAAK,cAAc,CAAC,EAAE;AAAA,YAClC,MAAM,KAAK,cAAc,CAAC,EAAE;AAAA,WAC7B;AACDA,wBAAAA,MAAA,MAAA,OAAA,iCAAY,kBAAkB;AAAA,YAC5B,qBAAqB,KAAK,cAAc,CAAC,EAAE;AAAA,YAC3C,iBAAiB,KAAK,cAAc,CAAC,EAAE;AAAA,YACvC,cAAc,KAAK,cAAc,CAAC,EAAE;AAAA,YACpC,kBAAkB,KAAK,cAAc,CAAC,EAAE;AAAA,WACzC;AAAA,QACH;AAGA,aAAK,aAAa;AAAA,MAClB,SAAO,OAAO;AACdA,sBAAAA,MAAc,MAAA,SAAA,iCAAA,mBAAmB,KAAK;AACtCA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,SACP;AAAA,MACH;AAAA,IACD;AAAA;AAAA,IAGD,MAAM,cAAc;;AAElB,UAAI,KAAK,SAAS;AAChBA,sBAAAA,MAAA,MAAA,OAAA,iCAAY,qBAAqB;AACjC;AAAA,MACF;AAEA,UAAI;AACFA,0EAAY,yBAAyB,KAAK,QAAQ;AAGlD,cAAM,YAAY,KAAK,aAAa,QAAQ,KAAK,aAAa,sBAAsB,KAAK,aAAa;AAGtG,cAAM,SAAS,MAAM,UAAU;AAAA,UAC7B,MAAM;AAAA,UACN,UAAU;AAAA,UACV,SAAS;AAAA,UACT,IAAI,KAAK;;SACV;AAEDA,sBAAAA,MAAY,MAAA,OAAA,iCAAA,mBAAmB,MAAM;AACrCA,4BAAA,MAAA,OAAA,iCAAY,yBAAuB,UAAK,kBAAL,mBAAoB,WAAU,CAAC;AAClEA,0EAAY,qBAAqB,KAAK,aAAa;AAGnD,aAAK,aAAa;AAClBA,sBAAAA,MAAI,oBAAoB;AAAA,MACxB,SAAO,OAAO;AACdA,sBAAAA,MAAI,oBAAoB;AACxBA,sBAAAA,MAAA,MAAA,SAAA,iCAAc,kBAAkB,KAAK;AACrCA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,SACP;AAAA,MACH;AAAA,IACD;AAAA;AAAA,IAGD,MAAM,WAAW;;AACf,UAAI,KAAK,WAAW,CAAC,KAAK;AAAS;AAEnC,UAAI;AACFA,sBAAAA,MAAA,MAAA,OAAA,iCAAY,uBAAuB,KAAK,WAAW,SAAS,SAAS,KAAK,QAAQ;AAClF,cAAM,WAAW,KAAK,WAAW,UAAU;AAE3C,cAAM,YAAY,KAAK,aAAa,QAAQ,KAAK,aAAa,sBAAsB,KAAK,aAAa;AACtG,cAAM,UAAU;AAAA,UACd,MAAM;AAAA,UACN,UAAU;AAAA,UACV,QAAQ,KAAK;AAAA,UACb,GAAG,KAAK;AAAA,SACT;AACDA,4BAAA,MAAA,OAAA,iCAAY,yBAAuB,UAAK,kBAAL,mBAAoB,WAAU,CAAC;AAAA,MAClE,SAAO,OAAO;AACdA,sBAAAA,MAAA,MAAA,SAAA,iCAAc,kBAAkB,KAAK;AACrCA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,SACP;AAAA,MACH;AAAA,IACD;AAAA;AAAA,IAGD,MAAM,aAAa,QAAQ;AACzB,WAAK,iBAAiB;AACtB,UAAI;AAEF,cAAM,YAAY,KAAK,aAAa,QAAQ,KAAK,aAAa,sBAAsB,KAAK,aAAa;AACtG,cAAM,UAAU;AAAA,UACd,MAAM;AAAA,UACN,UAAU;AAAA,UACV;AAAA,UACA,SAAS;AAAA,UACT,GAAG,KAAK;AAAA,SACT;AAAA,MACD,SAAO,OAAO;AACdA,sBAAAA,MAAc,MAAA,SAAA,iCAAA,SAAS,KAAK;AAAA,MAC9B;AAAA,IACD;AAAA;AAAA,IAGD,MAAM,WAAW,MAAM;AACrB,UAAI,KAAK,aAAa,MAAM;AAC1BA,sBAAAA,MAAY,MAAA,OAAA,iCAAA,mBAAmB;AAC/B;AAAA,MACF;AAEAA,0BAAY,MAAA,OAAA,iCAAA,kBAAkB,KAAK,UAAU,KAAK,IAAI;AACtD,WAAK,WAAW;AAChB,WAAK,iBAAiB;AAEtB,UAAI;AAEF,cAAM,KAAK,YAAY;AAAA,MACvB,SAAO,OAAO;AACdA,sBAAAA,MAAA,MAAA,SAAA,iCAAc,WAAW,KAAK;AAC9BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,SACP;AAED,aAAK,WAAW,SAAS,QAAQ,aAAa;AAAA,MAChD;AAAA,IACD;AAAA;AAAA,IAGD,iBAAiB,WAAW;AAC1BA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,4BAA4B,SAAS;AAAA,OAC3C;AAAA,IACF;AAAA;AAAA,IAGD,eAAe,SAAS;AAEtB,UAAI,KAAK,YAAY,QAAQ,oBAAoB,KAAK,SAAS,UAAU;AACvE,eAAO;AAAA,MACT;AAGA,UAAI,KAAK,oBAAoB,QAAQ,EAAE,GAAG;AACxC,eAAO;AAAA,MACT;AAEA,aAAO,QAAQ,WAAW,WAClB,QAAQ,uBAAuB,MAAM,QAAQ,mBAAmB;AAAA,IACzE;AAAA;AAAA,IAGD,YAAY,SAAS;AACnB,aAAO,KAAK,YAAY,QAAQ,oBAAoB,KAAK,SAAS;AAAA,IACnE;AAAA;AAAA,IAGD,oBAAoB,WAAW;AAC7B,aAAO,KAAK,iBAAiB;AAAA,QAAK,SAChC,IAAI,gBAAgB,IAAI,aAAa,OAAO;AAAA,MAC9C;AAAA,IACD;AAAA;AAAA,IAGD,MAAM,uBAAuB;AAC3B,UAAI;AACF,cAAM,eAAe,MAAM,KAAK,aAAa,qBAAqB;AAClE,aAAK,mBAAmB,gBAAgB,CAAC;AAAA,MACzC,SAAO,OAAO;AACdA,sBAAAA,MAAA,MAAA,SAAA,iCAAc,eAAe,KAAK;AAClC,aAAK,mBAAmB,CAAC;AAAA,MAC3B;AAAA,IACD;AAAA;AAAA,IAGD,YAAY,WAAW;AACrB,WAAK,iBAAiB,KAAK,cAAc,KAAK,OAAK,EAAE,OAAO,SAAS;AAErE,WAAK,eAAe;AACpB,WAAK,MAAM,UAAU,KAAK;AAAA,IAC3B;AAAA;AAAA,IAGD,iBAAiB;;AACf,WAAK,YAAY;AAAA,QACf,UAAU;AAAA;AAAA,QACV,eAAa,UAAK,aAAL,mBAAe,YAAS,UAAK,aAAL,mBAAe,WAAU;AAAA;AAAA,QAC9D,SAAS;AAAA,MACX;AAAA,IACD;AAAA;AAAA,IAGD,iBAAiB;AACf,WAAK,MAAM,UAAU,MAAM;AAC3B,WAAK,iBAAiB;AACtB,WAAK,eAAe;AAAA,IACrB;AAAA;AAAA,IAGD,MAAM,oBAAoB;AACxB,UAAI,CAAC,KAAK,sBAAsB;AAC9BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,SACP;AACD;AAAA,MACF;AAEA,UAAI;AACFA,sBAAAA,MAAI,YAAY,EAAE,OAAO,UAAU;AAEnC,cAAM,kBAAkB;AAAA,UACtB,UAAU,KAAK,UAAU,SAAS,KAAM;AAAA,UACxC,aAAa,KAAK,UAAU,YAAY,KAAM;AAAA,UAC9C,SAAS,KAAK,UAAU,QAAQ,KAAK;AAAA,QACvC;AAEA,cAAM,WAAW,MAAM,KAAK,aAAa,kBAAkB;AAAA,UACzD,SAAS,KAAK,eAAe;AAAA,UAC7B,MAAM;AAAA,SACP;AAEDA,sBAAAA,MAAI,YAAY;AAChB,aAAK,eAAe;AAGpB,YAAI,YAAY,SAAS,QAAQ,SAAS,KAAK,WAAW,4BAA4B;AAEpFA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,SAAS;AAAA,YACT,YAAY;AAAA,YACZ,aAAa;AAAA,YACb,SAAS,MAAM;AAEbA,4BAAAA,MAAI,WAAW;AAAA,gBACb,KAAK,gCAAgC,CAAC,SAAS,KAAK,EAAE;AAAA,eACvD;AAAA,YACH;AAAA,WACD;AAAA,QACH,WAAW,YAAY,SAAS,QAAQ,SAAS,KAAK,WAAW,YAAY;AAE3EA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,SAAS;AAAA,YACT,YAAY;AAAA,YACZ,aAAa;AAAA,YACb,SAAS,MAAM;AAEbA,4BAAAA,MAAI,WAAW;AAAA,gBACb,KAAK,gCAAgC,CAAC,SAAS,KAAK,EAAE;AAAA,eACvD;AAAA,YACH;AAAA,WACD;AAAA,eACI;AAELA,wBAAAA,MAAI,UAAU;AAAA,YACZ,QAAO,qCAAU,YAAW;AAAA,YAC5B,MAAM;AAAA,YACN,UAAU;AAAA,WACX;AAAA,QACH;AAGA,cAAM,KAAK,YAAY;AACvB,cAAM,KAAK,qBAAqB;AAAA,MAEhC,SAAO,OAAO;AACdA,sBAAAA,MAAI,YAAY;AAChBA,sBAAAA,MAAA,MAAA,SAAA,iCAAc,WAAW,KAAK;AAC9BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO,MAAM,WAAW;AAAA,UACxB,MAAM;AAAA,SACP;AAAA,MACH;AAAA,IACD;AAAA;AAAA,IAGD,kBAAkB;AAChB,WAAK,MAAM,YAAY,KAAK;AAAA,IAC7B;AAAA;AAAA,IAGD,mBAAmB;AACjB,WAAK,MAAM,YAAY,MAAM;AAAA,IAC9B;AAAA;AAAA,IAGD,WAAW,MAAM;AACf,WAAK,cAAc,OAAO;AAAA,IAC3B;AAAA;AAAA,IAGD,mBAAmB,cAAc;AAC/B,WAAK,cAAc,eAAe;AAAA,IACnC;AAAA;AAAA,IAGD,cAAc;AACZ,WAAK,gBAAgB;AAAA,QACnB,MAAM;AAAA,QACN,UAAU;AAAA,QACV,UAAU;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACD;AAAA;AAAA,IAGD,MAAM,cAAc;AAClB,WAAK,iBAAiB;AACtB,UAAI;AACF,cAAM,KAAK,aAAa,yBAAyB;AAAA,UAC/C,MAAM;AAAA,UACN,UAAU;AAAA,UACV,QAAQ,KAAK;AAAA,UACb,SAAS;AAAA,UACT,GAAG,KAAK;AAAA,SACT;AAAA,MACD,SAAO,OAAO;AACdA,sBAAAA,MAAA,MAAA,SAAA,iCAAc,WAAW,KAAK;AAAA,MAChC;AAAA,IACD;AAAA;AAAA,IAGD,WAAW,MAAM;AACf,UAAI,CAAC;AAAM,eAAO;AAClB,aAAOC,cAAU,WAAC,MAAM,OAAO;AAAA,IAChC;AAAA;AAAA,IAGD,eAAe,UAAU;AACvB,UAAI,CAAC;AAAU,eAAO;AACtB,aAAOC,cAAAA,eAAe,QAAQ;AAAA,IAC/B;AAAA;AAAA,IAGD,eAAe,WAAW,SAAS;AACjC,UAAI,CAAC,aAAa,CAAC,SAAS;AAC1B,eAAO;AAAA,MACT;AACA,UAAI,aAAa,CAAC,SAAS;AACzB,eAAO;AAAA,MACT;AACA,UAAI,CAAC,aAAa,SAAS;AACzB,eAAO;AAAA,MACT;AACA,aAAO,GAAG,SAAS,IAAI,OAAO;AAAA,IAC/B;AAAA;AAAA,IAGD,gBAAgB,SAAS;AACvB,YAAM,YAAY,QAAQ,aAAa,QAAQ;AAC/C,YAAM,UAAU,QAAQ,WAAW,QAAQ;AAC3C,YAAM,gBAAgB,QAAQ,iBAAiB;AAE/C,UAAI,CAAC,aAAa,CAAC,SAAS;AAC1B,eAAO;AAAA,MACT;AAGA,YAAM,aAAa,CAAC,YAAY;AAC9B,YAAI,CAAC;AAAS,iBAAO;AAErB,YAAI,QAAQ,SAAS,KAAK,QAAQ,SAAS,GAAG,GAAG;AAC/C,iBAAO,QAAQ,UAAU,GAAG,CAAC;AAAA,QAC/B;AACA,eAAO;AAAA,MACT;AAEA,YAAM,iBAAiB,WAAW,SAAS;AAC3C,YAAM,eAAe,WAAW,OAAO;AAGvC,YAAM,UAAU,KAAK,WAAW,QAAQ,WAAW;AAGnD,UAAI,gBAAgB,GAAG;AACrB,eAAO,GAAG,OAAO,IAAI,cAAc,MAAM,YAAY,KAAK,aAAa;AAAA,MACzE;AAEA,aAAO,GAAG,OAAO,IAAI,cAAc,MAAM,YAAY;AAAA,IACtD;AAAA;AAAA,IAGD,iBAAiB,UAAU;AACzB,UAAI,CAAC;AAAU,eAAO;AACtB,UAAI;AAEF,YAAI,UAAU;AACd,YAAI,OAAO,YAAY,YAAY,QAAQ,SAAS,GAAG,KAAK,CAAC,QAAQ,SAAS,GAAG,GAAG;AAClF,oBAAU,QAAQ,QAAQ,KAAK,GAAG;AAAA,QACpC;AAEA,cAAM,OAAO,IAAI,KAAK,OAAO;AAC7B,YAAI,MAAM,KAAK,QAAS,CAAA;AAAG,iBAAO;AAClC,cAAM,OAAO,KAAK,YAAY;AAC9B,cAAM,QAAQ,OAAO,KAAK,SAAQ,IAAK,CAAC,EAAE,SAAS,GAAG,GAAG;AACzD,cAAM,MAAM,OAAO,KAAK,QAAS,CAAA,EAAE,SAAS,GAAG,GAAG;AAClD,cAAM,OAAO,OAAO,KAAK,SAAU,CAAA,EAAE,SAAS,GAAG,GAAG;AACpD,cAAM,SAAS,OAAO,KAAK,WAAY,CAAA,EAAE,SAAS,GAAG,GAAG;AACxD,eAAO,GAAG,IAAI,IAAI,KAAK,IAAI,GAAG,IAAI,IAAI,IAAI,MAAM;AAAA,MAChD,SAAO,OAAO;AACdF,sBAAAA,MAAA,MAAA,SAAA,iCAAc,YAAY,KAAK;AAC/B,eAAO;AAAA,MACT;AAAA,IACD;AAAA;AAAA,IAGD,iBAAiB;AACf,UAAI,CAAC,KAAK;AAAgB,eAAO;AACjC,aAAO,GAAG,KAAK,WAAW,KAAK,eAAe,WAAW,CAAC,IAAI,KAAK,eAAe,KAAK,eAAe,WAAW,KAAK,eAAe,OAAO,CAAC;AAAA,IAC9I;AAAA;AAAA,IAGD,YAAY,OAAO;AACjB,UAAI,CAAC,SAAS,UAAU;AAAG,eAAO;AAClC,YAAM,WAAW,OAAO,KAAK;AAC7B,UAAI,MAAM,QAAQ;AAAG,eAAO;AAC5B,aAAO,SAAS,QAAQ,CAAC;AAAA,IAC1B;AAAA;AAAA,IAGD,iBAAiB,SAAS;AACxB,YAAM,UAAU,QAAQ,uBAAuB;AAC/C,YAAM,MAAM,QAAQ,mBAAmB;AACvC,aAAO,KAAK,IAAK,UAAU,MAAO,KAAK,GAAG;AAAA,IAC3C;AAAA;AAAA,IAGD,oBAAoB,OAAO;AACzB,aAAOG,gBAAAA,oBAAoB,KAAK;AAAA,IACjC;AAAA;AAAA,IAGD,mBAAmB,OAAO;AACxBH,oBAAY,MAAA,MAAA,OAAA,iCAAA,cAAc,MAAM,OAAO;AAEvC,WAAK,YAAY;AAAA,IAClB;AAAA;AAAA,IAGD,eAAe,QAAQ;AACrB,YAAM,YAAY;AAAA,QAChB,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,aAAa;AAAA,QACb,aAAa;AAAA,QACb,WAAW;AAAA,MACb;AACA,aAAO,UAAU,MAAM,KAAK;AAAA,IAC7B;AAAA;AAAA,IAGD,cAAc,QAAQ;AACpB,YAAM,YAAY;AAAA,QAChB,QAAQ;AAAA,QACR,4BAA4B;AAAA,QAC5B,mBAAmB;AAAA,QACnB,aAAa;AAAA,QACb,aAAa;AAAA,QACb,WAAW;AAAA,MACb;AACA,aAAO,UAAU,MAAM,KAAK;AAAA,IAC7B;AAAA;AAAA,IAGD,qBAAqB,MAAM;AACzBA,oBAAAA,MAAA,MAAA,OAAA,iCAAY,oBAAoB,IAAI;AAGpC,UAAI,KAAK,iBAAiB,KAAK,SAAS;AACtC,cAAM,QAAQ,KAAK,cAAc,KAAK,OAAK,EAAE,MAAM,KAAK,OAAO;AAC/D,YAAI,OAAO;AAET,cAAI,KAAK,wBAAwB,QAAW;AAC1C,kBAAM,sBAAsB,KAAK;AAAA,UACnC;AAGA,cAAI,KAAK,WAAW,cAAc,MAAM,uBAAuB,GAAG;AAChE,kBAAM,SAAS;AAAA,UACjB;AAEAA,wBAAAA,oDAAY,mBAAmB,KAAK;AAAA,QACtC;AAAA,MACF;AAGA,iBAAW,MAAM;AACf,aAAK,YAAY;AAAA,MAClB,GAAE,GAAI;AAAA,IACR;AAAA;AAAA,IAGD,iBAAiB,MAAM;AACrBA,oBAAAA,MAAA,MAAA,OAAA,kCAAY,oBAAoB,IAAI;AAEpC,UAAI,KAAK,SAAS,aAAa,KAAK,SAAS;AAC3CA,sBAAAA,MAAA,MAAA,OAAA,kCAAY,oBAAoB;AAGhC,mBAAW,MAAM;AACfA,wBAAAA,MAAA,MAAA,OAAA,kCAAY,eAAe;AAC3B,eAAK,YAAY;AAAA,QAClB,GAAE,IAAI;AAAA,MACT;AAAA,IACD;AAAA;AAAA,IAGD,kBAAkB,SAAS;AAEzB,UAAI,KAAK,YAAY,OAAO,GAAG;AAC7B,eAAO;AAAA,MACT;AAEA,UAAI,KAAK,oBAAoB,OAAO,GAAG;AACrC,eAAO;AAAA,MACT;AACA,UAAI,QAAQ,WAAW,QAAQ;AAC7B,eAAO;AAAA,MACT;AACA,UAAI,QAAQ,WAAW,aAAa;AAClC,eAAO;AAAA,MACT;AACA,UAAI,QAAQ,WAAW,aAAa;AAClC,eAAO;AAAA,MACT;AACA,UAAI,QAAQ,WAAW,WAAW;AAChC,eAAO;AAAA,MACT;AACA,aAAO;AAAA,IACR;AAAA;AAAA,IAGD,eAAe;AACbA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK;AAAA,OACN;AAAA,IACH;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACzhCA,GAAG,WAAW,eAAe;"}