{"version": 3, "file": "payment-fix.js", "sources": ["pages/test/payment-fix.vue", "pages/test/payment-fix.vue?type=page"], "sourcesContent": ["<template>\n  <view class=\"test-container\">\n    <view class=\"test-header\">\n      <text class=\"test-title\">支付问题修复测试</text>\n    </view>\n\n    <!-- 测试1: 订单金额显示 -->\n    <view class=\"test-section\">\n      <view class=\"section-title\">测试1: 订单金额显示修复</view>\n      \n      <view class=\"test-item\">\n        <text class=\"test-label\">测试订单ID:</text>\n        <input v-model=\"testOrderId\" placeholder=\"输入订单ID\" class=\"test-input\" />\n      </view>\n      \n      <button @click=\"testOrderAmount\" class=\"test-button\">测试订单金额计算</button>\n      \n      <view v-if=\"orderTestResult\" class=\"test-result\">\n        <text class=\"result-title\">测试结果:</text>\n        <view class=\"result-item\">\n          <text>原始价格: ¥{{ orderTestResult.originalPrice }}</text>\n        </view>\n        <view class=\"result-item\">\n          <text>计算价格: ¥{{ orderTestResult.calculatedPrice }}</text>\n        </view>\n        <view class=\"result-item\">\n          <text>计算方法: {{ orderTestResult.calculationMethod }}</text>\n        </view>\n        <view class=\"result-item\">\n          <text>建议: {{ orderTestResult.recommendation }}</text>\n        </view>\n      </view>\n    </view>\n\n    <!-- 测试2: 时间段刷新 -->\n    <view class=\"test-section\">\n      <view class=\"section-title\">测试2: 时间段刷新修复</view>\n      \n      <view class=\"test-item\">\n        <text class=\"test-label\">场馆ID:</text>\n        <input v-model=\"testVenueId\" placeholder=\"输入场馆ID\" class=\"test-input\" />\n      </view>\n      \n      <view class=\"test-item\">\n        <text class=\"test-label\">日期:</text>\n        <input v-model=\"testDate\" placeholder=\"YYYY-MM-DD\" class=\"test-input\" />\n      </view>\n      \n      <button @click=\"testTimeSlotRefresh\" class=\"test-button\">测试时间段刷新</button>\n      \n      <view v-if=\"timeSlotTestResult\" class=\"test-result\">\n        <text class=\"result-title\">刷新前状态:</text>\n        <view class=\"result-item\">\n          <text>时间段数量: {{ timeSlotTestResult.before?.currentSlotsCount || 0 }}</text>\n        </view>\n        \n        <text class=\"result-title\">刷新后状态:</text>\n        <view class=\"result-item\">\n          <text>时间段数量: {{ timeSlotTestResult.after?.newSlotsCount || 0 }}</text>\n        </view>\n        <view class=\"result-item\">\n          <text>刷新成功: {{ timeSlotTestResult.after?.success ? '是' : '否' }}</text>\n        </view>\n      </view>\n    </view>\n\n    <!-- 测试3: 综合测试 -->\n    <view class=\"test-section\">\n      <view class=\"section-title\">测试3: 创建预约流程测试</view>\n      \n      <button @click=\"testFullFlow\" class=\"test-button\">测试完整预约流程</button>\n      \n      <view v-if=\"fullFlowResult\" class=\"test-result\">\n        <text class=\"result-title\">流程测试结果:</text>\n        <view v-for=\"(step, index) in fullFlowResult.steps\" :key=\"index\" class=\"result-item\">\n          <text>{{ step.name }}: {{ step.success ? '✅' : '❌' }} {{ step.message }}</text>\n        </view>\n      </view>\n    </view>\n\n    <!-- 日志显示 -->\n    <view class=\"test-section\">\n      <view class=\"section-title\">测试日志</view>\n      <view class=\"log-container\">\n        <text v-for=\"(log, index) in testLogs\" :key=\"index\" class=\"log-item\">{{ log }}</text>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script>\nimport { useBookingStore } from '@/stores/booking.js'\nimport { useVenueStore } from '@/stores/venue.js'\nimport { debugOrderAmount, debugTimeSlotRefresh, forceRefreshTimeSlots } from '@/utils/payment-debug.js'\n\nexport default {\n  name: 'PaymentFixTest',\n  data() {\n    return {\n      testOrderId: '401',\n      testVenueId: '25',\n      testDate: '2025-07-19',\n      orderTestResult: null,\n      timeSlotTestResult: null,\n      fullFlowResult: null,\n      testLogs: []\n    }\n  },\n  \n  setup() {\n    const bookingStore = useBookingStore()\n    const venueStore = useVenueStore()\n    \n    return {\n      bookingStore,\n      venueStore\n    }\n  },\n  \n  methods: {\n    addLog(message) {\n      const timestamp = new Date().toLocaleTimeString()\n      this.testLogs.unshift(`[${timestamp}] ${message}`)\n      if (this.testLogs.length > 20) {\n        this.testLogs = this.testLogs.slice(0, 20)\n      }\n    },\n    \n    async testOrderAmount() {\n      try {\n        this.addLog('开始测试订单金额计算...')\n        \n        // 获取订单信息\n        const orderInfo = await this.bookingStore.getBookingDetail(this.testOrderId)\n        this.addLog(`获取订单信息: ${orderInfo ? '成功' : '失败'}`)\n        \n        if (orderInfo) {\n          // 使用调试工具分析\n          this.orderTestResult = debugOrderAmount(orderInfo)\n          this.addLog(`金额计算完成: ¥${this.orderTestResult.calculatedPrice}`)\n        } else {\n          this.addLog('无法获取订单信息')\n        }\n      } catch (error) {\n        this.addLog(`测试失败: ${error.message}`)\n        console.error('订单金额测试失败:', error)\n      }\n    },\n    \n    async testTimeSlotRefresh() {\n      try {\n        this.addLog('开始测试时间段刷新...')\n        \n        // 刷新前状态\n        const beforeResult = debugTimeSlotRefresh(this.testVenueId, this.testDate, this.venueStore)\n        this.addLog(`刷新前时间段数量: ${beforeResult.currentSlotsCount}`)\n        \n        // 执行刷新\n        const refreshResult = await forceRefreshTimeSlots(this.testVenueId, this.testDate, this.venueStore)\n        this.addLog(`刷新执行: ${refreshResult.success ? '成功' : '失败'}`)\n        \n        this.timeSlotTestResult = {\n          before: beforeResult,\n          after: refreshResult\n        }\n        \n        this.addLog(`刷新后时间段数量: ${refreshResult.newSlotsCount || 0}`)\n      } catch (error) {\n        this.addLog(`测试失败: ${error.message}`)\n        console.error('时间段刷新测试失败:', error)\n      }\n    },\n    \n    async testFullFlow() {\n      try {\n        this.addLog('开始测试完整预约流程...')\n        \n        const steps = []\n        \n        // 步骤1: 获取场馆信息\n        try {\n          await this.venueStore.getVenueDetail(this.testVenueId)\n          steps.push({ name: '获取场馆信息', success: true, message: '成功' })\n          this.addLog('✅ 场馆信息获取成功')\n        } catch (error) {\n          steps.push({ name: '获取场馆信息', success: false, message: error.message })\n          this.addLog('❌ 场馆信息获取失败')\n        }\n        \n        // 步骤2: 获取时间段\n        try {\n          await this.venueStore.getTimeSlots(this.testVenueId, this.testDate)\n          steps.push({ name: '获取时间段', success: true, message: '成功' })\n          this.addLog('✅ 时间段获取成功')\n        } catch (error) {\n          steps.push({ name: '获取时间段', success: false, message: error.message })\n          this.addLog('❌ 时间段获取失败')\n        }\n        \n        // 步骤3: 测试价格计算\n        try {\n          const mockOrder = {\n            startTime: '18:00',\n            endTime: '20:00',\n            bookingType: 'EXCLUSIVE',\n            venueName: '测试场馆'\n          }\n          const priceResult = debugOrderAmount(mockOrder)\n          steps.push({ \n            name: '价格计算', \n            success: priceResult.calculatedPrice > 0, \n            message: `¥${priceResult.calculatedPrice}` \n          })\n          this.addLog(`✅ 价格计算: ¥${priceResult.calculatedPrice}`)\n        } catch (error) {\n          steps.push({ name: '价格计算', success: false, message: error.message })\n          this.addLog('❌ 价格计算失败')\n        }\n        \n        this.fullFlowResult = { steps }\n        this.addLog('完整流程测试完成')\n      } catch (error) {\n        this.addLog(`流程测试失败: ${error.message}`)\n        console.error('完整流程测试失败:', error)\n      }\n    }\n  }\n}\n</script>\n\n<style scoped>\n.test-container {\n  padding: 20rpx;\n  background-color: #f5f5f5;\n  min-height: 100vh;\n}\n\n.test-header {\n  text-align: center;\n  margin-bottom: 40rpx;\n}\n\n.test-title {\n  font-size: 36rpx;\n  font-weight: bold;\n  color: #333;\n}\n\n.test-section {\n  background-color: white;\n  margin-bottom: 30rpx;\n  padding: 30rpx;\n  border-radius: 10rpx;\n}\n\n.section-title {\n  font-size: 32rpx;\n  font-weight: bold;\n  color: #333;\n  margin-bottom: 20rpx;\n}\n\n.test-item {\n  display: flex;\n  align-items: center;\n  margin-bottom: 20rpx;\n}\n\n.test-label {\n  width: 200rpx;\n  font-size: 28rpx;\n  color: #666;\n}\n\n.test-input {\n  flex: 1;\n  padding: 20rpx;\n  border: 1px solid #ddd;\n  border-radius: 5rpx;\n  font-size: 28rpx;\n}\n\n.test-button {\n  background-color: #007aff;\n  color: white;\n  padding: 20rpx 40rpx;\n  border-radius: 5rpx;\n  font-size: 28rpx;\n  margin: 20rpx 0;\n}\n\n.test-result {\n  margin-top: 20rpx;\n  padding: 20rpx;\n  background-color: #f8f8f8;\n  border-radius: 5rpx;\n}\n\n.result-title {\n  font-size: 28rpx;\n  font-weight: bold;\n  color: #333;\n  margin-bottom: 10rpx;\n}\n\n.result-item {\n  margin-bottom: 10rpx;\n}\n\n.result-item text {\n  font-size: 26rpx;\n  color: #666;\n}\n\n.log-container {\n  max-height: 400rpx;\n  overflow-y: auto;\n  background-color: #f8f8f8;\n  padding: 20rpx;\n  border-radius: 5rpx;\n}\n\n.log-item {\n  display: block;\n  font-size: 24rpx;\n  color: #666;\n  margin-bottom: 10rpx;\n  font-family: monospace;\n}\n</style>\n", "import MiniProgramPage from '/Users/<USER>/Desktop/体育馆预约 2/体育馆预约2.0/pages/test/payment-fix.vue'\nwx.createPage(MiniProgramPage)"], "names": ["useBookingStore", "useVenueStore", "debugOrderAmount", "uni", "debugTimeSlotRefresh", "forceRefreshTimeSlots"], "mappings": ";;;;;AA+FA,MAAK,YAAU;AAAA,EACb,MAAM;AAAA,EACN,OAAO;AACL,WAAO;AAAA,MACL,aAAa;AAAA,MACb,aAAa;AAAA,MACb,UAAU;AAAA,MACV,iBAAiB;AAAA,MACjB,oBAAoB;AAAA,MACpB,gBAAgB;AAAA,MAChB,UAAU,CAAC;AAAA,IACb;AAAA,EACD;AAAA,EAED,QAAQ;AACN,UAAM,eAAeA,eAAAA,gBAAgB;AACrC,UAAM,aAAaC,aAAAA,cAAc;AAEjC,WAAO;AAAA,MACL;AAAA,MACA;AAAA,IACF;AAAA,EACD;AAAA,EAED,SAAS;AAAA,IACP,OAAO,SAAS;AACd,YAAM,aAAY,oBAAI,KAAM,GAAC,mBAAmB;AAChD,WAAK,SAAS,QAAQ,IAAI,SAAS,KAAK,OAAO,EAAE;AACjD,UAAI,KAAK,SAAS,SAAS,IAAI;AAC7B,aAAK,WAAW,KAAK,SAAS,MAAM,GAAG,EAAE;AAAA,MAC3C;AAAA,IACD;AAAA,IAED,MAAM,kBAAkB;AACtB,UAAI;AACF,aAAK,OAAO,eAAe;AAG3B,cAAM,YAAY,MAAM,KAAK,aAAa,iBAAiB,KAAK,WAAW;AAC3E,aAAK,OAAO,WAAW,YAAY,OAAO,IAAI,EAAE;AAEhD,YAAI,WAAW;AAEb,eAAK,kBAAkBC,mBAAgB,iBAAC,SAAS;AACjD,eAAK,OAAO,YAAY,KAAK,gBAAgB,eAAe,EAAE;AAAA,eACzD;AACL,eAAK,OAAO,UAAU;AAAA,QACxB;AAAA,MACA,SAAO,OAAO;AACd,aAAK,OAAO,SAAS,MAAM,OAAO,EAAE;AACpCC,sBAAAA,MAAc,MAAA,SAAA,qCAAA,aAAa,KAAK;AAAA,MAClC;AAAA,IACD;AAAA,IAED,MAAM,sBAAsB;AAC1B,UAAI;AACF,aAAK,OAAO,cAAc;AAG1B,cAAM,eAAeC,mBAAoB,qBAAC,KAAK,aAAa,KAAK,UAAU,KAAK,UAAU;AAC1F,aAAK,OAAO,aAAa,aAAa,iBAAiB,EAAE;AAGzD,cAAM,gBAAgB,MAAMC,mBAAAA,sBAAsB,KAAK,aAAa,KAAK,UAAU,KAAK,UAAU;AAClG,aAAK,OAAO,SAAS,cAAc,UAAU,OAAO,IAAI,EAAE;AAE1D,aAAK,qBAAqB;AAAA,UACxB,QAAQ;AAAA,UACR,OAAO;AAAA,QACT;AAEA,aAAK,OAAO,aAAa,cAAc,iBAAiB,CAAC,EAAE;AAAA,MAC3D,SAAO,OAAO;AACd,aAAK,OAAO,SAAS,MAAM,OAAO,EAAE;AACpCF,sBAAAA,MAAc,MAAA,SAAA,qCAAA,cAAc,KAAK;AAAA,MACnC;AAAA,IACD;AAAA,IAED,MAAM,eAAe;AACnB,UAAI;AACF,aAAK,OAAO,eAAe;AAE3B,cAAM,QAAQ,CAAC;AAGf,YAAI;AACF,gBAAM,KAAK,WAAW,eAAe,KAAK,WAAW;AACrD,gBAAM,KAAK,EAAE,MAAM,UAAU,SAAS,MAAM,SAAS,MAAM;AAC3D,eAAK,OAAO,YAAY;AAAA,QACxB,SAAO,OAAO;AACd,gBAAM,KAAK,EAAE,MAAM,UAAU,SAAS,OAAO,SAAS,MAAM,SAAS;AACrE,eAAK,OAAO,YAAY;AAAA,QAC1B;AAGA,YAAI;AACF,gBAAM,KAAK,WAAW,aAAa,KAAK,aAAa,KAAK,QAAQ;AAClE,gBAAM,KAAK,EAAE,MAAM,SAAS,SAAS,MAAM,SAAS,MAAM;AAC1D,eAAK,OAAO,WAAW;AAAA,QACvB,SAAO,OAAO;AACd,gBAAM,KAAK,EAAE,MAAM,SAAS,SAAS,OAAO,SAAS,MAAM,SAAS;AACpE,eAAK,OAAO,WAAW;AAAA,QACzB;AAGA,YAAI;AACF,gBAAM,YAAY;AAAA,YAChB,WAAW;AAAA,YACX,SAAS;AAAA,YACT,aAAa;AAAA,YACb,WAAW;AAAA,UACb;AACA,gBAAM,cAAcD,mBAAgB,iBAAC,SAAS;AAC9C,gBAAM,KAAK;AAAA,YACT,MAAM;AAAA,YACN,SAAS,YAAY,kBAAkB;AAAA,YACvC,SAAS,IAAI,YAAY,eAAe;AAAA,WACzC;AACD,eAAK,OAAO,YAAY,YAAY,eAAe,EAAE;AAAA,QACrD,SAAO,OAAO;AACd,gBAAM,KAAK,EAAE,MAAM,QAAQ,SAAS,OAAO,SAAS,MAAM,SAAS;AACnE,eAAK,OAAO,UAAU;AAAA,QACxB;AAEA,aAAK,iBAAiB,EAAE,MAAM;AAC9B,aAAK,OAAO,UAAU;AAAA,MACtB,SAAO,OAAO;AACd,aAAK,OAAO,WAAW,MAAM,OAAO,EAAE;AACtCC,sBAAAA,MAAc,MAAA,SAAA,qCAAA,aAAa,KAAK;AAAA,MAClC;AAAA,IACF;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AClOA,GAAG,WAAW,eAAe;"}