{"version": 3, "file": "migration-validation.js", "sources": ["pages/test/migration-validation.vue", "pages/test/migration-validation.vue?type=page"], "sourcesContent": ["<template>\n  <view class=\"test-container\">\n    <view class=\"test-header\">\n      <text class=\"test-title\">🧪 Pinia迁移全面验证</text>\n      <text class=\"test-subtitle\">测试所有核心功能确保迁移成功</text>\n    </view>\n\n    <!-- 总体状态 -->\n    <view class=\"status-section\">\n      <text class=\"section-title\">📊 总体测试状态</text>\n      <view class=\"status-grid\">\n        <view class=\"status-item\" :class=\"{ 'success': allTestsPassed, 'error': !allTestsPassed }\">\n          <text class=\"status-text\">{{ allTestsPassed ? '✅ 全部通过' : '❌ 存在问题' }}</text>\n        </view>\n        <view class=\"status-item\">\n          <text class=\"status-text\">通过: {{ passedTests }}/{{ totalTests }}</text>\n        </view>\n      </view>\n    </view>\n\n    <!-- Store连接测试 -->\n    <view class=\"test-section\">\n      <text class=\"section-title\">🔗 Store连接测试</text>\n      <view class=\"test-grid\">\n        <view class=\"test-item\" :class=\"getTestStatus('userStore')\">\n          <text class=\"test-name\">User Store</text>\n          <text class=\"test-result\">{{ testResults.userStore ? '✅' : '❌' }}</text>\n        </view>\n        <view class=\"test-item\" :class=\"getTestStatus('venueStore')\">\n          <text class=\"test-name\">Venue Store</text>\n          <text class=\"test-result\">{{ testResults.venueStore ? '✅' : '❌' }}</text>\n        </view>\n        <view class=\"test-item\" :class=\"getTestStatus('bookingStore')\">\n          <text class=\"test-name\">Booking Store</text>\n          <text class=\"test-result\">{{ testResults.bookingStore ? '✅' : '❌' }}</text>\n        </view>\n        <view class=\"test-item\" :class=\"getTestStatus('sharingStore')\">\n          <text class=\"test-name\">Sharing Store</text>\n          <text class=\"test-result\">{{ testResults.sharingStore ? '✅' : '❌' }}</text>\n        </view>\n        <view class=\"test-item\" :class=\"getTestStatus('appStore')\">\n          <text class=\"test-name\">App Store</text>\n          <text class=\"test-result\">{{ testResults.appStore ? '✅' : '❌' }}</text>\n        </view>\n      </view>\n    </view>\n\n    <!-- 功能测试 -->\n    <view class=\"test-section\">\n      <text class=\"section-title\">⚡ 核心功能测试</text>\n      <view class=\"function-tests\">\n        <view class=\"function-group\">\n          <text class=\"group-title\">用户功能</text>\n          <button class=\"test-btn\" @click=\"testUserFunctions\" :disabled=\"testing\">\n            {{ testing ? '测试中...' : '测试用户功能' }}\n          </button>\n          <text class=\"test-status\">{{ functionResults.user || '待测试' }}</text>\n        </view>\n        \n        <view class=\"function-group\">\n          <text class=\"group-title\">场馆功能</text>\n          <button class=\"test-btn\" @click=\"testVenueFunctions\" :disabled=\"testing\">\n            {{ testing ? '测试中...' : '测试场馆功能' }}\n          </button>\n          <text class=\"test-status\">{{ functionResults.venue || '待测试' }}</text>\n        </view>\n        \n        <view class=\"function-group\">\n          <text class=\"group-title\">预订功能</text>\n          <button class=\"test-btn\" @click=\"testBookingFunctions\" :disabled=\"testing\">\n            {{ testing ? '测试中...' : '测试预订功能' }}\n          </button>\n          <text class=\"test-status\">{{ functionResults.booking || '待测试' }}</text>\n        </view>\n        \n        <view class=\"function-group\">\n          <text class=\"group-title\">拼场功能</text>\n          <button class=\"test-btn\" @click=\"testSharingFunctions\" :disabled=\"testing\">\n            {{ testing ? '测试中...' : '测试拼场功能' }}\n          </button>\n          <text class=\"test-status\">{{ functionResults.sharing || '待测试' }}</text>\n        </view>\n      </view>\n    </view>\n\n    <!-- 页面导航测试 -->\n    <view class=\"test-section\">\n      <text class=\"section-title\">📱 页面导航测试</text>\n      <view class=\"nav-tests\">\n        <button class=\"nav-btn\" @click=\"testPageNavigation('user/login')\">登录页面</button>\n        <button class=\"nav-btn\" @click=\"testPageNavigation('user/profile')\">个人中心</button>\n        <button class=\"nav-btn\" @click=\"testPageNavigation('venue/list')\">场馆列表</button>\n        <button class=\"nav-btn\" @click=\"testPageNavigation('booking/list')\">我的预订</button>\n        <button class=\"nav-btn\" @click=\"testPageNavigation('sharing/list')\">拼场列表</button>\n      </view>\n    </view>\n\n    <!-- 全面测试按钮 -->\n    <view class=\"action-section\">\n      <button class=\"full-test-btn\" @click=\"testGetterNamingFix\" :disabled=\"testing\" style=\"background-color: #ff6b00; margin-bottom: 20rpx;\">\n        {{ testing ? '🔄 测试进行中...' : '🔧 测试Getter命名修复' }}\n      </button>\n      <button class=\"full-test-btn\" @click=\"runFullTest\" :disabled=\"testing\">\n        {{ testing ? '🔄 测试进行中...' : '🚀 运行全面测试' }}\n      </button>\n    </view>\n\n    <!-- 测试日志 -->\n    <view class=\"log-section\" v-if=\"testLogs.length > 0\">\n      <text class=\"section-title\">📝 测试日志</text>\n      <view class=\"log-container\">\n        <text v-for=\"(log, index) in testLogs\" :key=\"index\" class=\"log-item\" :class=\"log.type\">\n          {{ log.time }} - {{ log.message }}\n        </text>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script>\nimport { useUserStore } from '@/stores/user.js'\nimport { useVenueStore } from '@/stores/venue.js'\nimport { useBookingStore } from '@/stores/booking.js'\nimport { useSharingStore } from '@/stores/sharing.js'\nimport { useAppStore } from '@/stores/app.js'\n\nexport default {\n  name: 'MigrationValidation',\n  \n  data() {\n    return {\n      userStore: null,\n      venueStore: null,\n      bookingStore: null,\n      sharingStore: null,\n      appStore: null,\n      \n      testing: false,\n      testResults: {\n        userStore: false,\n        venueStore: false,\n        bookingStore: false,\n        sharingStore: false,\n        appStore: false\n      },\n      \n      functionResults: {\n        user: '',\n        venue: '',\n        booking: '',\n        sharing: ''\n      },\n      \n      testLogs: []\n    }\n  },\n  \n  computed: {\n    totalTests() {\n      return Object.keys(this.testResults).length\n    },\n    \n    passedTests() {\n      return Object.values(this.testResults).filter(result => result).length\n    },\n    \n    allTestsPassed() {\n      return this.passedTests === this.totalTests\n    }\n  },\n  \n  onLoad() {\n    this.initializeStores()\n    this.runStoreConnectionTests()\n  },\n  \n  methods: {\n    initializeStores() {\n      try {\n        this.userStore = useUserStore()\n        this.venueStore = useVenueStore()\n        this.bookingStore = useBookingStore()\n        this.sharingStore = useSharingStore()\n        this.appStore = useAppStore()\n        this.addLog('success', '所有Store初始化成功')\n      } catch (error) {\n        this.addLog('error', `Store初始化失败: ${error.message}`)\n      }\n    },\n    \n    runStoreConnectionTests() {\n      // 测试每个store的连接\n      this.testResults.userStore = !!this.userStore\n      this.testResults.venueStore = !!this.venueStore\n      this.testResults.bookingStore = !!this.bookingStore\n      this.testResults.sharingStore = !!this.sharingStore\n      this.testResults.appStore = !!this.appStore\n\n      this.addLog('info', `Store连接测试完成: ${this.passedTests}/${this.totalTests} 通过`)\n    },\n\n    getTestStatus(testName) {\n      return this.testResults[testName] ? 'success' : 'error'\n    },\n\n    addLog(type, message) {\n      const time = new Date().toLocaleTimeString()\n      this.testLogs.push({ type, message, time })\n    },\n\n    async testUserFunctions() {\n      this.testing = true\n      try {\n        this.addLog('info', '开始测试用户功能...')\n\n        // 测试用户store的基本方法\n        if (typeof this.userStore.getUserInfo === 'function') {\n          this.addLog('success', '✅ getUserInfo方法存在')\n        } else {\n          this.addLog('error', '❌ getUserInfo方法不存在')\n        }\n\n        if (typeof this.userStore.login === 'function') {\n          this.addLog('success', '✅ login方法存在')\n        } else {\n          this.addLog('error', '❌ login方法不存在')\n        }\n\n        if (typeof this.userStore.logout === 'function') {\n          this.addLog('success', '✅ logout方法存在')\n        } else {\n          this.addLog('error', '❌ logout方法不存在')\n        }\n\n        this.functionResults.user = '✅ 用户功能测试通过'\n        this.addLog('success', '用户功能测试完成')\n\n      } catch (error) {\n        this.functionResults.user = '❌ 用户功能测试失败'\n        this.addLog('error', `用户功能测试失败: ${error.message}`)\n      } finally {\n        this.testing = false\n      }\n    },\n\n    async testVenueFunctions() {\n      this.testing = true\n      try {\n        this.addLog('info', '开始测试场馆功能...')\n\n        // 测试场馆store的基本方法\n        if (typeof this.venueStore.getVenueList === 'function') {\n          this.addLog('success', '✅ getVenueList方法存在')\n        } else {\n          this.addLog('error', '❌ getVenueList方法不存在')\n        }\n\n        if (typeof this.venueStore.getVenueDetail === 'function') {\n          this.addLog('success', '✅ getVenueDetail方法存在')\n        } else {\n          this.addLog('error', '❌ getVenueDetail方法不存在')\n        }\n\n        if (typeof this.venueStore.searchVenues === 'function') {\n          this.addLog('success', '✅ searchVenues方法存在')\n        } else {\n          this.addLog('error', '❌ searchVenues方法不存在')\n        }\n\n        this.functionResults.venue = '✅ 场馆功能测试通过'\n        this.addLog('success', '场馆功能测试完成')\n\n      } catch (error) {\n        this.functionResults.venue = '❌ 场馆功能测试失败'\n        this.addLog('error', `场馆功能测试失败: ${error.message}`)\n      } finally {\n        this.testing = false\n      }\n    },\n\n    async testBookingFunctions() {\n      this.testing = true\n      try {\n        this.addLog('info', '开始测试预订功能...')\n\n        // 测试预订store的基本方法\n        if (typeof this.bookingStore.getMyBookings === 'function') {\n          this.addLog('success', '✅ getMyBookings方法存在')\n        } else {\n          this.addLog('error', '❌ getMyBookings方法不存在')\n        }\n\n        if (typeof this.bookingStore.createBooking === 'function') {\n          this.addLog('success', '✅ createBooking方法存在')\n        } else {\n          this.addLog('error', '❌ createBooking方法不存在')\n        }\n\n        if (typeof this.bookingStore.getBookingDetail === 'function') {\n          this.addLog('success', '✅ getBookingDetail方法存在')\n        } else {\n          this.addLog('error', '❌ getBookingDetail方法不存在')\n        }\n\n        this.functionResults.booking = '✅ 预订功能测试通过'\n        this.addLog('success', '预订功能测试完成')\n\n      } catch (error) {\n        this.functionResults.booking = '❌ 预订功能测试失败'\n        this.addLog('error', `预订功能测试失败: ${error.message}`)\n      } finally {\n        this.testing = false\n      }\n    },\n\n    async testSharingFunctions() {\n      this.testing = true\n      try {\n        this.addLog('info', '开始测试拼场功能...')\n\n        // 测试拼场store的基本方法\n        if (typeof this.sharingStore.getJoinableSharingOrders === 'function') {\n          this.addLog('success', '✅ getJoinableSharingOrders方法存在')\n        } else {\n          this.addLog('error', '❌ getJoinableSharingOrders方法不存在')\n        }\n\n        if (typeof this.sharingStore.createSharingOrder === 'function') {\n          this.addLog('success', '✅ createSharingOrder方法存在')\n        } else {\n          this.addLog('error', '❌ createSharingOrder方法不存在')\n        }\n\n        if (typeof this.sharingStore.applySharingOrder === 'function') {\n          this.addLog('success', '✅ applySharingOrder方法存在')\n        } else {\n          this.addLog('error', '❌ applySharingOrder方法不存在')\n        }\n\n        this.functionResults.sharing = '✅ 拼场功能测试通过'\n        this.addLog('success', '拼场功能测试完成')\n\n      } catch (error) {\n        this.functionResults.sharing = '❌ 拼场功能测试失败'\n        this.addLog('error', `拼场功能测试失败: ${error.message}`)\n      } finally {\n        this.testing = false\n      }\n    },\n\n    testPageNavigation(page) {\n      try {\n        uni.navigateTo({\n          url: `/pages/${page}`\n        })\n        this.addLog('success', `✅ 成功导航到 ${page}`)\n      } catch (error) {\n        this.addLog('error', `❌ 导航到 ${page} 失败: ${error.message}`)\n      }\n    },\n\n    // 测试Getter命名修复\n    async testGetterNamingFix() {\n      this.testing = true\n      this.addLog('info', '🔧 开始测试Getter命名修复...')\n\n      try {\n        // 测试Booking Store的新getter名称\n        if (this.bookingStore) {\n          this.addLog('info', '测试Booking Store getter命名...')\n\n          // 测试新的getter名称\n          const bookingList = this.bookingStore.bookingListGetter\n          const bookingDetail = this.bookingStore.bookingDetailGetter\n          const sharingOrders = this.bookingStore.sharingOrdersGetter\n\n          this.addLog('success', `✅ bookingListGetter: ${typeof bookingList}`)\n          this.addLog('success', `✅ bookingDetailGetter: ${typeof bookingDetail}`)\n          this.addLog('success', `✅ sharingOrdersGetter: ${typeof sharingOrders}`)\n        }\n\n        // 测试Sharing Store的新getter名称\n        if (this.sharingStore) {\n          this.addLog('info', '测试Sharing Store getter命名...')\n\n          const sharingOrdersGetter = this.sharingStore.sharingOrdersGetter\n          const mySharingOrdersGetter = this.sharingStore.mySharingOrdersGetter\n          const sharingOrderDetailGetter = this.sharingStore.sharingOrderDetailGetter\n\n          this.addLog('success', `✅ sharingOrdersGetter: ${typeof sharingOrdersGetter}`)\n          this.addLog('success', `✅ mySharingOrdersGetter: ${typeof mySharingOrdersGetter}`)\n          this.addLog('success', `✅ sharingOrderDetailGetter: ${typeof sharingOrderDetailGetter}`)\n        }\n\n        this.addLog('success', '🎉 Getter命名修复测试完成！所有命名冲突已解决')\n      } catch (error) {\n        this.addLog('error', `Getter命名修复测试失败: ${error.message}`)\n      } finally {\n        this.testing = false\n      }\n    },\n\n    async runFullTest() {\n      this.testing = true\n      this.addLog('info', '🚀 开始运行全面测试...')\n\n      try {\n        await this.testUserFunctions()\n        await new Promise(resolve => setTimeout(resolve, 500))\n\n        await this.testVenueFunctions()\n        await new Promise(resolve => setTimeout(resolve, 500))\n\n        await this.testBookingFunctions()\n        await new Promise(resolve => setTimeout(resolve, 500))\n\n        await this.testSharingFunctions()\n        await new Promise(resolve => setTimeout(resolve, 500))\n\n        this.addLog('success', '🎉 全面测试完成！')\n\n        if (this.allTestsPassed) {\n          uni.showToast({\n            title: '🎉 所有测试通过！',\n            icon: 'success',\n            duration: 3000\n          })\n        } else {\n          uni.showToast({\n            title: '⚠️ 部分测试失败',\n            icon: 'none',\n            duration: 3000\n          })\n        }\n\n      } catch (error) {\n        this.addLog('error', `全面测试失败: ${error.message}`)\n      } finally {\n        this.testing = false\n      }\n    }\n  }\n}\n</script>\n\n<style scoped>\n.test-container {\n  padding: 20px;\n  background-color: #f5f5f5;\n}\n\n.test-header {\n  text-align: center;\n  margin-bottom: 30px;\n}\n\n.test-title {\n  font-size: 24px;\n  font-weight: bold;\n  color: #333;\n  display: block;\n  margin-bottom: 10px;\n}\n\n.test-subtitle {\n  font-size: 14px;\n  color: #666;\n  display: block;\n}\n\n.section-title {\n  font-size: 18px;\n  font-weight: bold;\n  color: #333;\n  margin-bottom: 15px;\n  display: block;\n}\n\n.test-section {\n  background: white;\n  border-radius: 10px;\n  padding: 20px;\n  margin-bottom: 20px;\n  box-shadow: 0 2px 10px rgba(0,0,0,0.1);\n}\n\n.status-grid, .test-grid {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 10px;\n}\n\n.status-item, .test-item {\n  flex: 1;\n  min-width: 120px;\n  padding: 10px;\n  border-radius: 8px;\n  text-align: center;\n  border: 2px solid #ddd;\n}\n\n.status-item.success {\n  background-color: #d4edda;\n  border-color: #28a745;\n}\n\n.status-item.error {\n  background-color: #f8d7da;\n  border-color: #dc3545;\n}\n\n.test-item.success {\n  background-color: #d4edda;\n  border-color: #28a745;\n}\n\n.test-item.error {\n  background-color: #f8d7da;\n  border-color: #dc3545;\n}\n\n.function-tests {\n  display: flex;\n  flex-direction: column;\n  gap: 15px;\n}\n\n.function-group {\n  padding: 15px;\n  border: 1px solid #ddd;\n  border-radius: 8px;\n  background-color: #fafafa;\n}\n\n.group-title {\n  font-weight: bold;\n  color: #333;\n  margin-bottom: 10px;\n  display: block;\n}\n\n.test-btn, .nav-btn, .full-test-btn {\n  padding: 10px 20px;\n  border: none;\n  border-radius: 6px;\n  background-color: #007bff;\n  color: white;\n  font-size: 14px;\n  margin: 5px;\n}\n\n.test-btn:disabled, .full-test-btn:disabled {\n  background-color: #6c757d;\n}\n\n.full-test-btn {\n  width: 100%;\n  padding: 15px;\n  font-size: 16px;\n  font-weight: bold;\n  background-color: #28a745;\n}\n\n.nav-tests {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 10px;\n}\n\n.log-section {\n  background: white;\n  border-radius: 10px;\n  padding: 20px;\n  margin-top: 20px;\n}\n\n.log-container {\n  max-height: 300px;\n  overflow-y: auto;\n  background-color: #f8f9fa;\n  padding: 15px;\n  border-radius: 6px;\n}\n\n.log-item {\n  display: block;\n  padding: 5px 0;\n  font-family: monospace;\n  font-size: 12px;\n}\n\n.log-item.success {\n  color: #28a745;\n}\n\n.log-item.error {\n  color: #dc3545;\n}\n\n.log-item.info {\n  color: #17a2b8;\n}\n</style>\n", "import MiniProgramPage from '/Users/<USER>/Desktop/体育馆预约 2/体育馆预约2.0/pages/test/migration-validation.vue'\nwx.createPage(MiniProgramPage)"], "names": ["useUserStore", "useVenueStore", "useBookingStore", "useSharingStore", "useAppStore", "uni"], "mappings": ";;;;;;;AA8HA,MAAK,YAAU;AAAA,EACb,MAAM;AAAA,EAEN,OAAO;AACL,WAAO;AAAA,MACL,WAAW;AAAA,MACX,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,cAAc;AAAA,MACd,UAAU;AAAA,MAEV,SAAS;AAAA,MACT,aAAa;AAAA,QACX,WAAW;AAAA,QACX,YAAY;AAAA,QACZ,cAAc;AAAA,QACd,cAAc;AAAA,QACd,UAAU;AAAA,MACX;AAAA,MAED,iBAAiB;AAAA,QACf,MAAM;AAAA,QACN,OAAO;AAAA,QACP,SAAS;AAAA,QACT,SAAS;AAAA,MACV;AAAA,MAED,UAAU,CAAC;AAAA,IACb;AAAA,EACD;AAAA,EAED,UAAU;AAAA,IACR,aAAa;AACX,aAAO,OAAO,KAAK,KAAK,WAAW,EAAE;AAAA,IACtC;AAAA,IAED,cAAc;AACZ,aAAO,OAAO,OAAO,KAAK,WAAW,EAAE,OAAO,YAAU,MAAM,EAAE;AAAA,IACjE;AAAA,IAED,iBAAiB;AACf,aAAO,KAAK,gBAAgB,KAAK;AAAA,IACnC;AAAA,EACD;AAAA,EAED,SAAS;AACP,SAAK,iBAAiB;AACtB,SAAK,wBAAwB;AAAA,EAC9B;AAAA,EAED,SAAS;AAAA,IACP,mBAAmB;AACjB,UAAI;AACF,aAAK,YAAYA,yBAAa;AAC9B,aAAK,aAAaC,2BAAc;AAChC,aAAK,eAAeC,+BAAgB;AACpC,aAAK,eAAeC,+BAAgB;AACpC,aAAK,WAAWC,uBAAY;AAC5B,aAAK,OAAO,WAAW,cAAc;AAAA,MACrC,SAAO,OAAO;AACd,aAAK,OAAO,SAAS,eAAe,MAAM,OAAO,EAAE;AAAA,MACrD;AAAA,IACD;AAAA,IAED,0BAA0B;AAExB,WAAK,YAAY,YAAY,CAAC,CAAC,KAAK;AACpC,WAAK,YAAY,aAAa,CAAC,CAAC,KAAK;AACrC,WAAK,YAAY,eAAe,CAAC,CAAC,KAAK;AACvC,WAAK,YAAY,eAAe,CAAC,CAAC,KAAK;AACvC,WAAK,YAAY,WAAW,CAAC,CAAC,KAAK;AAEnC,WAAK,OAAO,QAAQ,gBAAgB,KAAK,WAAW,IAAI,KAAK,UAAU,KAAK;AAAA,IAC7E;AAAA,IAED,cAAc,UAAU;AACtB,aAAO,KAAK,YAAY,QAAQ,IAAI,YAAY;AAAA,IACjD;AAAA,IAED,OAAO,MAAM,SAAS;AACpB,YAAM,QAAO,oBAAI,KAAM,GAAC,mBAAmB;AAC3C,WAAK,SAAS,KAAK,EAAE,MAAM,SAAS,MAAM;AAAA,IAC3C;AAAA,IAED,MAAM,oBAAoB;AACxB,WAAK,UAAU;AACf,UAAI;AACF,aAAK,OAAO,QAAQ,aAAa;AAGjC,YAAI,OAAO,KAAK,UAAU,gBAAgB,YAAY;AACpD,eAAK,OAAO,WAAW,mBAAmB;AAAA,eACrC;AACL,eAAK,OAAO,SAAS,oBAAoB;AAAA,QAC3C;AAEA,YAAI,OAAO,KAAK,UAAU,UAAU,YAAY;AAC9C,eAAK,OAAO,WAAW,aAAa;AAAA,eAC/B;AACL,eAAK,OAAO,SAAS,cAAc;AAAA,QACrC;AAEA,YAAI,OAAO,KAAK,UAAU,WAAW,YAAY;AAC/C,eAAK,OAAO,WAAW,cAAc;AAAA,eAChC;AACL,eAAK,OAAO,SAAS,eAAe;AAAA,QACtC;AAEA,aAAK,gBAAgB,OAAO;AAC5B,aAAK,OAAO,WAAW,UAAU;AAAA,MAEjC,SAAO,OAAO;AACd,aAAK,gBAAgB,OAAO;AAC5B,aAAK,OAAO,SAAS,aAAa,MAAM,OAAO,EAAE;AAAA,MACnD,UAAU;AACR,aAAK,UAAU;AAAA,MACjB;AAAA,IACD;AAAA,IAED,MAAM,qBAAqB;AACzB,WAAK,UAAU;AACf,UAAI;AACF,aAAK,OAAO,QAAQ,aAAa;AAGjC,YAAI,OAAO,KAAK,WAAW,iBAAiB,YAAY;AACtD,eAAK,OAAO,WAAW,oBAAoB;AAAA,eACtC;AACL,eAAK,OAAO,SAAS,qBAAqB;AAAA,QAC5C;AAEA,YAAI,OAAO,KAAK,WAAW,mBAAmB,YAAY;AACxD,eAAK,OAAO,WAAW,sBAAsB;AAAA,eACxC;AACL,eAAK,OAAO,SAAS,uBAAuB;AAAA,QAC9C;AAEA,YAAI,OAAO,KAAK,WAAW,iBAAiB,YAAY;AACtD,eAAK,OAAO,WAAW,oBAAoB;AAAA,eACtC;AACL,eAAK,OAAO,SAAS,qBAAqB;AAAA,QAC5C;AAEA,aAAK,gBAAgB,QAAQ;AAC7B,aAAK,OAAO,WAAW,UAAU;AAAA,MAEjC,SAAO,OAAO;AACd,aAAK,gBAAgB,QAAQ;AAC7B,aAAK,OAAO,SAAS,aAAa,MAAM,OAAO,EAAE;AAAA,MACnD,UAAU;AACR,aAAK,UAAU;AAAA,MACjB;AAAA,IACD;AAAA,IAED,MAAM,uBAAuB;AAC3B,WAAK,UAAU;AACf,UAAI;AACF,aAAK,OAAO,QAAQ,aAAa;AAGjC,YAAI,OAAO,KAAK,aAAa,kBAAkB,YAAY;AACzD,eAAK,OAAO,WAAW,qBAAqB;AAAA,eACvC;AACL,eAAK,OAAO,SAAS,sBAAsB;AAAA,QAC7C;AAEA,YAAI,OAAO,KAAK,aAAa,kBAAkB,YAAY;AACzD,eAAK,OAAO,WAAW,qBAAqB;AAAA,eACvC;AACL,eAAK,OAAO,SAAS,sBAAsB;AAAA,QAC7C;AAEA,YAAI,OAAO,KAAK,aAAa,qBAAqB,YAAY;AAC5D,eAAK,OAAO,WAAW,wBAAwB;AAAA,eAC1C;AACL,eAAK,OAAO,SAAS,yBAAyB;AAAA,QAChD;AAEA,aAAK,gBAAgB,UAAU;AAC/B,aAAK,OAAO,WAAW,UAAU;AAAA,MAEjC,SAAO,OAAO;AACd,aAAK,gBAAgB,UAAU;AAC/B,aAAK,OAAO,SAAS,aAAa,MAAM,OAAO,EAAE;AAAA,MACnD,UAAU;AACR,aAAK,UAAU;AAAA,MACjB;AAAA,IACD;AAAA,IAED,MAAM,uBAAuB;AAC3B,WAAK,UAAU;AACf,UAAI;AACF,aAAK,OAAO,QAAQ,aAAa;AAGjC,YAAI,OAAO,KAAK,aAAa,6BAA6B,YAAY;AACpE,eAAK,OAAO,WAAW,gCAAgC;AAAA,eAClD;AACL,eAAK,OAAO,SAAS,iCAAiC;AAAA,QACxD;AAEA,YAAI,OAAO,KAAK,aAAa,uBAAuB,YAAY;AAC9D,eAAK,OAAO,WAAW,0BAA0B;AAAA,eAC5C;AACL,eAAK,OAAO,SAAS,2BAA2B;AAAA,QAClD;AAEA,YAAI,OAAO,KAAK,aAAa,sBAAsB,YAAY;AAC7D,eAAK,OAAO,WAAW,yBAAyB;AAAA,eAC3C;AACL,eAAK,OAAO,SAAS,0BAA0B;AAAA,QACjD;AAEA,aAAK,gBAAgB,UAAU;AAC/B,aAAK,OAAO,WAAW,UAAU;AAAA,MAEjC,SAAO,OAAO;AACd,aAAK,gBAAgB,UAAU;AAC/B,aAAK,OAAO,SAAS,aAAa,MAAM,OAAO,EAAE;AAAA,MACnD,UAAU;AACR,aAAK,UAAU;AAAA,MACjB;AAAA,IACD;AAAA,IAED,mBAAmB,MAAM;AACvB,UAAI;AACFC,sBAAAA,MAAI,WAAW;AAAA,UACb,KAAK,UAAU,IAAI;AAAA,SACpB;AACD,aAAK,OAAO,WAAW,WAAW,IAAI,EAAE;AAAA,MACxC,SAAO,OAAO;AACd,aAAK,OAAO,SAAS,SAAS,IAAI,QAAQ,MAAM,OAAO,EAAE;AAAA,MAC3D;AAAA,IACD;AAAA;AAAA,IAGD,MAAM,sBAAsB;AAC1B,WAAK,UAAU;AACf,WAAK,OAAO,QAAQ,sBAAsB;AAE1C,UAAI;AAEF,YAAI,KAAK,cAAc;AACrB,eAAK,OAAO,QAAQ,6BAA6B;AAGjD,gBAAM,cAAc,KAAK,aAAa;AACtC,gBAAM,gBAAgB,KAAK,aAAa;AACxC,gBAAM,gBAAgB,KAAK,aAAa;AAExC,eAAK,OAAO,WAAW,wBAAwB,OAAO,WAAW,EAAE;AACnE,eAAK,OAAO,WAAW,0BAA0B,OAAO,aAAa,EAAE;AACvE,eAAK,OAAO,WAAW,0BAA0B,OAAO,aAAa,EAAE;AAAA,QACzE;AAGA,YAAI,KAAK,cAAc;AACrB,eAAK,OAAO,QAAQ,6BAA6B;AAEjD,gBAAM,sBAAsB,KAAK,aAAa;AAC9C,gBAAM,wBAAwB,KAAK,aAAa;AAChD,gBAAM,2BAA2B,KAAK,aAAa;AAEnD,eAAK,OAAO,WAAW,0BAA0B,OAAO,mBAAmB,EAAE;AAC7E,eAAK,OAAO,WAAW,4BAA4B,OAAO,qBAAqB,EAAE;AACjF,eAAK,OAAO,WAAW,+BAA+B,OAAO,wBAAwB,EAAE;AAAA,QACzF;AAEA,aAAK,OAAO,WAAW,6BAA6B;AAAA,MACpD,SAAO,OAAO;AACd,aAAK,OAAO,SAAS,mBAAmB,MAAM,OAAO,EAAE;AAAA,MACzD,UAAU;AACR,aAAK,UAAU;AAAA,MACjB;AAAA,IACD;AAAA,IAED,MAAM,cAAc;AAClB,WAAK,UAAU;AACf,WAAK,OAAO,QAAQ,gBAAgB;AAEpC,UAAI;AACF,cAAM,KAAK,kBAAkB;AAC7B,cAAM,IAAI,QAAQ,aAAW,WAAW,SAAS,GAAG,CAAC;AAErD,cAAM,KAAK,mBAAmB;AAC9B,cAAM,IAAI,QAAQ,aAAW,WAAW,SAAS,GAAG,CAAC;AAErD,cAAM,KAAK,qBAAqB;AAChC,cAAM,IAAI,QAAQ,aAAW,WAAW,SAAS,GAAG,CAAC;AAErD,cAAM,KAAK,qBAAqB;AAChC,cAAM,IAAI,QAAQ,aAAW,WAAW,SAAS,GAAG,CAAC;AAErD,aAAK,OAAO,WAAW,YAAY;AAEnC,YAAI,KAAK,gBAAgB;AACvBA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,YACN,UAAU;AAAA,WACX;AAAA,eACI;AACLA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,YACN,UAAU;AAAA,WACX;AAAA,QACH;AAAA,MAEA,SAAO,OAAO;AACd,aAAK,OAAO,SAAS,WAAW,MAAM,OAAO,EAAE;AAAA,MACjD,UAAU;AACR,aAAK,UAAU;AAAA,MACjB;AAAA,IACF;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACzbA,GAAG,WAAW,eAAe;"}