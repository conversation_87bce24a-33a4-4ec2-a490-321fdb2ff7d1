{"version": 3, "file": "api-diagnosis.js", "sources": ["pages/test/api-diagnosis.vue", "pages/test/api-diagnosis.vue?type=page"], "sourcesContent": ["<template>\n  <view class=\"container\">\n    <view class=\"header\">\n      <text class=\"title\">🔍 API诊断工具</text>\n      <text class=\"subtitle\">专门用于排查Pinia迁移中的API问题</text>\n    </view>\n\n    <!-- API测试按钮 -->\n    <view class=\"test-section\">\n      <text class=\"section-title\">🌐 API连通性测试</text>\n      \n      <button class=\"test-btn\" @click=\"testBookingApis\" :disabled=\"testing\">\n        测试Booking APIs\n      </button>\n      \n      <button class=\"test-btn\" @click=\"testSharingApis\" :disabled=\"testing\">\n        测试Sharing APIs\n      </button>\n      \n      <button class=\"test-btn\" @click=\"testUserApis\" :disabled=\"testing\">\n        测试User APIs\n      </button>\n      \n      <button class=\"test-btn\" @click=\"testAllApis\" :disabled=\"testing\">\n        测试所有APIs\n      </button>\n    </view>\n\n    <!-- 测试结果 -->\n    <view class=\"results-section\">\n      <text class=\"section-title\">📊 测试结果</text>\n      <view class=\"log-container\">\n        <view v-for=\"(log, index) in logs\" :key=\"index\" :class=\"['log-item', log.type]\">\n          <text class=\"log-text\">{{ log.message }}</text>\n          <text class=\"log-time\">{{ log.time }}</text>\n        </view>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script>\nimport { useBookingStore } from '@/stores/booking.js'\nimport { useSharingStore } from '@/stores/sharing.js'\nimport { useUserStore } from '@/stores/user.js'\n\nexport default {\n  name: 'ApiDiagnosis',\n  data() {\n    return {\n      testing: false,\n      logs: []\n    }\n  },\n  \n  setup() {\n    const bookingStore = useBookingStore()\n    const sharingStore = useSharingStore()\n    const userStore = useUserStore()\n    \n    return {\n      bookingStore,\n      sharingStore,\n      userStore\n    }\n  },\n\n  methods: {\n    addLog(type, message) {\n      this.logs.push({\n        type,\n        message,\n        time: new Date().toLocaleTimeString()\n      })\n      console.log(`[API诊断] ${type.toUpperCase()}: ${message}`)\n    },\n\n    clearLogs() {\n      this.logs = []\n    },\n\n    // 测试Booking APIs\n    async testBookingApis() {\n      this.testing = true\n      this.addLog('info', '🔍 开始测试Booking APIs...')\n\n      try {\n        // 测试API方法存在性\n        const bookingApiMethods = [\n          'createBooking', 'getBookingDetail', 'createSharedBooking', \n          'cancelBooking', 'getUserBookings', 'getSharingOrdersList'\n        ]\n        \n        for (const method of bookingApiMethods) {\n          if (typeof this.bookingStore[method] === 'function') {\n            this.addLog('success', `✅ bookingStore.${method}: 方法存在`)\n          } else {\n            this.addLog('error', `❌ bookingStore.${method}: 方法不存在或类型错误`)\n          }\n        }\n\n        // 测试实际API调用（使用安全的测试数据）\n        try {\n          this.addLog('info', '测试getUserBookings API调用...')\n          await this.bookingStore.getUserBookings({ page: 1, pageSize: 1 })\n          this.addLog('success', '✅ getUserBookings API调用成功')\n        } catch (error) {\n          this.addLog('error', `❌ getUserBookings API调用失败: ${error.message}`)\n        }\n\n        this.addLog('success', '🎉 Booking APIs测试完成')\n      } catch (error) {\n        this.addLog('error', `Booking APIs测试失败: ${error.message}`)\n      } finally {\n        this.testing = false\n      }\n    },\n\n    // 测试Sharing APIs\n    async testSharingApis() {\n      this.testing = true\n      this.addLog('info', '🔍 开始测试Sharing APIs...')\n\n      try {\n        // 测试API方法存在性\n        const sharingApiMethods = [\n          'getSharingOrdersList', 'getOrderDetail', 'createOrder', \n          'handleRequest', 'applyJoinSharingOrder', 'confirmSharingOrder'\n        ]\n        \n        for (const method of sharingApiMethods) {\n          if (typeof this.sharingStore[method] === 'function') {\n            this.addLog('success', `✅ sharingStore.${method}: 方法存在`)\n          } else {\n            this.addLog('error', `❌ sharingStore.${method}: 方法不存在或类型错误`)\n          }\n        }\n\n        // 测试实际API调用\n        try {\n          this.addLog('info', '测试getSharingOrdersList API调用...')\n          await this.sharingStore.getSharingOrdersList({ page: 1, pageSize: 1 })\n          this.addLog('success', '✅ getSharingOrdersList API调用成功')\n        } catch (error) {\n          this.addLog('error', `❌ getSharingOrdersList API调用失败: ${error.message}`)\n        }\n\n        this.addLog('success', '🎉 Sharing APIs测试完成')\n      } catch (error) {\n        this.addLog('error', `Sharing APIs测试失败: ${error.message}`)\n      } finally {\n        this.testing = false\n      }\n    },\n\n    // 测试User APIs\n    async testUserApis() {\n      this.testing = true\n      this.addLog('info', '🔍 开始测试User APIs...')\n\n      try {\n        // 测试API方法存在性\n        const userApiMethods = [\n          'login', 'logout', 'getUserInfo', 'updateUserInfo'\n        ]\n        \n        for (const method of userApiMethods) {\n          if (typeof this.userStore[method] === 'function') {\n            this.addLog('success', `✅ userStore.${method}: 方法存在`)\n          } else {\n            this.addLog('error', `❌ userStore.${method}: 方法不存在或类型错误`)\n          }\n        }\n\n        // 测试实际API调用\n        try {\n          this.addLog('info', '测试getUserInfo API调用...')\n          await this.userStore.getUserInfo()\n          this.addLog('success', '✅ getUserInfo API调用成功')\n        } catch (error) {\n          this.addLog('error', `❌ getUserInfo API调用失败: ${error.message}`)\n        }\n\n        this.addLog('success', '🎉 User APIs测试完成')\n      } catch (error) {\n        this.addLog('error', `User APIs测试失败: ${error.message}`)\n      } finally {\n        this.testing = false\n      }\n    },\n\n    // 测试所有APIs\n    async testAllApis() {\n      this.testing = true\n      this.clearLogs()\n      this.addLog('info', '🚀 开始全面API测试...')\n\n      try {\n        await this.testBookingApis()\n        await new Promise(resolve => setTimeout(resolve, 1000))\n        \n        await this.testSharingApis()\n        await new Promise(resolve => setTimeout(resolve, 1000))\n        \n        await this.testUserApis()\n        \n        this.addLog('success', '🎉 全面API测试完成！')\n      } catch (error) {\n        this.addLog('error', `全面API测试失败: ${error.message}`)\n      } finally {\n        this.testing = false\n      }\n    }\n  }\n}\n</script>\n\n<style scoped>\n.container {\n  padding: 40rpx;\n  background-color: #f5f5f5;\n  min-height: 100vh;\n}\n\n.header {\n  text-align: center;\n  margin-bottom: 60rpx;\n}\n\n.title {\n  font-size: 48rpx;\n  font-weight: bold;\n  color: #333;\n  display: block;\n  margin-bottom: 20rpx;\n}\n\n.subtitle {\n  font-size: 28rpx;\n  color: #666;\n  display: block;\n}\n\n.test-section {\n  background: white;\n  border-radius: 20rpx;\n  padding: 40rpx;\n  margin-bottom: 40rpx;\n  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);\n}\n\n.section-title {\n  font-size: 32rpx;\n  font-weight: bold;\n  color: #333;\n  display: block;\n  margin-bottom: 30rpx;\n}\n\n.test-btn {\n  width: 100%;\n  height: 80rpx;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  border: none;\n  border-radius: 40rpx;\n  font-size: 28rpx;\n  margin-bottom: 20rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.test-btn:disabled {\n  background: #ccc;\n}\n\n.results-section {\n  background: white;\n  border-radius: 20rpx;\n  padding: 40rpx;\n  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);\n}\n\n.log-container {\n  max-height: 800rpx;\n  overflow-y: auto;\n}\n\n.log-item {\n  padding: 20rpx;\n  margin-bottom: 10rpx;\n  border-radius: 10rpx;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.log-item.info {\n  background-color: #e3f2fd;\n  border-left: 4rpx solid #2196f3;\n}\n\n.log-item.success {\n  background-color: #e8f5e8;\n  border-left: 4rpx solid #4caf50;\n}\n\n.log-item.error {\n  background-color: #ffebee;\n  border-left: 4rpx solid #f44336;\n}\n\n.log-text {\n  font-size: 26rpx;\n  flex: 1;\n}\n\n.log-time {\n  font-size: 22rpx;\n  color: #999;\n  margin-left: 20rpx;\n}\n</style>\n", "import MiniProgramPage from '/Users/<USER>/Desktop/体育馆预约 2/体育馆预约2.0/pages/test/api-diagnosis.vue'\nwx.createPage(MiniProgramPage)"], "names": ["useBookingStore", "useSharingStore", "useUserStore", "uni"], "mappings": ";;;;;AA8CA,MAAK,YAAU;AAAA,EACb,MAAM;AAAA,EACN,OAAO;AACL,WAAO;AAAA,MACL,SAAS;AAAA,MACT,MAAM,CAAC;AAAA,IACT;AAAA,EACD;AAAA,EAED,QAAQ;AACN,UAAM,eAAeA,eAAAA,gBAAgB;AACrC,UAAM,eAAeC,eAAAA,gBAAgB;AACrC,UAAM,YAAYC,YAAAA,aAAa;AAE/B,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACD;AAAA,EAED,SAAS;AAAA,IACP,OAAO,MAAM,SAAS;AACpB,WAAK,KAAK,KAAK;AAAA,QACb;AAAA,QACA;AAAA,QACA,OAAM,oBAAI,KAAM,GAAC,mBAAmB;AAAA,OACrC;AACDC,oBAAAA,MAAY,MAAA,OAAA,sCAAA,WAAW,KAAK,YAAa,CAAA,KAAK,OAAO,EAAE;AAAA,IACxD;AAAA,IAED,YAAY;AACV,WAAK,OAAO,CAAC;AAAA,IACd;AAAA;AAAA,IAGD,MAAM,kBAAkB;AACtB,WAAK,UAAU;AACf,WAAK,OAAO,QAAQ,wBAAwB;AAE5C,UAAI;AAEF,cAAM,oBAAoB;AAAA,UACxB;AAAA,UAAiB;AAAA,UAAoB;AAAA,UACrC;AAAA,UAAiB;AAAA,UAAmB;AAAA,QACtC;AAEA,mBAAW,UAAU,mBAAmB;AACtC,cAAI,OAAO,KAAK,aAAa,MAAM,MAAM,YAAY;AACnD,iBAAK,OAAO,WAAW,kBAAkB,MAAM,QAAQ;AAAA,iBAClD;AACL,iBAAK,OAAO,SAAS,kBAAkB,MAAM,cAAc;AAAA,UAC7D;AAAA,QACF;AAGA,YAAI;AACF,eAAK,OAAO,QAAQ,4BAA4B;AAChD,gBAAM,KAAK,aAAa,gBAAgB,EAAE,MAAM,GAAG,UAAU,GAAG;AAChE,eAAK,OAAO,WAAW,2BAA2B;AAAA,QAClD,SAAO,OAAO;AACd,eAAK,OAAO,SAAS,8BAA8B,MAAM,OAAO,EAAE;AAAA,QACpE;AAEA,aAAK,OAAO,WAAW,qBAAqB;AAAA,MAC5C,SAAO,OAAO;AACd,aAAK,OAAO,SAAS,qBAAqB,MAAM,OAAO,EAAE;AAAA,MAC3D,UAAU;AACR,aAAK,UAAU;AAAA,MACjB;AAAA,IACD;AAAA;AAAA,IAGD,MAAM,kBAAkB;AACtB,WAAK,UAAU;AACf,WAAK,OAAO,QAAQ,wBAAwB;AAE5C,UAAI;AAEF,cAAM,oBAAoB;AAAA,UACxB;AAAA,UAAwB;AAAA,UAAkB;AAAA,UAC1C;AAAA,UAAiB;AAAA,UAAyB;AAAA,QAC5C;AAEA,mBAAW,UAAU,mBAAmB;AACtC,cAAI,OAAO,KAAK,aAAa,MAAM,MAAM,YAAY;AACnD,iBAAK,OAAO,WAAW,kBAAkB,MAAM,QAAQ;AAAA,iBAClD;AACL,iBAAK,OAAO,SAAS,kBAAkB,MAAM,cAAc;AAAA,UAC7D;AAAA,QACF;AAGA,YAAI;AACF,eAAK,OAAO,QAAQ,iCAAiC;AACrD,gBAAM,KAAK,aAAa,qBAAqB,EAAE,MAAM,GAAG,UAAU,GAAG;AACrE,eAAK,OAAO,WAAW,gCAAgC;AAAA,QACvD,SAAO,OAAO;AACd,eAAK,OAAO,SAAS,mCAAmC,MAAM,OAAO,EAAE;AAAA,QACzE;AAEA,aAAK,OAAO,WAAW,qBAAqB;AAAA,MAC5C,SAAO,OAAO;AACd,aAAK,OAAO,SAAS,qBAAqB,MAAM,OAAO,EAAE;AAAA,MAC3D,UAAU;AACR,aAAK,UAAU;AAAA,MACjB;AAAA,IACD;AAAA;AAAA,IAGD,MAAM,eAAe;AACnB,WAAK,UAAU;AACf,WAAK,OAAO,QAAQ,qBAAqB;AAEzC,UAAI;AAEF,cAAM,iBAAiB;AAAA,UACrB;AAAA,UAAS;AAAA,UAAU;AAAA,UAAe;AAAA,QACpC;AAEA,mBAAW,UAAU,gBAAgB;AACnC,cAAI,OAAO,KAAK,UAAU,MAAM,MAAM,YAAY;AAChD,iBAAK,OAAO,WAAW,eAAe,MAAM,QAAQ;AAAA,iBAC/C;AACL,iBAAK,OAAO,SAAS,eAAe,MAAM,cAAc;AAAA,UAC1D;AAAA,QACF;AAGA,YAAI;AACF,eAAK,OAAO,QAAQ,wBAAwB;AAC5C,gBAAM,KAAK,UAAU,YAAY;AACjC,eAAK,OAAO,WAAW,uBAAuB;AAAA,QAC9C,SAAO,OAAO;AACd,eAAK,OAAO,SAAS,0BAA0B,MAAM,OAAO,EAAE;AAAA,QAChE;AAEA,aAAK,OAAO,WAAW,kBAAkB;AAAA,MACzC,SAAO,OAAO;AACd,aAAK,OAAO,SAAS,kBAAkB,MAAM,OAAO,EAAE;AAAA,MACxD,UAAU;AACR,aAAK,UAAU;AAAA,MACjB;AAAA,IACD;AAAA;AAAA,IAGD,MAAM,cAAc;AAClB,WAAK,UAAU;AACf,WAAK,UAAU;AACf,WAAK,OAAO,QAAQ,iBAAiB;AAErC,UAAI;AACF,cAAM,KAAK,gBAAgB;AAC3B,cAAM,IAAI,QAAQ,aAAW,WAAW,SAAS,GAAI,CAAC;AAEtD,cAAM,KAAK,gBAAgB;AAC3B,cAAM,IAAI,QAAQ,aAAW,WAAW,SAAS,GAAI,CAAC;AAEtD,cAAM,KAAK,aAAa;AAExB,aAAK,OAAO,WAAW,eAAe;AAAA,MACtC,SAAO,OAAO;AACd,aAAK,OAAO,SAAS,cAAc,MAAM,OAAO,EAAE;AAAA,MACpD,UAAU;AACR,aAAK,UAAU;AAAA,MACjB;AAAA,IACF;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;ACrNA,GAAG,WAAW,eAAe;"}