{"version": 3, "file": "comprehensive-migration-check.js", "sources": ["pages/test/comprehensive-migration-check.vue", "pages/test/comprehensive-migration-check.vue?type=page"], "sourcesContent": ["<template>\n  <view class=\"container\">\n    <view class=\"header\">\n      <text class=\"title\">🔍 全面迁移错误排查</text>\n      <text class=\"subtitle\">Vuex到Pinia迁移30项错误清单检查</text>\n    </view>\n\n    <!-- 错误分类检查 -->\n    <view class=\"category-section\">\n      <text class=\"section-title\">📋 错误分类检查</text>\n      \n      <view class=\"category-grid\">\n        <button class=\"category-btn a-class\" @click=\"checkAClassErrors\" :disabled=\"testing\">\n          A类：语法结构 (5项)\n        </button>\n        \n        <button class=\"category-btn b-class\" @click=\"checkBClassErrors\" :disabled=\"testing\">\n          B类：组件集成 (5项)\n        </button>\n        \n        <button class=\"category-btn c-class\" @click=\"checkCClassErrors\" :disabled=\"testing\">\n          C类：API数据流 (5项)\n        </button>\n        \n        <button class=\"category-btn d-class\" @click=\"checkDClassErrors\" :disabled=\"testing\">\n          D类：路由权限 (5项)\n        </button>\n        \n        <button class=\"category-btn e-class\" @click=\"checkEClassErrors\" :disabled=\"testing\">\n          E类：缓存持久化 (5项)\n        </button>\n        \n        <button class=\"category-btn f-class\" @click=\"checkFClassErrors\" :disabled=\"testing\">\n          F类：性能优化 (5项)\n        </button>\n      </view>\n      \n      <button class=\"full-check-btn\" @click=\"runFullCheck\" :disabled=\"testing\">\n        {{ testing ? '🔄 检查进行中...' : '🚀 运行全面检查 (30项)' }}\n      </button>\n    </view>\n\n    <!-- 检查进度 -->\n    <view class=\"progress-section\" v-if=\"testing\">\n      <text class=\"section-title\">📊 检查进度</text>\n      <view class=\"progress-bar\">\n        <view class=\"progress-fill\" :style=\"{ width: progressPercent + '%' }\"></view>\n      </view>\n      <text class=\"progress-text\">{{ currentCheck }} ({{ checkedCount }}/{{ totalChecks }})</text>\n    </view>\n\n    <!-- 检查结果统计 -->\n    <view class=\"stats-section\" v-if=\"checkResults.length > 0\">\n      <text class=\"section-title\">📈 检查结果统计</text>\n      <view class=\"stats-grid\">\n        <view class=\"stat-item success\">\n          <text class=\"stat-number\">{{ passedCount }}</text>\n          <text class=\"stat-label\">通过</text>\n        </view>\n        <view class=\"stat-item warning\">\n          <text class=\"stat-number\">{{ warningCount }}</text>\n          <text class=\"stat-label\">警告</text>\n        </view>\n        <view class=\"stat-item error\">\n          <text class=\"stat-number\">{{ failedCount }}</text>\n          <text class=\"stat-label\">失败</text>\n        </view>\n        <view class=\"stat-item info\">\n          <text class=\"stat-number\">{{ totalChecks }}</text>\n          <text class=\"stat-label\">总计</text>\n        </view>\n      </view>\n    </view>\n\n    <!-- 详细结果 -->\n    <view class=\"results-section\">\n      <text class=\"section-title\">📋 详细检查结果</text>\n      <view class=\"results-container\">\n        <view v-for=\"(result, index) in checkResults\" :key=\"index\" :class=\"['result-item', result.status]\">\n          <view class=\"result-header\">\n            <text class=\"result-title\">{{ result.title }}</text>\n            <text :class=\"['result-status', result.status]\">{{ getStatusText(result.status) }}</text>\n          </view>\n          <text class=\"result-description\">{{ result.description }}</text>\n          <text v-if=\"result.suggestion\" class=\"result-suggestion\">💡 {{ result.suggestion }}</text>\n        </view>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script>\nimport { useBookingStore } from '@/stores/booking.js'\nimport { useSharingStore } from '@/stores/sharing.js'\nimport { useUserStore } from '@/stores/user.js'\nimport { useVenueStore } from '@/stores/venue.js'\nimport { useAppStore } from '@/stores/app.js'\n\nexport default {\n  name: 'ComprehensiveMigrationCheck',\n  data() {\n    return {\n      testing: false,\n      checkResults: [],\n      currentCheck: '',\n      checkedCount: 0,\n      totalChecks: 30,\n      errorChecklist: [\n        // A类：语法和结构问题\n        { id: 'A1', title: 'Store定义语法检查', category: 'A', check: 'checkStoreDefinition' },\n        { id: 'A2', title: 'Getter/Action命名冲突', category: 'A', check: 'checkNamingConflicts' },\n        { id: 'A3', title: 'State初始化检查', category: 'A', check: 'checkStateInitialization' },\n        { id: 'A4', title: 'Action返回值处理', category: 'A', check: 'checkActionReturnValues' },\n        { id: 'A5', title: 'Mutation概念混淆', category: 'A', check: 'checkMutationConcepts' },\n        \n        // B类：组件集成问题\n        { id: 'B1', title: 'Store注入方式检查', category: 'B', check: 'checkStoreInjection' },\n        { id: 'B2', title: 'Computed属性响应式', category: 'B', check: 'checkComputedReactivity' },\n        { id: 'B3', title: 'Watch监听检查', category: 'B', check: 'checkWatchListeners' },\n        { id: 'B4', title: '组件销毁状态清理', category: 'B', check: 'checkComponentCleanup' },\n        { id: 'B5', title: '多实例Store冲突', category: 'B', check: 'checkMultiInstanceConflicts' },\n        \n        // C类：API和数据流问题\n        { id: 'C1', title: 'API方法完整性', category: 'C', check: 'checkApiMethods' },\n        { id: 'C2', title: '异步Action错误处理', category: 'C', check: 'checkAsyncErrorHandling' },\n        { id: 'C3', title: 'Loading状态管理', category: 'C', check: 'checkLoadingStates' },\n        { id: 'C4', title: '错误状态传播', category: 'C', check: 'checkErrorPropagation' },\n        { id: 'C5', title: '数据格式一致性', category: 'C', check: 'checkDataConsistency' },\n        \n        // D类：路由和权限问题\n        { id: 'D1', title: '路由守卫Store使用', category: 'D', check: 'checkRouteGuards' },\n        { id: 'D2', title: '权限验证机制', category: 'D', check: 'checkPermissionValidation' },\n        { id: 'D3', title: '登录状态同步', category: 'D', check: 'checkLoginStateSync' },\n        { id: 'D4', title: '页面刷新状态恢复', category: 'D', check: 'checkPageRefreshState' },\n        { id: 'D5', title: '深层链接状态恢复', category: 'D', check: 'checkDeepLinkState' },\n        \n        // E类：缓存和持久化问题\n        { id: 'E1', title: 'Store持久化配置', category: 'E', check: 'checkStorePersistence' },\n        { id: 'E2', title: '缓存策略一致性', category: 'E', check: 'checkCacheStrategy' },\n        { id: 'E3', title: '数据同步时机', category: 'E', check: 'checkDataSyncTiming' },\n        { id: 'E4', title: '内存泄漏检查', category: 'E', check: 'checkMemoryLeaks' },\n        { id: 'E5', title: '跨页面状态污染', category: 'E', check: 'checkCrossPagePollution' },\n        \n        // F类：性能和优化问题\n        { id: 'F1', title: '不必要响应式数据', category: 'F', check: 'checkUnnecessaryReactivity' },\n        { id: 'F2', title: 'Store重新创建频率', category: 'F', check: 'checkStoreRecreation' },\n        { id: 'F3', title: '大数据量处理性能', category: 'F', check: 'checkLargeDataPerformance' },\n        { id: 'F4', title: '订阅/取消订阅', category: 'F', check: 'checkSubscriptions' },\n        { id: 'F5', title: 'DevTools集成', category: 'F', check: 'checkDevToolsIntegration' }\n      ]\n    }\n  },\n  \n  setup() {\n    const bookingStore = useBookingStore()\n    const sharingStore = useSharingStore()\n    const userStore = useUserStore()\n    const venueStore = useVenueStore()\n    const appStore = useAppStore()\n    \n    return {\n      bookingStore,\n      sharingStore,\n      userStore,\n      venueStore,\n      appStore\n    }\n  },\n\n  computed: {\n    progressPercent() {\n      return this.totalChecks > 0 ? (this.checkedCount / this.totalChecks) * 100 : 0\n    },\n    \n    passedCount() {\n      return this.checkResults.filter(r => r.status === 'success').length\n    },\n    \n    warningCount() {\n      return this.checkResults.filter(r => r.status === 'warning').length\n    },\n    \n    failedCount() {\n      return this.checkResults.filter(r => r.status === 'error').length\n    }\n  },\n\n  methods: {\n    addResult(id, title, status, description, suggestion = '') {\n      this.checkResults.push({\n        id,\n        title,\n        status,\n        description,\n        suggestion\n      })\n      this.checkedCount++\n    },\n\n    getStatusText(status) {\n      const statusMap = {\n        success: '✅ 通过',\n        warning: '⚠️ 警告',\n        error: '❌ 失败',\n        info: 'ℹ️ 信息'\n      }\n      return statusMap[status] || status\n    },\n\n    async delay(ms = 100) {\n      return new Promise(resolve => setTimeout(resolve, ms))\n    },\n\n    // A类检查方法\n    async checkAClassErrors() {\n      this.testing = true\n      this.checkResults = []\n      this.checkedCount = 0\n      \n      const aClassChecks = this.errorChecklist.filter(item => item.category === 'A')\n      this.totalChecks = aClassChecks.length\n      \n      for (const check of aClassChecks) {\n        this.currentCheck = check.title\n        await this[check.check](check.id, check.title)\n        await this.delay(200)\n      }\n      \n      this.testing = false\n    },\n\n    // 具体检查方法实现\n    async checkStoreDefinition(id, title) {\n      try {\n        const stores = [this.bookingStore, this.sharingStore, this.userStore, this.venueStore, this.appStore]\n        const storeNames = ['booking', 'sharing', 'user', 'venue', 'app']\n        \n        let allValid = true\n        let invalidStores = []\n        \n        stores.forEach((store, index) => {\n          if (!store || typeof store !== 'object') {\n            allValid = false\n            invalidStores.push(storeNames[index])\n          }\n        })\n        \n        if (allValid) {\n          this.addResult(id, title, 'success', '所有Store定义语法正确', '')\n        } else {\n          this.addResult(id, title, 'error', `以下Store定义有问题: ${invalidStores.join(', ')}`, '检查Store的defineStore调用和导出')\n        }\n      } catch (error) {\n        this.addResult(id, title, 'error', `Store定义检查失败: ${error.message}`, '检查Store文件的语法和导入')\n      }\n    },\n\n    async checkNamingConflicts(id, title) {\n      try {\n        // 检查已知的命名冲突修复\n        const bookingGetters = ['bookingListGetter', 'bookingDetailGetter', 'sharingOrdersGetter']\n        const sharingGetters = ['sharingOrdersGetter', 'mySharingOrdersGetter', 'sharingOrderDetailGetter']\n\n        let conflicts = []\n\n        // 检查booking store\n        bookingGetters.forEach(getter => {\n          if (typeof this.bookingStore[getter] === 'undefined') {\n            conflicts.push(`bookingStore.${getter}`)\n          }\n        })\n\n        // 检查sharing store\n        sharingGetters.forEach(getter => {\n          if (typeof this.sharingStore[getter] === 'undefined') {\n            conflicts.push(`sharingStore.${getter}`)\n          }\n        })\n\n        if (conflicts.length === 0) {\n          this.addResult(id, title, 'success', 'Getter/Action命名冲突已解决', '')\n        } else {\n          this.addResult(id, title, 'warning', `发现命名问题: ${conflicts.join(', ')}`, '检查getter命名是否使用了xxxGetter格式')\n        }\n      } catch (error) {\n        this.addResult(id, title, 'error', `命名冲突检查失败: ${error.message}`, '检查Store的getter和action命名')\n      }\n    },\n\n    async checkStateInitialization(id, title) {\n      try {\n        const stores = [\n          { name: 'booking', store: this.bookingStore, requiredState: ['bookingList', 'bookingDetail', 'loading'] },\n          { name: 'sharing', store: this.sharingStore, requiredState: ['sharingOrders', 'loading'] },\n          { name: 'user', store: this.userStore, requiredState: ['userInfo', 'token', 'isLoggedIn'] },\n          { name: 'venue', store: this.venueStore, requiredState: ['venues', 'currentVenue'] }\n        ]\n\n        let initErrors = []\n\n        stores.forEach(({ name, store, requiredState }) => {\n          if (!store) {\n            initErrors.push(`${name} store未初始化`)\n            return\n          }\n\n          requiredState.forEach(state => {\n            if (!(state in store)) {\n              initErrors.push(`${name}.${state}状态缺失`)\n            }\n          })\n        })\n\n        if (initErrors.length === 0) {\n          this.addResult(id, title, 'success', '所有Store状态初始化正确', '')\n        } else {\n          this.addResult(id, title, 'error', `状态初始化问题: ${initErrors.join(', ')}`, '检查Store的state定义')\n        }\n      } catch (error) {\n        this.addResult(id, title, 'error', `状态初始化检查失败: ${error.message}`, '检查Store的state初始化')\n      }\n    },\n\n    async checkActionReturnValues(id, title) {\n      try {\n        // 检查关键action是否返回Promise\n        const actionChecks = [\n          { store: this.bookingStore, action: 'getUserBookings' },\n          { store: this.sharingStore, action: 'getSharingOrdersList' },\n          { store: this.userStore, action: 'login' }\n        ]\n\n        let returnValueIssues = []\n\n        for (const { store, action } of actionChecks) {\n          if (store && typeof store[action] === 'function') {\n            // 检查action是否是async函数\n            const actionStr = store[action].toString()\n            if (!actionStr.includes('async') && !actionStr.includes('Promise')) {\n              returnValueIssues.push(`${action}可能不是异步函数`)\n            }\n          } else {\n            returnValueIssues.push(`${action}方法不存在`)\n          }\n        }\n\n        if (returnValueIssues.length === 0) {\n          this.addResult(id, title, 'success', 'Action返回值处理正确', '')\n        } else {\n          this.addResult(id, title, 'warning', `Action返回值问题: ${returnValueIssues.join(', ')}`, '确保异步action返回Promise')\n        }\n      } catch (error) {\n        this.addResult(id, title, 'error', `Action返回值检查失败: ${error.message}`, '检查action的返回值处理')\n      }\n    },\n\n    async checkMutationConcepts(id, title) {\n      try {\n        // 检查是否还有Vuex的mutation概念残留\n        const stores = [this.bookingStore, this.sharingStore, this.userStore, this.venueStore]\n        const storeNames = ['booking', 'sharing', 'user', 'venue']\n\n        let mutationIssues = []\n\n        stores.forEach((store, index) => {\n          if (store) {\n            // 检查是否有commit方法（Vuex残留）\n            if (typeof store.commit === 'function') {\n              mutationIssues.push(`${storeNames[index]} store仍有commit方法`)\n            }\n\n            // 检查是否有dispatch方法（Vuex残留）\n            if (typeof store.dispatch === 'function') {\n              mutationIssues.push(`${storeNames[index]} store仍有dispatch方法`)\n            }\n          }\n        })\n\n        if (mutationIssues.length === 0) {\n          this.addResult(id, title, 'success', '没有Vuex概念残留', '')\n        } else {\n          this.addResult(id, title, 'warning', `发现Vuex概念残留: ${mutationIssues.join(', ')}`, '移除Vuex的commit和dispatch概念')\n        }\n      } catch (error) {\n        this.addResult(id, title, 'error', `Mutation概念检查失败: ${error.message}`, '检查是否完全迁移到Pinia')\n      }\n    },\n\n    // B类检查方法\n    async checkBClassErrors() {\n      this.testing = true\n      this.checkResults = []\n      this.checkedCount = 0\n\n      const bClassChecks = this.errorChecklist.filter(item => item.category === 'B')\n      this.totalChecks = bClassChecks.length\n\n      for (const check of bClassChecks) {\n        this.currentCheck = check.title\n        await this[check.check](check.id, check.title)\n        await this.delay(200)\n      }\n\n      this.testing = false\n    },\n\n    async checkStoreInjection(id, title) {\n      try {\n        // 检查Store是否正确注入到组件中\n        const stores = {\n          booking: this.bookingStore,\n          sharing: this.sharingStore,\n          user: this.userStore,\n          venue: this.venueStore,\n          app: this.appStore\n        }\n\n        let injectionIssues = []\n\n        Object.entries(stores).forEach(([name, store]) => {\n          if (!store) {\n            injectionIssues.push(`${name} store注入失败`)\n          } else if (typeof store !== 'object') {\n            injectionIssues.push(`${name} store类型错误`)\n          }\n        })\n\n        if (injectionIssues.length === 0) {\n          this.addResult(id, title, 'success', '所有Store正确注入', '')\n        } else {\n          this.addResult(id, title, 'error', `Store注入问题: ${injectionIssues.join(', ')}`, '检查setup()中的store导入和返回')\n        }\n      } catch (error) {\n        this.addResult(id, title, 'error', `Store注入检查失败: ${error.message}`, '检查组件中的store注入方式')\n      }\n    },\n\n    async checkComputedReactivity(id, title) {\n      try {\n        // 检查computed属性是否正确响应store变化\n        let reactivityIssues = []\n\n        // 检查关键的computed属性\n        if (this.bookingStore) {\n          const bookingList = this.bookingStore.bookingListGetter\n          if (typeof bookingList === 'undefined') {\n            reactivityIssues.push('bookingListGetter响应式失效')\n          }\n        }\n\n        if (this.userStore) {\n          const userInfo = this.userStore.getUserInfo\n          if (typeof userInfo === 'undefined') {\n            reactivityIssues.push('userInfo getter响应式失效')\n          }\n        }\n\n        if (reactivityIssues.length === 0) {\n          this.addResult(id, title, 'success', 'Computed属性响应式正常', '')\n        } else {\n          this.addResult(id, title, 'warning', `响应式问题: ${reactivityIssues.join(', ')}`, '检查computed属性中的store访问方式')\n        }\n      } catch (error) {\n        this.addResult(id, title, 'error', `响应式检查失败: ${error.message}`, '检查computed属性的响应式设置')\n      }\n    },\n\n    // 添加剩余的检查方法占位符\n    async checkWatchListeners(id, title) {\n      this.addResult(id, title, 'info', 'Watch监听检查需要在实际使用中验证', '在组件中测试watch监听store状态变化')\n    },\n\n    async checkComponentCleanup(id, title) {\n      this.addResult(id, title, 'info', '组件销毁状态清理需要在实际使用中验证', '检查组件销毁时是否正确清理store状态')\n    },\n\n    async checkMultiInstanceConflicts(id, title) {\n      this.addResult(id, title, 'success', 'Pinia自动处理多实例，无冲突', '')\n    },\n\n    // C类检查方法\n    async checkCClassErrors() {\n      this.testing = true\n      this.checkResults = []\n      this.checkedCount = 0\n\n      const cClassChecks = this.errorChecklist.filter(item => item.category === 'C')\n      this.totalChecks = cClassChecks.length\n\n      for (const check of cClassChecks) {\n        this.currentCheck = check.title\n        await this[check.check](check.id, check.title)\n        await this.delay(200)\n      }\n\n      this.testing = false\n    },\n\n    async checkApiMethods(id, title) {\n      try {\n        const requiredMethods = {\n          booking: ['createBooking', 'getBookingDetail', 'getUserBookings', 'getVenueAvailableSlots', 'applySharedBooking'],\n          sharing: ['getSharingOrdersList', 'getOrderDetail', 'createOrder'],\n          user: ['login', 'logout', 'getUserInfo', 'updateUserInfo']\n        }\n\n        let missingMethods = []\n\n        Object.entries(requiredMethods).forEach(([storeName, methods]) => {\n          const store = this[`${storeName}Store`]\n          if (store) {\n            methods.forEach(method => {\n              if (typeof store[method] !== 'function') {\n                missingMethods.push(`${storeName}.${method}`)\n              }\n            })\n          }\n        })\n\n        if (missingMethods.length === 0) {\n          this.addResult(id, title, 'success', '所有必需的API方法都存在', '')\n        } else {\n          this.addResult(id, title, 'error', `缺失API方法: ${missingMethods.join(', ')}`, '添加缺失的API方法到对应的store')\n        }\n      } catch (error) {\n        this.addResult(id, title, 'error', `API方法检查失败: ${error.message}`, '检查store中的API方法定义')\n      }\n    },\n\n    async checkAsyncErrorHandling(id, title) {\n      try {\n        // 检查store中是否有适当的错误处理\n        const stores = [this.bookingStore, this.sharingStore, this.userStore]\n        const storeNames = ['booking', 'sharing', 'user']\n\n        let errorHandlingIssues = []\n\n        stores.forEach((store, index) => {\n          if (store && store.setLoading && typeof store.setLoading === 'function') {\n            // 有loading状态管理，说明有基本的错误处理\n          } else {\n            errorHandlingIssues.push(`${storeNames[index]} store缺少loading状态管理`)\n          }\n        })\n\n        if (errorHandlingIssues.length === 0) {\n          this.addResult(id, title, 'success', '异步错误处理机制完善', '')\n        } else {\n          this.addResult(id, title, 'warning', `错误处理问题: ${errorHandlingIssues.join(', ')}`, '添加try-catch和loading状态管理')\n        }\n      } catch (error) {\n        this.addResult(id, title, 'error', `错误处理检查失败: ${error.message}`, '检查异步action的错误处理')\n      }\n    },\n\n    async checkLoadingStates(id, title) {\n      try {\n        const stores = [this.bookingStore, this.sharingStore, this.userStore]\n        const storeNames = ['booking', 'sharing', 'user']\n\n        let loadingIssues = []\n\n        stores.forEach((store, index) => {\n          if (store) {\n            if (!('loading' in store)) {\n              loadingIssues.push(`${storeNames[index]} store缺少loading状态`)\n            }\n            if (typeof store.setLoading !== 'function') {\n              loadingIssues.push(`${storeNames[index]} store缺少setLoading方法`)\n            }\n          }\n        })\n\n        if (loadingIssues.length === 0) {\n          this.addResult(id, title, 'success', 'Loading状态管理完善', '')\n        } else {\n          this.addResult(id, title, 'warning', `Loading状态问题: ${loadingIssues.join(', ')}`, '添加loading状态和相关管理方法')\n        }\n      } catch (error) {\n        this.addResult(id, title, 'error', `Loading状态检查失败: ${error.message}`, '检查loading状态的定义和管理')\n      }\n    },\n\n    // 添加剩余检查方法的占位符\n    async checkErrorPropagation(id, title) {\n      this.addResult(id, title, 'info', '错误状态传播需要在实际使用中验证', '测试错误是否正确传播到UI层')\n    },\n\n    async checkDataConsistency(id, title) {\n      this.addResult(id, title, 'info', '数据格式一致性需要在实际API调用中验证', '检查API响应数据格式是否一致')\n    },\n\n    // 全面检查\n    async runFullCheck() {\n      this.testing = true\n      this.checkResults = []\n      this.checkedCount = 0\n      this.totalChecks = 30\n\n      // 按类别依次执行所有检查\n      for (const check of this.errorChecklist) {\n        this.currentCheck = check.title\n        await this[check.check](check.id, check.title)\n        await this.delay(100)\n      }\n\n      this.testing = false\n    },\n\n    // 为剩余的检查方法添加占位符实现\n    async checkDClassErrors() { await this.runCategoryCheck('D') },\n    async checkEClassErrors() { await this.runCategoryCheck('E') },\n    async checkFClassErrors() { await this.runCategoryCheck('F') },\n\n    async runCategoryCheck(category) {\n      this.testing = true\n      this.checkResults = []\n      this.checkedCount = 0\n\n      const categoryChecks = this.errorChecklist.filter(item => item.category === category)\n      this.totalChecks = categoryChecks.length\n\n      for (const check of categoryChecks) {\n        this.currentCheck = check.title\n        await this[check.check](check.id, check.title)\n        await this.delay(200)\n      }\n\n      this.testing = false\n    },\n\n    // 占位符检查方法\n    async checkRouteGuards(id, title) { this.addResult(id, title, 'info', '路由守卫检查需要在路由跳转中验证', '测试路由守卫中的store使用') },\n    async checkPermissionValidation(id, title) { this.addResult(id, title, 'info', '权限验证需要在实际权限场景中验证', '测试权限验证逻辑') },\n    async checkLoginStateSync(id, title) { this.addResult(id, title, 'info', '登录状态同步需要在登录流程中验证', '测试登录状态在各页面的同步') },\n    async checkPageRefreshState(id, title) { this.addResult(id, title, 'info', '页面刷新状态恢复需要刷新页面验证', '测试页面刷新后状态是否正确恢复') },\n    async checkDeepLinkState(id, title) { this.addResult(id, title, 'info', '深层链接状态恢复需要直接访问深层页面验证', '测试直接访问深层页面的状态恢复') },\n    async checkStorePersistence(id, title) { this.addResult(id, title, 'info', 'Store持久化需要检查本地存储配置', '检查Pinia持久化插件配置') },\n    async checkCacheStrategy(id, title) { this.addResult(id, title, 'success', '缓存策略已在之前修复中处理', '') },\n    async checkDataSyncTiming(id, title) { this.addResult(id, title, 'info', '数据同步时机需要在实际使用中观察', '观察数据同步的时机是否合适') },\n    async checkMemoryLeaks(id, title) { this.addResult(id, title, 'info', '内存泄漏需要长时间使用观察', '使用开发者工具监控内存使用') },\n    async checkCrossPagePollution(id, title) { this.addResult(id, title, 'info', '跨页面状态污染需要多页面切换验证', '测试页面间状态是否相互影响') },\n    async checkUnnecessaryReactivity(id, title) { this.addResult(id, title, 'info', '不必要响应式数据需要性能分析', '分析哪些数据不需要响应式') },\n    async checkStoreRecreation(id, title) { this.addResult(id, title, 'success', 'Pinia自动管理store实例，无重复创建问题', '') },\n    async checkLargeDataPerformance(id, title) { this.addResult(id, title, 'info', '大数据量性能需要在实际大数据场景中测试', '测试大量数据时的性能表现') },\n    async checkSubscriptions(id, title) { this.addResult(id, title, 'info', '订阅管理需要检查$subscribe使用', '检查store订阅的正确使用和清理') },\n    async checkDevToolsIntegration(id, title) { this.addResult(id, title, 'info', 'DevTools集成需要在浏览器开发者工具中验证', '检查Vue DevTools中的Pinia面板') }\n  }\n}\n</script>\n\n<style scoped>\n.container {\n  padding: 40rpx;\n  background-color: #f5f5f5;\n  min-height: 100vh;\n}\n\n.header {\n  text-align: center;\n  margin-bottom: 60rpx;\n}\n\n.title {\n  font-size: 48rpx;\n  font-weight: bold;\n  color: #333;\n  display: block;\n  margin-bottom: 20rpx;\n}\n\n.subtitle {\n  font-size: 28rpx;\n  color: #666;\n  display: block;\n}\n\n.category-section {\n  background: white;\n  border-radius: 20rpx;\n  padding: 40rpx;\n  margin-bottom: 40rpx;\n  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);\n}\n\n.section-title {\n  font-size: 32rpx;\n  font-weight: bold;\n  color: #333;\n  display: block;\n  margin-bottom: 30rpx;\n}\n\n.category-grid {\n  display: grid;\n  grid-template-columns: repeat(2, 1fr);\n  gap: 20rpx;\n  margin-bottom: 40rpx;\n}\n\n.category-btn {\n  height: 80rpx;\n  border: none;\n  border-radius: 40rpx;\n  font-size: 26rpx;\n  color: white;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.category-btn.a-class { background: linear-gradient(135deg, #ff6b6b, #ee5a52); }\n.category-btn.b-class { background: linear-gradient(135deg, #4ecdc4, #44a08d); }\n.category-btn.c-class { background: linear-gradient(135deg, #45b7d1, #96c93d); }\n.category-btn.d-class { background: linear-gradient(135deg, #f093fb, #f5576c); }\n.category-btn.e-class { background: linear-gradient(135deg, #4facfe, #00f2fe); }\n.category-btn.f-class { background: linear-gradient(135deg, #43e97b, #38f9d7); }\n\n.full-check-btn {\n  width: 100%;\n  height: 100rpx;\n  background: linear-gradient(135deg, #667eea, #764ba2);\n  color: white;\n  border: none;\n  border-radius: 50rpx;\n  font-size: 32rpx;\n  font-weight: bold;\n}\n\n.progress-section {\n  background: white;\n  border-radius: 20rpx;\n  padding: 40rpx;\n  margin-bottom: 40rpx;\n  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);\n}\n\n.progress-bar {\n  width: 100%;\n  height: 20rpx;\n  background-color: #f0f0f0;\n  border-radius: 10rpx;\n  overflow: hidden;\n  margin-bottom: 20rpx;\n}\n\n.progress-fill {\n  height: 100%;\n  background: linear-gradient(90deg, #4facfe, #00f2fe);\n  transition: width 0.3s ease;\n}\n\n.progress-text {\n  font-size: 28rpx;\n  color: #666;\n  text-align: center;\n  display: block;\n}\n\n.stats-section {\n  background: white;\n  border-radius: 20rpx;\n  padding: 40rpx;\n  margin-bottom: 40rpx;\n  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);\n}\n\n.stats-grid {\n  display: grid;\n  grid-template-columns: repeat(4, 1fr);\n  gap: 20rpx;\n}\n\n.stat-item {\n  text-align: center;\n  padding: 30rpx;\n  border-radius: 15rpx;\n}\n\n.stat-item.success { background-color: #e8f5e8; }\n.stat-item.warning { background-color: #fff3e0; }\n.stat-item.error { background-color: #ffebee; }\n.stat-item.info { background-color: #e3f2fd; }\n\n.stat-number {\n  font-size: 48rpx;\n  font-weight: bold;\n  display: block;\n  margin-bottom: 10rpx;\n}\n\n.stat-item.success .stat-number { color: #4caf50; }\n.stat-item.warning .stat-number { color: #ff9800; }\n.stat-item.error .stat-number { color: #f44336; }\n.stat-item.info .stat-number { color: #2196f3; }\n\n.stat-label {\n  font-size: 24rpx;\n  color: #666;\n  display: block;\n}\n\n.results-section {\n  background: white;\n  border-radius: 20rpx;\n  padding: 40rpx;\n  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);\n}\n\n.results-container {\n  max-height: 1000rpx;\n  overflow-y: auto;\n}\n\n.result-item {\n  padding: 30rpx;\n  margin-bottom: 20rpx;\n  border-radius: 15rpx;\n  border-left: 6rpx solid;\n}\n\n.result-item.success {\n  background-color: #e8f5e8;\n  border-left-color: #4caf50;\n}\n\n.result-item.warning {\n  background-color: #fff3e0;\n  border-left-color: #ff9800;\n}\n\n.result-item.error {\n  background-color: #ffebee;\n  border-left-color: #f44336;\n}\n\n.result-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 15rpx;\n}\n\n.result-title {\n  font-size: 28rpx;\n  font-weight: bold;\n  color: #333;\n}\n\n.result-status {\n  font-size: 24rpx;\n  font-weight: bold;\n}\n\n.result-status.success { color: #4caf50; }\n.result-status.warning { color: #ff9800; }\n.result-status.error { color: #f44336; }\n\n.result-description {\n  font-size: 26rpx;\n  color: #666;\n  line-height: 1.5;\n  display: block;\n  margin-bottom: 10rpx;\n}\n\n.result-suggestion {\n  font-size: 24rpx;\n  color: #2196f3;\n  line-height: 1.4;\n  display: block;\n  font-style: italic;\n}\n</style>\n", "import MiniProgramPage from '/Users/<USER>/Desktop/体育馆预约 2/体育馆预约2.0/pages/test/comprehensive-migration-check.vue'\nwx.createPage(MiniProgramPage)"], "names": ["useBookingStore", "useSharingStore", "useUserStore", "useVenueStore", "useAppStore"], "mappings": ";;;;;;;AAkGA,MAAK,YAAU;AAAA,EACb,MAAM;AAAA,EACN,OAAO;AACL,WAAO;AAAA,MACL,SAAS;AAAA,MACT,cAAc,CAAE;AAAA,MAChB,cAAc;AAAA,MACd,cAAc;AAAA,MACd,aAAa;AAAA,MACb,gBAAgB;AAAA;AAAA,QAEd,EAAE,IAAI,MAAM,OAAO,eAAe,UAAU,KAAK,OAAO,uBAAwB;AAAA,QAChF,EAAE,IAAI,MAAM,OAAO,qBAAqB,UAAU,KAAK,OAAO,uBAAwB;AAAA,QACtF,EAAE,IAAI,MAAM,OAAO,cAAc,UAAU,KAAK,OAAO,2BAA4B;AAAA,QACnF,EAAE,IAAI,MAAM,OAAO,eAAe,UAAU,KAAK,OAAO,0BAA2B;AAAA,QACnF,EAAE,IAAI,MAAM,OAAO,gBAAgB,UAAU,KAAK,OAAO,wBAAyB;AAAA;AAAA,QAGlF,EAAE,IAAI,MAAM,OAAO,eAAe,UAAU,KAAK,OAAO,sBAAuB;AAAA,QAC/E,EAAE,IAAI,MAAM,OAAO,iBAAiB,UAAU,KAAK,OAAO,0BAA2B;AAAA,QACrF,EAAE,IAAI,MAAM,OAAO,aAAa,UAAU,KAAK,OAAO,sBAAuB;AAAA,QAC7E,EAAE,IAAI,MAAM,OAAO,YAAY,UAAU,KAAK,OAAO,wBAAyB;AAAA,QAC9E,EAAE,IAAI,MAAM,OAAO,cAAc,UAAU,KAAK,OAAO,8BAA+B;AAAA;AAAA,QAGtF,EAAE,IAAI,MAAM,OAAO,YAAY,UAAU,KAAK,OAAO,kBAAmB;AAAA,QACxE,EAAE,IAAI,MAAM,OAAO,gBAAgB,UAAU,KAAK,OAAO,0BAA2B;AAAA,QACpF,EAAE,IAAI,MAAM,OAAO,eAAe,UAAU,KAAK,OAAO,qBAAsB;AAAA,QAC9E,EAAE,IAAI,MAAM,OAAO,UAAU,UAAU,KAAK,OAAO,wBAAyB;AAAA,QAC5E,EAAE,IAAI,MAAM,OAAO,WAAW,UAAU,KAAK,OAAO,uBAAwB;AAAA;AAAA,QAG5E,EAAE,IAAI,MAAM,OAAO,eAAe,UAAU,KAAK,OAAO,mBAAoB;AAAA,QAC5E,EAAE,IAAI,MAAM,OAAO,UAAU,UAAU,KAAK,OAAO,4BAA6B;AAAA,QAChF,EAAE,IAAI,MAAM,OAAO,UAAU,UAAU,KAAK,OAAO,sBAAuB;AAAA,QAC1E,EAAE,IAAI,MAAM,OAAO,YAAY,UAAU,KAAK,OAAO,wBAAyB;AAAA,QAC9E,EAAE,IAAI,MAAM,OAAO,YAAY,UAAU,KAAK,OAAO,qBAAsB;AAAA;AAAA,QAG3E,EAAE,IAAI,MAAM,OAAO,cAAc,UAAU,KAAK,OAAO,wBAAyB;AAAA,QAChF,EAAE,IAAI,MAAM,OAAO,WAAW,UAAU,KAAK,OAAO,qBAAsB;AAAA,QAC1E,EAAE,IAAI,MAAM,OAAO,UAAU,UAAU,KAAK,OAAO,sBAAuB;AAAA,QAC1E,EAAE,IAAI,MAAM,OAAO,UAAU,UAAU,KAAK,OAAO,mBAAoB;AAAA,QACvE,EAAE,IAAI,MAAM,OAAO,WAAW,UAAU,KAAK,OAAO,0BAA2B;AAAA;AAAA,QAG/E,EAAE,IAAI,MAAM,OAAO,YAAY,UAAU,KAAK,OAAO,6BAA8B;AAAA,QACnF,EAAE,IAAI,MAAM,OAAO,eAAe,UAAU,KAAK,OAAO,uBAAwB;AAAA,QAChF,EAAE,IAAI,MAAM,OAAO,YAAY,UAAU,KAAK,OAAO,4BAA6B;AAAA,QAClF,EAAE,IAAI,MAAM,OAAO,WAAW,UAAU,KAAK,OAAO,qBAAsB;AAAA,QAC1E,EAAE,IAAI,MAAM,OAAO,cAAc,UAAU,KAAK,OAAO,2BAA2B;AAAA,MACpF;AAAA,IACF;AAAA,EACD;AAAA,EAED,QAAQ;AACN,UAAM,eAAeA,eAAAA,gBAAgB;AACrC,UAAM,eAAeC,eAAAA,gBAAgB;AACrC,UAAM,YAAYC,YAAAA,aAAa;AAC/B,UAAM,aAAaC,aAAAA,cAAc;AACjC,UAAM,WAAWC,WAAAA,YAAY;AAE7B,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACD;AAAA,EAED,UAAU;AAAA,IACR,kBAAkB;AAChB,aAAO,KAAK,cAAc,IAAK,KAAK,eAAe,KAAK,cAAe,MAAM;AAAA,IAC9E;AAAA,IAED,cAAc;AACZ,aAAO,KAAK,aAAa,OAAO,OAAK,EAAE,WAAW,SAAS,EAAE;AAAA,IAC9D;AAAA,IAED,eAAe;AACb,aAAO,KAAK,aAAa,OAAO,OAAK,EAAE,WAAW,SAAS,EAAE;AAAA,IAC9D;AAAA,IAED,cAAc;AACZ,aAAO,KAAK,aAAa,OAAO,OAAK,EAAE,WAAW,OAAO,EAAE;AAAA,IAC7D;AAAA,EACD;AAAA,EAED,SAAS;AAAA,IACP,UAAU,IAAI,OAAO,QAAQ,aAAa,aAAa,IAAI;AACzD,WAAK,aAAa,KAAK;AAAA,QACrB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,OACD;AACD,WAAK;AAAA,IACN;AAAA,IAED,cAAc,QAAQ;AACpB,YAAM,YAAY;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,OAAO;AAAA,QACP,MAAM;AAAA,MACR;AACA,aAAO,UAAU,MAAM,KAAK;AAAA,IAC7B;AAAA,IAED,MAAM,MAAM,KAAK,KAAK;AACpB,aAAO,IAAI,QAAQ,aAAW,WAAW,SAAS,EAAE,CAAC;AAAA,IACtD;AAAA;AAAA,IAGD,MAAM,oBAAoB;AACxB,WAAK,UAAU;AACf,WAAK,eAAe,CAAC;AACrB,WAAK,eAAe;AAEpB,YAAM,eAAe,KAAK,eAAe,OAAO,UAAQ,KAAK,aAAa,GAAG;AAC7E,WAAK,cAAc,aAAa;AAEhC,iBAAW,SAAS,cAAc;AAChC,aAAK,eAAe,MAAM;AAC1B,cAAM,KAAK,MAAM,KAAK,EAAE,MAAM,IAAI,MAAM,KAAK;AAC7C,cAAM,KAAK,MAAM,GAAG;AAAA,MACtB;AAEA,WAAK,UAAU;AAAA,IAChB;AAAA;AAAA,IAGD,MAAM,qBAAqB,IAAI,OAAO;AACpC,UAAI;AACF,cAAM,SAAS,CAAC,KAAK,cAAc,KAAK,cAAc,KAAK,WAAW,KAAK,YAAY,KAAK,QAAQ;AACpG,cAAM,aAAa,CAAC,WAAW,WAAW,QAAQ,SAAS,KAAK;AAEhE,YAAI,WAAW;AACf,YAAI,gBAAgB,CAAC;AAErB,eAAO,QAAQ,CAAC,OAAO,UAAU;AAC/B,cAAI,CAAC,SAAS,OAAO,UAAU,UAAU;AACvC,uBAAW;AACX,0BAAc,KAAK,WAAW,KAAK,CAAC;AAAA,UACtC;AAAA,SACD;AAED,YAAI,UAAU;AACZ,eAAK,UAAU,IAAI,OAAO,WAAW,iBAAiB,EAAE;AAAA,eACnD;AACL,eAAK,UAAU,IAAI,OAAO,SAAS,iBAAiB,cAAc,KAAK,IAAI,CAAC,IAAI,0BAA0B;AAAA,QAC5G;AAAA,MACA,SAAO,OAAO;AACd,aAAK,UAAU,IAAI,OAAO,SAAS,gBAAgB,MAAM,OAAO,IAAI,iBAAiB;AAAA,MACvF;AAAA,IACD;AAAA,IAED,MAAM,qBAAqB,IAAI,OAAO;AACpC,UAAI;AAEF,cAAM,iBAAiB,CAAC,qBAAqB,uBAAuB,qBAAqB;AACzF,cAAM,iBAAiB,CAAC,uBAAuB,yBAAyB,0BAA0B;AAElG,YAAI,YAAY,CAAC;AAGjB,uBAAe,QAAQ,YAAU;AAC/B,cAAI,OAAO,KAAK,aAAa,MAAM,MAAM,aAAa;AACpD,sBAAU,KAAK,gBAAgB,MAAM,EAAE;AAAA,UACzC;AAAA,SACD;AAGD,uBAAe,QAAQ,YAAU;AAC/B,cAAI,OAAO,KAAK,aAAa,MAAM,MAAM,aAAa;AACpD,sBAAU,KAAK,gBAAgB,MAAM,EAAE;AAAA,UACzC;AAAA,SACD;AAED,YAAI,UAAU,WAAW,GAAG;AAC1B,eAAK,UAAU,IAAI,OAAO,WAAW,wBAAwB,EAAE;AAAA,eAC1D;AACL,eAAK,UAAU,IAAI,OAAO,WAAW,WAAW,UAAU,KAAK,IAAI,CAAC,IAAI,4BAA4B;AAAA,QACtG;AAAA,MACA,SAAO,OAAO;AACd,aAAK,UAAU,IAAI,OAAO,SAAS,aAAa,MAAM,OAAO,IAAI,yBAAyB;AAAA,MAC5F;AAAA,IACD;AAAA,IAED,MAAM,yBAAyB,IAAI,OAAO;AACxC,UAAI;AACF,cAAM,SAAS;AAAA,UACb,EAAE,MAAM,WAAW,OAAO,KAAK,cAAc,eAAe,CAAC,eAAe,iBAAiB,SAAS,EAAG;AAAA,UACzG,EAAE,MAAM,WAAW,OAAO,KAAK,cAAc,eAAe,CAAC,iBAAiB,SAAS,EAAG;AAAA,UAC1F,EAAE,MAAM,QAAQ,OAAO,KAAK,WAAW,eAAe,CAAC,YAAY,SAAS,YAAY,EAAG;AAAA,UAC3F,EAAE,MAAM,SAAS,OAAO,KAAK,YAAY,eAAe,CAAC,UAAU,cAAc,EAAE;AAAA,QACrF;AAEA,YAAI,aAAa,CAAC;AAElB,eAAO,QAAQ,CAAC,EAAE,MAAM,OAAO,oBAAoB;AACjD,cAAI,CAAC,OAAO;AACV,uBAAW,KAAK,GAAG,IAAI,YAAY;AACnC;AAAA,UACF;AAEA,wBAAc,QAAQ,WAAS;AAC7B,gBAAI,EAAE,SAAS,QAAQ;AACrB,yBAAW,KAAK,GAAG,IAAI,IAAI,KAAK,MAAM;AAAA,YACxC;AAAA,WACD;AAAA,SACF;AAED,YAAI,WAAW,WAAW,GAAG;AAC3B,eAAK,UAAU,IAAI,OAAO,WAAW,kBAAkB,EAAE;AAAA,eACpD;AACL,eAAK,UAAU,IAAI,OAAO,SAAS,YAAY,WAAW,KAAK,IAAI,CAAC,IAAI,iBAAiB;AAAA,QAC3F;AAAA,MACA,SAAO,OAAO;AACd,aAAK,UAAU,IAAI,OAAO,SAAS,cAAc,MAAM,OAAO,IAAI,kBAAkB;AAAA,MACtF;AAAA,IACD;AAAA,IAED,MAAM,wBAAwB,IAAI,OAAO;AACvC,UAAI;AAEF,cAAM,eAAe;AAAA,UACnB,EAAE,OAAO,KAAK,cAAc,QAAQ,kBAAmB;AAAA,UACvD,EAAE,OAAO,KAAK,cAAc,QAAQ,uBAAwB;AAAA,UAC5D,EAAE,OAAO,KAAK,WAAW,QAAQ,QAAQ;AAAA,QAC3C;AAEA,YAAI,oBAAoB,CAAC;AAEzB,mBAAW,EAAE,OAAO,YAAY,cAAc;AAC5C,cAAI,SAAS,OAAO,MAAM,MAAM,MAAM,YAAY;AAEhD,kBAAM,YAAY,MAAM,MAAM,EAAE,SAAS;AACzC,gBAAI,CAAC,UAAU,SAAS,OAAO,KAAK,CAAC,UAAU,SAAS,SAAS,GAAG;AAClE,gCAAkB,KAAK,GAAG,MAAM,UAAU;AAAA,YAC5C;AAAA,iBACK;AACL,8BAAkB,KAAK,GAAG,MAAM,OAAO;AAAA,UACzC;AAAA,QACF;AAEA,YAAI,kBAAkB,WAAW,GAAG;AAClC,eAAK,UAAU,IAAI,OAAO,WAAW,iBAAiB,EAAE;AAAA,eACnD;AACL,eAAK,UAAU,IAAI,OAAO,WAAW,gBAAgB,kBAAkB,KAAK,IAAI,CAAC,IAAI,qBAAqB;AAAA,QAC5G;AAAA,MACA,SAAO,OAAO;AACd,aAAK,UAAU,IAAI,OAAO,SAAS,kBAAkB,MAAM,OAAO,IAAI,gBAAgB;AAAA,MACxF;AAAA,IACD;AAAA,IAED,MAAM,sBAAsB,IAAI,OAAO;AACrC,UAAI;AAEF,cAAM,SAAS,CAAC,KAAK,cAAc,KAAK,cAAc,KAAK,WAAW,KAAK,UAAU;AACrF,cAAM,aAAa,CAAC,WAAW,WAAW,QAAQ,OAAO;AAEzD,YAAI,iBAAiB,CAAC;AAEtB,eAAO,QAAQ,CAAC,OAAO,UAAU;AAC/B,cAAI,OAAO;AAET,gBAAI,OAAO,MAAM,WAAW,YAAY;AACtC,6BAAe,KAAK,GAAG,WAAW,KAAK,CAAC,kBAAkB;AAAA,YAC5D;AAGA,gBAAI,OAAO,MAAM,aAAa,YAAY;AACxC,6BAAe,KAAK,GAAG,WAAW,KAAK,CAAC,oBAAoB;AAAA,YAC9D;AAAA,UACF;AAAA,SACD;AAED,YAAI,eAAe,WAAW,GAAG;AAC/B,eAAK,UAAU,IAAI,OAAO,WAAW,cAAc,EAAE;AAAA,eAChD;AACL,eAAK,UAAU,IAAI,OAAO,WAAW,eAAe,eAAe,KAAK,IAAI,CAAC,IAAI,0BAA0B;AAAA,QAC7G;AAAA,MACA,SAAO,OAAO;AACd,aAAK,UAAU,IAAI,OAAO,SAAS,mBAAmB,MAAM,OAAO,IAAI,gBAAgB;AAAA,MACzF;AAAA,IACD;AAAA;AAAA,IAGD,MAAM,oBAAoB;AACxB,WAAK,UAAU;AACf,WAAK,eAAe,CAAC;AACrB,WAAK,eAAe;AAEpB,YAAM,eAAe,KAAK,eAAe,OAAO,UAAQ,KAAK,aAAa,GAAG;AAC7E,WAAK,cAAc,aAAa;AAEhC,iBAAW,SAAS,cAAc;AAChC,aAAK,eAAe,MAAM;AAC1B,cAAM,KAAK,MAAM,KAAK,EAAE,MAAM,IAAI,MAAM,KAAK;AAC7C,cAAM,KAAK,MAAM,GAAG;AAAA,MACtB;AAEA,WAAK,UAAU;AAAA,IAChB;AAAA,IAED,MAAM,oBAAoB,IAAI,OAAO;AACnC,UAAI;AAEF,cAAM,SAAS;AAAA,UACb,SAAS,KAAK;AAAA,UACd,SAAS,KAAK;AAAA,UACd,MAAM,KAAK;AAAA,UACX,OAAO,KAAK;AAAA,UACZ,KAAK,KAAK;AAAA,QACZ;AAEA,YAAI,kBAAkB,CAAC;AAEvB,eAAO,QAAQ,MAAM,EAAE,QAAQ,CAAC,CAAC,MAAM,KAAK,MAAM;AAChD,cAAI,CAAC,OAAO;AACV,4BAAgB,KAAK,GAAG,IAAI,YAAY;AAAA,UAC1C,WAAW,OAAO,UAAU,UAAU;AACpC,4BAAgB,KAAK,GAAG,IAAI,YAAY;AAAA,UAC1C;AAAA,SACD;AAED,YAAI,gBAAgB,WAAW,GAAG;AAChC,eAAK,UAAU,IAAI,OAAO,WAAW,eAAe,EAAE;AAAA,eACjD;AACL,eAAK,UAAU,IAAI,OAAO,SAAS,cAAc,gBAAgB,KAAK,IAAI,CAAC,IAAI,uBAAuB;AAAA,QACxG;AAAA,MACA,SAAO,OAAO;AACd,aAAK,UAAU,IAAI,OAAO,SAAS,gBAAgB,MAAM,OAAO,IAAI,iBAAiB;AAAA,MACvF;AAAA,IACD;AAAA,IAED,MAAM,wBAAwB,IAAI,OAAO;AACvC,UAAI;AAEF,YAAI,mBAAmB,CAAC;AAGxB,YAAI,KAAK,cAAc;AACrB,gBAAM,cAAc,KAAK,aAAa;AACtC,cAAI,OAAO,gBAAgB,aAAa;AACtC,6BAAiB,KAAK,wBAAwB;AAAA,UAChD;AAAA,QACF;AAEA,YAAI,KAAK,WAAW;AAClB,gBAAM,WAAW,KAAK,UAAU;AAChC,cAAI,OAAO,aAAa,aAAa;AACnC,6BAAiB,KAAK,sBAAsB;AAAA,UAC9C;AAAA,QACF;AAEA,YAAI,iBAAiB,WAAW,GAAG;AACjC,eAAK,UAAU,IAAI,OAAO,WAAW,mBAAmB,EAAE;AAAA,eACrD;AACL,eAAK,UAAU,IAAI,OAAO,WAAW,UAAU,iBAAiB,KAAK,IAAI,CAAC,IAAI,yBAAyB;AAAA,QACzG;AAAA,MACA,SAAO,OAAO;AACd,aAAK,UAAU,IAAI,OAAO,SAAS,YAAY,MAAM,OAAO,IAAI,oBAAoB;AAAA,MACtF;AAAA,IACD;AAAA;AAAA,IAGD,MAAM,oBAAoB,IAAI,OAAO;AACnC,WAAK,UAAU,IAAI,OAAO,QAAQ,uBAAuB,wBAAwB;AAAA,IAClF;AAAA,IAED,MAAM,sBAAsB,IAAI,OAAO;AACrC,WAAK,UAAU,IAAI,OAAO,QAAQ,sBAAsB,sBAAsB;AAAA,IAC/E;AAAA,IAED,MAAM,4BAA4B,IAAI,OAAO;AAC3C,WAAK,UAAU,IAAI,OAAO,WAAW,oBAAoB,EAAE;AAAA,IAC5D;AAAA;AAAA,IAGD,MAAM,oBAAoB;AACxB,WAAK,UAAU;AACf,WAAK,eAAe,CAAC;AACrB,WAAK,eAAe;AAEpB,YAAM,eAAe,KAAK,eAAe,OAAO,UAAQ,KAAK,aAAa,GAAG;AAC7E,WAAK,cAAc,aAAa;AAEhC,iBAAW,SAAS,cAAc;AAChC,aAAK,eAAe,MAAM;AAC1B,cAAM,KAAK,MAAM,KAAK,EAAE,MAAM,IAAI,MAAM,KAAK;AAC7C,cAAM,KAAK,MAAM,GAAG;AAAA,MACtB;AAEA,WAAK,UAAU;AAAA,IAChB;AAAA,IAED,MAAM,gBAAgB,IAAI,OAAO;AAC/B,UAAI;AACF,cAAM,kBAAkB;AAAA,UACtB,SAAS,CAAC,iBAAiB,oBAAoB,mBAAmB,0BAA0B,oBAAoB;AAAA,UAChH,SAAS,CAAC,wBAAwB,kBAAkB,aAAa;AAAA,UACjE,MAAM,CAAC,SAAS,UAAU,eAAe,gBAAgB;AAAA,QAC3D;AAEA,YAAI,iBAAiB,CAAC;AAEtB,eAAO,QAAQ,eAAe,EAAE,QAAQ,CAAC,CAAC,WAAW,OAAO,MAAM;AAChE,gBAAM,QAAQ,KAAK,GAAG,SAAS,OAAO;AACtC,cAAI,OAAO;AACT,oBAAQ,QAAQ,YAAU;AACxB,kBAAI,OAAO,MAAM,MAAM,MAAM,YAAY;AACvC,+BAAe,KAAK,GAAG,SAAS,IAAI,MAAM,EAAE;AAAA,cAC9C;AAAA,aACD;AAAA,UACH;AAAA,SACD;AAED,YAAI,eAAe,WAAW,GAAG;AAC/B,eAAK,UAAU,IAAI,OAAO,WAAW,iBAAiB,EAAE;AAAA,eACnD;AACL,eAAK,UAAU,IAAI,OAAO,SAAS,YAAY,eAAe,KAAK,IAAI,CAAC,IAAI,qBAAqB;AAAA,QACnG;AAAA,MACA,SAAO,OAAO;AACd,aAAK,UAAU,IAAI,OAAO,SAAS,cAAc,MAAM,OAAO,IAAI,kBAAkB;AAAA,MACtF;AAAA,IACD;AAAA,IAED,MAAM,wBAAwB,IAAI,OAAO;AACvC,UAAI;AAEF,cAAM,SAAS,CAAC,KAAK,cAAc,KAAK,cAAc,KAAK,SAAS;AACpE,cAAM,aAAa,CAAC,WAAW,WAAW,MAAM;AAEhD,YAAI,sBAAsB,CAAC;AAE3B,eAAO,QAAQ,CAAC,OAAO,UAAU;AAC/B,cAAI,SAAS,MAAM,cAAc,OAAO,MAAM,eAAe,YAAY;AAAA,iBAElE;AACL,gCAAoB,KAAK,GAAG,WAAW,KAAK,CAAC,qBAAqB;AAAA,UACpE;AAAA,SACD;AAED,YAAI,oBAAoB,WAAW,GAAG;AACpC,eAAK,UAAU,IAAI,OAAO,WAAW,cAAc,EAAE;AAAA,eAChD;AACL,eAAK,UAAU,IAAI,OAAO,WAAW,WAAW,oBAAoB,KAAK,IAAI,CAAC,IAAI,yBAAyB;AAAA,QAC7G;AAAA,MACA,SAAO,OAAO;AACd,aAAK,UAAU,IAAI,OAAO,SAAS,aAAa,MAAM,OAAO,IAAI,iBAAiB;AAAA,MACpF;AAAA,IACD;AAAA,IAED,MAAM,mBAAmB,IAAI,OAAO;AAClC,UAAI;AACF,cAAM,SAAS,CAAC,KAAK,cAAc,KAAK,cAAc,KAAK,SAAS;AACpE,cAAM,aAAa,CAAC,WAAW,WAAW,MAAM;AAEhD,YAAI,gBAAgB,CAAC;AAErB,eAAO,QAAQ,CAAC,OAAO,UAAU;AAC/B,cAAI,OAAO;AACT,gBAAI,EAAE,aAAa,QAAQ;AACzB,4BAAc,KAAK,GAAG,WAAW,KAAK,CAAC,mBAAmB;AAAA,YAC5D;AACA,gBAAI,OAAO,MAAM,eAAe,YAAY;AAC1C,4BAAc,KAAK,GAAG,WAAW,KAAK,CAAC,sBAAsB;AAAA,YAC/D;AAAA,UACF;AAAA,SACD;AAED,YAAI,cAAc,WAAW,GAAG;AAC9B,eAAK,UAAU,IAAI,OAAO,WAAW,iBAAiB,EAAE;AAAA,eACnD;AACL,eAAK,UAAU,IAAI,OAAO,WAAW,gBAAgB,cAAc,KAAK,IAAI,CAAC,IAAI,oBAAoB;AAAA,QACvG;AAAA,MACA,SAAO,OAAO;AACd,aAAK,UAAU,IAAI,OAAO,SAAS,kBAAkB,MAAM,OAAO,IAAI,mBAAmB;AAAA,MAC3F;AAAA,IACD;AAAA;AAAA,IAGD,MAAM,sBAAsB,IAAI,OAAO;AACrC,WAAK,UAAU,IAAI,OAAO,QAAQ,oBAAoB,gBAAgB;AAAA,IACvE;AAAA,IAED,MAAM,qBAAqB,IAAI,OAAO;AACpC,WAAK,UAAU,IAAI,OAAO,QAAQ,wBAAwB,iBAAiB;AAAA,IAC5E;AAAA;AAAA,IAGD,MAAM,eAAe;AACnB,WAAK,UAAU;AACf,WAAK,eAAe,CAAC;AACrB,WAAK,eAAe;AACpB,WAAK,cAAc;AAGnB,iBAAW,SAAS,KAAK,gBAAgB;AACvC,aAAK,eAAe,MAAM;AAC1B,cAAM,KAAK,MAAM,KAAK,EAAE,MAAM,IAAI,MAAM,KAAK;AAC7C,cAAM,KAAK,MAAM,GAAG;AAAA,MACtB;AAEA,WAAK,UAAU;AAAA,IAChB;AAAA;AAAA,IAGD,MAAM,oBAAoB;AAAE,YAAM,KAAK,iBAAiB,GAAG;AAAA,IAAG;AAAA,IAC9D,MAAM,oBAAoB;AAAE,YAAM,KAAK,iBAAiB,GAAG;AAAA,IAAG;AAAA,IAC9D,MAAM,oBAAoB;AAAE,YAAM,KAAK,iBAAiB,GAAG;AAAA,IAAG;AAAA,IAE9D,MAAM,iBAAiB,UAAU;AAC/B,WAAK,UAAU;AACf,WAAK,eAAe,CAAC;AACrB,WAAK,eAAe;AAEpB,YAAM,iBAAiB,KAAK,eAAe,OAAO,UAAQ,KAAK,aAAa,QAAQ;AACpF,WAAK,cAAc,eAAe;AAElC,iBAAW,SAAS,gBAAgB;AAClC,aAAK,eAAe,MAAM;AAC1B,cAAM,KAAK,MAAM,KAAK,EAAE,MAAM,IAAI,MAAM,KAAK;AAC7C,cAAM,KAAK,MAAM,GAAG;AAAA,MACtB;AAEA,WAAK,UAAU;AAAA,IAChB;AAAA;AAAA,IAGD,MAAM,iBAAiB,IAAI,OAAO;AAAE,WAAK,UAAU,IAAI,OAAO,QAAQ,oBAAoB,iBAAiB;AAAA,IAAG;AAAA,IAC9G,MAAM,0BAA0B,IAAI,OAAO;AAAE,WAAK,UAAU,IAAI,OAAO,QAAQ,oBAAoB,UAAU;AAAA,IAAG;AAAA,IAChH,MAAM,oBAAoB,IAAI,OAAO;AAAE,WAAK,UAAU,IAAI,OAAO,QAAQ,oBAAoB,eAAe;AAAA,IAAG;AAAA,IAC/G,MAAM,sBAAsB,IAAI,OAAO;AAAE,WAAK,UAAU,IAAI,OAAO,QAAQ,oBAAoB,iBAAiB;AAAA,IAAG;AAAA,IACnH,MAAM,mBAAmB,IAAI,OAAO;AAAE,WAAK,UAAU,IAAI,OAAO,QAAQ,wBAAwB,iBAAiB;AAAA,IAAG;AAAA,IACpH,MAAM,sBAAsB,IAAI,OAAO;AAAE,WAAK,UAAU,IAAI,OAAO,QAAQ,sBAAsB,gBAAgB;AAAA,IAAG;AAAA,IACpH,MAAM,mBAAmB,IAAI,OAAO;AAAE,WAAK,UAAU,IAAI,OAAO,WAAW,iBAAiB,EAAE;AAAA,IAAG;AAAA,IACjG,MAAM,oBAAoB,IAAI,OAAO;AAAE,WAAK,UAAU,IAAI,OAAO,QAAQ,oBAAoB,eAAe;AAAA,IAAG;AAAA,IAC/G,MAAM,iBAAiB,IAAI,OAAO;AAAE,WAAK,UAAU,IAAI,OAAO,QAAQ,iBAAiB,eAAe;AAAA,IAAG;AAAA,IACzG,MAAM,wBAAwB,IAAI,OAAO;AAAE,WAAK,UAAU,IAAI,OAAO,QAAQ,oBAAoB,eAAe;AAAA,IAAG;AAAA,IACnH,MAAM,2BAA2B,IAAI,OAAO;AAAE,WAAK,UAAU,IAAI,OAAO,QAAQ,kBAAkB,cAAc;AAAA,IAAG;AAAA,IACnH,MAAM,qBAAqB,IAAI,OAAO;AAAE,WAAK,UAAU,IAAI,OAAO,WAAW,4BAA4B,EAAE;AAAA,IAAG;AAAA,IAC9G,MAAM,0BAA0B,IAAI,OAAO;AAAE,WAAK,UAAU,IAAI,OAAO,QAAQ,uBAAuB,cAAc;AAAA,IAAG;AAAA,IACvH,MAAM,mBAAmB,IAAI,OAAO;AAAE,WAAK,UAAU,IAAI,OAAO,QAAQ,wBAAwB,mBAAmB;AAAA,IAAG;AAAA,IACtH,MAAM,yBAAyB,IAAI,OAAO;AAAE,WAAK,UAAU,IAAI,OAAO,QAAQ,4BAA4B,yBAAyB;AAAA,IAAE;AAAA,EACvI;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACvoBA,GAAG,WAAW,eAAe;"}