{"version": 3, "file": "naming-conflict-fix.js", "sources": ["pages/test/naming-conflict-fix.vue", "pages/test/naming-conflict-fix.vue?type=page"], "sourcesContent": ["<template>\n  <view class=\"container\">\n    <view class=\"header\">\n      <text class=\"title\">🔧 命名冲突修复验证</text>\n      <text class=\"subtitle\">专门解决Getter/Action命名冲突问题</text>\n    </view>\n\n    <!-- 问题说明 -->\n    <view class=\"problem-section\">\n      <text class=\"section-title\">🚨 发现的问题</text>\n      <view class=\"problem-card\">\n        <text class=\"problem-title\">getUserInfo 命名冲突</text>\n        <text class=\"problem-desc\">在User Store中同时存在：</text>\n        <text class=\"code-line\">• Getter: getUserInfo: (state) => state.userInfo</text>\n        <text class=\"code-line\">• Action: async getUserInfo() { ... }</text>\n        <text class=\"problem-result\">结果：Action被Getter覆盖，导致 \"is not a function\" 错误</text>\n      </view>\n    </view>\n\n    <!-- 修复方案 -->\n    <view class=\"solution-section\">\n      <text class=\"section-title\">✅ 修复方案</text>\n      <view class=\"solution-card\">\n        <text class=\"solution-title\">重命名Getter避免冲突</text>\n        <text class=\"code-line\">• 原Getter: getUserInfo → userInfoGetter</text>\n        <text class=\"code-line\">• Action保持: getUserInfo (不变)</text>\n        <text class=\"code-line\">• 更新引用: 所有使用getter的地方</text>\n      </view>\n    </view>\n\n    <!-- 验证测试 -->\n    <view class=\"test-section\">\n      <text class=\"section-title\">🧪 修复验证</text>\n      \n      <button class=\"test-btn primary\" @click=\"testNamingConflictFix\" :disabled=\"testing\">\n        验证命名冲突修复\n      </button>\n      \n      <button class=\"test-btn info\" @click=\"testUserStoreStructure\" :disabled=\"testing\">\n        检查User Store结构\n      </button>\n      \n      <button class=\"test-btn success\" @click=\"testGetterActionSeparation\" :disabled=\"testing\">\n        验证Getter/Action分离\n      </button>\n    </view>\n\n    <!-- 测试结果 -->\n    <view class=\"results-section\">\n      <text class=\"section-title\">📊 测试结果</text>\n      <view class=\"log-container\">\n        <view v-for=\"(log, index) in logs\" :key=\"index\" :class=\"['log-item', log.type]\">\n          <text class=\"log-text\">{{ log.message }}</text>\n          <text class=\"log-time\">{{ log.time }}</text>\n        </view>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script>\nimport { useUserStore } from '@/stores/user.js'\n\nexport default {\n  name: 'NamingConflictFix',\n  data() {\n    return {\n      testing: false,\n      logs: []\n    }\n  },\n  \n  setup() {\n    const userStore = useUserStore()\n    return { userStore }\n  },\n\n  methods: {\n    addLog(type, message) {\n      this.logs.push({\n        type,\n        message,\n        time: new Date().toLocaleTimeString()\n      })\n      console.log(`[命名冲突修复] ${type.toUpperCase()}: ${message}`)\n    },\n\n    clearLogs() {\n      this.logs = []\n    },\n\n    // 验证命名冲突修复\n    async testNamingConflictFix() {\n      this.testing = true\n      this.clearLogs()\n      this.addLog('info', '🔧 开始验证命名冲突修复...')\n\n      try {\n        // 1. 检查getter是否已重命名\n        this.addLog('info', '检查Getter重命名...')\n        \n        if (typeof this.userStore.getUserInfo !== 'undefined') {\n          const getUserInfoType = typeof this.userStore.getUserInfo\n          this.addLog('info', `getUserInfo类型: ${getUserInfoType}`)\n          \n          if (getUserInfoType === 'function') {\n            this.addLog('success', '✅ getUserInfo现在是function (Action)')\n          } else {\n            this.addLog('error', `❌ getUserInfo仍然是${getUserInfoType} (可能是Getter)`)\n          }\n        } else {\n          this.addLog('error', '❌ getUserInfo完全不存在')\n        }\n\n        // 2. 检查新的getter名称\n        if (typeof this.userStore.userInfoGetter !== 'undefined') {\n          this.addLog('success', '✅ userInfoGetter存在 (新的Getter名称)')\n          \n          const userInfo = this.userStore.userInfoGetter\n          this.addLog('info', `userInfoGetter返回值类型: ${typeof userInfo}`)\n          \n          if (userInfo && typeof userInfo === 'object') {\n            this.addLog('success', '✅ userInfoGetter返回用户信息对象')\n          } else {\n            this.addLog('warning', '⚠️ userInfoGetter返回值为空或类型异常')\n          }\n        } else {\n          this.addLog('error', '❌ userInfoGetter不存在 (新Getter未创建)')\n        }\n\n        // 3. 测试Action调用\n        this.addLog('info', '测试getUserInfo Action调用...')\n        \n        if (typeof this.userStore.getUserInfo === 'function') {\n          try {\n            // 不实际调用，只验证是否为函数\n            this.addLog('success', '✅ getUserInfo Action可以调用')\n          } catch (error) {\n            this.addLog('error', `❌ getUserInfo Action调用失败: ${error.message}`)\n          }\n        } else {\n          this.addLog('error', '❌ getUserInfo不是函数，无法作为Action调用')\n        }\n\n        this.addLog('success', '🎉 命名冲突修复验证完成！')\n      } catch (error) {\n        this.addLog('error', `验证过程出错: ${error.message}`)\n      } finally {\n        this.testing = false\n      }\n    },\n\n    // 检查User Store结构\n    async testUserStoreStructure() {\n      this.testing = true\n      this.addLog('info', '🔍 检查User Store结构...')\n\n      try {\n        // 获取所有属性和方法\n        const storeKeys = Object.keys(this.userStore)\n        const storeProps = Object.getOwnPropertyNames(this.userStore)\n        \n        this.addLog('info', `Store可枚举属性数量: ${storeKeys.length}`)\n        this.addLog('info', `Store所有属性数量: ${storeProps.length}`)\n\n        // 检查关键属性\n        const keyProperties = ['userInfo', 'token', 'isLoggedIn']\n        keyProperties.forEach(prop => {\n          if (prop in this.userStore) {\n            this.addLog('success', `✅ 状态属性 ${prop} 存在`)\n          } else {\n            this.addLog('error', `❌ 状态属性 ${prop} 缺失`)\n          }\n        })\n\n        // 检查关键方法\n        const keyMethods = ['getUserInfo', 'login', 'logout', 'setUserInfo']\n        keyMethods.forEach(method => {\n          if (typeof this.userStore[method] === 'function') {\n            this.addLog('success', `✅ Action方法 ${method} 存在`)\n          } else {\n            this.addLog('error', `❌ Action方法 ${method} 缺失或类型错误`)\n          }\n        })\n\n        // 检查getter\n        const keyGetters = ['userInfoGetter', 'getIsLoggedIn', 'userId', 'username']\n        keyGetters.forEach(getter => {\n          if (getter in this.userStore) {\n            this.addLog('success', `✅ Getter ${getter} 存在`)\n          } else {\n            this.addLog('warning', `⚠️ Getter ${getter} 可能缺失`)\n          }\n        })\n\n        this.addLog('success', '🎉 User Store结构检查完成！')\n      } catch (error) {\n        this.addLog('error', `结构检查失败: ${error.message}`)\n      } finally {\n        this.testing = false\n      }\n    },\n\n    // 验证Getter/Action分离\n    async testGetterActionSeparation() {\n      this.testing = true\n      this.addLog('info', '🔬 验证Getter/Action分离...')\n\n      try {\n        // 检查是否还有其他命名冲突\n        const potentialConflicts = [\n          { name: 'getUserInfo', shouldBe: 'function' },\n          { name: 'userInfoGetter', shouldBe: 'object' },\n          { name: 'getIsLoggedIn', shouldBe: 'boolean' },\n          { name: 'login', shouldBe: 'function' },\n          { name: 'logout', shouldBe: 'function' }\n        ]\n\n        let conflictCount = 0\n        \n        potentialConflicts.forEach(({ name, shouldBe }) => {\n          const actualType = typeof this.userStore[name]\n          \n          if (shouldBe === 'object' && this.userStore[name] !== null) {\n            // 对于userInfoGetter，检查是否返回对象\n            if (actualType === 'object' || this.userStore[name] === null) {\n              this.addLog('success', `✅ ${name}: 类型正确 (${actualType})`)\n            } else {\n              this.addLog('error', `❌ ${name}: 期望object，实际${actualType}`)\n              conflictCount++\n            }\n          } else if (shouldBe === 'boolean') {\n            // 对于boolean getter\n            const value = this.userStore[name]\n            if (typeof value === 'boolean') {\n              this.addLog('success', `✅ ${name}: 类型正确 (${actualType})`)\n            } else {\n              this.addLog('error', `❌ ${name}: 期望boolean，实际${actualType}`)\n              conflictCount++\n            }\n          } else if (shouldBe === 'function') {\n            // 对于action方法\n            if (actualType === 'function') {\n              this.addLog('success', `✅ ${name}: 类型正确 (${actualType})`)\n            } else {\n              this.addLog('error', `❌ ${name}: 期望function，实际${actualType}`)\n              conflictCount++\n            }\n          }\n        })\n\n        if (conflictCount === 0) {\n          this.addLog('success', '🎉 所有Getter/Action分离正确，无命名冲突！')\n        } else {\n          this.addLog('warning', `⚠️ 发现 ${conflictCount} 个潜在问题`)\n        }\n\n      } catch (error) {\n        this.addLog('error', `分离验证失败: ${error.message}`)\n      } finally {\n        this.testing = false\n      }\n    }\n  }\n}\n</script>\n\n<style scoped>\n.container {\n  padding: 40rpx;\n  background-color: #f5f5f5;\n  min-height: 100vh;\n}\n\n.header {\n  text-align: center;\n  margin-bottom: 60rpx;\n}\n\n.title {\n  font-size: 48rpx;\n  font-weight: bold;\n  color: #333;\n  display: block;\n  margin-bottom: 20rpx;\n}\n\n.subtitle {\n  font-size: 28rpx;\n  color: #666;\n  display: block;\n}\n\n.problem-section, .solution-section, .test-section, .results-section {\n  background: white;\n  border-radius: 20rpx;\n  padding: 40rpx;\n  margin-bottom: 40rpx;\n  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);\n}\n\n.section-title {\n  font-size: 32rpx;\n  font-weight: bold;\n  color: #333;\n  display: block;\n  margin-bottom: 30rpx;\n}\n\n.problem-card, .solution-card {\n  background: #f8f9fa;\n  border-radius: 15rpx;\n  padding: 30rpx;\n  border-left: 6rpx solid #ff6b6b;\n}\n\n.solution-card {\n  border-left-color: #4caf50;\n}\n\n.problem-title, .solution-title {\n  font-size: 28rpx;\n  font-weight: bold;\n  color: #333;\n  display: block;\n  margin-bottom: 15rpx;\n}\n\n.problem-desc {\n  font-size: 26rpx;\n  color: #666;\n  display: block;\n  margin-bottom: 15rpx;\n}\n\n.code-line {\n  font-size: 24rpx;\n  color: #555;\n  font-family: 'Courier New', monospace;\n  background: rgba(0,0,0,0.05);\n  padding: 8rpx 15rpx;\n  border-radius: 8rpx;\n  display: block;\n  margin-bottom: 10rpx;\n}\n\n.problem-result {\n  font-size: 26rpx;\n  color: #ff6b6b;\n  font-weight: bold;\n  display: block;\n  margin-top: 15rpx;\n}\n\n.test-btn {\n  width: 100%;\n  height: 80rpx;\n  color: white;\n  border: none;\n  border-radius: 40rpx;\n  font-size: 28rpx;\n  margin-bottom: 20rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.test-btn.primary {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n}\n\n.test-btn.info {\n  background: linear-gradient(135deg, #2196f3 0%, #1976d2 100%);\n}\n\n.test-btn.success {\n  background: linear-gradient(135deg, #4caf50 0%, #45a049 100%);\n}\n\n.test-btn:disabled {\n  background: #ccc !important;\n}\n\n.log-container {\n  max-height: 800rpx;\n  overflow-y: auto;\n}\n\n.log-item {\n  padding: 20rpx;\n  margin-bottom: 10rpx;\n  border-radius: 10rpx;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.log-item.info {\n  background-color: #e3f2fd;\n  border-left: 4rpx solid #2196f3;\n}\n\n.log-item.success {\n  background-color: #e8f5e8;\n  border-left: 4rpx solid #4caf50;\n}\n\n.log-item.error {\n  background-color: #ffebee;\n  border-left: 4rpx solid #f44336;\n}\n\n.log-item.warning {\n  background-color: #fff3e0;\n  border-left: 4rpx solid #ff9800;\n}\n\n.log-text {\n  font-size: 26rpx;\n  flex: 1;\n}\n\n.log-time {\n  font-size: 22rpx;\n  color: #999;\n  margin-left: 20rpx;\n}\n</style>\n", "import MiniProgramPage from '/Users/<USER>/Desktop/体育馆预约 2/体育馆预约2.0/pages/test/naming-conflict-fix.vue'\nwx.createPage(MiniProgramPage)"], "names": ["useUserStore", "uni"], "mappings": ";;;AA+DA,MAAK,YAAU;AAAA,EACb,MAAM;AAAA,EACN,OAAO;AACL,WAAO;AAAA,MACL,SAAS;AAAA,MACT,MAAM,CAAC;AAAA,IACT;AAAA,EACD;AAAA,EAED,QAAQ;AACN,UAAM,YAAYA,YAAAA,aAAa;AAC/B,WAAO,EAAE,UAAU;AAAA,EACpB;AAAA,EAED,SAAS;AAAA,IACP,OAAO,MAAM,SAAS;AACpB,WAAK,KAAK,KAAK;AAAA,QACb;AAAA,QACA;AAAA,QACA,OAAM,oBAAI,KAAM,GAAC,mBAAmB;AAAA,OACrC;AACDC,oBAAAA,MAAY,MAAA,OAAA,4CAAA,YAAY,KAAK,YAAa,CAAA,KAAK,OAAO,EAAE;AAAA,IACzD;AAAA,IAED,YAAY;AACV,WAAK,OAAO,CAAC;AAAA,IACd;AAAA;AAAA,IAGD,MAAM,wBAAwB;AAC5B,WAAK,UAAU;AACf,WAAK,UAAU;AACf,WAAK,OAAO,QAAQ,kBAAkB;AAEtC,UAAI;AAEF,aAAK,OAAO,QAAQ,gBAAgB;AAEpC,YAAI,OAAO,KAAK,UAAU,gBAAgB,aAAa;AACrD,gBAAM,kBAAkB,OAAO,KAAK,UAAU;AAC9C,eAAK,OAAO,QAAQ,kBAAkB,eAAe,EAAE;AAEvD,cAAI,oBAAoB,YAAY;AAClC,iBAAK,OAAO,WAAW,mCAAmC;AAAA,iBACrD;AACL,iBAAK,OAAO,SAAS,mBAAmB,eAAe,cAAc;AAAA,UACvE;AAAA,eACK;AACL,eAAK,OAAO,SAAS,oBAAoB;AAAA,QAC3C;AAGA,YAAI,OAAO,KAAK,UAAU,mBAAmB,aAAa;AACxD,eAAK,OAAO,WAAW,iCAAiC;AAExD,gBAAM,WAAW,KAAK,UAAU;AAChC,eAAK,OAAO,QAAQ,wBAAwB,OAAO,QAAQ,EAAE;AAE7D,cAAI,YAAY,OAAO,aAAa,UAAU;AAC5C,iBAAK,OAAO,WAAW,0BAA0B;AAAA,iBAC5C;AACL,iBAAK,OAAO,WAAW,6BAA6B;AAAA,UACtD;AAAA,eACK;AACL,eAAK,OAAO,SAAS,kCAAkC;AAAA,QACzD;AAGA,aAAK,OAAO,QAAQ,2BAA2B;AAE/C,YAAI,OAAO,KAAK,UAAU,gBAAgB,YAAY;AACpD,cAAI;AAEF,iBAAK,OAAO,WAAW,0BAA0B;AAAA,UACjD,SAAO,OAAO;AACd,iBAAK,OAAO,SAAS,6BAA6B,MAAM,OAAO,EAAE;AAAA,UACnE;AAAA,eACK;AACL,eAAK,OAAO,SAAS,gCAAgC;AAAA,QACvD;AAEA,aAAK,OAAO,WAAW,gBAAgB;AAAA,MACvC,SAAO,OAAO;AACd,aAAK,OAAO,SAAS,WAAW,MAAM,OAAO,EAAE;AAAA,MACjD,UAAU;AACR,aAAK,UAAU;AAAA,MACjB;AAAA,IACD;AAAA;AAAA,IAGD,MAAM,yBAAyB;AAC7B,WAAK,UAAU;AACf,WAAK,OAAO,QAAQ,sBAAsB;AAE1C,UAAI;AAEF,cAAM,YAAY,OAAO,KAAK,KAAK,SAAS;AAC5C,cAAM,aAAa,OAAO,oBAAoB,KAAK,SAAS;AAE5D,aAAK,OAAO,QAAQ,iBAAiB,UAAU,MAAM,EAAE;AACvD,aAAK,OAAO,QAAQ,gBAAgB,WAAW,MAAM,EAAE;AAGvD,cAAM,gBAAgB,CAAC,YAAY,SAAS,YAAY;AACxD,sBAAc,QAAQ,UAAQ;AAC5B,cAAI,QAAQ,KAAK,WAAW;AAC1B,iBAAK,OAAO,WAAW,UAAU,IAAI,KAAK;AAAA,iBACrC;AACL,iBAAK,OAAO,SAAS,UAAU,IAAI,KAAK;AAAA,UAC1C;AAAA,SACD;AAGD,cAAM,aAAa,CAAC,eAAe,SAAS,UAAU,aAAa;AACnE,mBAAW,QAAQ,YAAU;AAC3B,cAAI,OAAO,KAAK,UAAU,MAAM,MAAM,YAAY;AAChD,iBAAK,OAAO,WAAW,cAAc,MAAM,KAAK;AAAA,iBAC3C;AACL,iBAAK,OAAO,SAAS,cAAc,MAAM,UAAU;AAAA,UACrD;AAAA,SACD;AAGD,cAAM,aAAa,CAAC,kBAAkB,iBAAiB,UAAU,UAAU;AAC3E,mBAAW,QAAQ,YAAU;AAC3B,cAAI,UAAU,KAAK,WAAW;AAC5B,iBAAK,OAAO,WAAW,YAAY,MAAM,KAAK;AAAA,iBACzC;AACL,iBAAK,OAAO,WAAW,aAAa,MAAM,OAAO;AAAA,UACnD;AAAA,SACD;AAED,aAAK,OAAO,WAAW,sBAAsB;AAAA,MAC7C,SAAO,OAAO;AACd,aAAK,OAAO,SAAS,WAAW,MAAM,OAAO,EAAE;AAAA,MACjD,UAAU;AACR,aAAK,UAAU;AAAA,MACjB;AAAA,IACD;AAAA;AAAA,IAGD,MAAM,6BAA6B;AACjC,WAAK,UAAU;AACf,WAAK,OAAO,QAAQ,yBAAyB;AAE7C,UAAI;AAEF,cAAM,qBAAqB;AAAA,UACzB,EAAE,MAAM,eAAe,UAAU,WAAY;AAAA,UAC7C,EAAE,MAAM,kBAAkB,UAAU,SAAU;AAAA,UAC9C,EAAE,MAAM,iBAAiB,UAAU,UAAW;AAAA,UAC9C,EAAE,MAAM,SAAS,UAAU,WAAY;AAAA,UACvC,EAAE,MAAM,UAAU,UAAU,WAAW;AAAA,QACzC;AAEA,YAAI,gBAAgB;AAEpB,2BAAmB,QAAQ,CAAC,EAAE,MAAM,eAAe;AACjD,gBAAM,aAAa,OAAO,KAAK,UAAU,IAAI;AAE7C,cAAI,aAAa,YAAY,KAAK,UAAU,IAAI,MAAM,MAAM;AAE1D,gBAAI,eAAe,YAAY,KAAK,UAAU,IAAI,MAAM,MAAM;AAC5D,mBAAK,OAAO,WAAW,KAAK,IAAI,WAAW,UAAU,GAAG;AAAA,mBACnD;AACL,mBAAK,OAAO,SAAS,KAAK,IAAI,gBAAgB,UAAU,EAAE;AAC1D;AAAA,YACF;AAAA,qBACS,aAAa,WAAW;AAEjC,kBAAM,QAAQ,KAAK,UAAU,IAAI;AACjC,gBAAI,OAAO,UAAU,WAAW;AAC9B,mBAAK,OAAO,WAAW,KAAK,IAAI,WAAW,UAAU,GAAG;AAAA,mBACnD;AACL,mBAAK,OAAO,SAAS,KAAK,IAAI,iBAAiB,UAAU,EAAE;AAC3D;AAAA,YACF;AAAA,UACF,WAAW,aAAa,YAAY;AAElC,gBAAI,eAAe,YAAY;AAC7B,mBAAK,OAAO,WAAW,KAAK,IAAI,WAAW,UAAU,GAAG;AAAA,mBACnD;AACL,mBAAK,OAAO,SAAS,KAAK,IAAI,kBAAkB,UAAU,EAAE;AAC5D;AAAA,YACF;AAAA,UACF;AAAA,SACD;AAED,YAAI,kBAAkB,GAAG;AACvB,eAAK,OAAO,WAAW,+BAA+B;AAAA,eACjD;AACL,eAAK,OAAO,WAAW,SAAS,aAAa,QAAQ;AAAA,QACvD;AAAA,MAEA,SAAO,OAAO;AACd,aAAK,OAAO,SAAS,WAAW,MAAM,OAAO,EAAE;AAAA,MACjD,UAAU;AACR,aAAK,UAAU;AAAA,MACjB;AAAA,IACF;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;ACvQA,GAAG,WAAW,eAAe;"}