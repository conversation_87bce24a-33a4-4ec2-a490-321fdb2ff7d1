{"version": 3, "file": "quick-fix-validation.js", "sources": ["pages/test/quick-fix-validation.vue", "pages/test/quick-fix-validation.vue?type=page"], "sourcesContent": ["<template>\n  <view class=\"container\">\n    <view class=\"header\">\n      <text class=\"title\">🔧 快速修复验证</text>\n      <text class=\"subtitle\">验证刚刚修复的API方法</text>\n    </view>\n\n    <!-- 修复验证按钮 -->\n    <view class=\"test-section\">\n      <text class=\"section-title\">🚀 修复验证测试</text>\n      \n      <button class=\"test-btn success\" @click=\"testFixedMethods\" :disabled=\"testing\">\n        验证修复的方法\n      </button>\n      \n      <button class=\"test-btn info\" @click=\"testBookingStoreMethods\" :disabled=\"testing\">\n        测试Booking Store方法\n      </button>\n      \n      <button class=\"test-btn warning\" @click=\"testUserStoreMethods\" :disabled=\"testing\">\n        测试User Store方法\n      </button>\n    </view>\n\n    <!-- 测试结果 -->\n    <view class=\"results-section\">\n      <text class=\"section-title\">📊 验证结果</text>\n      <view class=\"log-container\">\n        <view v-for=\"(log, index) in logs\" :key=\"index\" :class=\"['log-item', log.type]\">\n          <text class=\"log-text\">{{ log.message }}</text>\n          <text class=\"log-time\">{{ log.time }}</text>\n        </view>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script>\nimport { useBookingStore } from '@/stores/booking.js'\nimport { useUserStore } from '@/stores/user.js'\n\nexport default {\n  name: 'QuickFixValidation',\n  data() {\n    return {\n      testing: false,\n      logs: []\n    }\n  },\n  \n  setup() {\n    const bookingStore = useBookingStore()\n    const userStore = useUserStore()\n    \n    return {\n      bookingStore,\n      userStore\n    }\n  },\n\n  methods: {\n    addLog(type, message) {\n      this.logs.push({\n        type,\n        message,\n        time: new Date().toLocaleTimeString()\n      })\n      console.log(`[快速修复验证] ${type.toUpperCase()}: ${message}`)\n    },\n\n    clearLogs() {\n      this.logs = []\n    },\n\n    // 验证修复的方法\n    async testFixedMethods() {\n      this.testing = true\n      this.clearLogs()\n      this.addLog('info', '🔧 开始验证修复的方法...')\n\n      try {\n        // 验证Booking Store新增的方法\n        this.addLog('info', '验证Booking Store新增方法...')\n        \n        const fixedBookingMethods = [\n          'getVenueAvailableSlots',\n          'applySharedBooking'\n        ]\n        \n        for (const method of fixedBookingMethods) {\n          if (typeof this.bookingStore[method] === 'function') {\n            this.addLog('success', `✅ bookingStore.${method}: 方法已修复`)\n          } else {\n            this.addLog('error', `❌ bookingStore.${method}: 方法仍然缺失`)\n          }\n        }\n\n        // 验证User Store新增的方法\n        this.addLog('info', '验证User Store新增方法...')\n        \n        const fixedUserMethods = [\n          'getUserInfo',\n          'updateUserInfo'\n        ]\n        \n        for (const method of fixedUserMethods) {\n          if (typeof this.userStore[method] === 'function') {\n            this.addLog('success', `✅ userStore.${method}: 方法已修复`)\n          } else {\n            this.addLog('error', `❌ userStore.${method}: 方法仍然缺失`)\n          }\n        }\n\n        this.addLog('success', '🎉 修复验证完成！')\n      } catch (error) {\n        this.addLog('error', `修复验证失败: ${error.message}`)\n      } finally {\n        this.testing = false\n      }\n    },\n\n    // 测试Booking Store方法\n    async testBookingStoreMethods() {\n      this.testing = true\n      this.addLog('info', '🔍 测试Booking Store所有方法...')\n\n      try {\n        const allBookingMethods = [\n          'createBooking', 'getBookingDetail', 'createSharedBooking', \n          'cancelBooking', 'getUserBookings', 'getSharingOrdersList',\n          'getVenueAvailableSlots', 'applySharedBooking', 'createSharingOrderNew'\n        ]\n        \n        let successCount = 0\n        let totalCount = allBookingMethods.length\n        \n        for (const method of allBookingMethods) {\n          if (typeof this.bookingStore[method] === 'function') {\n            this.addLog('success', `✅ bookingStore.${method}: 存在`)\n            successCount++\n          } else {\n            this.addLog('error', `❌ bookingStore.${method}: 不存在`)\n          }\n        }\n\n        this.addLog('info', `📊 Booking Store方法统计: ${successCount}/${totalCount} 个方法可用`)\n        \n        if (successCount === totalCount) {\n          this.addLog('success', '🎉 所有Booking Store方法都可用！')\n        } else {\n          this.addLog('warning', `⚠️ 还有 ${totalCount - successCount} 个方法需要修复`)\n        }\n\n      } catch (error) {\n        this.addLog('error', `Booking Store方法测试失败: ${error.message}`)\n      } finally {\n        this.testing = false\n      }\n    },\n\n    // 测试User Store方法\n    async testUserStoreMethods() {\n      this.testing = true\n      this.addLog('info', '🔍 测试User Store所有方法...')\n\n      try {\n        const allUserMethods = [\n          'login', 'logout', 'register', 'getUserInfo', 'updateUserInfo', 'checkLoginStatus'\n        ]\n        \n        let successCount = 0\n        let totalCount = allUserMethods.length\n        \n        for (const method of allUserMethods) {\n          if (typeof this.userStore[method] === 'function') {\n            this.addLog('success', `✅ userStore.${method}: 存在`)\n            successCount++\n          } else {\n            this.addLog('error', `❌ userStore.${method}: 不存在`)\n          }\n        }\n\n        this.addLog('info', `📊 User Store方法统计: ${successCount}/${totalCount} 个方法可用`)\n        \n        if (successCount === totalCount) {\n          this.addLog('success', '🎉 所有User Store方法都可用！')\n        } else {\n          this.addLog('warning', `⚠️ 还有 ${totalCount - successCount} 个方法需要修复`)\n        }\n\n      } catch (error) {\n        this.addLog('error', `User Store方法测试失败: ${error.message}`)\n      } finally {\n        this.testing = false\n      }\n    }\n  }\n}\n</script>\n\n<style scoped>\n.container {\n  padding: 40rpx;\n  background-color: #f5f5f5;\n  min-height: 100vh;\n}\n\n.header {\n  text-align: center;\n  margin-bottom: 60rpx;\n}\n\n.title {\n  font-size: 48rpx;\n  font-weight: bold;\n  color: #333;\n  display: block;\n  margin-bottom: 20rpx;\n}\n\n.subtitle {\n  font-size: 28rpx;\n  color: #666;\n  display: block;\n}\n\n.test-section {\n  background: white;\n  border-radius: 20rpx;\n  padding: 40rpx;\n  margin-bottom: 40rpx;\n  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);\n}\n\n.section-title {\n  font-size: 32rpx;\n  font-weight: bold;\n  color: #333;\n  display: block;\n  margin-bottom: 30rpx;\n}\n\n.test-btn {\n  width: 100%;\n  height: 80rpx;\n  color: white;\n  border: none;\n  border-radius: 40rpx;\n  font-size: 28rpx;\n  margin-bottom: 20rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.test-btn.success {\n  background: linear-gradient(135deg, #4caf50 0%, #45a049 100%);\n}\n\n.test-btn.info {\n  background: linear-gradient(135deg, #2196f3 0%, #1976d2 100%);\n}\n\n.test-btn.warning {\n  background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%);\n}\n\n.test-btn:disabled {\n  background: #ccc !important;\n}\n\n.results-section {\n  background: white;\n  border-radius: 20rpx;\n  padding: 40rpx;\n  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);\n}\n\n.log-container {\n  max-height: 800rpx;\n  overflow-y: auto;\n}\n\n.log-item {\n  padding: 20rpx;\n  margin-bottom: 10rpx;\n  border-radius: 10rpx;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.log-item.info {\n  background-color: #e3f2fd;\n  border-left: 4rpx solid #2196f3;\n}\n\n.log-item.success {\n  background-color: #e8f5e8;\n  border-left: 4rpx solid #4caf50;\n}\n\n.log-item.error {\n  background-color: #ffebee;\n  border-left: 4rpx solid #f44336;\n}\n\n.log-item.warning {\n  background-color: #fff3e0;\n  border-left: 4rpx solid #ff9800;\n}\n\n.log-text {\n  font-size: 26rpx;\n  flex: 1;\n}\n\n.log-time {\n  font-size: 22rpx;\n  color: #999;\n  margin-left: 20rpx;\n}\n</style>\n", "import MiniProgramPage from '/Users/<USER>/Desktop/体育馆预约 2/体育馆预约2.0/pages/test/quick-fix-validation.vue'\nwx.createPage(MiniProgramPage)"], "names": ["useBookingStore", "useUserStore", "uni"], "mappings": ";;;;AAyCA,MAAK,YAAU;AAAA,EACb,MAAM;AAAA,EACN,OAAO;AACL,WAAO;AAAA,MACL,SAAS;AAAA,MACT,MAAM,CAAC;AAAA,IACT;AAAA,EACD;AAAA,EAED,QAAQ;AACN,UAAM,eAAeA,eAAAA,gBAAgB;AACrC,UAAM,YAAYC,YAAAA,aAAa;AAE/B,WAAO;AAAA,MACL;AAAA,MACA;AAAA,IACF;AAAA,EACD;AAAA,EAED,SAAS;AAAA,IACP,OAAO,MAAM,SAAS;AACpB,WAAK,KAAK,KAAK;AAAA,QACb;AAAA,QACA;AAAA,QACA,OAAM,oBAAI,KAAM,GAAC,mBAAmB;AAAA,OACrC;AACDC,oBAAAA,MAAY,MAAA,OAAA,6CAAA,YAAY,KAAK,YAAa,CAAA,KAAK,OAAO,EAAE;AAAA,IACzD;AAAA,IAED,YAAY;AACV,WAAK,OAAO,CAAC;AAAA,IACd;AAAA;AAAA,IAGD,MAAM,mBAAmB;AACvB,WAAK,UAAU;AACf,WAAK,UAAU;AACf,WAAK,OAAO,QAAQ,iBAAiB;AAErC,UAAI;AAEF,aAAK,OAAO,QAAQ,wBAAwB;AAE5C,cAAM,sBAAsB;AAAA,UAC1B;AAAA,UACA;AAAA,QACF;AAEA,mBAAW,UAAU,qBAAqB;AACxC,cAAI,OAAO,KAAK,aAAa,MAAM,MAAM,YAAY;AACnD,iBAAK,OAAO,WAAW,kBAAkB,MAAM,SAAS;AAAA,iBACnD;AACL,iBAAK,OAAO,SAAS,kBAAkB,MAAM,UAAU;AAAA,UACzD;AAAA,QACF;AAGA,aAAK,OAAO,QAAQ,qBAAqB;AAEzC,cAAM,mBAAmB;AAAA,UACvB;AAAA,UACA;AAAA,QACF;AAEA,mBAAW,UAAU,kBAAkB;AACrC,cAAI,OAAO,KAAK,UAAU,MAAM,MAAM,YAAY;AAChD,iBAAK,OAAO,WAAW,eAAe,MAAM,SAAS;AAAA,iBAChD;AACL,iBAAK,OAAO,SAAS,eAAe,MAAM,UAAU;AAAA,UACtD;AAAA,QACF;AAEA,aAAK,OAAO,WAAW,YAAY;AAAA,MACnC,SAAO,OAAO;AACd,aAAK,OAAO,SAAS,WAAW,MAAM,OAAO,EAAE;AAAA,MACjD,UAAU;AACR,aAAK,UAAU;AAAA,MACjB;AAAA,IACD;AAAA;AAAA,IAGD,MAAM,0BAA0B;AAC9B,WAAK,UAAU;AACf,WAAK,OAAO,QAAQ,2BAA2B;AAE/C,UAAI;AACF,cAAM,oBAAoB;AAAA,UACxB;AAAA,UAAiB;AAAA,UAAoB;AAAA,UACrC;AAAA,UAAiB;AAAA,UAAmB;AAAA,UACpC;AAAA,UAA0B;AAAA,UAAsB;AAAA,QAClD;AAEA,YAAI,eAAe;AACnB,YAAI,aAAa,kBAAkB;AAEnC,mBAAW,UAAU,mBAAmB;AACtC,cAAI,OAAO,KAAK,aAAa,MAAM,MAAM,YAAY;AACnD,iBAAK,OAAO,WAAW,kBAAkB,MAAM,MAAM;AACrD;AAAA,iBACK;AACL,iBAAK,OAAO,SAAS,kBAAkB,MAAM,OAAO;AAAA,UACtD;AAAA,QACF;AAEA,aAAK,OAAO,QAAQ,yBAAyB,YAAY,IAAI,UAAU,QAAQ;AAE/E,YAAI,iBAAiB,YAAY;AAC/B,eAAK,OAAO,WAAW,0BAA0B;AAAA,eAC5C;AACL,eAAK,OAAO,WAAW,SAAS,aAAa,YAAY,UAAU;AAAA,QACrE;AAAA,MAEA,SAAO,OAAO;AACd,aAAK,OAAO,SAAS,wBAAwB,MAAM,OAAO,EAAE;AAAA,MAC9D,UAAU;AACR,aAAK,UAAU;AAAA,MACjB;AAAA,IACD;AAAA;AAAA,IAGD,MAAM,uBAAuB;AAC3B,WAAK,UAAU;AACf,WAAK,OAAO,QAAQ,wBAAwB;AAE5C,UAAI;AACF,cAAM,iBAAiB;AAAA,UACrB;AAAA,UAAS;AAAA,UAAU;AAAA,UAAY;AAAA,UAAe;AAAA,UAAkB;AAAA,QAClE;AAEA,YAAI,eAAe;AACnB,YAAI,aAAa,eAAe;AAEhC,mBAAW,UAAU,gBAAgB;AACnC,cAAI,OAAO,KAAK,UAAU,MAAM,MAAM,YAAY;AAChD,iBAAK,OAAO,WAAW,eAAe,MAAM,MAAM;AAClD;AAAA,iBACK;AACL,iBAAK,OAAO,SAAS,eAAe,MAAM,OAAO;AAAA,UACnD;AAAA,QACF;AAEA,aAAK,OAAO,QAAQ,sBAAsB,YAAY,IAAI,UAAU,QAAQ;AAE5E,YAAI,iBAAiB,YAAY;AAC/B,eAAK,OAAO,WAAW,uBAAuB;AAAA,eACzC;AACL,eAAK,OAAO,WAAW,SAAS,aAAa,YAAY,UAAU;AAAA,QACrE;AAAA,MAEA,SAAO,OAAO;AACd,aAAK,OAAO,SAAS,qBAAqB,MAAM,OAAO,EAAE;AAAA,MAC3D,UAAU;AACR,aAAK,UAAU;AAAA,MACjB;AAAA,IACF;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;ACpMA,GAAG,WAAW,eAAe;"}