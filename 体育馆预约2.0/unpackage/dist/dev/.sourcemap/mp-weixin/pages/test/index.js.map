{"version": 3, "file": "index.js", "sources": ["pages/test/index.vue", "pages/test/index.vue?type=page"], "sourcesContent": ["<template>\n  <view class=\"container\">\n    <view class=\"header\">\n      <text class=\"title\">🧪 Pinia迁移测试中心</text>\n      <text class=\"subtitle\">Vuex到Pinia迁移验证工具集</text>\n    </view>\n\n    <!-- 快速状态概览 -->\n    <view class=\"status-section\">\n      <text class=\"section-title\">📊 迁移状态概览</text>\n      <view class=\"status-grid\">\n        <view class=\"status-item success\">\n          <text class=\"status-icon\">✅</text>\n          <text class=\"status-label\">已修复</text>\n          <text class=\"status-count\">5项</text>\n        </view>\n        <view class=\"status-item warning\">\n          <text class=\"status-icon\">⚠️</text>\n          <text class=\"status-label\">需检查</text>\n          <text class=\"status-count\">15项</text>\n        </view>\n        <view class=\"status-item info\">\n          <text class=\"status-icon\">📋</text>\n          <text class=\"status-label\">待测试</text>\n          <text class=\"status-count\">10项</text>\n        </view>\n      </view>\n    </view>\n\n    <!-- 测试工具入口 -->\n    <view class=\"tools-section\">\n      <text class=\"section-title\">🛠️ 测试工具</text>\n      \n      <!-- 核心测试工具 -->\n      <view class=\"tool-category\">\n        <text class=\"category-title\">🎯 核心测试</text>\n        \n        <view class=\"tool-card primary\" @click=\"navigateToTool('comprehensive-migration-check')\">\n          <view class=\"tool-header\">\n            <text class=\"tool-icon\">🔍</text>\n            <view class=\"tool-info\">\n              <text class=\"tool-name\">全面迁移错误排查</text>\n              <text class=\"tool-desc\">30项完整错误清单检查</text>\n            </view>\n          </view>\n          <text class=\"tool-status\">推荐首选</text>\n        </view>\n        \n        <view class=\"tool-card success\" @click=\"navigateToTool('quick-fix-validation')\">\n          <view class=\"tool-header\">\n            <text class=\"tool-icon\">🔧</text>\n            <view class=\"tool-info\">\n              <text class=\"tool-name\">快速修复验证</text>\n              <text class=\"tool-desc\">验证已修复的API方法</text>\n            </view>\n          </view>\n          <text class=\"tool-status\">已修复验证</text>\n        </view>\n\n        <view class=\"tool-card warning\" @click=\"navigateToTool('naming-conflict-fix')\">\n          <view class=\"tool-header\">\n            <text class=\"tool-icon\">⚡</text>\n            <view class=\"tool-info\">\n              <text class=\"tool-name\">命名冲突修复验证</text>\n              <text class=\"tool-desc\">专门解决Getter/Action冲突</text>\n            </view>\n          </view>\n          <text class=\"tool-status\">冲突修复</text>\n        </view>\n      </view>\n\n      <!-- 专项测试工具 -->\n      <view class=\"tool-category\">\n        <text class=\"category-title\">🔬 专项测试</text>\n        \n        <view class=\"tool-card info\" @click=\"navigateToTool('api-diagnosis')\">\n          <view class=\"tool-header\">\n            <text class=\"tool-icon\">🌐</text>\n            <view class=\"tool-info\">\n              <text class=\"tool-name\">API诊断工具</text>\n              <text class=\"tool-desc\">专门的API连通性测试</text>\n            </view>\n          </view>\n          <text class=\"tool-status\">API专项</text>\n        </view>\n        \n        <view class=\"tool-card warning\" @click=\"navigateToTool('migration-validation')\">\n          <view class=\"tool-header\">\n            <text class=\"tool-icon\">🧪</text>\n            <view class=\"tool-info\">\n              <text class=\"tool-name\">Pinia迁移验证</text>\n              <text class=\"tool-desc\">基础迁移功能验证</text>\n            </view>\n          </view>\n          <text class=\"tool-status\">基础验证</text>\n        </view>\n      </view>\n    </view>\n\n    <!-- 快速操作 -->\n    <view class=\"quick-actions\">\n      <text class=\"section-title\">⚡ 快速操作</text>\n      \n      <button class=\"action-btn primary\" @click=\"runQuickCheck\">\n        🚀 运行快速检查\n      </button>\n      \n      <button class=\"action-btn success\" @click=\"viewResults\">\n        📊 查看测试结果\n      </button>\n      \n      <button class=\"action-btn info\" @click=\"viewDocumentation\">\n        📚 查看迁移文档\n      </button>\n    </view>\n\n    <!-- 帮助信息 -->\n    <view class=\"help-section\">\n      <text class=\"section-title\">💡 使用建议</text>\n      <view class=\"help-content\">\n        <text class=\"help-item\">1. 首先运行\"全面迁移错误排查\"获得完整评估</text>\n        <text class=\"help-item\">2. 使用\"快速修复验证\"确认已修复的问题</text>\n        <text class=\"help-item\">3. 针对具体问题使用专项测试工具</text>\n        <text class=\"help-item\">4. 定期运行测试确保迁移质量</text>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script>\nexport default {\n  name: 'TestIndex',\n  data() {\n    return {\n      testResults: null\n    }\n  },\n\n  methods: {\n    // 导航到测试工具\n    navigateToTool(toolName) {\n      console.log(`导航到测试工具: ${toolName}`)\n      uni.navigateTo({\n        url: `/pages/test/${toolName}`,\n        fail: (error) => {\n          console.error('导航失败:', error)\n          uni.showToast({\n            title: '页面不存在',\n            icon: 'none'\n          })\n        }\n      })\n    },\n\n    // 运行快速检查\n    async runQuickCheck() {\n      uni.showLoading({\n        title: '正在检查...'\n      })\n\n      try {\n        // 模拟快速检查\n        await new Promise(resolve => setTimeout(resolve, 2000))\n        \n        uni.hideLoading()\n        uni.showModal({\n          title: '快速检查完成',\n          content: '发现3个需要关注的问题，建议使用详细测试工具进一步检查',\n          confirmText: '查看详情',\n          success: (res) => {\n            if (res.confirm) {\n              this.navigateToTool('comprehensive-migration-check')\n            }\n          }\n        })\n      } catch (error) {\n        uni.hideLoading()\n        uni.showToast({\n          title: '检查失败',\n          icon: 'error'\n        })\n      }\n    },\n\n    // 查看测试结果\n    viewResults() {\n      uni.showModal({\n        title: '测试结果',\n        content: '当前已修复5项问题，还有15项需要检查。建议运行完整测试获得详细报告。',\n        confirmText: '运行测试',\n        success: (res) => {\n          if (res.confirm) {\n            this.navigateToTool('comprehensive-migration-check')\n          }\n        }\n      })\n    },\n\n    // 查看文档\n    viewDocumentation() {\n      uni.showModal({\n        title: '迁移文档',\n        content: '迁移文档包含30项错误清单和详细修复指南，建议先阅读文档了解迁移要点。',\n        confirmText: '我知道了'\n      })\n    }\n  },\n\n  onLoad() {\n    console.log('测试中心页面加载')\n  }\n}\n</script>\n\n<style scoped>\n.container {\n  padding: 40rpx;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  min-height: 100vh;\n}\n\n.header {\n  text-align: center;\n  margin-bottom: 60rpx;\n  color: white;\n}\n\n.title {\n  font-size: 52rpx;\n  font-weight: bold;\n  display: block;\n  margin-bottom: 20rpx;\n}\n\n.subtitle {\n  font-size: 28rpx;\n  opacity: 0.9;\n  display: block;\n}\n\n.status-section {\n  background: rgba(255, 255, 255, 0.95);\n  border-radius: 20rpx;\n  padding: 40rpx;\n  margin-bottom: 40rpx;\n  backdrop-filter: blur(10rpx);\n}\n\n.section-title {\n  font-size: 32rpx;\n  font-weight: bold;\n  color: #333;\n  display: block;\n  margin-bottom: 30rpx;\n}\n\n.status-grid {\n  display: grid;\n  grid-template-columns: repeat(3, 1fr);\n  gap: 20rpx;\n}\n\n.status-item {\n  text-align: center;\n  padding: 30rpx;\n  border-radius: 15rpx;\n  background: white;\n  box-shadow: 0 4rpx 15rpx rgba(0,0,0,0.1);\n}\n\n.status-item.success { border-left: 6rpx solid #4caf50; }\n.status-item.warning { border-left: 6rpx solid #ff9800; }\n.status-item.info { border-left: 6rpx solid #2196f3; }\n\n.status-icon {\n  font-size: 40rpx;\n  display: block;\n  margin-bottom: 15rpx;\n}\n\n.status-label {\n  font-size: 24rpx;\n  color: #666;\n  display: block;\n  margin-bottom: 10rpx;\n}\n\n.status-count {\n  font-size: 32rpx;\n  font-weight: bold;\n  color: #333;\n  display: block;\n}\n\n.tools-section {\n  background: rgba(255, 255, 255, 0.95);\n  border-radius: 20rpx;\n  padding: 40rpx;\n  margin-bottom: 40rpx;\n  backdrop-filter: blur(10rpx);\n}\n\n.tool-category {\n  margin-bottom: 40rpx;\n}\n\n.category-title {\n  font-size: 28rpx;\n  font-weight: bold;\n  color: #555;\n  display: block;\n  margin-bottom: 25rpx;\n}\n\n.tool-card {\n  background: white;\n  border-radius: 15rpx;\n  padding: 30rpx;\n  margin-bottom: 20rpx;\n  box-shadow: 0 4rpx 15rpx rgba(0,0,0,0.1);\n  border-left: 6rpx solid;\n  transition: transform 0.2s ease;\n}\n\n.tool-card:active {\n  transform: scale(0.98);\n}\n\n.tool-card.primary { border-left-color: #667eea; }\n.tool-card.success { border-left-color: #4caf50; }\n.tool-card.info { border-left-color: #2196f3; }\n.tool-card.warning { border-left-color: #ff9800; }\n\n.tool-header {\n  display: flex;\n  align-items: center;\n  margin-bottom: 15rpx;\n}\n\n.tool-icon {\n  font-size: 40rpx;\n  margin-right: 20rpx;\n}\n\n.tool-info {\n  flex: 1;\n}\n\n.tool-name {\n  font-size: 28rpx;\n  font-weight: bold;\n  color: #333;\n  display: block;\n  margin-bottom: 8rpx;\n}\n\n.tool-desc {\n  font-size: 24rpx;\n  color: #666;\n  display: block;\n}\n\n.tool-status {\n  font-size: 22rpx;\n  color: #999;\n  text-align: right;\n}\n\n.quick-actions {\n  background: rgba(255, 255, 255, 0.95);\n  border-radius: 20rpx;\n  padding: 40rpx;\n  margin-bottom: 40rpx;\n  backdrop-filter: blur(10rpx);\n}\n\n.action-btn {\n  width: 100%;\n  height: 80rpx;\n  border: none;\n  border-radius: 40rpx;\n  font-size: 28rpx;\n  color: white;\n  margin-bottom: 20rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-weight: bold;\n}\n\n.action-btn.primary {\n  background: linear-gradient(135deg, #667eea, #764ba2);\n}\n\n.action-btn.success {\n  background: linear-gradient(135deg, #4caf50, #45a049);\n}\n\n.action-btn.info {\n  background: linear-gradient(135deg, #2196f3, #1976d2);\n}\n\n.help-section {\n  background: rgba(255, 255, 255, 0.95);\n  border-radius: 20rpx;\n  padding: 40rpx;\n  backdrop-filter: blur(10rpx);\n}\n\n.help-content {\n  background: #f8f9fa;\n  border-radius: 10rpx;\n  padding: 30rpx;\n}\n\n.help-item {\n  font-size: 26rpx;\n  color: #555;\n  line-height: 1.6;\n  display: block;\n  margin-bottom: 15rpx;\n  padding-left: 20rpx;\n  position: relative;\n}\n\n.help-item::before {\n  content: '•';\n  color: #667eea;\n  font-weight: bold;\n  position: absolute;\n  left: 0;\n}\n</style>\n", "import MiniProgramPage from '/Users/<USER>/Desktop/体育馆预约 2/体育馆预约2.0/pages/test/index.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni"], "mappings": ";;AAkIA,MAAK,YAAU;AAAA,EACb,MAAM;AAAA,EACN,OAAO;AACL,WAAO;AAAA,MACL,aAAa;AAAA,IACf;AAAA,EACD;AAAA,EAED,SAAS;AAAA;AAAA,IAEP,eAAe,UAAU;AACvBA,oBAAA,MAAA,MAAA,OAAA,+BAAY,YAAY,QAAQ,EAAE;AAClCA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,eAAe,QAAQ;AAAA,QAC5B,MAAM,CAAC,UAAU;AACfA,wBAAAA,MAAA,MAAA,SAAA,+BAAc,SAAS,KAAK;AAC5BA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,WACP;AAAA,QACH;AAAA,OACD;AAAA,IACF;AAAA;AAAA,IAGD,MAAM,gBAAgB;AACpBA,oBAAAA,MAAI,YAAY;AAAA,QACd,OAAO;AAAA,OACR;AAED,UAAI;AAEF,cAAM,IAAI,QAAQ,aAAW,WAAW,SAAS,GAAI,CAAC;AAEtDA,sBAAAA,MAAI,YAAY;AAChBA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,SAAS;AAAA,UACT,aAAa;AAAA,UACb,SAAS,CAAC,QAAQ;AAChB,gBAAI,IAAI,SAAS;AACf,mBAAK,eAAe,+BAA+B;AAAA,YACrD;AAAA,UACF;AAAA,SACD;AAAA,MACD,SAAO,OAAO;AACdA,sBAAAA,MAAI,YAAY;AAChBA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,SACP;AAAA,MACH;AAAA,IACD;AAAA;AAAA,IAGD,cAAc;AACZA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS;AAAA,QACT,aAAa;AAAA,QACb,SAAS,CAAC,QAAQ;AAChB,cAAI,IAAI,SAAS;AACf,iBAAK,eAAe,+BAA+B;AAAA,UACrD;AAAA,QACF;AAAA,OACD;AAAA,IACF;AAAA;AAAA,IAGD,oBAAoB;AAClBA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS;AAAA,QACT,aAAa;AAAA,OACd;AAAA,IACH;AAAA,EACD;AAAA,EAED,SAAS;AACPA,kBAAAA,MAAA,MAAA,OAAA,+BAAY,UAAU;AAAA,EACxB;AACF;;;;;;;;;;;;;;AClNA,GAAG,WAAW,eAAe;"}