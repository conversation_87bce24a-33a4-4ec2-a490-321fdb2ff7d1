{"version": 3, "file": "timeslot-sync.js", "sources": ["pages/test/timeslot-sync.vue", "pages/test/timeslot-sync.vue?type=page"], "sourcesContent": ["<template>\n  <view class=\"container\">\n    <view class=\"header\">\n      <text class=\"title\">时间段同步修复测试</text>\n    </view>\n    \n    <!-- 测试参数 -->\n    <view class=\"test-section\">\n      <view class=\"section-title\">测试参数</view>\n      \n      <view class=\"test-item\">\n        <text class=\"test-label\">场馆ID:</text>\n        <input v-model=\"testVenueId\" placeholder=\"输入场馆ID\" class=\"test-input\" />\n      </view>\n      \n      <view class=\"test-item\">\n        <text class=\"test-label\">测试日期:</text>\n        <input v-model=\"testDate\" placeholder=\"YYYY-MM-DD\" class=\"test-input\" />\n      </view>\n    </view>\n    \n    <!-- 测试1: 检查同步状态 -->\n    <view class=\"test-section\">\n      <view class=\"section-title\">测试1: 检查同步状态</view>\n      \n      <button @click=\"checkSyncStatus\" class=\"test-button\">检查同步状态</button>\n      \n      <view v-if=\"syncStatusResult\" class=\"test-result\">\n        <text class=\"result-title\">同步状态结果:</text>\n        <view class=\"result-item\">\n          <text>前端时间段: {{ syncStatusResult.frontendSlots }}个</text>\n        </view>\n        <view class=\"result-item\">\n          <text>后端时间段: {{ syncStatusResult.backendSlots }}个</text>\n        </view>\n        <view class=\"result-item\">\n          <text>已同步: {{ syncStatusResult.synced ? '✅' : '❌' }}</text>\n        </view>\n        <view class=\"result-item\">\n          <text>需要同步: {{ syncStatusResult.needsSync ? '是' : '否' }}</text>\n        </view>\n        <view v-if=\"syncStatusResult.issues.length > 0\" class=\"result-item\">\n          <text>问题: {{ syncStatusResult.issues.join(', ') }}</text>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 测试2: 修复时间段生成 -->\n    <view class=\"test-section\">\n      <view class=\"section-title\">测试2: 修复时间段生成</view>\n      \n      <button @click=\"fixTimeSlotGeneration\" class=\"test-button\">修复时间段生成</button>\n      \n      <view v-if=\"fixGenerationResult\" class=\"test-result\">\n        <text class=\"result-title\">修复生成结果:</text>\n        <view class=\"result-item\">\n          <text>成功: {{ fixGenerationResult.success ? '✅' : '❌' }}</text>\n        </view>\n        <view class=\"result-item\">\n          <text>生成时间段: {{ fixGenerationResult.generatedSlots?.length || 0 }}个</text>\n        </view>\n        <view class=\"result-item\">\n          <text>同步到后端: {{ fixGenerationResult.syncedToBackend ? '✅' : '❌' }}</text>\n        </view>\n        <view class=\"result-item\">\n          <text>执行步骤: {{ fixGenerationResult.steps.join(' → ') }}</text>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 测试3: 强制重新生成 -->\n    <view class=\"test-section\">\n      <view class=\"section-title\">测试3: 强制重新生成</view>\n      \n      <button @click=\"forceRegenerate\" class=\"test-button\">强制重新生成</button>\n      \n      <view v-if=\"forceRegenerateResult\" class=\"test-result\">\n        <text class=\"result-title\">强制重新生成结果:</text>\n        <view class=\"result-item\">\n          <text>成功: {{ forceRegenerateResult.success ? '✅' : '❌' }}</text>\n        </view>\n        <view class=\"result-item\">\n          <text>生成方式: {{ forceRegenerateResult.method }}</text>\n        </view>\n        <view class=\"result-item\">\n          <text>时间段数量: {{ forceRegenerateResult.slotsCount }}个</text>\n        </view>\n        <view v-if=\"forceRegenerateResult.error\" class=\"result-item\">\n          <text>错误: {{ forceRegenerateResult.error }}</text>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 测试4: 自动修复 -->\n    <view class=\"test-section\">\n      <view class=\"section-title\">测试4: 自动修复</view>\n      \n      <button @click=\"autoFix\" class=\"test-button\">自动修复问题</button>\n      \n      <view v-if=\"autoFixResult\" class=\"test-result\">\n        <text class=\"result-title\">自动修复结果:</text>\n        <view class=\"result-item\">\n          <text>成功: {{ autoFixResult.success ? '✅' : '❌' }}</text>\n        </view>\n        <view class=\"result-item\">\n          <text>最终时间段: {{ autoFixResult.finalSlotsCount }}个</text>\n        </view>\n        <view class=\"result-item\">\n          <text>执行步骤: {{ autoFixResult.steps.join(' → ') }}</text>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 测试日志 -->\n    <view class=\"test-section\">\n      <view class=\"section-title\">测试日志</view>\n      \n      <button @click=\"clearLogs\" class=\"test-button secondary\">清除日志</button>\n      \n      <view class=\"test-logs\">\n        <view v-for=\"(log, index) in testLogs\" :key=\"index\" class=\"log-item\">\n          <text>{{ log }}</text>\n        </view>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script>\nimport { useVenueStore } from '@/stores/venue.js'\n\nexport default {\n  name: 'TimeSlotSyncTest',\n  \n  data() {\n    return {\n      testVenueId: '34',\n      testDate: '2025-07-20',\n      syncStatusResult: null,\n      fixGenerationResult: null,\n      forceRegenerateResult: null,\n      autoFixResult: null,\n      testLogs: []\n    }\n  },\n  \n  setup() {\n    const venueStore = useVenueStore()\n    return {\n      venueStore\n    }\n  },\n  \n  methods: {\n    addLog(message) {\n      const timestamp = new Date().toLocaleTimeString()\n      this.testLogs.push(`[${timestamp}] ${message}`)\n      console.log(`[时间段同步测试] ${message}`)\n    },\n    \n    clearLogs() {\n      this.testLogs = []\n    },\n    \n    async checkSyncStatus() {\n      try {\n        this.addLog('开始检查同步状态...')\n        \n        const { checkTimeSlotSyncStatus } = await import('@/utils/timeslot-sync-fix.js')\n        this.syncStatusResult = await checkTimeSlotSyncStatus(\n          this.testVenueId, \n          this.testDate, \n          this.venueStore\n        )\n        \n        this.addLog(`同步状态检查完成: 前端${this.syncStatusResult.frontendSlots}个, 后端${this.syncStatusResult.backendSlots}个`)\n      } catch (error) {\n        this.addLog(`检查同步状态失败: ${error.message}`)\n        console.error('检查同步状态失败:', error)\n      }\n    },\n    \n    async fixTimeSlotGeneration() {\n      try {\n        this.addLog('开始修复时间段生成...')\n        \n        const { fixTimeSlotGeneration } = await import('@/utils/timeslot-sync-fix.js')\n        this.fixGenerationResult = await fixTimeSlotGeneration(\n          this.testVenueId, \n          this.testDate, \n          this.venueStore\n        )\n        \n        this.addLog(`时间段生成修复完成: ${this.fixGenerationResult.success ? '成功' : '失败'}`)\n        if (this.fixGenerationResult.success) {\n          this.addLog(`生成了${this.fixGenerationResult.generatedSlots?.length || 0}个时间段`)\n        }\n      } catch (error) {\n        this.addLog(`修复时间段生成失败: ${error.message}`)\n        console.error('修复时间段生成失败:', error)\n      }\n    },\n    \n    async forceRegenerate() {\n      try {\n        this.addLog('开始强制重新生成...')\n        \n        const { forceRegenerateTimeSlots } = await import('@/utils/timeslot-sync-fix.js')\n        this.forceRegenerateResult = await forceRegenerateTimeSlots(\n          this.testVenueId, \n          this.testDate, \n          this.venueStore\n        )\n        \n        this.addLog(`强制重新生成完成: ${this.forceRegenerateResult.success ? '成功' : '失败'}`)\n        if (this.forceRegenerateResult.success) {\n          this.addLog(`使用${this.forceRegenerateResult.method}方式生成了${this.forceRegenerateResult.slotsCount}个时间段`)\n        }\n      } catch (error) {\n        this.addLog(`强制重新生成失败: ${error.message}`)\n        console.error('强制重新生成失败:', error)\n      }\n    },\n    \n    async autoFix() {\n      try {\n        this.addLog('开始自动修复...')\n        \n        const { autoFixTimeSlotIssues } = await import('@/utils/timeslot-sync-fix.js')\n        this.autoFixResult = await autoFixTimeSlotIssues(\n          this.testVenueId, \n          this.testDate, \n          this.venueStore\n        )\n        \n        this.addLog(`自动修复完成: ${this.autoFixResult.success ? '成功' : '失败'}`)\n        if (this.autoFixResult.success) {\n          this.addLog(`最终有${this.autoFixResult.finalSlotsCount}个时间段`)\n        }\n      } catch (error) {\n        this.addLog(`自动修复失败: ${error.message}`)\n        console.error('自动修复失败:', error)\n      }\n    }\n  }\n}\n</script>\n\n<style scoped>\n.container {\n  padding: 20px;\n  background-color: #f5f5f5;\n  min-height: 100vh;\n}\n\n.header {\n  text-align: center;\n  margin-bottom: 30px;\n}\n\n.title {\n  font-size: 24px;\n  font-weight: bold;\n  color: #333;\n}\n\n.test-section {\n  background-color: white;\n  border-radius: 8px;\n  padding: 20px;\n  margin-bottom: 20px;\n  box-shadow: 0 2px 4px rgba(0,0,0,0.1);\n}\n\n.section-title {\n  font-size: 18px;\n  font-weight: bold;\n  color: #333;\n  margin-bottom: 15px;\n  border-bottom: 2px solid #007AFF;\n  padding-bottom: 5px;\n}\n\n.test-item {\n  display: flex;\n  align-items: center;\n  margin-bottom: 10px;\n}\n\n.test-label {\n  width: 80px;\n  font-size: 14px;\n  color: #666;\n}\n\n.test-input {\n  flex: 1;\n  padding: 8px 12px;\n  border: 1px solid #ddd;\n  border-radius: 4px;\n  font-size: 14px;\n}\n\n.test-button {\n  background-color: #007AFF;\n  color: white;\n  border: none;\n  border-radius: 6px;\n  padding: 12px 24px;\n  font-size: 16px;\n  margin-bottom: 15px;\n}\n\n.test-button.secondary {\n  background-color: #666;\n}\n\n.test-result {\n  background-color: #f8f9fa;\n  border-radius: 6px;\n  padding: 15px;\n  border-left: 4px solid #007AFF;\n}\n\n.result-title {\n  font-weight: bold;\n  color: #333;\n  margin-bottom: 10px;\n  display: block;\n}\n\n.result-item {\n  margin-bottom: 8px;\n}\n\n.result-item text {\n  font-size: 14px;\n  color: #555;\n}\n\n.test-logs {\n  max-height: 300px;\n  overflow-y: auto;\n  background-color: #f8f9fa;\n  border-radius: 6px;\n  padding: 15px;\n}\n\n.log-item {\n  margin-bottom: 5px;\n  font-size: 12px;\n  color: #666;\n  font-family: monospace;\n}\n</style>\n", "import MiniProgramPage from '/Users/<USER>/Desktop/体育馆预约 2/体育馆预约2.0/pages/test/timeslot-sync.vue'\nwx.createPage(MiniProgramPage)"], "names": ["useVenueStore", "uni"], "mappings": ";;;AAmIA,MAAK,YAAU;AAAA,EACb,MAAM;AAAA,EAEN,OAAO;AACL,WAAO;AAAA,MACL,aAAa;AAAA,MACb,UAAU;AAAA,MACV,kBAAkB;AAAA,MAClB,qBAAqB;AAAA,MACrB,uBAAuB;AAAA,MACvB,eAAe;AAAA,MACf,UAAU,CAAC;AAAA,IACb;AAAA,EACD;AAAA,EAED,QAAQ;AACN,UAAM,aAAaA,aAAAA,cAAc;AACjC,WAAO;AAAA,MACL;AAAA,IACF;AAAA,EACD;AAAA,EAED,SAAS;AAAA,IACP,OAAO,SAAS;AACd,YAAM,aAAY,oBAAI,KAAM,GAAC,mBAAmB;AAChD,WAAK,SAAS,KAAK,IAAI,SAAS,KAAK,OAAO,EAAE;AAC9CC,oBAAA,MAAA,MAAA,OAAA,uCAAY,aAAa,OAAO,EAAE;AAAA,IACnC;AAAA,IAED,YAAY;AACV,WAAK,WAAW,CAAC;AAAA,IAClB;AAAA,IAED,MAAM,kBAAkB;AACtB,UAAI;AACF,aAAK,OAAO,aAAa;AAEzB,cAAM,EAAE,4BAA4B,MAAa;AACjD,aAAK,mBAAmB,MAAM;AAAA,UAC5B,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,QACP;AAEA,aAAK,OAAO,eAAe,KAAK,iBAAiB,aAAa,QAAQ,KAAK,iBAAiB,YAAY,GAAG;AAAA,MAC3G,SAAO,OAAO;AACd,aAAK,OAAO,aAAa,MAAM,OAAO,EAAE;AACxCA,sBAAAA,MAAc,MAAA,SAAA,uCAAA,aAAa,KAAK;AAAA,MAClC;AAAA,IACD;AAAA,IAED,MAAM,wBAAwB;;AAC5B,UAAI;AACF,aAAK,OAAO,cAAc;AAE1B,cAAM,EAAE,sBAAsB,IAAI,MAAa;AAC/C,aAAK,sBAAsB,MAAM;AAAA,UAC/B,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,QACP;AAEA,aAAK,OAAO,cAAc,KAAK,oBAAoB,UAAU,OAAO,IAAI,EAAE;AAC1E,YAAI,KAAK,oBAAoB,SAAS;AACpC,eAAK,OAAO,QAAM,UAAK,oBAAoB,mBAAzB,mBAAyC,WAAU,CAAC,MAAM;AAAA,QAC9E;AAAA,MACA,SAAO,OAAO;AACd,aAAK,OAAO,cAAc,MAAM,OAAO,EAAE;AACzCA,sBAAAA,MAAc,MAAA,SAAA,uCAAA,cAAc,KAAK;AAAA,MACnC;AAAA,IACD;AAAA,IAED,MAAM,kBAAkB;AACtB,UAAI;AACF,aAAK,OAAO,aAAa;AAEzB,cAAM,EAAE,6BAA6B,MAAa;AAClD,aAAK,wBAAwB,MAAM;AAAA,UACjC,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,QACP;AAEA,aAAK,OAAO,aAAa,KAAK,sBAAsB,UAAU,OAAO,IAAI,EAAE;AAC3E,YAAI,KAAK,sBAAsB,SAAS;AACtC,eAAK,OAAO,KAAK,KAAK,sBAAsB,MAAM,QAAQ,KAAK,sBAAsB,UAAU,MAAM;AAAA,QACvG;AAAA,MACA,SAAO,OAAO;AACd,aAAK,OAAO,aAAa,MAAM,OAAO,EAAE;AACxCA,sBAAAA,MAAc,MAAA,SAAA,uCAAA,aAAa,KAAK;AAAA,MAClC;AAAA,IACD;AAAA,IAED,MAAM,UAAU;AACd,UAAI;AACF,aAAK,OAAO,WAAW;AAEvB,cAAM,EAAE,sBAAsB,IAAI,MAAa;AAC/C,aAAK,gBAAgB,MAAM;AAAA,UACzB,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,QACP;AAEA,aAAK,OAAO,WAAW,KAAK,cAAc,UAAU,OAAO,IAAI,EAAE;AACjE,YAAI,KAAK,cAAc,SAAS;AAC9B,eAAK,OAAO,MAAM,KAAK,cAAc,eAAe,MAAM;AAAA,QAC5D;AAAA,MACA,SAAO,OAAO;AACd,aAAK,OAAO,WAAW,MAAM,OAAO,EAAE;AACtCA,sBAAAA,MAAA,MAAA,SAAA,uCAAc,WAAW,KAAK;AAAA,MAChC;AAAA,IACF;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACpPA,GAAG,WAAW,eAAe;"}