{"version": 3, "file": "sharing-test.js", "sources": ["pages/test/sharing-test.vue", "pages/test/sharing-test.vue?type=page"], "sourcesContent": ["<template>\n  <view class=\"container\">\n    <view class=\"header\">\n      <text class=\"title\">拼场功能测试</text>\n    </view>\n    \n    <!-- 测试参数 -->\n    <view class=\"test-section\">\n      <view class=\"section-title\">测试参数</view>\n      \n      <view class=\"test-item\">\n        <text class=\"test-label\">场馆ID:</text>\n        <input v-model=\"testVenueId\" placeholder=\"输入场馆ID\" class=\"test-input\" />\n      </view>\n      \n      <view class=\"test-item\">\n        <text class=\"test-label\">测试日期:</text>\n        <input v-model=\"testDate\" placeholder=\"YYYY-MM-DD\" class=\"test-input\" />\n      </view>\n      \n      <view class=\"test-item\">\n        <text class=\"test-label\">队伍名称:</text>\n        <input v-model=\"testTeamName\" placeholder=\"输入队伍名称\" class=\"test-input\" />\n      </view>\n      \n      <view class=\"test-item\">\n        <text class=\"test-label\">联系方式:</text>\n        <input v-model=\"testContactInfo\" placeholder=\"输入联系方式\" class=\"test-input\" />\n      </view>\n    </view>\n    \n    <!-- 测试1: 拼场价格计算诊断 -->\n    <view class=\"test-section\">\n      <view class=\"section-title\">测试1: 拼场价格计算诊断</view>\n      \n      <button @click=\"testPriceCalculation\" class=\"test-button\">测试价格计算</button>\n      \n      <view v-if=\"priceTestResult\" class=\"test-result\">\n        <text class=\"result-title\">价格计算结果:</text>\n        <view class=\"result-item\">\n          <text>总原价: ¥{{ priceTestResult.priceCalculation.totalOriginalPrice }}</text>\n        </view>\n        <view class=\"result-item\">\n          <text>每队价格: ¥{{ priceTestResult.priceCalculation.pricePerTeam }}</text>\n        </view>\n        <view class=\"result-item\">\n          <text>优惠金额: ¥{{ priceTestResult.priceCalculation.discountAmount }}</text>\n        </view>\n        <view v-if=\"priceTestResult.issues.length > 0\" class=\"result-item\">\n          <text>问题: {{ priceTestResult.issues.join(', ') }}</text>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 测试2: 拼场数据结构诊断 -->\n    <view class=\"test-section\">\n      <view class=\"section-title\">测试2: 拼场数据结构诊断</view>\n      \n      <button @click=\"testDataStructure\" class=\"test-button\">测试数据结构</button>\n      \n      <view v-if=\"dataTestResult\" class=\"test-result\">\n        <text class=\"result-title\">数据结构检查:</text>\n        <view v-for=\"(field, key) in dataTestResult.requiredFields\" :key=\"key\" class=\"result-item\">\n          <text>{{ key }}: {{ field ? '✅' : '❌' }}</text>\n        </view>\n        <view v-if=\"dataTestResult.issues.length > 0\" class=\"result-item\">\n          <text>问题: {{ dataTestResult.issues.join(', ') }}</text>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 测试3: 综合拼场诊断 -->\n    <view class=\"test-section\">\n      <view class=\"section-title\">测试3: 综合拼场诊断</view>\n      \n      <button @click=\"testComprehensiveDiagnosis\" class=\"test-button\">综合诊断</button>\n      \n      <view v-if=\"comprehensiveResult\" class=\"test-result\">\n        <text class=\"result-title\">综合诊断结果:</text>\n        <view class=\"result-item\">\n          <text>总体状态: {{ comprehensiveResult.overallStatus }}</text>\n        </view>\n        <view class=\"result-item\">\n          <text>总结: {{ comprehensiveResult.summary }}</text>\n        </view>\n        <view class=\"result-item\">\n          <text>发现问题: {{ comprehensiveResult.allIssues.length }}个</text>\n        </view>\n        <view class=\"result-item\">\n          <text>修复建议: {{ comprehensiveResult.allRecommendations.length }}个</text>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 测试4: 拼场快速修复 -->\n    <view class=\"test-section\">\n      <view class=\"section-title\">测试4: 拼场快速修复</view>\n      \n      <button @click=\"testQuickFix\" class=\"test-button\">快速修复</button>\n      \n      <view v-if=\"quickFixResult\" class=\"test-result\">\n        <text class=\"result-title\">快速修复结果:</text>\n        <view class=\"result-item\">\n          <text>修复成功: {{ quickFixResult.success ? '✅' : '❌' }}</text>\n        </view>\n        <view class=\"result-item\">\n          <text>应用修复: {{ quickFixResult.appliedFixes.length }}个</text>\n        </view>\n        <view v-if=\"quickFixResult.fixedData\" class=\"result-item\">\n          <text>修复后价格: ¥{{ quickFixResult.fixedData.price }}</text>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 测试5: 模拟拼场创建 -->\n    <view class=\"test-section\">\n      <view class=\"section-title\">测试5: 模拟拼场创建</view>\n      \n      <button @click=\"testSharingCreation\" class=\"test-button\">模拟创建拼场</button>\n      \n      <view v-if=\"creationResult\" class=\"test-result\">\n        <text class=\"result-title\">创建结果:</text>\n        <view class=\"result-item\">\n          <text>成功: {{ creationResult.success ? '✅' : '❌' }}</text>\n        </view>\n        <view v-if=\"creationResult.orderId\" class=\"result-item\">\n          <text>订单ID: {{ creationResult.orderId }}</text>\n        </view>\n        <view v-if=\"creationResult.error\" class=\"result-item\">\n          <text>错误: {{ creationResult.error }}</text>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 测试日志 -->\n    <view class=\"test-section\">\n      <view class=\"section-title\">测试日志</view>\n      \n      <button @click=\"clearLogs\" class=\"test-button secondary\">清除日志</button>\n      \n      <view class=\"test-logs\">\n        <view v-for=\"(log, index) in testLogs\" :key=\"index\" class=\"log-item\">\n          <text>{{ log }}</text>\n        </view>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script>\nimport { useVenueStore } from '@/stores/venue.js'\nimport { useBookingStore } from '@/stores/booking.js'\n\nexport default {\n  name: 'SharingTest',\n  \n  data() {\n    return {\n      testVenueId: '29',\n      testDate: '2025-07-20',\n      testTeamName: '测试队伍',\n      testContactInfo: '13800138000',\n      priceTestResult: null,\n      dataTestResult: null,\n      comprehensiveResult: null,\n      quickFixResult: null,\n      creationResult: null,\n      testLogs: []\n    }\n  },\n  \n  setup() {\n    const venueStore = useVenueStore()\n    const bookingStore = useBookingStore()\n    return {\n      venueStore,\n      bookingStore\n    }\n  },\n  \n  methods: {\n    addLog(message) {\n      const timestamp = new Date().toLocaleTimeString()\n      this.testLogs.push(`[${timestamp}] ${message}`)\n      console.log(`[拼场测试] ${message}`)\n    },\n    \n    clearLogs() {\n      this.testLogs = []\n    },\n    \n    // 生成测试时间段\n    generateTestSlots() {\n      return [\n        {\n          id: `default_${this.testVenueId}_${this.testDate}_12_0`,\n          startTime: '12:00',\n          endTime: '12:30',\n          price: 100\n        },\n        {\n          id: `default_${this.testVenueId}_${this.testDate}_12_30`,\n          startTime: '12:30',\n          endTime: '13:00',\n          price: 100\n        }\n      ]\n    },\n    \n    // 生成测试场馆\n    generateTestVenue() {\n      return {\n        id: parseInt(this.testVenueId),\n        name: '测试场馆',\n        price: 200\n      }\n    },\n    \n    // 生成测试表单\n    generateTestForm() {\n      return {\n        teamName: this.testTeamName,\n        contactInfo: this.testContactInfo,\n        description: '测试拼场',\n        bookingType: 'SHARED'\n      }\n    },\n    \n    async testPriceCalculation() {\n      try {\n        this.addLog('开始测试价格计算...')\n        \n        const sharingModule = await import('@/utils/sharing-fix-diagnosis.js')\n        this.priceTestResult = sharingModule.diagnoseSharingPriceCalculation(\n          this.generateTestSlots(),\n          this.generateTestVenue(),\n          this.generateTestForm()\n        )\n        \n        this.addLog(`价格计算测试完成: 每队¥${this.priceTestResult.priceCalculation.pricePerTeam}`)\n      } catch (error) {\n        this.addLog(`价格计算测试失败: ${error.message}`)\n        console.error('价格计算测试失败:', error)\n      }\n    },\n    \n    async testDataStructure() {\n      try {\n        this.addLog('开始测试数据结构...')\n        \n        const testData = {\n          venueId: parseInt(this.testVenueId),\n          date: this.testDate,\n          startTime: '12:00',\n          teamName: this.testTeamName,\n          contactInfo: this.testContactInfo,\n          maxParticipants: 2,\n          price: 100\n        }\n        \n        const sharingModule = await import('@/utils/sharing-fix-diagnosis.js')\n        this.dataTestResult = sharingModule.diagnoseSharingDataStructure(testData)\n        \n        this.addLog(`数据结构测试完成: ${this.dataTestResult.issues.length}个问题`)\n      } catch (error) {\n        this.addLog(`数据结构测试失败: ${error.message}`)\n        console.error('数据结构测试失败:', error)\n      }\n    },\n    \n    async testComprehensiveDiagnosis() {\n      try {\n        this.addLog('开始综合诊断...')\n        \n        const sharingModule = await import('@/utils/sharing-fix-diagnosis.js')\n        this.comprehensiveResult = await sharingModule.comprehensiveSharingDiagnosis(\n          this.generateTestSlots(),\n          this.generateTestVenue(),\n          this.generateTestForm(),\n          this.testVenueId,\n          this.testDate\n        )\n        \n        this.addLog(`综合诊断完成: ${this.comprehensiveResult.overallStatus}`)\n      } catch (error) {\n        this.addLog(`综合诊断失败: ${error.message}`)\n        console.error('综合诊断失败:', error)\n      }\n    },\n    \n    async testQuickFix() {\n      try {\n        this.addLog('开始快速修复测试...')\n        \n        const sharingModule = await import('@/utils/sharing-fix-diagnosis.js')\n        this.quickFixResult = sharingModule.quickSharingFix(\n          this.generateTestSlots(),\n          this.generateTestVenue(),\n          this.generateTestForm(),\n          this.testVenueId,\n          this.testDate\n        )\n        \n        this.addLog(`快速修复测试完成: ${this.quickFixResult.success ? '成功' : '失败'}`)\n      } catch (error) {\n        this.addLog(`快速修复测试失败: ${error.message}`)\n        console.error('快速修复测试失败:', error)\n      }\n    },\n    \n    async testSharingCreation() {\n      try {\n        this.addLog('开始模拟拼场创建...')\n        \n        const testData = {\n          venueId: parseInt(this.testVenueId),\n          date: this.testDate,\n          startTime: '12:00',\n          endTime: '13:00',\n          teamName: this.testTeamName,\n          contactInfo: this.testContactInfo,\n          maxParticipants: 2,\n          description: '测试拼场',\n          price: 100,\n          slotIds: [`default_${this.testVenueId}_${this.testDate}_12_0`]\n        }\n        \n        const response = await this.bookingStore.createSharedBooking(testData)\n        \n        this.creationResult = {\n          success: true,\n          orderId: response.id || response.orderId || response.data?.id,\n          response: response\n        }\n        \n        this.addLog(`拼场创建成功: 订单ID ${this.creationResult.orderId}`)\n      } catch (error) {\n        this.creationResult = {\n          success: false,\n          error: error.message\n        }\n        this.addLog(`拼场创建失败: ${error.message}`)\n        console.error('拼场创建失败:', error)\n      }\n    }\n  }\n}\n</script>\n\n<style scoped>\n.container {\n  padding: 20px;\n  background-color: #f5f5f5;\n  min-height: 100vh;\n}\n\n.header {\n  text-align: center;\n  margin-bottom: 30px;\n}\n\n.title {\n  font-size: 24px;\n  font-weight: bold;\n  color: #333;\n}\n\n.test-section {\n  background-color: white;\n  border-radius: 8px;\n  padding: 20px;\n  margin-bottom: 20px;\n  box-shadow: 0 2px 4px rgba(0,0,0,0.1);\n}\n\n.section-title {\n  font-size: 18px;\n  font-weight: bold;\n  color: #333;\n  margin-bottom: 15px;\n  border-bottom: 2px solid #007AFF;\n  padding-bottom: 5px;\n}\n\n.test-item {\n  display: flex;\n  align-items: center;\n  margin-bottom: 10px;\n}\n\n.test-label {\n  width: 80px;\n  font-size: 14px;\n  color: #666;\n}\n\n.test-input {\n  flex: 1;\n  padding: 8px 12px;\n  border: 1px solid #ddd;\n  border-radius: 4px;\n  font-size: 14px;\n}\n\n.test-button {\n  background-color: #007AFF;\n  color: white;\n  border: none;\n  border-radius: 6px;\n  padding: 12px 24px;\n  font-size: 16px;\n  margin-bottom: 15px;\n}\n\n.test-button.secondary {\n  background-color: #666;\n}\n\n.test-result {\n  background-color: #f8f9fa;\n  border-radius: 6px;\n  padding: 15px;\n  border-left: 4px solid #007AFF;\n}\n\n.result-title {\n  font-weight: bold;\n  color: #333;\n  margin-bottom: 10px;\n  display: block;\n}\n\n.result-item {\n  margin-bottom: 8px;\n}\n\n.result-item text {\n  font-size: 14px;\n  color: #555;\n}\n\n.test-logs {\n  max-height: 300px;\n  overflow-y: auto;\n  background-color: #f8f9fa;\n  border-radius: 6px;\n  padding: 15px;\n}\n\n.log-item {\n  margin-bottom: 5px;\n  font-size: 12px;\n  color: #666;\n  font-family: monospace;\n}\n</style>\n", "import MiniProgramPage from '/Users/<USER>/Desktop/体育馆预约 2/体育馆预约2.0/pages/test/sharing-test.vue'\nwx.createPage(MiniProgramPage)"], "names": ["useVenueStore", "useBookingStore", "uni"], "mappings": ";;;;AAyJA,MAAK,YAAU;AAAA,EACb,MAAM;AAAA,EAEN,OAAO;AACL,WAAO;AAAA,MACL,aAAa;AAAA,MACb,UAAU;AAAA,MACV,cAAc;AAAA,MACd,iBAAiB;AAAA,MACjB,iBAAiB;AAAA,MACjB,gBAAgB;AAAA,MAChB,qBAAqB;AAAA,MACrB,gBAAgB;AAAA,MAChB,gBAAgB;AAAA,MAChB,UAAU,CAAC;AAAA,IACb;AAAA,EACD;AAAA,EAED,QAAQ;AACN,UAAM,aAAaA,aAAAA,cAAc;AACjC,UAAM,eAAeC,eAAAA,gBAAgB;AACrC,WAAO;AAAA,MACL;AAAA,MACA;AAAA,IACF;AAAA,EACD;AAAA,EAED,SAAS;AAAA,IACP,OAAO,SAAS;AACd,YAAM,aAAY,oBAAI,KAAM,GAAC,mBAAmB;AAChD,WAAK,SAAS,KAAK,IAAI,SAAS,KAAK,OAAO,EAAE;AAC9CC,6EAAY,UAAU,OAAO,EAAE;AAAA,IAChC;AAAA,IAED,YAAY;AACV,WAAK,WAAW,CAAC;AAAA,IAClB;AAAA;AAAA,IAGD,oBAAoB;AAClB,aAAO;AAAA,QACL;AAAA,UACE,IAAI,WAAW,KAAK,WAAW,IAAI,KAAK,QAAQ;AAAA,UAChD,WAAW;AAAA,UACX,SAAS;AAAA,UACT,OAAO;AAAA,QACR;AAAA,QACD;AAAA,UACE,IAAI,WAAW,KAAK,WAAW,IAAI,KAAK,QAAQ;AAAA,UAChD,WAAW;AAAA,UACX,SAAS;AAAA,UACT,OAAO;AAAA,QACT;AAAA,MACF;AAAA,IACD;AAAA;AAAA,IAGD,oBAAoB;AAClB,aAAO;AAAA,QACL,IAAI,SAAS,KAAK,WAAW;AAAA,QAC7B,MAAM;AAAA,QACN,OAAO;AAAA,MACT;AAAA,IACD;AAAA;AAAA,IAGD,mBAAmB;AACjB,aAAO;AAAA,QACL,UAAU,KAAK;AAAA,QACf,aAAa,KAAK;AAAA,QAClB,aAAa;AAAA,QACb,aAAa;AAAA,MACf;AAAA,IACD;AAAA,IAED,MAAM,uBAAuB;AAC3B,UAAI;AACF,aAAK,OAAO,aAAa;AAEzB,cAAM,gBAAgB,MAAa;AACnC,aAAK,kBAAkB,cAAc;AAAA,UACnC,KAAK,kBAAmB;AAAA,UACxB,KAAK,kBAAmB;AAAA,UACxB,KAAK,iBAAiB;AAAA,QACxB;AAEA,aAAK,OAAO,gBAAgB,KAAK,gBAAgB,iBAAiB,YAAY,EAAE;AAAA,MAChF,SAAO,OAAO;AACd,aAAK,OAAO,aAAa,MAAM,OAAO,EAAE;AACxCA,sBAAAA,MAAc,MAAA,SAAA,sCAAA,aAAa,KAAK;AAAA,MAClC;AAAA,IACD;AAAA,IAED,MAAM,oBAAoB;AACxB,UAAI;AACF,aAAK,OAAO,aAAa;AAEzB,cAAM,WAAW;AAAA,UACf,SAAS,SAAS,KAAK,WAAW;AAAA,UAClC,MAAM,KAAK;AAAA,UACX,WAAW;AAAA,UACX,UAAU,KAAK;AAAA,UACf,aAAa,KAAK;AAAA,UAClB,iBAAiB;AAAA,UACjB,OAAO;AAAA,QACT;AAEA,cAAM,gBAAgB,MAAa;AACnC,aAAK,iBAAiB,cAAc,6BAA6B,QAAQ;AAEzE,aAAK,OAAO,aAAa,KAAK,eAAe,OAAO,MAAM,KAAK;AAAA,MAC/D,SAAO,OAAO;AACd,aAAK,OAAO,aAAa,MAAM,OAAO,EAAE;AACxCA,sBAAAA,MAAc,MAAA,SAAA,sCAAA,aAAa,KAAK;AAAA,MAClC;AAAA,IACD;AAAA,IAED,MAAM,6BAA6B;AACjC,UAAI;AACF,aAAK,OAAO,WAAW;AAEvB,cAAM,gBAAgB,MAAa;AACnC,aAAK,sBAAsB,MAAM,cAAc;AAAA,UAC7C,KAAK,kBAAmB;AAAA,UACxB,KAAK,kBAAmB;AAAA,UACxB,KAAK,iBAAkB;AAAA,UACvB,KAAK;AAAA,UACL,KAAK;AAAA,QACP;AAEA,aAAK,OAAO,WAAW,KAAK,oBAAoB,aAAa,EAAE;AAAA,MAC/D,SAAO,OAAO;AACd,aAAK,OAAO,WAAW,MAAM,OAAO,EAAE;AACtCA,sBAAAA,MAAA,MAAA,SAAA,sCAAc,WAAW,KAAK;AAAA,MAChC;AAAA,IACD;AAAA,IAED,MAAM,eAAe;AACnB,UAAI;AACF,aAAK,OAAO,aAAa;AAEzB,cAAM,gBAAgB,MAAa;AACnC,aAAK,iBAAiB,cAAc;AAAA,UAClC,KAAK,kBAAmB;AAAA,UACxB,KAAK,kBAAmB;AAAA,UACxB,KAAK,iBAAkB;AAAA,UACvB,KAAK;AAAA,UACL,KAAK;AAAA,QACP;AAEA,aAAK,OAAO,aAAa,KAAK,eAAe,UAAU,OAAO,IAAI,EAAE;AAAA,MACpE,SAAO,OAAO;AACd,aAAK,OAAO,aAAa,MAAM,OAAO,EAAE;AACxCA,sBAAAA,MAAc,MAAA,SAAA,sCAAA,aAAa,KAAK;AAAA,MAClC;AAAA,IACD;AAAA,IAED,MAAM,sBAAsB;;AAC1B,UAAI;AACF,aAAK,OAAO,aAAa;AAEzB,cAAM,WAAW;AAAA,UACf,SAAS,SAAS,KAAK,WAAW;AAAA,UAClC,MAAM,KAAK;AAAA,UACX,WAAW;AAAA,UACX,SAAS;AAAA,UACT,UAAU,KAAK;AAAA,UACf,aAAa,KAAK;AAAA,UAClB,iBAAiB;AAAA,UACjB,aAAa;AAAA,UACb,OAAO;AAAA,UACP,SAAS,CAAC,WAAW,KAAK,WAAW,IAAI,KAAK,QAAQ,OAAO;AAAA,QAC/D;AAEA,cAAM,WAAW,MAAM,KAAK,aAAa,oBAAoB,QAAQ;AAErE,aAAK,iBAAiB;AAAA,UACpB,SAAS;AAAA,UACT,SAAS,SAAS,MAAM,SAAS,aAAW,cAAS,SAAT,mBAAe;AAAA,UAC3D;AAAA,QACF;AAEA,aAAK,OAAO,gBAAgB,KAAK,eAAe,OAAO,EAAE;AAAA,MACzD,SAAO,OAAO;AACd,aAAK,iBAAiB;AAAA,UACpB,SAAS;AAAA,UACT,OAAO,MAAM;AAAA,QACf;AACA,aAAK,OAAO,WAAW,MAAM,OAAO,EAAE;AACtCA,sBAAAA,MAAA,MAAA,SAAA,sCAAc,WAAW,KAAK;AAAA,MAChC;AAAA,IACF;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACzVA,GAAG,WAAW,eAAe;"}