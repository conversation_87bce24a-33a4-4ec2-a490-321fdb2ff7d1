{"version": 3, "file": "create.js", "sources": ["pages/booking/create.vue", "pages/booking/create.vue?type=page"], "sourcesContent": ["<template>\n  <view class=\"container\">\n    <scroll-view scroll-y class=\"main-scroll-view\">      \n      <view class=\"content-wrapper\">\n    <!-- 场馆信息 -->\n    <view class=\"venue-summary\" v-if=\"venue\">\n        <image :src=\"venue.image || 'https://via.placeholder.com/400x200?text=场馆图片'\" class=\"venue-image\" mode=\"aspectFill\" />\n        <view class=\"venue-info\">\n          <text class=\"venue-name\">{{ venue.name }}</text>\n          <text class=\"venue-location\">{{ venue.location }}</text>\n          <text class=\"venue-price\">¥{{ venue.price }}/小时</text>\n        </view>\n      </view>\n      \n      <!-- 预约信息表单 -->\n      <view class=\"booking-form\">\n        <!-- 预约类型 -->\n        <!-- 预约类型显示 -->\n        <view class=\"form-section\">\n          <text class=\"section-title\">预约类型</text>\n          <view class=\"booking-type-display\">\n            <text class=\"booking-type-text\">{{ bookingForm.bookingType === 'EXCLUSIVE' ? '独享预约' : '拼场预约' }}</text>\n          </view>\n        </view>\n          \n          <!-- 拼场说明 -->\n          <view class=\"sharing-notice\" v-if=\"(venue && venue.supportSharing) && bookingForm.bookingType === 'SHARED'\">\n            <view class=\"notice-header\">\n              <view class=\"notice-icon\">🏆</view>\n              <text class=\"notice-title\">拼场预约说明</text>\n            </view>\n            <view class=\"notice-content\">\n              <view class=\"notice-item\">\n                <view class=\"item-icon\">✨</view>\n                <text class=\"item-text\">本平台专为球队提供拼场服务</text>\n              </view>\n              <view class=\"notice-item\">\n                <view class=\"item-icon\">⚡</view>\n                <text class=\"item-text\">只需一个球队申请即可成功拼场</text>\n              </view>\n              <view class=\"notice-item\">\n                <view class=\"item-icon\">📝</view>\n                <text class=\"item-text\">请在队伍名称中注明球队信息</text>\n              </view>\n              <view class=\"notice-item\">\n                <view class=\"item-icon\">📞</view>\n                <text class=\"item-text\">联系方式用于其他球队联系您</text>\n              </view>\n            </view>\n          </view>\n        </view>\n        \n        <!-- 时间选择 -->\n        <view class=\"form-section\">\n          <text class=\"section-title\">预约时间</text>\n          <view class=\"time-info\">\n            <text class=\"time-text\">{{ formatDateTime(selectedDate, selectedSlot) }}</text>\n          </view>\n          \n\n        </view>\n        \n        <!-- 拼场信息 (仅拼场时显示) -->\n        <view class=\"form-section\" v-if=\"bookingForm.bookingType === 'SHARED'\">\n          <text class=\"section-title\">拼场信息</text>\n          \n          <view class=\"form-item\">\n            <text class=\"item-label\">球队名称 <text class=\"required\">*</text></text>\n            <input \n              v-model=\"bookingForm.teamName\" \n              placeholder=\"请输入球队名称（如：XX篮球队）\"\n              class=\"form-input\"\n              maxlength=\"20\"\n            />\n          </view>\n          \n          <view class=\"form-item\">\n            <text class=\"item-label\">联系方式 <text class=\"required\">*</text></text>\n            <input \n              v-model=\"bookingForm.contactInfo\" \n              placeholder=\"请输入联系方式（供其他球队联系）\"\n              class=\"form-input\"\n              maxlength=\"20\"\n            />\n          </view>\n          \n\n          \n          <!-- 拼场说明 -->\n          <view class=\"sharing-notice\">\n            <view class=\"notice-header\">\n              <view class=\"notice-icon\">ℹ️</view>\n              <text class=\"notice-title\">拼场说明</text>\n            </view>\n            <view class=\"notice-content\">\n              <view class=\"notice-item\">\n                <view class=\"item-icon\">•</view>\n                <text class=\"item-text\">拼场预约需要等待其他用户加入</text>\n              </view>\n              <view class=\"notice-item\">\n                <view class=\"item-icon\">•</view>\n                <text class=\"item-text\">如果预约时间前2小时内无人加入，系统将自动退款</text>\n              </view>\n              <view class=\"notice-item\">\n                <view class=\"item-icon\">•</view>\n                <text class=\"item-text\">请确保联系方式准确，便于其他用户联系</text>\n              </view>\n            </view>\n          </view>\n        </view>\n        \n        <!-- 预约描述 -->\n        <view class=\"form-section\">\n          <text class=\"section-title\">{{ bookingForm.bookingType === 'SHARED' ? '拼场说明' : '备注信息' }}</text>\n          <textarea\n            v-model=\"bookingForm.description\"\n            :placeholder=\"bookingForm.bookingType === 'SHARED' ? '球队实力中等，出汗局' : '请输入备注信息（可选）'\"\n            class=\"form-textarea\"\n            maxlength=\"200\"\n          ></textarea>\n        </view>\n      </view>\n      \n      <!-- 费用明细 -->\n      <view class=\"cost-summary\">\n        <text class=\"summary-title\">费用明细</text>\n        \n        <!-- 多个时间段的费用明细 -->\n        <template v-if=\"selectedSlots && selectedSlots.length > 0\">\n          <view class=\"cost-item\" v-for=\"(slot, index) in selectedSlots\" :key=\"index\">\n            <text>{{ slot.startTime }}-{{ slot.endTime }}</text>\n            <text>¥{{ getSlotPrice(slot) }}</text>\n          </view>\n        </template>\n        \n        <!-- 单个时间段的费用明细（兼容） -->\n        <template v-else-if=\"selectedSlot\">\n          <view class=\"cost-item\">\n            <text>{{ selectedSlot.startTime }}-{{ selectedSlot.endTime }}</text>\n            <text>¥{{ getSlotPrice(selectedSlot) }}</text>\n          </view>\n        </template>\n        \n        <!-- 默认场地费用 -->\n        <template v-else>\n          <view class=\"cost-item\">\n            <text>场地费用</text>\n            <text>¥{{ venue?.price || 0 }}</text>\n          </view>\n        </template>\n        \n        <!-- 显示总价和拼场优惠信息 -->\n        <template v-if=\"bookingForm.bookingType === 'SHARED'\">\n          <view class=\"cost-item\" style=\"color: #ff6b00; background-color: #fff8f0; padding: 10rpx; border-radius: 8rpx; margin-top: 10rpx;\">\n            <text>拼场优惠</text>\n            <text>¥{{ (totalCost / 2).toFixed(2) }} (5折)</text>\n          </view>\n          <view class=\"cost-total\" style=\"border-top: 1px dashed #eee; padding-top: 20rpx; margin-top: 10rpx;\">\n            <text>总计（原价）</text>\n            <text>¥{{ totalCost }}</text>\n          </view>\n          <view class=\"cost-total\" style=\"margin-top: 5rpx;\">\n            <text>实付金额</text>\n            <text class=\"total-amount\" style=\"color: #ff6b00; font-size: 36rpx;\">¥{{ (totalCost / 2).toFixed(2) }}</text>\n          </view>\n          <view class=\"info-tip\" style=\"font-size: 24rpx; color: #999; margin-top: 10rpx; text-align: right;\">\n            <text>拼场订单，费用由两队均摊，每队支付总费用的50%</text>\n          </view>\n        </template>\n        <template v-else>\n          <view class=\"cost-total\">\n            <text>总计</text>\n            <text class=\"total-amount\">¥{{ totalCost }}</text>\n          </view>\n        </template>\n      </view>\n    </scroll-view>\n\n  <!-- 底部操作 -->\n  <view class=\"bottom-actions\">\n      <view class=\"bottom-cost\">\n        <text class=\"cost-label\">{{ bookingForm.bookingType === 'SHARED' ? '实付金额：' : '总费用：' }}</text>\n        <text class=\"cost-value\" :class=\"{'shared-price': bookingForm.bookingType === 'SHARED'}\">\n          ¥{{ bookingForm.bookingType === 'SHARED' ? (totalCost / 2).toFixed(2) : totalCost.toFixed(2) }}\n        </text>\n      </view>\n      <view class=\"action-buttons\">\n        <button class=\"cancel-btn\" @click=\"goBack\">取消</button>\n        <button class=\"confirm-btn\" :disabled=\"!canConfirm\" @click=\"confirmBooking\">确认预约</button>\n      </view>\n    </view>\n  </view>\n  </template>\n\n<script>\nimport { useVenueStore } from '@/stores/venue.js'\nimport { useBookingStore } from '@/stores/booking.js'\nimport { useUserStore } from '@/stores/user.js'\n\nexport default {\n  name: 'BookingCreate',\n  \n  data() {\n    return {\n      venueStore: null,\n      bookingStore: null,\n      userStore: null,\n      venueId: null,\n      selectedDate: '',\n      selectedSlot: null,\n      selectedSlots: [], // 存储多个选中的时间段\n\n\n      bookingForm: {\n        bookingType: 'EXCLUSIVE',\n        teamName: '',\n        contactInfo: '',\n        description: ''\n      },\n\n\n    }\n  },\n  \n  computed: {\n    timeSlots() {\n      return this.venueStore?.timeSlots || []\n    },\n\n    userInfo() {\n      return this.userStore?.getUserInfo || {}\n    },\n\n    // 获取场馆信息，确保有默认值\n    venue() {\n      const venueData = this.venueStore?.venueDetailGetter || this.venueStore?.venueDetail\n      console.log('venue计算属性 - 原始数据:', venueData)\n      console.log('venue计算属性 - venueStore:', this.venueStore)\n\n      if (!venueData) {\n        console.log('venue计算属性 - 无数据')\n        return null\n      }\n\n      const result = {\n        ...venueData,\n        supportSharing: venueData.supportSharing !== undefined ? venueData.supportSharing : true,\n        price: venueData.price || 0\n      }\n\n      console.log('venue计算属性 - 处理后数据:', result)\n      console.log('venue计算属性 - 价格:', result.price)\n      return result\n    },\n    \n    totalCost() {\n      // 如果有多个时间段，计算所有时间段的总价格\n      if (this.selectedSlots && this.selectedSlots.length > 0) {\n        const total = this.selectedSlots.reduce((sum, slot) => {\n          let slotPrice = 0\n          \n          // 优先使用时间段的价格\n          if (slot.price) {\n            slotPrice = parseFloat(slot.price)\n          } else if (slot.pricePerHour) {\n            slotPrice = parseFloat(slot.pricePerHour)\n          } else {\n            // 使用场馆价格\n            const venuePrice = this.venue?.price || 0\n            slotPrice = parseFloat(venuePrice) || 0\n          }\n          \n          console.log('时间段价格:', slot, slotPrice)\n          return sum + slotPrice\n        }, 0)\n        \n        console.log('totalCost - 多时间段总价格:', total)\n        return total\n      }\n      \n      // 兼容单个时间段的情况\n      if (this.selectedSlot && this.selectedSlot.price) {\n        console.log('totalCost - 使用时间段价格:', this.selectedSlot.price)\n        return parseFloat(this.selectedSlot.price)\n      }\n      \n      // 如果时间段有每小时价格信息\n      if (this.selectedSlot && this.selectedSlot.pricePerHour) {\n        console.log('totalCost - 使用时间段每小时价格:', this.selectedSlot.pricePerHour)\n        return parseFloat(this.selectedSlot.pricePerHour)\n      }\n      \n      const venuePrice = this.venue?.pricePerHour || this.venue?.price || 0\n      console.log('totalCost - 使用场馆价格:', venuePrice)\n      return parseFloat(venuePrice) || 0\n    },\n    \n\n    \n    canConfirm() {\n      const hasDate = !!this.selectedDate\n      const hasSlot = !!(this.selectedSlots?.length > 0 || this.selectedSlot)\n      const hasVenue = !!this.venue?.id\n      const hasPrice = !!(this.venue?.price)\n      \n      const baseValid = hasDate && hasSlot && hasVenue && hasPrice\n      \n      // 添加详细调试信息\n      console.log('canConfirm 详细检查:', {\n        selectedDate: this.selectedDate,\n        hasDate,\n        selectedSlot: this.selectedSlot,\n        hasSlot,\n        venue: this.venue,\n        hasVenue,\n        hasPrice,\n        venuePrice: this.venue?.price,\n        venuePricePerHour: this.venue?.price,\n        bookingType: this.bookingForm.bookingType,\n        teamName: this.bookingForm.teamName,\n        contactInfo: this.bookingForm.contactInfo,\n        baseValid\n      })\n      \n      if (this.bookingForm.bookingType === 'SHARED') {\n        const hasTeamName = !!(this.bookingForm.teamName && this.bookingForm.teamName.trim())\n        const hasContactInfo = !!(this.bookingForm.contactInfo && this.bookingForm.contactInfo.trim())\n        const result = baseValid && hasTeamName && hasContactInfo\n        \n        console.log('拼场模式额外检查:', {\n          hasTeamName,\n          hasContactInfo,\n          finalResult: result\n        })\n        return result\n      }\n      \n      console.log('独占模式 canConfirm 结果:', baseValid)\n      return baseValid\n    }\n  },\n  \n  onLoad(options) {\n    // 初始化Pinia stores\n    this.venueStore = useVenueStore()\n    this.bookingStore = useBookingStore()\n    this.userStore = useUserStore()\n\n    console.log('页面加载参数:', options)\n    this.venueId = options.venueId\n    this.selectedDate = options.date\n    \n    // 确保venueId有值\n    if (!this.venueId) {\n      console.error('警告: venueId为空!')\n      uni.showToast({\n        title: '场馆ID缺失，请返回重试',\n        icon: 'none'\n      })\n      setTimeout(() => {\n        uni.navigateBack()\n      }, 2000)\n      return\n    }\n    \n    // 从路由参数获取预约类型\n    if (options.bookingType) {\n      this.bookingForm.bookingType = options.bookingType\n    }\n    \n    if (options.selectedSlots) {\n      // 从场馆详情页传来的多个时间段数据\n      try {\n        this.selectedSlots = JSON.parse(decodeURIComponent(options.selectedSlots))\n        this.selectedSlot = this.selectedSlots[0] // 保持兼容性，设置第一个时间段\n        console.log('接收到的时间段数据:', this.selectedSlots)\n      } catch (error) {\n        console.error('解析时间段数据失败:', error)\n      }\n      this.loadVenueDetail()\n    } else if (options.slotId) {\n      // 兼容旧的单个时间段ID方式\n      this.loadVenueAndSlot(options.slotId)\n    } else {\n      this.loadVenueDetail()\n    }\n  },\n  \n  methods: {\n    \n    // 加载场馆详情\n    async loadVenueDetail() {\n      try {\n        console.log('开始加载场馆详情，venueId:', this.venueId)\n        await this.venueStore.getVenueDetail(this.venueId)\n        console.log('场馆详情加载完成，数据:', this.venueStore.venueDetailGetter)\n\n        // 如果有日期，加载时间段\n        if (this.selectedDate) {\n          await this.loadTimeSlots()\n        }\n      } catch (error) {\n        console.error('加载场馆详情失败:', error)\n        \n        // 如果后端不可用，设置模拟数据用于测试\n        console.log('设置模拟场馆数据用于测试')\n        this.venueStore.setVenueDetail({\n          id: this.venueId || 1,\n          name: '测试体育馆',\n          price: 120,\n          supportSharing: true,\n          location: '测试地址',\n          openingHours: '08:00 - 22:00'\n        })\n\n        // 设置模拟时间段数据\n        if (this.selectedDate) {\n          this.venueStore.setTimeSlots([\n            {\n              id: 1,\n              startTime: '09:00',\n              endTime: '10:00',\n              status: 'AVAILABLE',\n              price: 120\n            },\n            {\n              id: 2,\n              startTime: '10:00',\n              endTime: '11:00',\n              status: 'AVAILABLE',\n              price: 120\n            }\n          ])\n        }\n        \n        uni.showToast({\n          title: '加载失败',\n          icon: 'error'\n        })\n      }\n    },\n    \n    // 加载场馆和指定时间段\n    async loadVenueAndSlot(slotId) {\n      try {\n        console.log('loadVenueAndSlot 开始，slotId:', slotId)\n        await this.venueStore.getVenueDetail(this.venueId)\n        await this.loadTimeSlots()\n        \n        console.log('可用时间段:', this.timeSlots)\n        \n        // 查找并选择指定的时间段\n        let slot = this.timeSlots.find(s => s.id == slotId)\n        \n        // 如果通过ID没找到，尝试通过时间段字符串查找（格式：'09:00-10:00'）\n        if (!slot && slotId.includes('-')) {\n          const [startTime, endTime] = slotId.split('-')\n          slot = this.timeSlots.find(s => s.startTime === startTime && s.endTime === endTime)\n        }\n        \n        console.log('找到的时间段:', slot)\n        \n        if (slot) {\n          this.selectedSlot = slot\n          console.log('已设置 selectedSlot:', this.selectedSlot)\n        } else {\n          console.warn('未找到指定的时间段:', slotId)\n        }\n      } catch (error) {\n        console.error('加载失败:', error)\n        \n        // 如果后端不可用，设置模拟数据用于测试\n        console.log('设置模拟数据用于测试')\n        this.venueStore.setVenueDetail({\n          id: this.venueId || 1,\n          name: '测试体育馆',\n          price: 120,\n          supportSharing: true,\n          location: '测试地址',\n          openingHours: '08:00 - 22:00'\n        })\n        \n        // 设置模拟时间段数据\n        const mockSlots = [\n          {\n            id: 1,\n            startTime: '09:00',\n            endTime: '10:00',\n            status: 'AVAILABLE',\n            price: 120\n          },\n          {\n            id: 2,\n            startTime: '10:00',\n            endTime: '11:00',\n            status: 'AVAILABLE',\n            price: 120\n          },\n          {\n            id: 3,\n            startTime: '14:00',\n            endTime: '15:00',\n            status: 'AVAILABLE',\n            price: 120\n          }\n        ]\n        \n        this.venueStore.setTimeSlots(mockSlots)\n        \n        // 如果有指定的slotId，尝试选择对应的时间段\n        if (slotId) {\n          let slot = mockSlots.find(s => s.id == slotId)\n          \n          // 如果通过ID没找到，尝试通过时间段字符串查找\n          if (!slot && slotId.includes('-')) {\n            const [startTime, endTime] = slotId.split('-')\n            slot = mockSlots.find(s => s.startTime === startTime && s.endTime === endTime)\n          }\n          \n          if (slot) {\n            this.selectedSlot = slot\n            console.log('已设置模拟 selectedSlot:', this.selectedSlot)\n          }\n        }\n        \n        uni.showToast({\n          title: '使用模拟数据',\n          icon: 'none'\n        })\n      }\n    },\n    \n    // 加载时间段\n    async loadTimeSlots() {\n      if (!this.selectedDate) return\n\n      try {\n        console.log('loadTimeSlots 调用参数:', { venueId: this.venueId, date: this.selectedDate })\n        await this.venueStore.getTimeSlots(this.venueId, this.selectedDate)\n      } catch (error) {\n        console.error('加载时间段失败:', error)\n      }\n    },\n    \n\n    \n\n    \n\n    \n\n    \n    // 格式化日期时间\n    formatDateTime(date, slot) {\n      console.log('formatDateTime 调用:', { date, slot, selectedSlots: this.selectedSlots })\n      \n      if (!date) {\n        console.log('formatDateTime 返回默认值: 请选择时间')\n        return '请选择时间'\n      }\n      \n      try {\n        const dateObj = new Date(date)\n        const year = dateObj.getFullYear()\n        const month = dateObj.getMonth() + 1\n        const day = dateObj.getDate()\n        const weekDay = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'][dateObj.getDay()]\n        \n        const dateStr = `${year}年${month}月${day}日 ${weekDay}`\n        \n        // 如果有多个时间段，显示所有时间段\n        if (this.selectedSlots && this.selectedSlots.length > 0) {\n          const timeSlots = this.selectedSlots.map(slot => {\n            let startTime = slot.startTime\n            let endTime = slot.endTime\n            \n            // 如果时间包含秒，去掉秒部分\n            if (startTime && startTime.length > 5) {\n              startTime = startTime.substring(0, 5)\n            }\n            if (endTime && endTime.length > 5) {\n              endTime = endTime.substring(0, 5)\n            }\n            \n            return `${startTime}-${endTime}`\n          })\n          \n          // 计算总时长\n          const totalDuration = this.selectedSlots.reduce((total, slot) => {\n            return total + this.calculateDuration(slot.startTime, slot.endTime)\n          }, 0)\n          \n          // 格式化总时长显示\n          const durationText = totalDuration % 1 === 0 ? totalDuration : totalDuration.toFixed(1)\n          const result = `${dateStr} ${timeSlots.join('、')} (共${durationText}小时)`\n          console.log('formatDateTime 多时间段结果:', result)\n          return result\n        }\n        \n        // 兼容单个时间段的情况\n        if (!slot) {\n          console.log('formatDateTime 返回默认值: 请选择时间')\n          return '请选择时间'\n        }\n        \n        // 处理时间格式，确保显示正确\n        let startTime = slot.startTime\n        let endTime = slot.endTime\n        \n        // 如果时间包含秒，去掉秒部分\n        if (startTime && startTime.length > 5) {\n          startTime = startTime.substring(0, 5)\n        }\n        if (endTime && endTime.length > 5) {\n          endTime = endTime.substring(0, 5)\n        }\n        \n        // 计算时长\n        const duration = this.calculateDuration(startTime, endTime)\n        \n        // 格式化时长显示\n        const durationText = duration % 1 === 0 ? duration : duration.toFixed(1)\n        const timeStr = `${startTime}-${endTime}`\n        const result = `${dateStr} ${timeStr} (${durationText}小时)`\n        \n        console.log('formatDateTime 单时间段结果:', result)\n        return result\n      } catch (error) {\n        console.error('formatDateTime 错误:', error)\n        return '时间格式错误'\n      }\n    },\n    \n    // 计算时长\n    calculateDuration(startTime, endTime) {\n      try {\n        const [startHour, startMinute] = startTime.split(':').map(Number)\n        const [endHour, endMinute] = endTime.split(':').map(Number)\n        \n        const startMinutes = startHour * 60 + startMinute\n        const endMinutes = endHour * 60 + endMinute\n        \n        const durationMinutes = endMinutes - startMinutes\n        const hours = durationMinutes / 60\n        \n        // 返回精确的小时数（保留一位小数）\n        return Math.round(hours * 10) / 10\n      } catch (error) {\n        console.error('计算时长错误:', error)\n        return 1\n      }\n    },\n    \n    // 获取单个时间段的价格\n    getSlotPrice(slot) {\n      console.log('getSlotPrice 调用，slot:', slot)\n      console.log('slot.price:', slot.price)\n      console.log('slot.pricePerHour:', slot.pricePerHour)\n      console.log('venue.price:', this.venue?.price)\n\n      if (slot.price) {\n        const price = parseFloat(slot.price)\n        console.log('使用slot.price:', price)\n        return price\n      } else if (slot.pricePerHour) {\n        const price = parseFloat(slot.pricePerHour)\n        console.log('使用slot.pricePerHour:', price)\n        return price\n      } else {\n        // 使用场馆价格\n        const venuePrice = this.venue?.price || 0\n        const price = parseFloat(venuePrice) || 0\n        console.log('使用venue.price:', price)\n        return price\n      }\n    },\n    \n    // 获取时间段状态文本\n    getSlotStatusText(status) {\n      const statusMap = {\n        'AVAILABLE': '可预约',\n        'RESERVED': '已预约',\n        'OCCUPIED': '已占用',\n        'MAINTENANCE': '维护中'\n      }\n      return statusMap[status] || status\n    },\n    \n    // 确认预约\n    async confirmBooking() {\n      console.log('确认预约开始')\n      console.log('canConfirm:', this.canConfirm)\n      console.log('selectedSlots:', this.selectedSlots)\n      console.log('selectedSlot:', this.selectedSlot)\n      console.log('bookingType:', this.bookingType)\n\n      if (!this.canConfirm) {\n        console.log('无法确认预约，canConfirm为false')\n        return\n      }\n\n      // 验证表单\n      if (!this.validateForm()) {\n        console.log('表单验证失败')\n        return\n      }\n\n      try {\n        uni.showLoading({ title: '创建中...' })\n        \n        let result\n\n        console.log('预约创建 - 检查时间段选择:')\n        console.log('- selectedSlots:', this.selectedSlots)\n        console.log('- selectedSlots.length:', this.selectedSlots?.length)\n        console.log('- selectedSlot:', this.selectedSlot)\n        console.log('- 条件判断结果:', this.selectedSlots && this.selectedSlots.length > 0)\n\n        // 处理多个时间段的情况 - 只创建一个订单\n        if (this.selectedSlots && this.selectedSlots.length > 0) {\n          // 获取第一个时间段作为主要时间段\n          const firstSlot = this.selectedSlots[0];\n\n          console.log('多时间段预约 - bookingType:', this.bookingForm.bookingType)\n          console.log('多时间段预约 - 是否为拼场:', this.bookingForm.bookingType === 'SHARED')\n\n          if (this.bookingForm.bookingType === 'SHARED') {\n            // 拼场预约 - 只使用第一个时间段创建一个订单\n            const sharedBookingData = {\n              venueId: parseInt(this.venueId),\n              date: this.selectedDate,\n              startTime: firstSlot.startTime,\n              teamName: this.bookingForm.teamName || '',\n              contactInfo: this.bookingForm.contactInfo || '',\n              maxParticipants: 2, // 表示需要两个球队才能成功拼场\n              description: this.bookingForm.description || '',\n              slotIds: this.selectedSlots.map(slot => slot.id) // 传递所有选中的时间段ID\n            }\n            console.log('发送拼场预约数据:', sharedBookingData)\n            console.log('firstSlot.startTime 类型和值:', typeof firstSlot.startTime, firstSlot.startTime)\n            console.log('完整的 firstSlot 对象:', firstSlot)\n            result = await this.bookingStore.createSharedBooking(sharedBookingData)\n          } else {\n            // 独享预约 - 只创建一个订单，包含所有选中的时间段\n            console.log('进入独享预约分支')\n            console.log('selectedSlots数量:', this.selectedSlots.length)\n            console.log('firstSlot:', firstSlot)\n\n            const bookingData = {\n              venueId: this.venueId,\n              date: this.selectedDate,\n              startTime: firstSlot.startTime,\n              endTime: this.selectedSlots[this.selectedSlots.length - 1].endTime, // 使用最后一个时间段的结束时间\n              slotIds: this.selectedSlots.map(slot => slot.id), // 传递所有选中的时间段ID\n              bookingType: this.bookingForm.bookingType,\n              description: this.bookingForm.description,\n              price: this.selectedSlots.reduce((total, slot) => {\n                const slotPrice = this.getSlotPrice(slot)\n                console.log(`时间段 ${slot.startTime}-${slot.endTime} 价格:`, slotPrice)\n                return total + slotPrice\n              }, 0) // 计算总价格\n            }\n\n            console.log('发送给后端的预约数据:', bookingData)\n            console.log('计算的总价格:', bookingData.price)\n\n            result = await this.bookingStore.createBooking(bookingData)\n          }\n          \n          console.log('预约创建结果:', result)\n        } else {\n          // 单个时间段预约\n          const bookingData = {\n            venueId: this.venueId,\n            date: this.selectedDate,\n            startTime: this.selectedSlot.startTime,\n            endTime: this.selectedSlot.endTime,\n            slotId: this.selectedSlot.id,\n            bookingType: this.bookingForm.bookingType,\n            description: this.bookingForm.description,\n            price: this.getSlotPrice(this.selectedSlot)\n          }\n          \n          try {\n             // 根据预约类型选择合适的API\n             if (this.bookingForm.bookingType === 'SHARED') {\n               // 拼场接口使用不同的数据格式\n               const sharedBookingData = {\n                 venueId: parseInt(this.venueId),\n                 date: this.selectedDate,\n                 startTime: this.selectedSlot.startTime,\n                 teamName: this.bookingForm.teamName || '',\n                 contactInfo: this.bookingForm.contactInfo || '',\n                 maxParticipants: 2, // 修改为2，表示需要两个球队才能成功拼场\n                 description: this.bookingForm.description || ''\n               }\n               console.log('发送单时间段拼场预约数据:', sharedBookingData)\n               console.log('selectedSlot.startTime 类型和值:', typeof this.selectedSlot.startTime, this.selectedSlot.startTime)\n               console.log('完整的 selectedSlot 对象:', this.selectedSlot)\n               result = await this.bookingStore.createSharedBooking(sharedBookingData)\n               console.log('单时间段拼场预约创建结果:', result)\n             } else {\n               result = await this.bookingStore.createBooking(bookingData)\n               console.log('单时间段预约创建结果:', result)\n             }\n           } catch (error) {\n              console.error('预约创建失败:', error)\n              // 不再使用模拟响应，而是抛出错误以便上层捕获\n              throw error\n            }\n          }\n        \n        // 重新获取时间段数据以刷新状态\n        try {\n          console.log('刷新时间段状态，参数:', { venueId: this.venueId, date: this.selectedDate })\n\n          // 强制清除缓存，确保获取最新数据\n          console.log('清除时间段缓存')\n          this.venueStore.clearTimeSlots()\n\n          // 等待一下确保缓存清除完成\n          await new Promise(resolve => setTimeout(resolve, 100))\n\n          // 重新获取时间段数据，强制从服务器获取\n          console.log('重新获取时间段数据（强制刷新）')\n          await this.venueStore.getTimeSlots(this.venueId, this.selectedDate)\n          console.log('时间段状态已刷新')\n\n          // 等待一下确保数据更新完成\n          await new Promise(resolve => setTimeout(resolve, 200))\n\n          // 再次获取时间段数据确保状态正确\n          console.log('二次刷新时间段数据')\n          await this.venueStore.getTimeSlots(this.venueId, this.selectedDate)\n          console.log('二次刷新完成')\n\n          // 清除选中状态\n          this.selectedSlots = []\n          this.selectedSlot = null\n          console.log('已清除选中状态')\n\n          // 通知页面更新\n          this.$forceUpdate()\n          console.log('强制更新页面')\n\n          // 最后再等待一下确保UI更新\n          await new Promise(resolve => setTimeout(resolve, 100))\n        } catch (error) {\n          console.error('刷新时间段状态失败:', error)\n        }\n        \n        uni.hideLoading()\n        \n        uni.showToast({\n          title: '预约成功',\n          icon: 'success'\n        })\n\n        console.log('预约创建成功！准备跳转到支付页面')\n\n        // 获取订单ID，支持多种返回格式\n        console.log('预约创建结果详情:', result)\n        console.log('result.id:', result.id)\n        console.log('result.orderId:', result.orderId)\n        console.log('result.data:', result.data)\n        console.log('result.data?.id:', result.data?.id)\n\n        const orderId = result.id || result.orderId || result.data?.id\n        console.log('提取的订单ID:', orderId)\n        \n        // 所有订单创建后都优先跳转到支付页面\n        setTimeout(() => {\n          if (orderId) {\n            uni.redirectTo({\n              url: `/pages/payment/index?orderId=${orderId}&type=booking&from=create`\n            })\n          } else {\n            console.error('无法获取订单ID，跳转到预约列表')\n            uni.redirectTo({\n              url: '/pages/booking/list'\n            })\n          }\n        }, 1500)\n        \n      } catch (error) {\n        uni.hideLoading()\n        console.error('创建预约失败:', error)\n        uni.showToast({\n          title: error.message || '创建失败',\n          icon: 'error'\n        })\n      }\n    },\n    \n    // 验证表单\n    validateForm() {\n      \n      if (this.bookingForm.bookingType === 'SHARED') {\n        if (!this.bookingForm.teamName.trim()) {\n          uni.showToast({\n            title: '请输入队伍名称',\n            icon: 'error'\n          })\n          return false\n        }\n        \n        if (!this.bookingForm.contactInfo.trim()) {\n          uni.showToast({\n            title: '请输入联系方式',\n            icon: 'error'\n          })\n          return false\n        }\n      }\n      \n      return true\n    },\n    \n    // 返回\n    goBack() {\n      uni.navigateBack()\n    }\n  },\n  \n  watch: {\n    showTimeSelector(val) {\n      if (val) {\n        this.tempDate = this.selectedDate || new Date().toISOString().split('T')[0]\n        this.tempSlot = this.selectedSlot\n        this.$refs.timePopup.open()\n      }\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.container {\n  min-height: 100vh;\n  background-color: #f5f5f5;\n  padding-bottom: 600rpx; // 增加底部空间，确保内容不被遮挡\n}\n\n.main-scroll-view {\n  height: 100vh;\n  box-sizing: border-box;\n}\n\n// 场馆信息\n.venue-summary {\n  display: flex;\n  background-color: #ffffff;\n  padding: 30rpx;\n  margin-bottom: 20rpx;\n  \n  .venue-image {\n    width: 120rpx;\n    height: 120rpx;\n    border-radius: 12rpx;\n    margin-right: 24rpx;\n  }\n  \n  .venue-info {\n    flex: 1;\n    \n    .venue-name {\n      display: block;\n      font-size: 32rpx;\n      font-weight: 600;\n      color: #333333;\n      margin-bottom: 8rpx;\n    }\n    \n    .venue-location {\n      display: block;\n      font-size: 26rpx;\n      color: #666666;\n      margin-bottom: 8rpx;\n    }\n    \n    .venue-price {\n      display: block;\n      font-size: 28rpx;\n      color: #ff6b35;\n      font-weight: 600;\n    }\n  }\n}\n\n// 预约表单\n.booking-form {\n  .form-section {\n    background-color: #ffffff;\n    margin-bottom: 20rpx;\n    padding: 30rpx;\n    \n    .section-title {\n      display: block;\n      font-size: 28rpx;\n      font-weight: 600;\n      color: #333333;\n      margin-bottom: 24rpx;\n    }\n    \n    // 预约类型显示\n    .booking-type-display {\n      padding: 24rpx;\n      background: #f8f9fa;\n      border-radius: 12rpx;\n      border: 2rpx solid #e9ecef;\n      \n      .booking-type-text {\n        font-size: 30rpx;\n        font-weight: 600;\n        color: #ff6b35;\n      }\n    }\n    \n    // 时间信息\n    .time-info {\n      display: flex;\n      align-items: center;\n      justify-content: space-between;\n      padding: 20rpx 0;\n      \n      .time-text {\n        font-size: 28rpx;\n        color: #333333;\n      }\n      \n      .change-time-btn {\n        padding: 8rpx 16rpx;\n        background-color: #ff6b35;\n        color: #ffffff;\n        border: none;\n        border-radius: 6rpx;\n        font-size: 24rpx;\n      }\n    }\n    \n    // 表单项\n    .form-item {\n      margin-bottom: 30rpx;\n      \n      &:last-child {\n        margin-bottom: 0;\n      }\n      \n      .item-label {\n        display: block;\n        font-size: 26rpx;\n        color: #333333;\n        margin-bottom: 12rpx;\n        \n        .required {\n          color: #ff4d4f;\n        }\n      }\n      \n      .form-input {\n        width: 100%;\n        padding: 24rpx;\n        border: 2rpx solid #e8e8e8;\n        border-radius: 12rpx;\n        font-size: 28rpx;\n        color: #333333;\n        background-color: #ffffff;\n        \n        &:focus {\n          border-color: #1890ff;\n          background-color: #f6ffed;\n        }\n        \n        &::placeholder {\n          color: #cccccc;\n        }\n      }\n      \n      .form-picker {\n        width: 100%;\n        \n        .picker-text {\n          padding: 24rpx;\n          border: 2rpx solid #e8e8e8;\n          border-radius: 12rpx;\n          font-size: 28rpx;\n          color: #333333;\n          background-color: #ffffff;\n          text-align: left;\n          \n          &::after {\n            content: '';\n            position: absolute;\n            right: 24rpx;\n            top: 50%;\n            transform: translateY(-50%);\n            width: 0;\n            height: 0;\n            border-left: 8rpx solid #999999;\n            border-top: 6rpx solid transparent;\n            border-bottom: 6rpx solid transparent;\n          }\n        }\n      }\n      \n      .form-textarea {\n        width: 100%;\n        min-height: 120rpx;\n        padding: 20rpx;\n        background-color: #f8f8f8;\n        border: 1rpx solid #e8e8e8;\n        border-radius: 8rpx;\n        font-size: 28rpx;\n        color: #333333;\n        resize: none;\n      }\n      \n      .picker-text {\n        padding: 20rpx;\n        background-color: #f8f8f8;\n        border: 1rpx solid #e8e8e8;\n        border-radius: 8rpx;\n        font-size: 28rpx;\n        color: #333333;\n      }\n    }\n  }\n}\n\n// 费用明细\n.cost-summary {\n  background-color: #ffffff;\n  padding: 30rpx;\n  margin-bottom: 20rpx; // 恢复到合理值\n  \n  .summary-title {\n    display: block;\n    font-size: 28rpx;\n    font-weight: 600;\n    color: #333333;\n    margin-bottom: 24rpx;\n  }\n  \n  .cost-item {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    padding: 16rpx 0;\n    font-size: 26rpx;\n    color: #666666;\n  }\n  \n  .cost-total {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    margin-top: 20rpx;\n    padding: 20rpx 0;\n    border-top: 1px solid #f5f5f5;\n    font-size: 28rpx;\n    \n    .total-amount {\n      font-size: 36rpx;\n      font-weight: bold;\n      color: #333;\n    }\n  }\n  \n  .shared-price {\n    color: #ff6b00 !important;\n    font-weight: bold;\n    font-size: 36rpx !important;\n  }\n  \n  .info-tip {\n    font-size: 24rpx;\n    color: #999;\n    margin-top: 10rpx;\n    line-height: 1.4;\n  }\n}\n\n// 底部操作\n.bottom-actions {\n  position: fixed;\n  bottom: 0;\n  left: 0;\n  right: 0;\n  background-color: #ffffff;\n  padding: 20rpx 30rpx;\n  border-top: 1rpx solid #f0f0f0;\n  z-index: 999; // 确保底部操作栏在最上层\n  \n  .bottom-cost {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    padding: 16rpx 0;\n    border-bottom: 1rpx solid #f0f0f0;\n    margin-bottom: 16rpx;\n    \n    .cost-label {\n      font-size: 28rpx;\n      color: #333333;\n      font-weight: 600;\n    }\n    \n    .cost-value {\n      font-size: 32rpx;\n      color: #ff6b35;\n      font-weight: 700;\n    }\n  }\n  \n  .action-buttons {\n    display: flex;\n    \n    .cancel-btn {\n      flex: 1;\n      height: 80rpx;\n      background-color: #f5f5f5;\n      color: #666666;\n      border: none;\n      border-radius: 8rpx;\n      font-size: 28rpx;\n      margin-right: 20rpx;\n    }\n    \n    .confirm-btn {\n      flex: 2;\n      height: 80rpx;\n      background-color: #ff6b35;\n      color: #ffffff;\n      border: none;\n      border-radius: 8rpx;\n      font-size: 28rpx;\n      font-weight: 600;\n      \n      &[disabled] {\n        background-color: #cccccc;\n        color: #ffffff;\n      }\n    }\n  }\n}\n\n// 时间选择弹窗\n.time-selector {\n  background-color: #ffffff;\n  border-radius: 24rpx 24rpx 0 0;\n  max-height: 80vh;\n  \n  .selector-header {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    padding: 30rpx;\n    border-bottom: 1rpx solid #f0f0f0;\n    \n    .selector-title {\n      font-size: 32rpx;\n      font-weight: 600;\n      color: #333333;\n    }\n    \n    .close-btn {\n      font-size: 32rpx;\n      color: #999999;\n      padding: 8rpx;\n    }\n  }\n  \n  .slots-container {\n    padding: 30rpx;\n    \n    .slots-title {\n      display: block;\n      font-size: 28rpx;\n      font-weight: 600;\n      color: #333333;\n      margin-bottom: 20rpx;\n    }\n    \n    .slots-grid {\n      display: grid;\n      grid-template-columns: repeat(3, 1fr);\n      gap: 20rpx;\n      \n      .slot-item {\n        padding: 20rpx;\n        border-radius: 8rpx;\n        text-align: center;\n        border: 2rpx solid #e8e8e8;\n        transition: all 0.3s;\n        position: relative;\n        \n        &.available {\n          background: #f6ffed;\n          border-color: #b7eb8f;\n          \n          &:active {\n            background: #d9f7be;\n          }\n        }\n        \n        &.reserved {\n          background: #fff2f0;\n          border-color: #ffccc7;\n          opacity: 0.6;\n          cursor: not-allowed;\n          \n          .slot-time {\n            color: #999999;\n          }\n          \n          .slot-status {\n            color: #ff4d4f;\n            font-weight: 500;\n          }\n        }\n        \n        &.maintenance {\n          background: #fff7e6;\n          border-color: #ffd591;\n          opacity: 0.6;\n          cursor: not-allowed;\n          \n          .slot-time {\n            color: #999999;\n          }\n          \n          .slot-status {\n            color: #ff9500;\n            font-weight: 500;\n          }\n        }\n        \n        &.disabled {\n          pointer-events: none;\n          position: relative;\n          \n          &::after {\n            content: '';\n            position: absolute;\n            top: 0;\n            left: 0;\n            right: 0;\n            bottom: 0;\n            background-color: rgba(255, 255, 255, 0.3);\n            border-radius: 12rpx;\n          }\n        }\n        \n        &.time-restricted {\n          background: #fff1f0;\n          border-color: #ffccc7;\n          opacity: 0.7;\n          cursor: not-allowed;\n          \n          .slot-time {\n            color: #999999;\n          }\n          \n          .slot-status {\n            color: #ff4d4f;\n            font-weight: 500;\n          }\n        }\n        \n        &.selected {\n          background: #e6f7ff;\n          border-color: #91d5ff;\n          box-shadow: 0 0 0 4rpx rgba(24, 144, 255, 0.2);\n        }\n        \n        .slot-time {\n          display: block;\n          font-size: 26rpx;\n          font-weight: 600;\n          color: #333333;\n          margin-bottom: 8rpx;\n        }\n        \n        .slot-status {\n          display: block;\n          font-size: 22rpx;\n          color: #999999;\n        }\n      }\n    }\n  }\n  \n  .selector-actions {\n    display: flex;\n    padding: 30rpx;\n    border-top: 1rpx solid #f0f0f0;\n    \n    .selector-cancel {\n      flex: 1;\n      height: 80rpx;\n      background-color: #f5f5f5;\n      color: #666666;\n      border: none;\n      border-radius: 8rpx;\n      font-size: 28rpx;\n      margin-right: 20rpx;\n    }\n    \n    .selector-confirm {\n      flex: 2;\n      height: 80rpx;\n      background-color: #ff6b35;\n      color: #ffffff;\n      border: none;\n      border-radius: 8rpx;\n      font-size: 28rpx;\n      font-weight: 600;\n      \n      &[disabled] {\n        background-color: #cccccc;\n        color: #ffffff;\n      }\n    }\n  }\n}\n\n// 拼场说明\n.shared-info {\n  background-color: #e6f7ff;\n  border: 1rpx solid #91d5ff;\n  border-radius: 8rpx;\n  padding: 24rpx;\n  margin-bottom: 20rpx;\n  \n  .info-title {\n    display: flex;\n    align-items: center;\n    font-size: 26rpx;\n    font-weight: 600;\n    color: #1890ff;\n    margin-bottom: 16rpx;\n    \n    &::before {\n      content: 'ℹ';\n      margin-right: 8rpx;\n      font-size: 28rpx;\n    }\n  }\n  \n  .info-list {\n    .info-item {\n      display: flex;\n      align-items: flex-start;\n      font-size: 24rpx;\n      color: #666666;\n      margin-bottom: 8rpx;\n      \n      &:last-child {\n        margin-bottom: 0;\n      }\n      \n      &::before {\n        content: '•';\n        margin-right: 8rpx;\n        color: #1890ff;\n        font-weight: bold;\n      }\n    }\n  }\n}\n\n// 时间限制提示\n.time-restriction-tip {\n  background-color: #fff7e6;\n  border: 1rpx solid #ffd591;\n  border-radius: 8rpx;\n  padding: 20rpx;\n  margin-bottom: 20rpx;\n  \n  .tip-content {\n    display: flex;\n    align-items: center;\n    font-size: 24rpx;\n    color: #d46b08;\n    \n    &::before {\n      content: '⚠';\n      margin-right: 8rpx;\n      font-size: 26rpx;\n      color: #fa8c16;\n    }\n  }\n}\n\n// 拼场说明\n.sharing-notice {\n  background: linear-gradient(135deg, #e6f7ff 0%, #f0f9ff 100%);\n  border: 2rpx solid #40a9ff;\n  border-radius: 16rpx;\n  padding: 32rpx;\n  margin-top: 20rpx;\n  box-shadow: 0 4rpx 12rpx rgba(24, 144, 255, 0.1);\n  \n  .notice-header {\n    display: flex;\n    align-items: center;\n    margin-bottom: 24rpx;\n    \n    .notice-icon {\n      font-size: 32rpx;\n      margin-right: 12rpx;\n    }\n    \n    .notice-title {\n      font-size: 30rpx;\n      font-weight: 700;\n      color: #1890ff;\n      letter-spacing: 1rpx;\n    }\n  }\n  \n  .notice-content {\n    .notice-item {\n      display: flex;\n      align-items: flex-start;\n      margin-bottom: 16rpx;\n      padding: 12rpx 0;\n      \n      &:last-child {\n        margin-bottom: 0;\n      }\n      \n      .item-icon {\n        font-size: 24rpx;\n        margin-right: 12rpx;\n        margin-top: 2rpx;\n        flex-shrink: 0;\n      }\n      \n      .item-text {\n        font-size: 26rpx;\n        color: #333333;\n        line-height: 1.6;\n        flex: 1;\n      }\n    }\n  }\n}\n\n// 时间限制提示\n.time-notice {\n  background-color: #fff7e6;\n  border: 1rpx solid #ffd591;\n  border-radius: 8rpx;\n  padding: 20rpx;\n  margin-top: 16rpx;\n  \n  .notice-text {\n    display: block;\n    font-size: 24rpx;\n    color: #d46b08;\n    line-height: 1.5;\n  }\n}\n</style>", "import MiniProgramPage from '/Users/<USER>/Desktop/体育馆预约 2/体育馆预约2.0/pages/booking/create.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni", "venuePrice", "_a", "useVenueStore", "useBookingStore", "useUserStore", "slot", "startTime", "endTime", "durationText", "result"], "mappings": ";;;;;AAuMA,MAAK,YAAU;AAAA,EACb,MAAM;AAAA,EAEN,OAAO;AACL,WAAO;AAAA,MACL,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,WAAW;AAAA,MACX,SAAS;AAAA,MACT,cAAc;AAAA,MACd,cAAc;AAAA,MACd,eAAe,CAAE;AAAA;AAAA,MAGjB,aAAa;AAAA,QACX,aAAa;AAAA,QACb,UAAU;AAAA,QACV,aAAa;AAAA,QACb,aAAa;AAAA,MACd;AAAA,IAGH;AAAA,EACD;AAAA,EAED,UAAU;AAAA,IACR,YAAY;;AACV,eAAO,UAAK,eAAL,mBAAiB,cAAa,CAAC;AAAA,IACvC;AAAA,IAED,WAAW;;AACT,eAAO,UAAK,cAAL,mBAAgB,gBAAe,CAAC;AAAA,IACxC;AAAA;AAAA,IAGD,QAAQ;;AACN,YAAM,cAAY,UAAK,eAAL,mBAAiB,wBAAqB,UAAK,eAAL,mBAAiB;AACzEA,oBAAAA,sDAAY,qBAAqB,SAAS;AAC1CA,oBAAY,MAAA,MAAA,OAAA,mCAAA,2BAA2B,KAAK,UAAU;AAEtD,UAAI,CAAC,WAAW;AACdA,sBAAAA,MAAA,MAAA,OAAA,mCAAY,iBAAiB;AAC7B,eAAO;AAAA,MACT;AAEA,YAAM,SAAS;AAAA,QACb,GAAG;AAAA,QACH,gBAAgB,UAAU,mBAAmB,SAAY,UAAU,iBAAiB;AAAA,QACpF,OAAO,UAAU,SAAS;AAAA,MAC5B;AAEAA,oBAAAA,MAAY,MAAA,OAAA,mCAAA,sBAAsB,MAAM;AACxCA,0EAAY,mBAAmB,OAAO,KAAK;AAC3C,aAAO;AAAA,IACR;AAAA,IAED,YAAY;;AAEV,UAAI,KAAK,iBAAiB,KAAK,cAAc,SAAS,GAAG;AACvD,cAAM,QAAQ,KAAK,cAAc,OAAO,CAAC,KAAK,SAAS;;AACrD,cAAI,YAAY;AAGhB,cAAI,KAAK,OAAO;AACd,wBAAY,WAAW,KAAK,KAAK;AAAA,qBACxB,KAAK,cAAc;AAC5B,wBAAY,WAAW,KAAK,YAAY;AAAA,iBACnC;AAEL,kBAAMC,gBAAaC,MAAA,KAAK,UAAL,gBAAAA,IAAY,UAAS;AACxC,wBAAY,WAAWD,WAAU,KAAK;AAAA,UACxC;AAEAD,8EAAY,UAAU,MAAM,SAAS;AACrC,iBAAO,MAAM;AAAA,QACd,GAAE,CAAC;AAEJA,sBAAAA,MAAA,MAAA,OAAA,mCAAY,wBAAwB,KAAK;AACzC,eAAO;AAAA,MACT;AAGA,UAAI,KAAK,gBAAgB,KAAK,aAAa,OAAO;AAChDA,sBAAA,MAAA,MAAA,OAAA,mCAAY,wBAAwB,KAAK,aAAa,KAAK;AAC3D,eAAO,WAAW,KAAK,aAAa,KAAK;AAAA,MAC3C;AAGA,UAAI,KAAK,gBAAgB,KAAK,aAAa,cAAc;AACvDA,sBAAA,MAAA,MAAA,OAAA,mCAAY,2BAA2B,KAAK,aAAa,YAAY;AACrE,eAAO,WAAW,KAAK,aAAa,YAAY;AAAA,MAClD;AAEA,YAAM,eAAa,UAAK,UAAL,mBAAY,mBAAgB,UAAK,UAAL,mBAAY,UAAS;AACpEA,oBAAAA,sDAAY,uBAAuB,UAAU;AAC7C,aAAO,WAAW,UAAU,KAAK;AAAA,IAClC;AAAA,IAID,aAAa;;AACX,YAAM,UAAU,CAAC,CAAC,KAAK;AACvB,YAAM,UAAU,CAAC,IAAE,UAAK,kBAAL,mBAAoB,UAAS,KAAK,KAAK;AAC1D,YAAM,WAAW,CAAC,GAAC,UAAK,UAAL,mBAAY;AAC/B,YAAM,WAAW,CAAC,GAAE,UAAK,UAAL,mBAAY;AAEhC,YAAM,YAAY,WAAW,WAAW,YAAY;AAGpDA,oBAAAA,MAAY,MAAA,OAAA,mCAAA,oBAAoB;AAAA,QAC9B,cAAc,KAAK;AAAA,QACnB;AAAA,QACA,cAAc,KAAK;AAAA,QACnB;AAAA,QACA,OAAO,KAAK;AAAA,QACZ;AAAA,QACA;AAAA,QACA,aAAY,UAAK,UAAL,mBAAY;AAAA,QACxB,oBAAmB,UAAK,UAAL,mBAAY;AAAA,QAC/B,aAAa,KAAK,YAAY;AAAA,QAC9B,UAAU,KAAK,YAAY;AAAA,QAC3B,aAAa,KAAK,YAAY;AAAA,QAC9B;AAAA,OACD;AAED,UAAI,KAAK,YAAY,gBAAgB,UAAU;AAC7C,cAAM,cAAc,CAAC,EAAE,KAAK,YAAY,YAAY,KAAK,YAAY,SAAS;AAC9E,cAAM,iBAAiB,CAAC,EAAE,KAAK,YAAY,eAAe,KAAK,YAAY,YAAY;AACvF,cAAM,SAAS,aAAa,eAAe;AAE3CA,sBAAAA,sDAAY,aAAa;AAAA,UACvB;AAAA,UACA;AAAA,UACA,aAAa;AAAA,SACd;AACD,eAAO;AAAA,MACT;AAEAA,oBAAAA,sDAAY,uBAAuB,SAAS;AAC5C,aAAO;AAAA,IACT;AAAA,EACD;AAAA,EAED,OAAO,SAAS;AAEd,SAAK,aAAaG,2BAAc;AAChC,SAAK,eAAeC,+BAAgB;AACpC,SAAK,YAAYC,yBAAa;AAE9BL,kBAAAA,MAAA,MAAA,OAAA,mCAAY,WAAW,OAAO;AAC9B,SAAK,UAAU,QAAQ;AACvB,SAAK,eAAe,QAAQ;AAG5B,QAAI,CAAC,KAAK,SAAS;AACjBA,oBAAAA,MAAc,MAAA,SAAA,mCAAA,gBAAgB;AAC9BA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,OACP;AACD,iBAAW,MAAM;AACfA,sBAAAA,MAAI,aAAa;AAAA,MAClB,GAAE,GAAI;AACP;AAAA,IACF;AAGA,QAAI,QAAQ,aAAa;AACvB,WAAK,YAAY,cAAc,QAAQ;AAAA,IACzC;AAEA,QAAI,QAAQ,eAAe;AAEzB,UAAI;AACF,aAAK,gBAAgB,KAAK,MAAM,mBAAmB,QAAQ,aAAa,CAAC;AACzE,aAAK,eAAe,KAAK,cAAc,CAAC;AACxCA,sBAAY,MAAA,MAAA,OAAA,mCAAA,cAAc,KAAK,aAAa;AAAA,MAC5C,SAAO,OAAO;AACdA,sBAAAA,MAAc,MAAA,SAAA,mCAAA,cAAc,KAAK;AAAA,MACnC;AACA,WAAK,gBAAgB;AAAA,eACZ,QAAQ,QAAQ;AAEzB,WAAK,iBAAiB,QAAQ,MAAM;AAAA,WAC/B;AACL,WAAK,gBAAgB;AAAA,IACvB;AAAA,EACD;AAAA,EAED,SAAS;AAAA;AAAA,IAGP,MAAM,kBAAkB;AACtB,UAAI;AACFA,sBAAY,MAAA,MAAA,OAAA,mCAAA,qBAAqB,KAAK,OAAO;AAC7C,cAAM,KAAK,WAAW,eAAe,KAAK,OAAO;AACjDA,sBAAY,MAAA,MAAA,OAAA,mCAAA,gBAAgB,KAAK,WAAW,iBAAiB;AAG7D,YAAI,KAAK,cAAc;AACrB,gBAAM,KAAK,cAAc;AAAA,QAC3B;AAAA,MACA,SAAO,OAAO;AACdA,sBAAAA,MAAc,MAAA,SAAA,mCAAA,aAAa,KAAK;AAGhCA,sBAAAA,MAAY,MAAA,OAAA,mCAAA,cAAc;AAC1B,aAAK,WAAW,eAAe;AAAA,UAC7B,IAAI,KAAK,WAAW;AAAA,UACpB,MAAM;AAAA,UACN,OAAO;AAAA,UACP,gBAAgB;AAAA,UAChB,UAAU;AAAA,UACV,cAAc;AAAA,SACf;AAGD,YAAI,KAAK,cAAc;AACrB,eAAK,WAAW,aAAa;AAAA,YAC3B;AAAA,cACE,IAAI;AAAA,cACJ,WAAW;AAAA,cACX,SAAS;AAAA,cACT,QAAQ;AAAA,cACR,OAAO;AAAA,YACR;AAAA,YACD;AAAA,cACE,IAAI;AAAA,cACJ,WAAW;AAAA,cACX,SAAS;AAAA,cACT,QAAQ;AAAA,cACR,OAAO;AAAA,YACT;AAAA,WACD;AAAA,QACH;AAEAA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,SACP;AAAA,MACH;AAAA,IACD;AAAA;AAAA,IAGD,MAAM,iBAAiB,QAAQ;AAC7B,UAAI;AACFA,sBAAAA,sDAAY,+BAA+B,MAAM;AACjD,cAAM,KAAK,WAAW,eAAe,KAAK,OAAO;AACjD,cAAM,KAAK,cAAc;AAEzBA,sBAAY,MAAA,MAAA,OAAA,mCAAA,UAAU,KAAK,SAAS;AAGpC,YAAI,OAAO,KAAK,UAAU,KAAK,OAAK,EAAE,MAAM,MAAM;AAGlD,YAAI,CAAC,QAAQ,OAAO,SAAS,GAAG,GAAG;AACjC,gBAAM,CAAC,WAAW,OAAO,IAAI,OAAO,MAAM,GAAG;AAC7C,iBAAO,KAAK,UAAU,KAAK,OAAK,EAAE,cAAc,aAAa,EAAE,YAAY,OAAO;AAAA,QACpF;AAEAA,sBAAAA,MAAY,MAAA,OAAA,mCAAA,WAAW,IAAI;AAE3B,YAAI,MAAM;AACR,eAAK,eAAe;AACpBA,wBAAY,MAAA,MAAA,OAAA,mCAAA,qBAAqB,KAAK,YAAY;AAAA,eAC7C;AACLA,wBAAAA,MAAA,MAAA,QAAA,mCAAa,cAAc,MAAM;AAAA,QACnC;AAAA,MACA,SAAO,OAAO;AACdA,sBAAAA,MAAc,MAAA,SAAA,mCAAA,SAAS,KAAK;AAG5BA,sBAAAA,sDAAY,YAAY;AACxB,aAAK,WAAW,eAAe;AAAA,UAC7B,IAAI,KAAK,WAAW;AAAA,UACpB,MAAM;AAAA,UACN,OAAO;AAAA,UACP,gBAAgB;AAAA,UAChB,UAAU;AAAA,UACV,cAAc;AAAA,SACf;AAGD,cAAM,YAAY;AAAA,UAChB;AAAA,YACE,IAAI;AAAA,YACJ,WAAW;AAAA,YACX,SAAS;AAAA,YACT,QAAQ;AAAA,YACR,OAAO;AAAA,UACR;AAAA,UACD;AAAA,YACE,IAAI;AAAA,YACJ,WAAW;AAAA,YACX,SAAS;AAAA,YACT,QAAQ;AAAA,YACR,OAAO;AAAA,UACR;AAAA,UACD;AAAA,YACE,IAAI;AAAA,YACJ,WAAW;AAAA,YACX,SAAS;AAAA,YACT,QAAQ;AAAA,YACR,OAAO;AAAA,UACT;AAAA,QACF;AAEA,aAAK,WAAW,aAAa,SAAS;AAGtC,YAAI,QAAQ;AACV,cAAI,OAAO,UAAU,KAAK,OAAK,EAAE,MAAM,MAAM;AAG7C,cAAI,CAAC,QAAQ,OAAO,SAAS,GAAG,GAAG;AACjC,kBAAM,CAAC,WAAW,OAAO,IAAI,OAAO,MAAM,GAAG;AAC7C,mBAAO,UAAU,KAAK,OAAK,EAAE,cAAc,aAAa,EAAE,YAAY,OAAO;AAAA,UAC/E;AAEA,cAAI,MAAM;AACR,iBAAK,eAAe;AACpBA,0BAAA,MAAA,MAAA,OAAA,mCAAY,uBAAuB,KAAK,YAAY;AAAA,UACtD;AAAA,QACF;AAEAA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,SACP;AAAA,MACH;AAAA,IACD;AAAA;AAAA,IAGD,MAAM,gBAAgB;AACpB,UAAI,CAAC,KAAK;AAAc;AAExB,UAAI;AACFA,sBAAAA,MAAA,MAAA,OAAA,mCAAY,uBAAuB,EAAE,SAAS,KAAK,SAAS,MAAM,KAAK,aAAW,CAAG;AACrF,cAAM,KAAK,WAAW,aAAa,KAAK,SAAS,KAAK,YAAY;AAAA,MAClE,SAAO,OAAO;AACdA,sBAAAA,MAAA,MAAA,SAAA,mCAAc,YAAY,KAAK;AAAA,MACjC;AAAA,IACD;AAAA;AAAA,IAWD,eAAe,MAAM,MAAM;AACzBA,oBAAAA,sDAAY,sBAAsB,EAAE,MAAM,MAAM,eAAe,KAAK,cAAY,CAAG;AAEnF,UAAI,CAAC,MAAM;AACTA,sBAAAA,MAAA,MAAA,OAAA,mCAAY,6BAA6B;AACzC,eAAO;AAAA,MACT;AAEA,UAAI;AACF,cAAM,UAAU,IAAI,KAAK,IAAI;AAC7B,cAAM,OAAO,QAAQ,YAAY;AACjC,cAAM,QAAQ,QAAQ,SAAQ,IAAK;AACnC,cAAM,MAAM,QAAQ,QAAQ;AAC5B,cAAM,UAAU,CAAC,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,IAAI,EAAE,QAAQ,OAAM,CAAE;AAE3E,cAAM,UAAU,GAAG,IAAI,IAAI,KAAK,IAAI,GAAG,KAAK,OAAO;AAGnD,YAAI,KAAK,iBAAiB,KAAK,cAAc,SAAS,GAAG;AACvD,gBAAM,YAAY,KAAK,cAAc,IAAI,CAAAM,UAAQ;AAC/C,gBAAIC,aAAYD,MAAK;AACrB,gBAAIE,WAAUF,MAAK;AAGnB,gBAAIC,cAAaA,WAAU,SAAS,GAAG;AACrC,cAAAA,aAAYA,WAAU,UAAU,GAAG,CAAC;AAAA,YACtC;AACA,gBAAIC,YAAWA,SAAQ,SAAS,GAAG;AACjC,cAAAA,WAAUA,SAAQ,UAAU,GAAG,CAAC;AAAA,YAClC;AAEA,mBAAO,GAAGD,UAAS,IAAIC,QAAO;AAAA,WAC/B;AAGD,gBAAM,gBAAgB,KAAK,cAAc,OAAO,CAAC,OAAOF,UAAS;AAC/D,mBAAO,QAAQ,KAAK,kBAAkBA,MAAK,WAAWA,MAAK,OAAO;AAAA,UACnE,GAAE,CAAC;AAGJ,gBAAMG,gBAAe,gBAAgB,MAAM,IAAI,gBAAgB,cAAc,QAAQ,CAAC;AACtF,gBAAMC,UAAS,GAAG,OAAO,IAAI,UAAU,KAAK,GAAG,CAAC,MAAMD,aAAY;AAClET,wBAAAA,sDAAY,0BAA0BU,OAAM;AAC5C,iBAAOA;AAAA,QACT;AAGA,YAAI,CAAC,MAAM;AACTV,wBAAAA,MAAA,MAAA,OAAA,mCAAY,6BAA6B;AACzC,iBAAO;AAAA,QACT;AAGA,YAAI,YAAY,KAAK;AACrB,YAAI,UAAU,KAAK;AAGnB,YAAI,aAAa,UAAU,SAAS,GAAG;AACrC,sBAAY,UAAU,UAAU,GAAG,CAAC;AAAA,QACtC;AACA,YAAI,WAAW,QAAQ,SAAS,GAAG;AACjC,oBAAU,QAAQ,UAAU,GAAG,CAAC;AAAA,QAClC;AAGA,cAAM,WAAW,KAAK,kBAAkB,WAAW,OAAO;AAG1D,cAAM,eAAe,WAAW,MAAM,IAAI,WAAW,SAAS,QAAQ,CAAC;AACvE,cAAM,UAAU,GAAG,SAAS,IAAI,OAAO;AACvC,cAAM,SAAS,GAAG,OAAO,IAAI,OAAO,KAAK,YAAY;AAErDA,sBAAAA,MAAY,MAAA,OAAA,mCAAA,0BAA0B,MAAM;AAC5C,eAAO;AAAA,MACP,SAAO,OAAO;AACdA,sBAAAA,MAAc,MAAA,SAAA,mCAAA,sBAAsB,KAAK;AACzC,eAAO;AAAA,MACT;AAAA,IACD;AAAA;AAAA,IAGD,kBAAkB,WAAW,SAAS;AACpC,UAAI;AACF,cAAM,CAAC,WAAW,WAAW,IAAI,UAAU,MAAM,GAAG,EAAE,IAAI,MAAM;AAChE,cAAM,CAAC,SAAS,SAAS,IAAI,QAAQ,MAAM,GAAG,EAAE,IAAI,MAAM;AAE1D,cAAM,eAAe,YAAY,KAAK;AACtC,cAAM,aAAa,UAAU,KAAK;AAElC,cAAM,kBAAkB,aAAa;AACrC,cAAM,QAAQ,kBAAkB;AAGhC,eAAO,KAAK,MAAM,QAAQ,EAAE,IAAI;AAAA,MAChC,SAAO,OAAO;AACdA,sBAAAA,MAAA,MAAA,SAAA,mCAAc,WAAW,KAAK;AAC9B,eAAO;AAAA,MACT;AAAA,IACD;AAAA;AAAA,IAGD,aAAa,MAAM;;AACjBA,oBAAAA,MAAY,MAAA,OAAA,mCAAA,yBAAyB,IAAI;AACzCA,oBAAA,MAAA,MAAA,OAAA,mCAAY,eAAe,KAAK,KAAK;AACrCA,oBAAY,MAAA,MAAA,OAAA,mCAAA,sBAAsB,KAAK,YAAY;AACnDA,0EAAY,iBAAgB,UAAK,UAAL,mBAAY,KAAK;AAE7C,UAAI,KAAK,OAAO;AACd,cAAM,QAAQ,WAAW,KAAK,KAAK;AACnCA,sBAAAA,MAAA,MAAA,OAAA,mCAAY,iBAAiB,KAAK;AAClC,eAAO;AAAA,iBACE,KAAK,cAAc;AAC5B,cAAM,QAAQ,WAAW,KAAK,YAAY;AAC1CA,sBAAAA,MAAA,MAAA,OAAA,mCAAY,wBAAwB,KAAK;AACzC,eAAO;AAAA,aACF;AAEL,cAAM,eAAa,UAAK,UAAL,mBAAY,UAAS;AACxC,cAAM,QAAQ,WAAW,UAAU,KAAK;AACxCA,sBAAAA,MAAA,MAAA,OAAA,mCAAY,kBAAkB,KAAK;AACnC,eAAO;AAAA,MACT;AAAA,IACD;AAAA;AAAA,IAGD,kBAAkB,QAAQ;AACxB,YAAM,YAAY;AAAA,QAChB,aAAa;AAAA,QACb,YAAY;AAAA,QACZ,YAAY;AAAA,QACZ,eAAe;AAAA,MACjB;AACA,aAAO,UAAU,MAAM,KAAK;AAAA,IAC7B;AAAA;AAAA,IAGD,MAAM,iBAAiB;;AACrBA,oBAAAA,MAAY,MAAA,OAAA,mCAAA,QAAQ;AACpBA,0EAAY,eAAe,KAAK,UAAU;AAC1CA,oBAAA,MAAA,MAAA,OAAA,mCAAY,kBAAkB,KAAK,aAAa;AAChDA,oBAAA,MAAA,MAAA,OAAA,mCAAY,iBAAiB,KAAK,YAAY;AAC9CA,0EAAY,gBAAgB,KAAK,WAAW;AAE5C,UAAI,CAAC,KAAK,YAAY;AACpBA,sBAAAA,MAAY,MAAA,OAAA,mCAAA,yBAAyB;AACrC;AAAA,MACF;AAGA,UAAI,CAAC,KAAK,gBAAgB;AACxBA,sBAAAA,MAAA,MAAA,OAAA,mCAAY,QAAQ;AACpB;AAAA,MACF;AAEA,UAAI;AACFA,sBAAAA,MAAI,YAAY,EAAE,OAAO,UAAU;AAEnC,YAAI;AAEJA,sBAAAA,MAAA,MAAA,OAAA,mCAAY,iBAAiB;AAC7BA,4EAAY,oBAAoB,KAAK,aAAa;AAClDA,sBAAA,MAAA,MAAA,OAAA,mCAAY,4BAA2B,UAAK,kBAAL,mBAAoB,MAAM;AACjEA,4EAAY,mBAAmB,KAAK,YAAY;AAChDA,sBAAAA,MAAY,MAAA,OAAA,mCAAA,aAAa,KAAK,iBAAiB,KAAK,cAAc,SAAS,CAAC;AAG5E,YAAI,KAAK,iBAAiB,KAAK,cAAc,SAAS,GAAG;AAEvD,gBAAM,YAAY,KAAK,cAAc,CAAC;AAEtCA,wBAAA,MAAA,MAAA,OAAA,mCAAY,yBAAyB,KAAK,YAAY,WAAW;AACjEA,8BAAY,MAAA,OAAA,mCAAA,mBAAmB,KAAK,YAAY,gBAAgB,QAAQ;AAExE,cAAI,KAAK,YAAY,gBAAgB,UAAU;AAE7C,kBAAM,oBAAoB;AAAA,cACxB,SAAS,SAAS,KAAK,OAAO;AAAA,cAC9B,MAAM,KAAK;AAAA,cACX,WAAW,UAAU;AAAA,cACrB,UAAU,KAAK,YAAY,YAAY;AAAA,cACvC,aAAa,KAAK,YAAY,eAAe;AAAA,cAC7C,iBAAiB;AAAA;AAAA,cACjB,aAAa,KAAK,YAAY,eAAe;AAAA,cAC7C,SAAS,KAAK,cAAc,IAAI,UAAQ,KAAK,EAAE;AAAA;AAAA,YACjD;AACAA,0BAAAA,MAAA,MAAA,OAAA,mCAAY,aAAa,iBAAiB;AAC1CA,gCAAA,MAAA,OAAA,mCAAY,6BAA6B,OAAO,UAAU,WAAW,UAAU,SAAS;AACxFA,0BAAAA,MAAA,MAAA,OAAA,mCAAY,qBAAqB,SAAS;AAC1C,qBAAS,MAAM,KAAK,aAAa,oBAAoB,iBAAiB;AAAA,iBACjE;AAELA,0BAAAA,sDAAY,UAAU;AACtBA,0BAAY,MAAA,MAAA,OAAA,mCAAA,oBAAoB,KAAK,cAAc,MAAM;AACzDA,0BAAAA,MAAA,MAAA,OAAA,mCAAY,cAAc,SAAS;AAEnC,kBAAM,cAAc;AAAA,cAClB,SAAS,KAAK;AAAA,cACd,MAAM,KAAK;AAAA,cACX,WAAW,UAAU;AAAA,cACrB,SAAS,KAAK,cAAc,KAAK,cAAc,SAAS,CAAC,EAAE;AAAA;AAAA,cAC3D,SAAS,KAAK,cAAc,IAAI,UAAQ,KAAK,EAAE;AAAA;AAAA,cAC/C,aAAa,KAAK,YAAY;AAAA,cAC9B,aAAa,KAAK,YAAY;AAAA,cAC9B,OAAO,KAAK,cAAc,OAAO,CAAC,OAAO,SAAS;AAChD,sBAAM,YAAY,KAAK,aAAa,IAAI;AACxCA,8BAAAA,MAAA,MAAA,OAAA,mCAAY,OAAO,KAAK,SAAS,IAAI,KAAK,OAAO,QAAQ,SAAS;AAClE,uBAAO,QAAQ;AAAA,cAChB,GAAE,CAAC;AAAA;AAAA,YACN;AAEAA,0BAAAA,MAAY,MAAA,OAAA,mCAAA,eAAe,WAAW;AACtCA,gFAAY,WAAW,YAAY,KAAK;AAExC,qBAAS,MAAM,KAAK,aAAa,cAAc,WAAW;AAAA,UAC5D;AAEAA,wBAAAA,MAAY,MAAA,OAAA,mCAAA,WAAW,MAAM;AAAA,eACxB;AAEL,gBAAM,cAAc;AAAA,YAClB,SAAS,KAAK;AAAA,YACd,MAAM,KAAK;AAAA,YACX,WAAW,KAAK,aAAa;AAAA,YAC7B,SAAS,KAAK,aAAa;AAAA,YAC3B,QAAQ,KAAK,aAAa;AAAA,YAC1B,aAAa,KAAK,YAAY;AAAA,YAC9B,aAAa,KAAK,YAAY;AAAA,YAC9B,OAAO,KAAK,aAAa,KAAK,YAAY;AAAA,UAC5C;AAEA,cAAI;AAED,gBAAI,KAAK,YAAY,gBAAgB,UAAU;AAE7C,oBAAM,oBAAoB;AAAA,gBACxB,SAAS,SAAS,KAAK,OAAO;AAAA,gBAC9B,MAAM,KAAK;AAAA,gBACX,WAAW,KAAK,aAAa;AAAA,gBAC7B,UAAU,KAAK,YAAY,YAAY;AAAA,gBACvC,aAAa,KAAK,YAAY,eAAe;AAAA,gBAC7C,iBAAiB;AAAA;AAAA,gBACjB,aAAa,KAAK,YAAY,eAAe;AAAA,cAC/C;AACAA,4BAAAA,sDAAY,iBAAiB,iBAAiB;AAC9CA,4BAAAA,MAAA,MAAA,OAAA,mCAAY,gCAAgC,OAAO,KAAK,aAAa,WAAW,KAAK,aAAa,SAAS;AAC3GA,4BAAY,MAAA,MAAA,OAAA,mCAAA,wBAAwB,KAAK,YAAY;AACrD,uBAAS,MAAM,KAAK,aAAa,oBAAoB,iBAAiB;AACtEA,4BAAAA,MAAY,MAAA,OAAA,mCAAA,iBAAiB,MAAM;AAAA,mBAC9B;AACL,uBAAS,MAAM,KAAK,aAAa,cAAc,WAAW;AAC1DA,4BAAAA,MAAA,MAAA,OAAA,mCAAY,eAAe,MAAM;AAAA,YACnC;AAAA,UACA,SAAO,OAAO;AACbA,0BAAAA,MAAc,MAAA,SAAA,mCAAA,WAAW,KAAK;AAE9B,kBAAM;AAAA,UACR;AAAA,QACF;AAGF,YAAI;AACFA,wBAAAA,sDAAY,eAAe,EAAE,SAAS,KAAK,SAAS,MAAM,KAAK,aAAW,CAAG;AAG7EA,wBAAAA,sDAAY,SAAS;AACrB,eAAK,WAAW,eAAe;AAG/B,gBAAM,IAAI,QAAQ,aAAW,WAAW,SAAS,GAAG,CAAC;AAGrDA,wBAAAA,MAAY,MAAA,OAAA,mCAAA,iBAAiB;AAC7B,gBAAM,KAAK,WAAW,aAAa,KAAK,SAAS,KAAK,YAAY;AAClEA,wBAAAA,MAAA,MAAA,OAAA,mCAAY,UAAU;AAGtB,gBAAM,IAAI,QAAQ,aAAW,WAAW,SAAS,GAAG,CAAC;AAGrDA,wBAAAA,MAAA,MAAA,OAAA,mCAAY,WAAW;AACvB,gBAAM,KAAK,WAAW,aAAa,KAAK,SAAS,KAAK,YAAY;AAClEA,wBAAAA,MAAY,MAAA,OAAA,mCAAA,QAAQ;AAGpB,eAAK,gBAAgB,CAAC;AACtB,eAAK,eAAe;AACpBA,wBAAAA,sDAAY,SAAS;AAGrB,eAAK,aAAa;AAClBA,wBAAAA,MAAY,MAAA,OAAA,mCAAA,QAAQ;AAGpB,gBAAM,IAAI,QAAQ,aAAW,WAAW,SAAS,GAAG,CAAC;AAAA,QACrD,SAAO,OAAO;AACdA,wBAAAA,MAAA,MAAA,SAAA,mCAAc,cAAc,KAAK;AAAA,QACnC;AAEAA,sBAAAA,MAAI,YAAY;AAEhBA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,SACP;AAEDA,sBAAAA,MAAY,MAAA,OAAA,mCAAA,kBAAkB;AAG9BA,sBAAAA,MAAY,MAAA,OAAA,mCAAA,aAAa,MAAM;AAC/BA,sBAAA,MAAA,MAAA,OAAA,mCAAY,cAAc,OAAO,EAAE;AACnCA,sBAAY,MAAA,MAAA,OAAA,mCAAA,mBAAmB,OAAO,OAAO;AAC7CA,sBAAY,MAAA,MAAA,OAAA,mCAAA,gBAAgB,OAAO,IAAI;AACvCA,sBAAY,MAAA,MAAA,OAAA,mCAAA,qBAAoB,YAAO,SAAP,mBAAa,EAAE;AAE/C,cAAM,UAAU,OAAO,MAAM,OAAO,aAAW,YAAO,SAAP,mBAAa;AAC5DA,sBAAAA,MAAY,MAAA,OAAA,mCAAA,YAAY,OAAO;AAG/B,mBAAW,MAAM;AACf,cAAI,SAAS;AACXA,0BAAAA,MAAI,WAAW;AAAA,cACb,KAAK,gCAAgC,OAAO;AAAA,aAC7C;AAAA,iBACI;AACLA,0BAAAA,MAAA,MAAA,SAAA,mCAAc,kBAAkB;AAChCA,0BAAAA,MAAI,WAAW;AAAA,cACb,KAAK;AAAA,aACN;AAAA,UACH;AAAA,QACD,GAAE,IAAI;AAAA,MAEP,SAAO,OAAO;AACdA,sBAAAA,MAAI,YAAY;AAChBA,sBAAAA,MAAA,MAAA,SAAA,mCAAc,WAAW,KAAK;AAC9BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO,MAAM,WAAW;AAAA,UACxB,MAAM;AAAA,SACP;AAAA,MACH;AAAA,IACD;AAAA;AAAA,IAGD,eAAe;AAEb,UAAI,KAAK,YAAY,gBAAgB,UAAU;AAC7C,YAAI,CAAC,KAAK,YAAY,SAAS,KAAI,GAAI;AACrCA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,WACP;AACD,iBAAO;AAAA,QACT;AAEA,YAAI,CAAC,KAAK,YAAY,YAAY,KAAI,GAAI;AACxCA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,WACP;AACD,iBAAO;AAAA,QACT;AAAA,MACF;AAEA,aAAO;AAAA,IACR;AAAA;AAAA,IAGD,SAAS;AACPA,oBAAAA,MAAI,aAAa;AAAA,IACnB;AAAA,EACD;AAAA,EAED,OAAO;AAAA,IACL,iBAAiB,KAAK;AACpB,UAAI,KAAK;AACP,aAAK,WAAW,KAAK,iBAAgB,oBAAI,QAAO,cAAc,MAAM,GAAG,EAAE,CAAC;AAC1E,aAAK,WAAW,KAAK;AACrB,aAAK,MAAM,UAAU,KAAK;AAAA,MAC5B;AAAA,IACF;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACp6BA,GAAG,WAAW,eAAe;"}