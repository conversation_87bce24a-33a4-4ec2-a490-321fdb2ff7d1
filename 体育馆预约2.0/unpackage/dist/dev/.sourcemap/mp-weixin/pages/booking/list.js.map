{"version": 3, "file": "list.js", "sources": ["pages/booking/list.vue", "pages/booking/list.vue?type=page"], "sourcesContent": ["<template>\n  <view class=\"container\">\n    <!-- 状态筛选 -->\n    <view class=\"status-filter\">\n      <view \n        v-for=\"status in statusOptions\" \n        :key=\"status.value\" \n        class=\"filter-item\"\n        :class=\"{ active: selectedStatus === status.value }\"\n        @click=\"selectStatus(status.value)\"\n      >\n        {{ status.label }}\n      </view>\n    </view>\n    \n\n    \n    <!-- 预约列表 -->\n    <view class=\"booking-list\">\n      <view \n        v-for=\"booking in filteredBookings\" \n        :key=\"booking.id\" \n        class=\"booking-card\"\n        @click=\"navigateToDetail(booking.id)\"\n      >\n        <view class=\"card-header\">\n          <view class=\"venue-info\">\n            <!-- 场馆名和类型标识在同一行 -->\n            <view class=\"venue-name-row\">\n              <text class=\"venue-name\">{{ booking.venueName || '未知场馆' }}</text>\n              <!-- 订单类型标识显示在场馆名右边 -->\n              <view class=\"booking-type-tag\" v-if=\"booking.bookingType || booking.type || booking.orderType\">\n                <text class=\"tag-text\" :class=\"getBookingTypeClass(booking.bookingType || booking.type || booking.orderType)\">\n                  {{ getBookingTypeText(booking.bookingType || booking.type || booking.orderType) }}\n                </text>\n              </view>\n              <!-- 虚拟订单标识 -->\n              <view class=\"virtual-order-tag\" v-if=\"isVirtualOrder(booking)\">\n                <text class=\"virtual-tag-text\">拼场申请</text>\n              </view>\n            </view>\n            <text class=\"booking-date\">{{ formatBookingDate(booking) }}</text>\n          </view>\n          <view class=\"booking-status\" :class=\"getStatusClass(booking.status)\">\n            {{ getStatusText(booking.status) }}\n          </view>\n        </view>\n        \n        <view class=\"card-content\">\n          <view class=\"time-info\">\n            <text class=\"time-icon\">🕐</text>\n            <text class=\"time-text\">{{ formatTimeRange(booking) }}</text>\n          </view>\n          \n          <view class=\"location-info\">\n            <text class=\"location-icon\">📍</text>\n            <text class=\"location-text\">{{ booking.venueLocation || '未知地点' }}</text>\n          </view>\n          \n\n          \n          <view class=\"order-info\">\n            <text class=\"order-icon\">📋</text>\n            <text class=\"order-text\">订单号：{{ booking.orderNo || (booking.id ? booking.id : '') }}</text>\n          </view>\n          \n          <view class=\"create-time-info\">\n            <text class=\"time-icon\">📅</text>\n            <text class=\"create-time-text\">创建时间：{{ formatCreateTime(booking && (booking.createdAt || booking.createTime)) }}</text>\n          </view>\n          \n          <view class=\"price-info\">\n            <text class=\"price-label\">费用：</text>\n            <text class=\"price-value\">¥{{ getBookingPrice(booking) }}</text>\n          </view>\n\n          <!-- 倒计时显示（仅拼场订单） -->\n          <CountdownTimer\n            v-if=\"shouldShowCountdown(booking)\"\n            :order=\"booking\"\n            label=\"自动取消\"\n            :short=\"true\"\n            class=\"simple\"\n            @expired=\"onCountdownExpired\"\n          />\n        </view>\n        \n        <view class=\"card-actions\">\n          <!-- 待支付状态 -->\n          <template v-if=\"booking.status === 'PENDING'\">\n            <button class=\"action-btn pay-btn\" @click.stop=\"payOrder(booking)\">立即支付</button>\n            <button class=\"action-btn cancel-btn\" @click.stop=\"showCancelModal(booking.id)\">取消预约</button>\n          </template>\n\n          <!-- 已支付状态 -->\n          <template v-else-if=\"booking.status === 'PAID'\">\n            <button class=\"action-btn info-btn\" @click.stop=\"viewOrderDetail(booking)\">查看详情</button>\n            <button class=\"action-btn cancel-btn\" @click.stop=\"showCancelModal(booking.id)\">取消预约</button>\n          </template>\n\n          <!-- 拼场相关状态 -->\n          <template v-else-if=\"booking.status === 'OPEN' || booking.status === 'SHARING' || booking.status === 'PENDING_FULL'\">\n            <button class=\"action-btn info-btn\" @click.stop=\"viewOrderDetail(booking)\">查看详情</button>\n            <button class=\"action-btn participants-btn\" @click.stop=\"viewParticipants(booking)\">查看参与者</button>\n            <button class=\"action-btn cancel-btn\" @click.stop=\"showCancelModal(booking.id)\">取消预约</button>\n          </template>\n\n          <!-- 拼场成功/已满员状态 -->\n          <template v-else-if=\"booking.status === 'SHARING_SUCCESS' || booking.status === 'FULL'\">\n            <button class=\"action-btn info-btn\" @click.stop=\"viewOrderDetail(booking)\">查看详情</button>\n            <button class=\"action-btn participants-btn\" @click.stop=\"viewParticipants(booking)\">查看参与者</button>\n          </template>\n\n          <!-- 已确认状态 -->\n          <template v-else-if=\"booking.status === 'CONFIRMED'\">\n            <button class=\"action-btn checkin-btn\" @click.stop=\"checkinOrder(booking)\">签到</button>\n            <button class=\"action-btn cancel-btn\" @click.stop=\"showCancelModal(booking.id)\">取消预约</button>\n          </template>\n\n          <!-- 已核销状态 -->\n          <template v-else-if=\"booking.status === 'VERIFIED'\">\n            <button class=\"action-btn complete-btn\" @click.stop=\"completeOrder(booking)\">完成订单</button>\n          </template>\n\n          <!-- 已完成状态 -->\n          <template v-else-if=\"booking.status === 'COMPLETED'\">\n            <button class=\"action-btn review-btn\" @click.stop=\"reviewVenue(booking)\">评价场馆</button>\n            <button class=\"action-btn rebook-btn\" @click.stop=\"rebookVenue(booking)\">再次预约</button>\n          </template>\n\n          <!-- 已取消/已过期状态 -->\n          <template v-else-if=\"booking.status === 'CANCELLED' || booking.status === 'EXPIRED'\">\n            <button class=\"action-btn rebook-btn\" @click.stop=\"rebookVenue(booking)\">再次预约</button>\n          </template>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 空状态 -->\n    <view v-if=\"filteredBookings.length === 0\" class=\"empty-state\">\n      <text class=\"empty-icon\">📅</text>\n      <text class=\"empty-text\">暂无预约记录</text>\n      <button class=\"empty-btn\" @click=\"navigateToVenueList\">去预约场馆</button>\n    </view>\n    \n    <!-- 加载更多 -->\n    <view v-if=\"hasMore && filteredBookings.length > 0\" class=\"load-more\" @click=\"loadMore\">\n      <text>{{ loading ? '加载中...' : '加载更多' }}</text>\n    </view>\n    \n    <!-- 取消预约确认弹窗 -->\n    <uni-popup ref=\"cancelPopup\" type=\"center\">\n      <view class=\"cancel-modal\">\n        <view class=\"modal-header\">\n          <text class=\"modal-title\">取消预约</text>\n        </view>\n        \n        <view class=\"modal-content\">\n          <text class=\"modal-text\">确定要取消这个预约吗？</text>\n          <text class=\"modal-note\">取消后可能产生手续费，具体以场馆规定为准</text>\n        </view>\n        \n        <view class=\"modal-actions\">\n          <button class=\"modal-btn cancel-btn\" @click=\"closeCancelModal\">暂不取消</button>\n          <button class=\"modal-btn confirm-btn\" @click=\"confirmCancel\">确认取消</button>\n        </view>\n      </view>\n    </uni-popup>\n    \n\n  </view>\n</template>\n\n<script setup>\nimport { ref, computed, onMounted, onUnmounted } from 'vue'\nimport { useBookingStore } from '@/stores/booking.js'\nimport { useUserStore } from '@/stores/user.js'\nimport { formatDate, formatTime } from '@/utils/helpers.js'\nimport CountdownTimer from '@/components/CountdownTimer.vue'\nimport { shouldShowCountdown } from '@/utils/countdown.js'\nimport { onShow, onPullDownRefresh, onReachBottom } from '@dcloudio/uni-app'\n\n// 使用Pinia stores\nconst bookingStore = useBookingStore()\nconst userStore = useUserStore()\n\n// 响应式数据\nconst selectedStatus = ref('all')\nconst statusOptions = ref([\n  { label: '全部', value: 'all' },\n  { label: '待支付', value: 'PENDING' },\n  { label: '已支付', value: 'PAID' },\n  { label: '已确认', value: 'CONFIRMED' },\n  { label: '已核销', value: 'VERIFIED' },\n  { label: '已完成', value: 'COMPLETED' },\n  { label: '已取消', value: 'CANCELLED' },\n  { label: '已过期', value: 'EXPIRED' },\n  // 拼场相关状态\n  { label: '开放中', value: 'OPEN' },\n  { label: '等待对方支付', value: 'APPROVED_PENDING_PAYMENT' },\n  { label: '拼场成功', value: 'SHARING_SUCCESS' }\n])\nconst currentBookingId = ref(null)\nconst showCancelModal = ref(false)\n\n// 计算属性（修复：使用新的getter名称）\nconst bookingList = computed(() => bookingStore.bookingListGetter)\nconst loading = computed(() => bookingStore.isLoading)\n\nconst pagination = computed(() => {\n  return bookingStore.getPagination\n})\n\nconst hasMore = computed(() => {\n  return pagination.value.current < pagination.value.totalPages\n})\n\nconst filteredBookings = computed(() => {\n  const bookings = bookingList.value || []\n\n  console.log('[BookingList] filteredBookings计算中...')\n  console.log('[BookingList] 原始数据:', bookings.length, '条')\n  console.log('[BookingList] 选中状态:', selectedStatus.value)\n\n  if (selectedStatus.value === 'all') {\n    console.log('[BookingList] 返回全部数据:', bookings.length, '条')\n    return bookings\n  }\n\n  const filtered = bookings.filter(\n    (booking) => {\n      console.log('[BookingList] 筛选订单:', booking.id, '状态:', booking.status, '匹配:', booking.status === selectedStatus.value)\n      return booking.status === selectedStatus.value\n    }\n  )\n\n  console.log('[BookingList] 筛选后数据:', filtered.length, '条')\n  return filtered\n})\n\n// 其他响应式数据\nconst lastRequestStatus = ref('')\nconst lastError = ref('')\nconst cancelPopup = ref(null)\n\n// 其他计算属性（如果模板中需要使用，可以取消注释）\n// const token = computed(() => userStore.getToken)\n// const userInfo = computed(() => userStore.getUserInfo)\n// const isLoggedIn = computed(() => userStore.isLoggedIn)\n\n    // 初始化数据\n    const initData = async () => {\n      try {\n        lastRequestStatus.value = '请求中...';\n        lastError.value = '无';\n\n        await bookingStore.getUserBookings({ page: 1, pageSize: 10 });\n        lastRequestStatus.value = '请求成功';\n      } catch (error) {\n        lastRequestStatus.value = '请求失败';\n        lastError.value = error.message || '获取预约列表失败';\n        console.error('初始化数据失败:', error);\n        uni.showToast({\n          title: error.message || '获取预约列表失败',\n          icon: 'none',\n          duration: 2000\n        });\n      }\n    };\n\n    // 刷新数据\n    const refreshData = async () => {\n      try {\n        lastRequestStatus.value = '刷新中...';\n        lastError.value = '无';\n\n        await bookingStore.getUserBookings({\n          page: 1,\n          pageSize: 10,\n          refresh: true,\n          timestamp: Date.now()\n        });\n        lastRequestStatus.value = '刷新成功';\n        uni.stopPullDownRefresh();\n      } catch (error) {\n        lastRequestStatus.value = '刷新失败';\n        lastError.value = error.message || '刷新数据失败';\n        uni.stopPullDownRefresh();\n        console.error('刷新数据失败:', error);\n        uni.showToast({\n          title: error.message || '刷新数据失败',\n          icon: 'none',\n          duration: 2000\n        });\n      }\n    };\n\n    // 加载更多\n    const loadMore = async () => {\n      if (loading.value || !hasMore.value) return;\n\n      try {\n        const nextPage = pagination.value.current + 1;\n        await bookingStore.getUserBookings({ page: nextPage, pageSize: 10 });\n      } catch (error) {\n        console.error('加载更多失败:', error);\n      }\n    };\n\n    // 选择状态\n    const selectStatus = (status) => {\n      selectedStatus.value = status;\n    };\n\n    // 跳转到详情页\n    const navigateToDetail = (bookingId) => {\n      uni.navigateTo({\n        url: `/pages/booking/detail?id=${bookingId}`\n      });\n    };\n\n    // 显示取消弹窗\n    const showCancelModalFunc = (bookingId) => {\n      currentBookingId.value = bookingId;\n      if (cancelPopup.value) {\n        cancelPopup.value.open();\n      }\n    };\n\n    // 关闭取消弹窗\n    const closeCancelModal = () => {\n      if (cancelPopup.value) {\n        cancelPopup.value.close();\n      }\n      currentBookingId.value = null;\n    };\n\n    // 确认取消\n    const confirmCancel = async () => {\n      try {\n        uni.showLoading({ title: '取消中...' });\n\n        await bookingStore.cancelBooking(currentBookingId.value);\n\n        uni.hideLoading();\n        closeCancelModal();\n\n        uni.showToast({\n          title: '取消成功',\n          icon: 'success'\n        });\n\n        await refreshData();\n      } catch (error) {\n        uni.hideLoading();\n        console.error('取消预约失败:', error);\n        uni.showToast({\n          title: error.message || '取消失败',\n          icon: 'error'\n        });\n      }\n    };\n\n    // 评价场馆\n    const reviewVenue = (booking) => {\n      uni.navigateTo({\n        url: `/pages/venue/review?venueId=${booking.venueId}&bookingId=${booking.id}`\n      });\n    };\n\n    // 再次预约\n    const rebookVenue = (booking) => {\n      uni.navigateTo({\n        url: `/pages/venue/detail?id=${booking.venueId}`\n      });\n    };\n\n    // 跳转到场馆列表\n    const navigateToVenueList = () => {\n      uni.switchTab({\n        url: '/pages/venue/list'\n      });\n    };\n\n    // 格式化日期\n    const formatDateFunc = (date) => {\n      if (!date) return '';\n      return formatDate(date, 'MM-DD dddd');\n    };\n\n    // 格式化创建时间\n    const formatCreateTime = (datetime) => {\n      return formatTime(datetime, 'YYYY-MM-DD HH:mm');\n    };\n\n    // 获取状态样式类\n    const getStatusClass = (status) => {\n      const statusMap = {\n        // 基础状态样式\n        'PENDING': 'status-pending',\n        'PAID': 'status-paid',\n        'CONFIRMED': 'status-confirmed',\n        'VERIFIED': 'status-verified',\n        'COMPLETED': 'status-completed',\n        'CANCELLED': 'status-cancelled',\n        'EXPIRED': 'status-expired',\n\n        // 拼场状态样式\n        'OPEN': 'status-open',\n        'APPROVED_PENDING_PAYMENT': 'status-approved-pending-payment',\n        'SHARING_SUCCESS': 'status-sharing-success',\n        'PENDING_FULL': 'status-pending-full',\n        'FULL': 'status-full'\n      };\n      return statusMap[status] || 'status-pending';\n    };\n\n    // 获取状态文本\n    const getStatusText = (status) => {\n      const statusMap = {\n        // 基础状态（所有订单通用）\n        'PENDING': '待支付',\n        'PAID': '已支付',\n        'CONFIRMED': '已确认',\n        'VERIFIED': '已核销',\n        'COMPLETED': '已完成',\n        'CANCELLED': '已取消',\n        'EXPIRED': '已过期',\n\n        // 拼场订单特有状态\n        'OPEN': '开放中(1/2)',\n        'APPROVED_PENDING_PAYMENT': '等待对方支付',\n        'SHARING_SUCCESS': '拼场成功(2人)',\n        'PENDING_FULL': '待满员',\n        'FULL': '已满员(2/2)'\n      };\n      return statusMap[status] || '待支付';\n    };\n\n    // 格式化时间范围显示\n    const formatTimeRange = (booking) => {\n      // 检查是否是虚拟订单\n      const bookingId = typeof booking.id === 'string' ? parseInt(booking.id) : booking.id;\n      const isVirtual = bookingId < 0;\n\n      if (isVirtual) {\n        // 虚拟订单使用预约列表API返回的 startTime 和 endTime 字段 (格式: \"HH:mm\")\n        const startTime = booking.startTime;\n        const endTime = booking.endTime;\n\n        console.log('虚拟订单时间显示 - startTime:', startTime, 'endTime:', endTime);\n\n        if (!startTime) return '时间待定';\n\n        try {\n          // 预约列表API返回的时间格式是 \"HH:mm\"，可以直接使用\n          const startTimeStr = startTime;\n          const endTimeStr = endTime;\n\n          if (endTimeStr) {\n            return `${startTimeStr} - ${endTimeStr}`;\n          } else {\n            return startTimeStr;\n          }\n        } catch (error) {\n          console.error('虚拟订单时间格式化错误:', error);\n          return '时间待定';\n        }\n      } else {\n        // 普通订单使用原有逻辑\n        const startTime = booking.startTime || booking.bookingStartTime;\n        const endTime = booking.endTime || booking.bookingEndTime;\n        const timeSlotCount = booking.timeSlotCount || 1;\n\n        if (!startTime || !endTime) {\n          return '时间待定';\n        }\n\n        const formatTime = (timeStr) => {\n          if (!timeStr) return '';\n          if (timeStr.length > 5 && timeStr.includes(':')) {\n            return timeStr.substring(0, 5);\n          }\n          return timeStr;\n        };\n\n        const formattedStart = formatTime(startTime);\n        const formattedEnd = formatTime(endTime);\n\n        if (timeSlotCount > 1) {\n          return `${formattedStart} - ${formattedEnd} (${timeSlotCount}个时段)`;\n        }\n\n        return `${formattedStart} - ${formattedEnd}`;\n      }\n    };\n\n    // 获取预约类型文本\n    const getBookingTypeText = (bookingType) => {\n      const typeMap = {\n        'EXCLUSIVE': '包场',\n        'SHARED': '拼场'\n      };\n      return bookingType ? (typeMap[bookingType] || '普通') : '普通';\n    };\n\n    // 获取预约类型样式类\n    const getBookingTypeClass = (bookingType) => {\n      const classMap = {\n        'EXCLUSIVE': 'tag-exclusive',\n        'SHARED': 'tag-shared'\n      };\n      return bookingType ? (classMap[bookingType] || 'tag-default') : 'tag-default';\n    };\n\n    // 检查是否是虚拟订单\n    const isVirtualOrder = (booking) => {\n      if (!booking) return false;\n      const bookingId = typeof booking.id === 'string' ? parseInt(booking.id) : booking.id;\n      return bookingId < 0;\n    };\n\n    // 获取订单价格（兼容虚拟订单和普通订单）\n    const getBookingPrice = (booking) => {\n      if (!booking) return '0.00';\n\n      // 检查是否是虚拟订单（负数ID）\n      const virtualOrder = isVirtualOrder(booking);\n\n      let price;\n      if (virtualOrder) {\n        // 虚拟订单使用 paymentAmount\n        price = booking.paymentAmount || 0;\n      } else {\n        // 普通订单使用 totalPrice\n        price = booking.totalPrice || 0;\n      }\n\n      return price.toFixed(2);\n    };\n\n    // 格式化预约日期（兼容虚拟订单和普通订单）\n    const formatBookingDate = (booking) => {\n      if (!booking) return '';\n\n      // 检查是否是虚拟订单\n      const virtualOrder = isVirtualOrder(booking);\n\n      if (virtualOrder) {\n        // 虚拟订单从 bookingTime 中提取日期\n        const bookingTime = booking.bookingTime;\n        if (!bookingTime) return '';\n\n        try {\n          let dateTime;\n          if (typeof bookingTime === 'string') {\n            let isoTime = bookingTime;\n            if (bookingTime.includes(' ') && !bookingTime.includes('T')) {\n              isoTime = bookingTime.replace(' ', 'T');\n            }\n            dateTime = new Date(isoTime);\n          } else {\n            dateTime = new Date(bookingTime);\n          }\n\n          if (isNaN(dateTime.getTime())) {\n            console.error('虚拟订单日期格式化错误 - 无效的时间:', bookingTime);\n            return '';\n          }\n\n          return dateTime.toLocaleDateString('zh-CN', {\n            year: 'numeric',\n            month: '2-digit',\n            day: '2-digit'\n          }).replace(/\\//g, '-');\n        } catch (error) {\n          console.error('虚拟订单日期格式化错误:', error);\n          return '';\n        }\n      } else {\n        // 普通订单使用 bookingDate 字段\n        if (booking.bookingDate) {\n          return formatDate(booking.bookingDate);\n        }\n        return '';\n      }\n    };\n\n    onMounted(() => {\n      // 监听拼场数据变化\n      uni.$on('sharingDataChanged', onSharingDataChanged)\n      initData();\n    });\n\n    onUnmounted(() => {\n      // 移除监听器\n      uni.$off('sharingDataChanged', onSharingDataChanged)\n    });\n\n    // 处理拼场数据变化\n    const onSharingDataChanged = (data) => {\n      console.log('预约列表页面：收到拼场数据变化通知:', data)\n      // 刷新数据以确保状态一致\n      initData()\n    };\n\n    onShow(async () => {\n      await refreshData();\n    });\n\n    onPullDownRefresh(async () => {\n      await refreshData();\n    });\n\n    onReachBottom(() => {\n      loadMore();\n    });\n\n    // 倒计时相关方法\n    const shouldShowCountdownFunc = (order) => {\n      return shouldShowCountdown(order);\n    };\n\n    const onCountdownExpired = (_order) => {\n      // 刷新数据，更新订单状态\n      initData();\n    };\n\n    // 支付订单\n    const payOrder = (booking) => {\n      uni.navigateTo({\n        url: `/pages/payment/index?orderId=${booking.id}&type=booking`\n      });\n    };\n\n    // 查看订单详情\n    const viewOrderDetail = (booking) => {\n      uni.navigateTo({\n        url: `/pages/booking/detail?id=${booking.id}`\n      });\n    };\n\n    // 查看参与者\n    const viewParticipants = (booking) => {\n      uni.navigateTo({\n        url: `/pages/sharing/participants?orderId=${booking.id}`\n      });\n    };\n\n    // 签到\n    const checkinOrder = (_booking) => {\n      uni.showModal({\n        title: '确认签到',\n        content: '确认已到达场馆并开始使用？',\n        success: (res) => {\n          if (res.confirm) {\n            // TODO: 调用签到API\n            uni.showToast({\n              title: '签到成功',\n              icon: 'success'\n            });\n            initData(); // 刷新数据\n          }\n        }\n      });\n    };\n\n    // 完成订单\n    const completeOrder = (_booking) => {\n      uni.showModal({\n        title: '完成订单',\n        content: '确认完成此次预约？',\n        success: (res) => {\n          if (res.confirm) {\n            // TODO: 调用完成订单API\n            uni.showToast({\n              title: '订单已完成',\n              icon: 'success'\n            });\n            initData(); // 刷新数据\n          }\n        }\n      });\n    };\n\n\n</script>\n\n\n<style lang=\"scss\" scoped>\n.container {\n  background-color: #f5f5f5;\n  min-height: 100vh;\n}\n\n// 状态筛选\n.status-filter {\n  display: flex;\n  background-color: #ffffff;\n  padding: 20rpx 30rpx;\n  border-bottom: 1rpx solid #f0f0f0;\n  \n  .filter-item {\n    flex: 1;\n    text-align: center;\n    padding: 16rpx 0;\n    font-size: 28rpx;\n    color: #666666;\n    position: relative;\n    \n    &.active {\n      color: #ff6b35;\n      font-weight: 600;\n      \n      &::after {\n        content: '';\n        position: absolute;\n        bottom: 0;\n        left: 50%;\n        transform: translateX(-50%);\n        width: 60rpx;\n        height: 4rpx;\n        background-color: #ff6b35;\n        border-radius: 2rpx;\n      }\n    }\n  }\n}\n\n// 预约列表\n.booking-list {\n  padding: 20rpx 30rpx;\n  \n  .booking-card {\n    background-color: #ffffff;\n    border-radius: 16rpx;\n    padding: 30rpx;\n    margin-bottom: 20rpx;\n    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);\n    \n    .card-header {\n      display: flex;\n      justify-content: space-between;\n      align-items: flex-start;\n      margin-bottom: 24rpx;\n      \n      .venue-info {\n        flex: 1;\n\n        .venue-name-row {\n          display: flex;\n          align-items: center;\n          margin-bottom: 8rpx;\n\n          .venue-name {\n            font-size: 32rpx;\n            font-weight: 600;\n            color: #333333;\n            margin-right: 12rpx;\n          }\n\n          .booking-type-tag {\n            display: inline-block;\n\n            .tag-text {\n              font-size: 20rpx;\n              padding: 4rpx 12rpx;\n              border-radius: 12rpx;\n              border: 1rpx solid;\n\n              // 拼场样式\n              &.tag-shared {\n                color: #ff6b35;\n                background-color: #fff7e6;\n                border-color: #ff6b35;\n              }\n\n              // 包场样式\n              &.tag-exclusive {\n                color: #1890ff;\n                background-color: #e6f7ff;\n                border-color: #1890ff;\n              }\n\n              // 默认样式\n              &.tag-default {\n                color: #666666;\n                background-color: #f5f5f5;\n                border-color: #d9d9d9;\n              }\n            }\n          }\n\n          // 虚拟订单标识\n          .virtual-order-tag {\n            display: inline-block;\n            margin-left: 8rpx;\n\n            .virtual-tag-text {\n              font-size: 20rpx;\n              padding: 4rpx 12rpx;\n              border-radius: 12rpx;\n              color: #722ed1;\n              background-color: #f9f0ff;\n              border: 1rpx solid #722ed1;\n            }\n          }\n        }\n\n        .booking-date {\n          font-size: 24rpx;\n          color: #666666;\n          margin-bottom: 8rpx;\n        }\n      }\n      \n      .booking-status {\n        font-size: 22rpx;\n        padding: 8rpx 16rpx;\n        border-radius: 16rpx;\n        \n        // 基础状态样式\n        &.status-pending {\n          background-color: #fff7e6;\n          color: #fa8c16;\n        }\n\n        &.status-paid {\n          background-color: #e6f7ff;\n          color: #1890ff;\n        }\n\n        &.status-confirmed {\n          background-color: #e6f7ff;\n          color: #1890ff;\n        }\n\n        &.status-verified {\n          background-color: #f6ffed;\n          color: #52c41a;\n        }\n\n        &.status-completed {\n          background-color: #f6ffed;\n          color: #52c41a;\n        }\n\n        &.status-cancelled {\n          background-color: #fff2f0;\n          color: #ff4d4f;\n        }\n\n        &.status-expired {\n          background-color: #f5f5f5;\n          color: #8c8c8c;\n        }\n\n        // 拼场状态样式\n        &.status-sharing {\n          background-color: #fff0f6;\n          color: #eb2f96;\n        }\n\n        &.status-sharing-success {\n          background-color: #f6ffed;\n          color: #52c41a;\n        }\n\n        &.status-open {\n          background-color: #fff0f6;\n          color: #eb2f96;\n        }\n\n        &.status-pending-full {\n          background-color: #fff7e6;\n          color: #fa8c16;\n        }\n\n        &.status-full {\n          background-color: #f6ffed;\n          color: #52c41a;\n        }\n      }\n    }\n    \n    .card-content {\n      margin-bottom: 24rpx;\n      \n      .time-info,\n      .location-info,\n      .order-info,\n      .create-time-info,\n      .price-info {\n        display: flex;\n        align-items: center;\n        margin-bottom: 16rpx;\n        \n        &:last-child {\n          margin-bottom: 0;\n        }\n      }\n      \n      .time-icon,\n      .location-icon,\n      .order-icon {\n        font-size: 24rpx;\n        margin-right: 12rpx;\n      }\n      \n      .time-text,\n      .location-text,\n      .order-text,\n      .create-time-text {\n        font-size: 26rpx;\n        color: #666666;\n      }\n      \n      .price-info {\n        .price-label {\n          font-size: 26rpx;\n          color: #666666;\n        }\n\n        .price-value {\n          font-size: 28rpx;\n          color: #ff6b35;\n          font-weight: 600;\n          margin-left: 8rpx;\n        }\n      }\n\n      // 倒计时样式\n      .countdown-container {\n        margin-top: 12rpx;\n        display: flex;\n        align-items: center;\n\n        &.simple {\n          padding: 8rpx 12rpx;\n          font-size: 22rpx;\n          border-radius: 8rpx;\n\n          .countdown-icon {\n            font-size: 24rpx;\n            margin-right: 6rpx;\n          }\n\n          .countdown-content {\n            display: flex;\n            align-items: center;\n\n            .countdown-label {\n              font-size: 20rpx;\n              margin-right: 6rpx;\n            }\n\n            .countdown-time {\n              font-size: 22rpx;\n              font-weight: bold;\n            }\n          }\n        }\n      }\n    }\n    \n    .card-actions {\n      display: flex;\n      justify-content: flex-end;\n      \n      .action-btn {\n        padding: 12rpx 24rpx;\n        border-radius: 8rpx;\n        font-size: 24rpx;\n        margin-left: 16rpx;\n        border: 1rpx solid;\n        \n        &.cancel-btn {\n          background-color: transparent;\n          color: #ff4d4f;\n          border-color: #ff4d4f;\n        }\n\n        &.pay-btn {\n          background-color: #ff6b35;\n          color: #ffffff;\n          border-color: #ff6b35;\n        }\n\n        &.info-btn {\n          background-color: transparent;\n          color: #1890ff;\n          border-color: #1890ff;\n        }\n        \n        &.share-btn {\n          background-color: #ff6b35;\n          color: #ffffff;\n          border-color: #ff6b35;\n        }\n        \n        &.review-btn {\n          background-color: transparent;\n          color: #1890ff;\n          border-color: #1890ff;\n        }\n        \n        &.rebook-btn {\n          background-color: #ff6b35;\n          color: #ffffff;\n          border-color: #ff6b35;\n        }\n\n        &.participants-btn {\n          background-color: transparent;\n          color: #722ed1;\n          border-color: #722ed1;\n        }\n\n        &.checkin-btn {\n          background-color: #52c41a;\n          color: #ffffff;\n          border-color: #52c41a;\n        }\n\n        &.complete-btn {\n          background-color: #1890ff;\n          color: #ffffff;\n          border-color: #1890ff;\n        }\n      }\n    }\n  }\n}\n\n// 空状态\n.empty-state {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  padding: 120rpx 60rpx;\n  \n  .empty-icon {\n    font-size: 120rpx;\n    margin-bottom: 30rpx;\n    opacity: 0.3;\n  }\n  \n  .empty-text {\n    font-size: 28rpx;\n    color: #999999;\n    margin-bottom: 40rpx;\n  }\n  \n  .empty-btn {\n    padding: 16rpx 40rpx;\n    background-color: #ff6b35;\n    color: #ffffff;\n    border: none;\n    border-radius: 8rpx;\n    font-size: 26rpx;\n  }\n}\n\n// 加载更多\n.load-more {\n  text-align: center;\n  padding: 40rpx;\n  font-size: 28rpx;\n  color: #666666;\n}\n\n// 弹窗通用样式\n.cancel-modal,\n.share-modal {\n  width: 600rpx;\n  background-color: #ffffff;\n  border-radius: 16rpx;\n  overflow: hidden;\n  \n  .modal-header {\n    padding: 30rpx;\n    text-align: center;\n    border-bottom: 1rpx solid #f0f0f0;\n    \n    .modal-title {\n      font-size: 32rpx;\n      font-weight: 600;\n      color: #333333;\n    }\n  }\n  \n  .modal-actions {\n    display: flex;\n    border-top: 1rpx solid #f0f0f0;\n    \n    .modal-btn {\n      flex: 1;\n      height: 100rpx;\n      border: none;\n      font-size: 28rpx;\n      \n      &.cancel-btn {\n        background-color: #f5f5f5;\n        color: #666666;\n      }\n      \n      &.confirm-btn {\n        background-color: #ff6b35;\n        color: #ffffff;\n      }\n    }\n  }\n}\n\n// 取消弹窗\n.cancel-modal {\n  .modal-content {\n    padding: 40rpx 30rpx;\n    text-align: center;\n    \n    .modal-text {\n      display: block;\n      font-size: 28rpx;\n      color: #333333;\n      margin-bottom: 16rpx;\n    }\n    \n    .modal-note {\n      font-size: 24rpx;\n      color: #999999;\n    }\n  }\n}\n\n// 拼场弹窗\n.share-modal {\n  .share-form {\n    padding: 30rpx;\n    \n    .form-item {\n      margin-bottom: 30rpx;\n      \n      &:last-child {\n        margin-bottom: 0;\n      }\n      \n      .form-label {\n        display: block;\n        font-size: 28rpx;\n        color: #333333;\n        margin-bottom: 16rpx;\n      }\n      \n      .form-input,\n      .picker-text {\n        width: 100%;\n        height: 80rpx;\n        border: 1rpx solid #e0e0e0;\n        border-radius: 8rpx;\n        padding: 0 20rpx;\n        font-size: 28rpx;\n        background-color: #ffffff;\n      }\n      \n      .picker-text {\n        display: flex;\n        align-items: center;\n        color: #333333;\n      }\n      \n      .form-textarea {\n        width: 100%;\n        min-height: 120rpx;\n        border: 1rpx solid #e0e0e0;\n        border-radius: 8rpx;\n        padding: 20rpx;\n        font-size: 28rpx;\n        background-color: #ffffff;\n      }\n    }\n  }\n}\n</style>", "import MiniProgramPage from '/Users/<USER>/Desktop/体育馆预约 2/体育馆预约2.0/pages/booking/list.vue'\nwx.createPage(MiniProgramPage)"], "names": ["useBookingStore", "useUserStore", "ref", "computed", "uni", "formatTime", "formatDate", "onMounted", "onUnmounted", "onShow", "onPullDownRefresh", "onReachBottom"], "mappings": ";;;;;;;;;;;;;;AAkLA,MAAM,iBAAiB,MAAW;;;;AAKlC,UAAM,eAAeA,eAAAA,gBAAiB;AACpBC,gBAAAA,aAAc;AAGhC,UAAM,iBAAiBC,cAAG,IAAC,KAAK;AAChC,UAAM,gBAAgBA,cAAAA,IAAI;AAAA,MACxB,EAAE,OAAO,MAAM,OAAO,MAAO;AAAA,MAC7B,EAAE,OAAO,OAAO,OAAO,UAAW;AAAA,MAClC,EAAE,OAAO,OAAO,OAAO,OAAQ;AAAA,MAC/B,EAAE,OAAO,OAAO,OAAO,YAAa;AAAA,MACpC,EAAE,OAAO,OAAO,OAAO,WAAY;AAAA,MACnC,EAAE,OAAO,OAAO,OAAO,YAAa;AAAA,MACpC,EAAE,OAAO,OAAO,OAAO,YAAa;AAAA,MACpC,EAAE,OAAO,OAAO,OAAO,UAAW;AAAA;AAAA,MAElC,EAAE,OAAO,OAAO,OAAO,OAAQ;AAAA,MAC/B,EAAE,OAAO,UAAU,OAAO,2BAA4B;AAAA,MACtD,EAAE,OAAO,QAAQ,OAAO,kBAAmB;AAAA,IAC7C,CAAC;AACD,UAAM,mBAAmBA,cAAG,IAAC,IAAI;AACjC,UAAM,kBAAkBA,cAAG,IAAC,KAAK;AAGjC,UAAM,cAAcC,cAAQ,SAAC,MAAM,aAAa,iBAAiB;AACjE,UAAM,UAAUA,cAAQ,SAAC,MAAM,aAAa,SAAS;AAErD,UAAM,aAAaA,cAAQ,SAAC,MAAM;AAChC,aAAO,aAAa;AAAA,IACtB,CAAC;AAED,UAAM,UAAUA,cAAQ,SAAC,MAAM;AAC7B,aAAO,WAAW,MAAM,UAAU,WAAW,MAAM;AAAA,IACrD,CAAC;AAED,UAAM,mBAAmBA,cAAQ,SAAC,MAAM;AACtC,YAAM,WAAW,YAAY,SAAS,CAAE;AAExCC,oBAAAA,MAAY,MAAA,OAAA,iCAAA,sCAAsC;AAClDA,wEAAY,uBAAuB,SAAS,QAAQ,GAAG;AACvDA,wEAAY,uBAAuB,eAAe,KAAK;AAEvD,UAAI,eAAe,UAAU,OAAO;AAClCA,sBAAA,MAAA,MAAA,OAAA,iCAAY,yBAAyB,SAAS,QAAQ,GAAG;AACzD,eAAO;AAAA,MACR;AAED,YAAM,WAAW,SAAS;AAAA,QACxB,CAAC,YAAY;AACXA,4EAAY,uBAAuB,QAAQ,IAAI,OAAO,QAAQ,QAAQ,OAAO,QAAQ,WAAW,eAAe,KAAK;AACpH,iBAAO,QAAQ,WAAW,eAAe;AAAA,QAC1C;AAAA,MACF;AAEDA,wEAAY,wBAAwB,SAAS,QAAQ,GAAG;AACxD,aAAO;AAAA,IACT,CAAC;AAGD,UAAM,oBAAoBF,cAAG,IAAC,EAAE;AAChC,UAAM,YAAYA,cAAG,IAAC,EAAE;AACxB,UAAM,cAAcA,cAAG,IAAC,IAAI;AAQxB,UAAM,WAAW,YAAY;AAC3B,UAAI;AACF,0BAAkB,QAAQ;AAC1B,kBAAU,QAAQ;AAElB,cAAM,aAAa,gBAAgB,EAAE,MAAM,GAAG,UAAU,GAAE,CAAE;AAC5D,0BAAkB,QAAQ;AAAA,MAC3B,SAAQ,OAAO;AACd,0BAAkB,QAAQ;AAC1B,kBAAU,QAAQ,MAAM,WAAW;AACnCE,sBAAc,MAAA,MAAA,SAAA,iCAAA,YAAY,KAAK;AAC/BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO,MAAM,WAAW;AAAA,UACxB,MAAM;AAAA,UACN,UAAU;AAAA,QACpB,CAAS;AAAA,MACF;AAAA,IACP;AAGI,UAAM,cAAc,YAAY;AAC9B,UAAI;AACF,0BAAkB,QAAQ;AAC1B,kBAAU,QAAQ;AAElB,cAAM,aAAa,gBAAgB;AAAA,UACjC,MAAM;AAAA,UACN,UAAU;AAAA,UACV,SAAS;AAAA,UACT,WAAW,KAAK,IAAK;AAAA,QAC/B,CAAS;AACD,0BAAkB,QAAQ;AAC1BA,sBAAG,MAAC,oBAAmB;AAAA,MACxB,SAAQ,OAAO;AACd,0BAAkB,QAAQ;AAC1B,kBAAU,QAAQ,MAAM,WAAW;AACnCA,sBAAG,MAAC,oBAAmB;AACvBA,sBAAA,MAAA,MAAA,SAAA,iCAAc,WAAW,KAAK;AAC9BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO,MAAM,WAAW;AAAA,UACxB,MAAM;AAAA,UACN,UAAU;AAAA,QACpB,CAAS;AAAA,MACF;AAAA,IACP;AAGI,UAAM,WAAW,YAAY;AAC3B,UAAI,QAAQ,SAAS,CAAC,QAAQ;AAAO;AAErC,UAAI;AACF,cAAM,WAAW,WAAW,MAAM,UAAU;AAC5C,cAAM,aAAa,gBAAgB,EAAE,MAAM,UAAU,UAAU,GAAE,CAAE;AAAA,MACpE,SAAQ,OAAO;AACdA,sBAAA,MAAA,MAAA,SAAA,iCAAc,WAAW,KAAK;AAAA,MAC/B;AAAA,IACP;AAGI,UAAM,eAAe,CAAC,WAAW;AAC/B,qBAAe,QAAQ;AAAA,IAC7B;AAGI,UAAM,mBAAmB,CAAC,cAAc;AACtCA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,4BAA4B,SAAS;AAAA,MAClD,CAAO;AAAA,IACP;AAWI,UAAM,mBAAmB,MAAM;AAC7B,UAAI,YAAY,OAAO;AACrB,oBAAY,MAAM;MACnB;AACD,uBAAiB,QAAQ;AAAA,IAC/B;AAGI,UAAM,gBAAgB,YAAY;AAChC,UAAI;AACFA,sBAAAA,MAAI,YAAY,EAAE,OAAO,SAAU,CAAA;AAEnC,cAAM,aAAa,cAAc,iBAAiB,KAAK;AAEvDA,sBAAG,MAAC,YAAW;AACf;AAEAA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QAChB,CAAS;AAED,cAAM,YAAW;AAAA,MAClB,SAAQ,OAAO;AACdA,sBAAG,MAAC,YAAW;AACfA,sBAAA,MAAA,MAAA,SAAA,iCAAc,WAAW,KAAK;AAC9BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO,MAAM,WAAW;AAAA,UACxB,MAAM;AAAA,QAChB,CAAS;AAAA,MACF;AAAA,IACP;AAGI,UAAM,cAAc,CAAC,YAAY;AAC/BA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,+BAA+B,QAAQ,OAAO,cAAc,QAAQ,EAAE;AAAA,MACnF,CAAO;AAAA,IACP;AAGI,UAAM,cAAc,CAAC,YAAY;AAC/BA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,0BAA0B,QAAQ,OAAO;AAAA,MACtD,CAAO;AAAA,IACP;AAGI,UAAM,sBAAsB,MAAM;AAChCA,oBAAAA,MAAI,UAAU;AAAA,QACZ,KAAK;AAAA,MACb,CAAO;AAAA,IACP;AASI,UAAM,mBAAmB,CAAC,aAAa;AACrC,aAAOC,cAAU,WAAC,UAAU,kBAAkB;AAAA,IACpD;AAGI,UAAM,iBAAiB,CAAC,WAAW;AACjC,YAAM,YAAY;AAAA;AAAA,QAEhB,WAAW;AAAA,QACX,QAAQ;AAAA,QACR,aAAa;AAAA,QACb,YAAY;AAAA,QACZ,aAAa;AAAA,QACb,aAAa;AAAA,QACb,WAAW;AAAA;AAAA,QAGX,QAAQ;AAAA,QACR,4BAA4B;AAAA,QAC5B,mBAAmB;AAAA,QACnB,gBAAgB;AAAA,QAChB,QAAQ;AAAA,MAChB;AACM,aAAO,UAAU,MAAM,KAAK;AAAA,IAClC;AAGI,UAAM,gBAAgB,CAAC,WAAW;AAChC,YAAM,YAAY;AAAA;AAAA,QAEhB,WAAW;AAAA,QACX,QAAQ;AAAA,QACR,aAAa;AAAA,QACb,YAAY;AAAA,QACZ,aAAa;AAAA,QACb,aAAa;AAAA,QACb,WAAW;AAAA;AAAA,QAGX,QAAQ;AAAA,QACR,4BAA4B;AAAA,QAC5B,mBAAmB;AAAA,QACnB,gBAAgB;AAAA,QAChB,QAAQ;AAAA,MAChB;AACM,aAAO,UAAU,MAAM,KAAK;AAAA,IAClC;AAGI,UAAM,kBAAkB,CAAC,YAAY;AAEnC,YAAM,YAAY,OAAO,QAAQ,OAAO,WAAW,SAAS,QAAQ,EAAE,IAAI,QAAQ;AAClF,YAAM,YAAY,YAAY;AAE9B,UAAI,WAAW;AAEb,cAAM,YAAY,QAAQ;AAC1B,cAAM,UAAU,QAAQ;AAExBD,4BAAA,MAAA,OAAA,iCAAY,yBAAyB,WAAW,YAAY,OAAO;AAEnE,YAAI,CAAC;AAAW,iBAAO;AAEvB,YAAI;AAEF,gBAAM,eAAe;AACrB,gBAAM,aAAa;AAEnB,cAAI,YAAY;AACd,mBAAO,GAAG,YAAY,MAAM,UAAU;AAAA,UAClD,OAAiB;AACL,mBAAO;AAAA,UACR;AAAA,QACF,SAAQ,OAAO;AACdA,wBAAc,MAAA,MAAA,SAAA,iCAAA,gBAAgB,KAAK;AACnC,iBAAO;AAAA,QACR;AAAA,MACT,OAAa;AAEL,cAAM,YAAY,QAAQ,aAAa,QAAQ;AAC/C,cAAM,UAAU,QAAQ,WAAW,QAAQ;AAC3C,cAAM,gBAAgB,QAAQ,iBAAiB;AAE/C,YAAI,CAAC,aAAa,CAAC,SAAS;AAC1B,iBAAO;AAAA,QACR;AAED,cAAM,aAAa,CAAC,YAAY;AAC9B,cAAI,CAAC;AAAS,mBAAO;AACrB,cAAI,QAAQ,SAAS,KAAK,QAAQ,SAAS,GAAG,GAAG;AAC/C,mBAAO,QAAQ,UAAU,GAAG,CAAC;AAAA,UAC9B;AACD,iBAAO;AAAA,QACjB;AAEQ,cAAM,iBAAiB,WAAW,SAAS;AAC3C,cAAM,eAAe,WAAW,OAAO;AAEvC,YAAI,gBAAgB,GAAG;AACrB,iBAAO,GAAG,cAAc,MAAM,YAAY,KAAK,aAAa;AAAA,QAC7D;AAED,eAAO,GAAG,cAAc,MAAM,YAAY;AAAA,MAC3C;AAAA,IACP;AAGI,UAAM,qBAAqB,CAAC,gBAAgB;AAC1C,YAAM,UAAU;AAAA,QACd,aAAa;AAAA,QACb,UAAU;AAAA,MAClB;AACM,aAAO,cAAe,QAAQ,WAAW,KAAK,OAAQ;AAAA,IAC5D;AAGI,UAAM,sBAAsB,CAAC,gBAAgB;AAC3C,YAAM,WAAW;AAAA,QACf,aAAa;AAAA,QACb,UAAU;AAAA,MAClB;AACM,aAAO,cAAe,SAAS,WAAW,KAAK,gBAAiB;AAAA,IACtE;AAGI,UAAM,iBAAiB,CAAC,YAAY;AAClC,UAAI,CAAC;AAAS,eAAO;AACrB,YAAM,YAAY,OAAO,QAAQ,OAAO,WAAW,SAAS,QAAQ,EAAE,IAAI,QAAQ;AAClF,aAAO,YAAY;AAAA,IACzB;AAGI,UAAM,kBAAkB,CAAC,YAAY;AACnC,UAAI,CAAC;AAAS,eAAO;AAGrB,YAAM,eAAe,eAAe,OAAO;AAE3C,UAAI;AACJ,UAAI,cAAc;AAEhB,gBAAQ,QAAQ,iBAAiB;AAAA,MACzC,OAAa;AAEL,gBAAQ,QAAQ,cAAc;AAAA,MAC/B;AAED,aAAO,MAAM,QAAQ,CAAC;AAAA,IAC5B;AAGI,UAAM,oBAAoB,CAAC,YAAY;AACrC,UAAI,CAAC;AAAS,eAAO;AAGrB,YAAM,eAAe,eAAe,OAAO;AAE3C,UAAI,cAAc;AAEhB,cAAM,cAAc,QAAQ;AAC5B,YAAI,CAAC;AAAa,iBAAO;AAEzB,YAAI;AACF,cAAI;AACJ,cAAI,OAAO,gBAAgB,UAAU;AACnC,gBAAI,UAAU;AACd,gBAAI,YAAY,SAAS,GAAG,KAAK,CAAC,YAAY,SAAS,GAAG,GAAG;AAC3D,wBAAU,YAAY,QAAQ,KAAK,GAAG;AAAA,YACvC;AACD,uBAAW,IAAI,KAAK,OAAO;AAAA,UACvC,OAAiB;AACL,uBAAW,IAAI,KAAK,WAAW;AAAA,UAChC;AAED,cAAI,MAAM,SAAS,QAAO,CAAE,GAAG;AAC7BA,gFAAc,wBAAwB,WAAW;AACjD,mBAAO;AAAA,UACR;AAED,iBAAO,SAAS,mBAAmB,SAAS;AAAA,YAC1C,MAAM;AAAA,YACN,OAAO;AAAA,YACP,KAAK;AAAA,UACN,CAAA,EAAE,QAAQ,OAAO,GAAG;AAAA,QACtB,SAAQ,OAAO;AACdA,wBAAc,MAAA,MAAA,SAAA,iCAAA,gBAAgB,KAAK;AACnC,iBAAO;AAAA,QACR;AAAA,MACT,OAAa;AAEL,YAAI,QAAQ,aAAa;AACvB,iBAAOE,cAAU,WAAC,QAAQ,WAAW;AAAA,QACtC;AACD,eAAO;AAAA,MACR;AAAA,IACP;AAEIC,kBAAAA,UAAU,MAAM;AAEdH,0BAAI,IAAI,sBAAsB,oBAAoB;AAClD;IACN,CAAK;AAEDI,kBAAAA,YAAY,MAAM;AAEhBJ,0BAAI,KAAK,sBAAsB,oBAAoB;AAAA,IACzD,CAAK;AAGD,UAAM,uBAAuB,CAAC,SAAS;AACrCA,oBAAAA,MAAY,MAAA,OAAA,iCAAA,sBAAsB,IAAI;AAEtC,eAAU;AAAA,IAChB;AAEIK,kBAAAA,OAAO,YAAY;AACjB,YAAM,YAAW;AAAA,IACvB,CAAK;AAEDC,kBAAAA,kBAAkB,YAAY;AAC5B,YAAM,YAAW;AAAA,IACvB,CAAK;AAEDC,kBAAAA,cAAc,MAAM;AAClB;IACN,CAAK;AAOD,UAAM,qBAAqB,CAAC,WAAW;AAErC;IACN;AAGI,UAAM,WAAW,CAAC,YAAY;AAC5BP,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,gCAAgC,QAAQ,EAAE;AAAA,MACvD,CAAO;AAAA,IACP;AAGI,UAAM,kBAAkB,CAAC,YAAY;AACnCA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,4BAA4B,QAAQ,EAAE;AAAA,MACnD,CAAO;AAAA,IACP;AAGI,UAAM,mBAAmB,CAAC,YAAY;AACpCA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,uCAAuC,QAAQ,EAAE;AAAA,MAC9D,CAAO;AAAA,IACP;AAGI,UAAM,eAAe,CAAC,aAAa;AACjCA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS;AAAA,QACT,SAAS,CAAC,QAAQ;AAChB,cAAI,IAAI,SAAS;AAEfA,0BAAAA,MAAI,UAAU;AAAA,cACZ,OAAO;AAAA,cACP,MAAM;AAAA,YACpB,CAAa;AACD;UACD;AAAA,QACF;AAAA,MACT,CAAO;AAAA,IACP;AAGI,UAAM,gBAAgB,CAAC,aAAa;AAClCA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS;AAAA,QACT,SAAS,CAAC,QAAQ;AAChB,cAAI,IAAI,SAAS;AAEfA,0BAAAA,MAAI,UAAU;AAAA,cACZ,OAAO;AAAA,cACP,MAAM;AAAA,YACpB,CAAa;AACD;UACD;AAAA,QACF;AAAA,MACT,CAAO;AAAA,IACP;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC1qBA,GAAG,WAAW,eAAe;"}