<template>
  <view class="container">
    <!-- 加载状态 -->
    <view v-if="loading" class="loading-state">
      <text class="loading-text">正在加载订单详情...</text>
    </view>
    
    <!-- 数据为空状态 -->
    <view v-else-if="!bookingDetail || !bookingDetail.orderNo" class="empty-state">
      <text class="empty-text">订单信息不存在</text>
      <button class="retry-btn" @click="initData">重新加载</button>
    </view>
    
    <!-- 正常内容 -->
    <view v-else>
    <!-- 预约状态 -->
    <view class="status-section">
      <view class="status-icon" :class="getStatusClass(bookingDetail?.status)">
        <text>{{ getStatusIcon(bookingDetail?.status) }}</text>
      </view>
      <view class="status-info">
        <text class="status-text">{{ getStatusText(bookingDetail?.status) }}</text>
        <text class="status-desc">{{ getStatusDesc(bookingDetail?.status) }}</text>
      </view>
    </view>
    
    <!-- 预约信息 -->
    <view class="info-section">
      <view class="section-title">预约信息</view>
      
      <view class="info-item">
        <text class="info-label">预约编号</text>
        <text class="info-value">{{ bookingDetail?.orderNo || '--' }}</text>
      </view>
      
      <view class="info-item">
        <text class="info-label">场馆名称</text>
        <text class="info-value">{{ bookingDetail?.venueName || '--' }}</text>
      </view>
      
      <!-- 预约类型标注 -->
      <view class="info-item">
        <text class="info-label">预约类型</text>
        <view class="booking-type-container">
          <text class="info-value booking-type" :class="getBookingTypeClass(bookingDetail?.bookingType)">
            {{ getBookingTypeText(bookingDetail?.bookingType) }}
          </text>
          <!-- 虚拟订单标识 -->
          <text v-if="isVirtualOrder()" class="virtual-order-badge">拼场申请</text>
        </view>
      </view>
      
      <view class="info-item">
        <text class="info-label">场馆地址</text>
        <text class="info-value">{{ bookingDetail?.venueLocation || '--' }}</text>
      </view>
      
      <view class="info-item">
        <text class="info-label">预约日期</text>
        <text class="info-value">{{ formatBookingDate() }}</text>
      </view>
      
      <view class="info-item">
        <text class="info-label">预约时间</text>
        <text class="info-value">{{ formatBookingTime() }}</text>
      </view>
      
      <!-- 预约费用 - 根据订单类型显示不同内容 -->
      <template v-if="isVirtualOrder()">
        <!-- 虚拟订单（拼场申请）的价格显示 -->
        <view class="info-item">
          <text class="info-label">支付金额</text>
          <text class="info-value price" style="color: #722ed1; font-size: 36rpx;">
            ¥{{ getPaymentAmount() }}
          </text>
        </view>
        <view class="info-tip" style="font-size: 24rpx; color: #999; margin-top: 10rpx;">
          <text>拼场申请订单，支付成功后即可参与拼场</text>
        </view>
      </template>
      <template v-else-if="bookingDetail && bookingDetail.isSharedBooking">
        <!-- 普通拼场订单的价格显示 -->
        <view class="price-section" style="margin: 20rpx 0;">
          <view class="info-item">
            <text class="info-label">总费用</text>
            <text class="info-value price">¥{{ (bookingDetail && bookingDetail.totalOriginalPrice) || 0 }}</text>
          </view>
          <view class="info-item" style="color: #ff6b00; background-color: #fff8f0; padding: 10rpx; border-radius: 8rpx;">
            <text class="info-label" style="color: #ff6b00;">拼场优惠</text>
            <text class="info-value" style="color: #ff6b00;">
              ¥{{ ((bookingDetail && bookingDetail.totalOriginalPrice) - (bookingDetail && bookingDetail.totalPrice)) || 0 }}（5折）
            </text>
          </view>
          <view class="info-item" style="font-weight: bold; margin-top: 10rpx;">
            <text class="info-label">实付金额</text>
            <text class="info-value price" style="color: #ff6b00; font-size: 36rpx;">
              ¥{{ (bookingDetail && bookingDetail.totalPrice) || 0 }}
            </text>
          </view>
          <view class="info-tip" style="font-size: 24rpx; color: #999; margin-top: 10rpx;">
            <text>拼场订单，费用由两队均摊，每队支付总费用的50%</text>
          </view>
        </view>
      </template>
      <template v-else>
        <!-- 普通订单的价格显示 -->
        <view class="info-item">
          <text class="info-label">预约费用</text>
          <text class="info-value price">¥{{ (bookingDetail && bookingDetail.totalPrice) || 0 }}</text>
        </view>
      </template>
  
      <view class="info-item">
        <text class="info-label">创建时间</text>
        <text class="info-value">{{ formatCreateTime((bookingDetail && bookingDetail.createdAt) || (bookingDetail && bookingDetail.createTime)) }}</text>
      </view>
    </view>
    
    <!-- 联系信息 -->
    <view class="contact-section">
      <view class="section-title">联系信息</view>
      
      <view class="contact-item" @click="callVenue">
        <view class="contact-icon">
          <text>📞</text>
        </view>
        <view class="contact-info">
          <text class="contact-label">场馆电话</text>
          <text class="contact-value">{{ (bookingDetail && bookingDetail.venuePhone) || '暂无' }}</text>
        </view>
        <view class="contact-arrow">
          <text>></text>
        </view>
      </view>
      
      <view class="contact-item" @click="openMap">
        <view class="contact-icon">
          <text>📍</text>
        </view>
        <view class="contact-info">
          <text class="contact-label">导航到场馆</text>
          <text class="contact-value">{{ (bookingDetail && bookingDetail.venueLocation) || '暂无' }}</text>
        </view>
        <view class="contact-arrow">
          <text>></text>
        </view>
      </view>
    </view>
    
    <!-- 拼场信息 -->
    <view v-if="bookingDetail && bookingDetail.sharingOrder" class="sharing-section">
      <view class="section-title">拼场信息</view>
      
      <view class="sharing-card" @click="navigateToSharingDetail">
        <view class="sharing-header">
          <text class="sharing-team">{{ (bookingDetail && bookingDetail.sharingOrder && bookingDetail.sharingOrder.teamName) || '' }}</text>
          <text class="sharing-status">{{ getSharingStatusText(bookingDetail && bookingDetail.sharingOrder && bookingDetail.sharingOrder.status) }}</text>
        </view>
        
        <view class="sharing-info">
          <text class="sharing-participants">
            当前人数：{{ (bookingDetail && bookingDetail.sharingOrder && bookingDetail.sharingOrder.currentParticipants) || 0 }}/{{ (bookingDetail && bookingDetail.sharingOrder && bookingDetail.sharingOrder.maxParticipants) || 0 }}人
          </text>
          <text class="sharing-price">人均：¥{{ (bookingDetail && bookingDetail.sharingOrder && bookingDetail.sharingOrder.pricePerPerson) || 0 }}</text>
        </view>
        
        <view class="sharing-desc">
          <text>{{ (bookingDetail && bookingDetail.sharingOrder && bookingDetail.sharingOrder.description) || '暂无说明' }}</text>
        </view>
      </view>
    </view>
    
    <!-- 操作按钮 -->
    <view class="action-section">
      <button v-if="bookingDetail && bookingDetail.status === 'PENDING'" class="action-btn cancel-btn" @click="cancelBooking">
        取消预约
      </button>

      <button v-if="bookingDetail && bookingDetail.status === 'PENDING'" class="action-btn pay-btn" @click="payBooking">
        立即支付
      </button>
      

      
      <button v-if="bookingDetail && bookingDetail.status === 'COMPLETED'" class="action-btn review-btn" @click="reviewVenue">
        评价场馆
      </button>
      
      <button class="action-btn rebook-btn" @click="rebookVenue">
        再次预约
      </button>
    </view>
    
    <!-- 取消预约确认弹窗 -->
    <uni-popup ref="cancelPopup" type="center">
      <view class="cancel-modal">
        <view class="modal-header">
          <text class="modal-title">取消预约</text>
        </view>
        
        <view class="modal-content">
          <text class="modal-text">确定要取消这个预约吗？</text>
          <text class="modal-note">取消后可能产生手续费，具体以场馆规定为准</text>
        </view>
        
        <view class="modal-actions">
          <button class="modal-btn cancel-btn" @click="closeCancelModal">暂不取消</button>
          <button class="modal-btn confirm-btn" @click="confirmCancel">确认取消</button>
        </view>
      </view>
    </uni-popup>
    </view>
  </view>
</template>

<script>
import { useBookingStore } from '@/stores/booking.js'
import { formatDate, formatDateTime, formatTime } from '@/utils/helpers.js'
import { clearCache } from '@/utils/request.js'

export default {
  name: 'BookingDetail',
  
  data() {
    return {
      bookingStore: null,
      bookingId: ''
    }
  },

  computed: {
    bookingDetail() {
      return this.bookingStore?.bookingDetailGetter || null
    },

    loading() {
      return this.bookingStore?.isLoading || false
    }
  },

  onLoad(options) {
    // 初始化Pinia store
    this.bookingStore = useBookingStore()

    this.bookingId = options.id
    this.initData()
  },
  
  onPullDownRefresh() {
    this.refreshData()
  },
  
  methods: {
    
    // 初始化数据
    async initData() {
      try {
        // 验证bookingId是否有效
        if (!this.bookingId) {
          throw new Error('订单ID无效，请重新进入页面')
        }

        // 清除可能存在的无效缓存
        clearCache(`/bookings/${this.bookingId}`)

        console.log('调试信息 - bookingStore:', this.bookingStore)
        console.log('调试信息 - getBookingDetail方法:', this.bookingStore?.getBookingDetail)
        console.log('调试信息 - bookingId:', this.bookingId)

        await this.bookingStore.getBookingDetail(this.bookingId)

        // 等待一下确保数据已经更新到store
        await this.$nextTick()

        // 检查数据是否有效
        if (!this.bookingDetail) {
          throw new Error('未能获取到订单数据，请检查网络连接')
        }

        if (!this.bookingDetail.orderNo && !this.bookingDetail.id) {
          throw new Error('订单数据不完整，订单可能不存在或已被删除')
        }

      } catch (error) {
        console.error('初始化数据失败:', error)
        
        uni.showModal({
          title: '加载失败',
          content: error.message || '无法获取订单详情，请检查订单号是否正确',
          showCancel: true,
          cancelText: '返回',
          confirmText: '重试',
          success: (res) => {
            if (res.confirm) {
              // 重试
              this.initData()
            } else {
              // 返回上一页
              uni.navigateBack()
            }
          }
        })
      }
    },
    
    // 刷新数据
    async refreshData() {
      try {
        await this.initData()
        uni.stopPullDownRefresh()
      } catch (error) {
        uni.stopPullDownRefresh()
        console.error('刷新数据失败:', error)
      }
    },
    
    // 取消预约
    cancelBooking() {
      this.$refs.cancelPopup.open()
    },
    
    // 关闭取消弹窗
    closeCancelModal() {
      this.$refs.cancelPopup.close()
    },
    
    // 确认取消
    async confirmCancel() {
      try {
        uni.showLoading({ title: '取消中...' })
        
        await this.bookingStore.cancelBooking(this.bookingId)
        
        uni.hideLoading()
        this.closeCancelModal()
        
        uni.showToast({
          title: '取消成功',
          icon: 'success'
        })
        
        // 刷新数据
        await this.refreshData()
        
      } catch (error) {
        uni.hideLoading()
        console.error('取消预约失败:', error)
        uni.showToast({
          title: error.message || '取消失败',
          icon: 'error'
        })
      }
    },
    

    
    // 评价场馆
    reviewVenue() {
      uni.navigateTo({
        url: `/pages/venue/review?venueId=${this.bookingDetail.venueId}&bookingId=${this.bookingId}`
      })
    },
    
    // 支付订单
    payBooking() {
      if (!this.bookingDetail || !this.bookingDetail.id) {
        uni.showToast({
          title: '订单信息不完整',
          icon: 'none'
        })
        return
      }

      console.log('跳转到支付页面，订单ID:', this.bookingDetail.id)
      uni.navigateTo({
        url: `/pages/payment/index?orderId=${this.bookingDetail.id}&type=booking`
      })
    },

    // 再次预约
    rebookVenue() {
      uni.navigateTo({
        url: `/pages/venue/detail?id=${this.bookingDetail.venueId}`
      })
    },
    
    // 跳转到拼场详情
    navigateToSharingDetail() {
      if (this.bookingDetail.sharingOrder) {
        uni.navigateTo({
          url: `/pages/sharing/detail?id=${this.bookingDetail.sharingOrder.id}`
        })
      }
    },
    
    // 拨打电话
    callVenue() {
      if (this.bookingDetail.venuePhone) {
        uni.makePhoneCall({
          phoneNumber: this.bookingDetail.venuePhone
        })
      } else {
        uni.showToast({
          title: '暂无联系方式',
          icon: 'none'
        })
      }
    },
    
    // 打开地图
    openMap() {
      if (this.bookingDetail.venueLatitude && this.bookingDetail.venueLongitude) {
        uni.openLocation({
          latitude: this.bookingDetail.venueLatitude,
          longitude: this.bookingDetail.venueLongitude,
          name: this.bookingDetail.venueName,
          address: this.bookingDetail.venueLocation
        })
      } else {
        uni.showToast({
          title: '暂无位置信息',
          icon: 'none'
        })
      }
    },
    
    // 格式化日期
    formatDate(date) {
      return formatDate(date, 'YYYY年MM月DD日 dddd')
    },
    
    // 格式化日期时间
    formatDateTime(datetime) {
      return formatDateTime(datetime, 'YYYY-MM-DD HH:mm')
    },
    
    // 格式化创建时间
    formatCreateTime(datetime) {
      return formatTime(datetime, 'YYYY-MM-DD HH:mm')
    },
    
    // 获取状态样式类
    getStatusClass(status) {
      const statusMap = {
        'PENDING': 'status-pending',
        'CONFIRMED': 'status-confirmed',
        'COMPLETED': 'status-completed',
        'CANCELLED': 'status-cancelled'
      }
      return statusMap[status] || 'status-pending'
    },
    
    // 获取状态图标
    getStatusIcon(status) {
      const iconMap = {
        'PENDING': '⏳',
        'CONFIRMED': '✅',
        'COMPLETED': '🎉',
        'CANCELLED': '❌'
      }
      return iconMap[status] || '⏳'
    },
    
    // 获取状态文本
    getStatusText(status) {
      const statusMap = {
        'PENDING': '待确认',
        'CONFIRMED': '已确认',
        'COMPLETED': '已完成',
        'CANCELLED': '已取消'
      }
      return statusMap[status] || '待确认'
    },
    
    // 获取状态描述
    getStatusDesc(status) {
      const descMap = {
        'PENDING': '场馆正在确认您的预约',
        'CONFIRMED': '预约已确认，请按时到场',
        'COMPLETED': '预约已完成，感谢您的使用',
        'CANCELLED': '预约已取消'
      }
      return descMap[status] || ''
    },
    
    // 获取拼场状态文本
    getSharingStatusText(status) {
      const statusMap = {
        'RECRUITING': '招募中',
        'FULL': '已满员',
        'COMPLETED': '已完成',
        'CANCELLED': '已取消'
      }
      return statusMap[status] || '招募中'
    },
    
    // 获取预约类型文本
    getBookingTypeText(bookingType) {
      const typeMap = {
        'EXCLUSIVE': '包场',
        'SHARED': '拼场'
      }
      return typeMap[bookingType] || '--'
    },
    
    // 获取预约类型样式类
    getBookingTypeClass(bookingType) {
      const classMap = {
        'EXCLUSIVE': 'booking-type-exclusive',
        'SHARED': 'booking-type-shared'
      }
      return classMap[bookingType] || ''
    },

    // 检查是否是虚拟订单
    isVirtualOrder() {
      if (!this.bookingDetail) return false
      const bookingId = typeof this.bookingDetail.id === 'string' ? parseInt(this.bookingDetail.id) : this.bookingDetail.id
      return bookingId < 0
    },

    // 获取支付金额（兼容虚拟订单和普通订单）
    getPaymentAmount() {
      if (!this.bookingDetail) return '0.00'

      if (this.isVirtualOrder()) {
        // 虚拟订单使用 paymentAmount
        const amount = this.bookingDetail.paymentAmount || 0
        return amount.toFixed(2)
      } else {
        // 普通订单使用 totalPrice
        const amount = this.bookingDetail.totalPrice || 0
        return amount.toFixed(2)
      }
    },

    // 格式化预约日期（兼容虚拟订单和普通订单）
    formatBookingDate() {
      if (!this.bookingDetail) return '--'

      if (this.isVirtualOrder()) {
        // 虚拟订单从 bookingTime 中提取日期
        const bookingTime = this.bookingDetail.bookingTime
        if (!bookingTime) return '--'

        try {
          let dateTime
          if (typeof bookingTime === 'string') {
            let isoTime = bookingTime
            if (bookingTime.includes(' ') && !bookingTime.includes('T')) {
              isoTime = bookingTime.replace(' ', 'T')
            }
            dateTime = new Date(isoTime)
          } else {
            dateTime = new Date(bookingTime)
          }

          if (isNaN(dateTime.getTime())) {
            console.error('虚拟订单日期格式化错误 - 无效的时间:', bookingTime)
            return '--'
          }

          return dateTime.toLocaleDateString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit'
          }).replace(/\//g, '-')
        } catch (error) {
          console.error('虚拟订单日期格式化错误:', error)
          return '--'
        }
      } else {
        // 普通订单使用 bookingDate 字段
        if (this.bookingDetail.bookingDate) {
          return this.formatDate(this.bookingDetail.bookingDate)
        }
        return '--'
      }
    },

    // 格式化预约时间（兼容虚拟订单和普通订单）
    formatBookingTime() {
      if (!this.bookingDetail) return '--'

      if (this.isVirtualOrder()) {
        // 虚拟订单使用 bookingTime 和 endTime (LocalDateTime格式: "yyyy-MM-dd HH:mm:ss")
        const startTime = this.bookingDetail.bookingTime
        const endTime = this.bookingDetail.endTime

        if (!startTime) return '--'

        try {
          // 处理后端返回的时间格式 "yyyy-MM-dd HH:mm:ss"，转换为iOS兼容格式
          let startDateTime, endDateTime

          if (typeof startTime === 'string') {
            // 确保iOS兼容性：将空格替换为T
            let isoTime = startTime
            if (startTime.includes(' ') && !startTime.includes('T')) {
              isoTime = startTime.replace(' ', 'T')
            }
            startDateTime = new Date(isoTime)
            console.log('预约详情时间转换 - 原始:', startTime, '转换后:', isoTime, '解析结果:', startDateTime)
          } else {
            startDateTime = new Date(startTime)
          }

          if (endTime) {
            if (typeof endTime === 'string') {
              let isoEndTime = endTime
              if (endTime.includes(' ') && !endTime.includes('T')) {
                isoEndTime = endTime.replace(' ', 'T')
              }
              endDateTime = new Date(isoEndTime)
              console.log('预约详情结束时间转换 - 原始:', endTime, '转换后:', isoEndTime, '解析结果:', endDateTime)
            } else {
              endDateTime = new Date(endTime)
            }
          }

          // 检查日期是否有效
          if (isNaN(startDateTime.getTime())) {
            console.error('虚拟订单时间格式化错误 - 无效的开始时间:', startTime)
            return '--'
          }

          // 格式化开始时间 (HH:mm)
          const startTimeStr = startDateTime.toLocaleTimeString('zh-CN', {
            hour: '2-digit',
            minute: '2-digit',
            hour12: false
          })

          // 格式化结束时间 (HH:mm)
          let endTimeStr = ''
          if (endDateTime && !isNaN(endDateTime.getTime())) {
            endTimeStr = endDateTime.toLocaleTimeString('zh-CN', {
              hour: '2-digit',
              minute: '2-digit',
              hour12: false
            })
          }

          return endTimeStr ? `${startTimeStr} - ${endTimeStr}` : startTimeStr
        } catch (error) {
          console.error('虚拟订单时间格式化错误:', error)
          return '--'
        }
      } else {
        // 普通订单使用 startTime 和 endTime
        if (this.bookingDetail.startTime && this.bookingDetail.endTime) {
          return `${this.bookingDetail.startTime} - ${this.bookingDetail.endTime}`
        }
        return '--'
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.container {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 120rpx;
}

// 状态区域
.status-section {
  display: flex;
  align-items: center;
  background-color: #ffffff;
  padding: 40rpx 30rpx;
  margin-bottom: 20rpx;
  
  .status-icon {
    width: 100rpx;
    height: 100rpx;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 48rpx;
    margin-right: 30rpx;
    
    &.status-pending {
      background-color: #fff7e6;
    }
    
    &.status-confirmed {
      background-color: #e6f7ff;
    }
    
    &.status-completed {
      background-color: #f6ffed;
    }
    
    &.status-cancelled {
      background-color: #fff2f0;
    }
  }
  
  .status-info {
    flex: 1;
    
    .status-text {
      display: block;
      font-size: 32rpx;
      font-weight: 600;
      color: #333333;
      margin-bottom: 8rpx;
    }
    
    .status-desc {
      font-size: 24rpx;
      color: #666666;
    }
  }
}

// 信息区域
.info-section,
.contact-section,
.sharing-section {
  background-color: #ffffff;
  margin-bottom: 20rpx;
  
  .section-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #333333;
    padding: 30rpx 30rpx 20rpx;
    border-bottom: 1rpx solid #f0f0f0;
  }
}

// 预约信息
.info-section {
  .info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 24rpx 30rpx;
    border-bottom: 1rpx solid #f8f8f8;
    
    &:last-child {
      border-bottom: none;
    }
    
    .info-label {
      font-size: 28rpx;
      color: #666666;
    }
    
    .info-value {
      font-size: 28rpx;
      color: #333333;
      text-align: right;
      max-width: 60%;
      
      &.price {
        color: #ff6b35;
        font-weight: 600;
      }
      
      &.booking-type {
        padding: 8rpx 16rpx;
        border-radius: 20rpx;
        font-size: 24rpx;
        font-weight: 500;

        &.booking-type-exclusive {
          background-color: #e6f7ff;
          color: #1890ff;
        }

        &.booking-type-shared {
          background-color: #fff7e6;
          color: #fa8c16;
        }
      }
    }

    // 预约类型容器
    .booking-type-container {
      display: flex;
      align-items: center;
      gap: 12rpx;
    }

    // 虚拟订单标识
    .virtual-order-badge {
      font-size: 20rpx;
      padding: 4rpx 12rpx;
      border-radius: 12rpx;
      background-color: #f9f0ff;
      color: #722ed1;
      border: 1rpx solid #722ed1;
    }
  }
}

// 联系信息
.contact-section {
  .contact-item {
    display: flex;
    align-items: center;
    padding: 24rpx 30rpx;
    border-bottom: 1rpx solid #f8f8f8;
    
    &:last-child {
      border-bottom: none;
    }
    
    .contact-icon {
      width: 60rpx;
      height: 60rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 32rpx;
      margin-right: 20rpx;
    }
    
    .contact-info {
      flex: 1;
      
      .contact-label {
        display: block;
        font-size: 28rpx;
        color: #333333;
        margin-bottom: 4rpx;
      }
      
      .contact-value {
        font-size: 24rpx;
        color: #666666;
      }
    }
    
    .contact-arrow {
      font-size: 24rpx;
      color: #cccccc;
    }
  }
}

// 拼场信息
.sharing-section {
  .sharing-card {
    padding: 30rpx;
    
    .sharing-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16rpx;
      
      .sharing-team {
        font-size: 28rpx;
        font-weight: 600;
        color: #333333;
      }
      
      .sharing-status {
        font-size: 22rpx;
        padding: 6rpx 16rpx;
        background-color: #e6f7ff;
        color: #1890ff;
        border-radius: 16rpx;
      }
    }
    
    .sharing-info {
      display: flex;
      justify-content: space-between;
      margin-bottom: 16rpx;
      
      .sharing-participants,
      .sharing-price {
        font-size: 24rpx;
        color: #666666;
      }
      
      .sharing-price {
        color: #ff6b35;
        font-weight: 600;
      }
    }
    
    .sharing-desc {
      font-size: 24rpx;
      color: #999999;
      line-height: 1.4;
    }
  }
}

// 操作按钮
.actions-section {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  background-color: #ffffff;
  padding: 20rpx 30rpx;
  border-top: 1rpx solid #f0f0f0;
  z-index: 100;
  
  .action-btn {
    flex: 1;
    height: 80rpx;
    border-radius: 8rpx;
    font-size: 28rpx;
    margin-right: 20rpx;
    border: 1rpx solid;
    
    &:last-child {
      margin-right: 0;
    }
    
    &.cancel-btn {
      background-color: transparent;
      color: #ff4d4f;
      border-color: #ff4d4f;
    }
    
    &.share-btn {
      background-color: #ff6b35;
      color: #ffffff;
      border-color: #ff6b35;
    }
    
    &.review-btn {
      background-color: transparent;
      color: #1890ff;
      border-color: #1890ff;
    }
    
    &.rebook-btn {
      background-color: #ff6b35;
      color: #ffffff;
      border-color: #ff6b35;
    }
  }
}

// 取消弹窗
.cancel-modal {
  width: 600rpx;
  background-color: #ffffff;
  border-radius: 16rpx;
  overflow: hidden;
  
  .modal-header {
    padding: 30rpx;
    text-align: center;
    border-bottom: 1rpx solid #f0f0f0;
    
    .modal-title {
      font-size: 32rpx;
      font-weight: 600;
      color: #333333;
    }
  }
  
  .modal-content {
    padding: 40rpx 30rpx;
    text-align: center;
    
    .modal-text {
      display: block;
      font-size: 28rpx;
      color: #333333;
      margin-bottom: 16rpx;
    }
    
    .modal-note {
      font-size: 24rpx;
      color: #999999;
    }
  }
  
  .modal-actions {
    display: flex;
    border-top: 1rpx solid #f0f0f0;
    
    .modal-btn {
      flex: 1;
      height: 100rpx;
      border: none;
      font-size: 28rpx;
      
      &.cancel-btn {
        background-color: #f5f5f5;
        color: #666666;
      }
      
      &.confirm-btn {
        background-color: #ff6b35;
        color: #ffffff;
      }
    }
  }
}

// 加载状态
.loading-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400rpx;
  
  .loading-text {
    font-size: 28rpx;
    color: #999999;
  }
}

// 空状态
.empty-state {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 400rpx;
  
  .empty-text {
    font-size: 28rpx;
    color: #999999;
    margin-bottom: 30rpx;
  }
  
  .retry-btn {
    padding: 16rpx 32rpx;
    background-color: #007aff;
    color: #ffffff;
    border: none;
    border-radius: 8rpx;
    font-size: 26rpx;
  }
}
</style>