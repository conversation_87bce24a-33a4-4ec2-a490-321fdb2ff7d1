<template>
  <view class="container">
    <scroll-view scroll-y class="main-scroll-view">      
      <view class="content-wrapper">
    <!-- 场馆信息 -->
    <view class="venue-summary" v-if="venue">
        <image :src="venue.image || 'https://via.placeholder.com/400x200?text=场馆图片'" class="venue-image" mode="aspectFill" />
        <view class="venue-info">
          <text class="venue-name">{{ venue.name }}</text>
          <text class="venue-location">{{ venue.location }}</text>
          <text class="venue-price">¥{{ venue.price }}/小时</text>
        </view>
      </view>
      
      <!-- 预约信息表单 -->
      <view class="booking-form">
        <!-- 预约类型 -->
        <!-- 预约类型显示 -->
        <view class="form-section">
          <text class="section-title">预约类型</text>
          <view class="booking-type-display">
            <text class="booking-type-text">{{ bookingForm.bookingType === 'EXCLUSIVE' ? '独享预约' : '拼场预约' }}</text>
          </view>
        </view>
          
          <!-- 拼场说明 -->
          <view class="sharing-notice" v-if="(venue && venue.supportSharing) && bookingForm.bookingType === 'SHARED'">
            <view class="notice-header">
              <view class="notice-icon">🏆</view>
              <text class="notice-title">拼场预约说明</text>
            </view>
            <view class="notice-content">
              <view class="notice-item">
                <view class="item-icon">✨</view>
                <text class="item-text">本平台专为球队提供拼场服务</text>
              </view>
              <view class="notice-item">
                <view class="item-icon">⚡</view>
                <text class="item-text">只需一个球队申请即可成功拼场</text>
              </view>
              <view class="notice-item">
                <view class="item-icon">📝</view>
                <text class="item-text">请在队伍名称中注明球队信息</text>
              </view>
              <view class="notice-item">
                <view class="item-icon">📞</view>
                <text class="item-text">联系方式用于其他球队联系您</text>
              </view>
            </view>
          </view>
        </view>
        
        <!-- 时间选择 -->
        <view class="form-section">
          <text class="section-title">预约时间</text>
          <view class="time-info">
            <text class="time-text">{{ formatDateTime(selectedDate, selectedSlot) }}</text>
          </view>
          

        </view>
        
        <!-- 拼场信息 (仅拼场时显示) -->
        <view class="form-section" v-if="bookingForm.bookingType === 'SHARED'">
          <text class="section-title">拼场信息</text>
          
          <view class="form-item">
            <text class="item-label">球队名称 <text class="required">*</text></text>
            <input 
              v-model="bookingForm.teamName" 
              placeholder="请输入球队名称（如：XX篮球队）"
              class="form-input"
              maxlength="20"
            />
          </view>
          
          <view class="form-item">
            <text class="item-label">联系方式 <text class="required">*</text></text>
            <input 
              v-model="bookingForm.contactInfo" 
              placeholder="请输入联系方式（供其他球队联系）"
              class="form-input"
              maxlength="20"
            />
          </view>
          

          
          <!-- 拼场说明 -->
          <view class="sharing-notice">
            <view class="notice-header">
              <view class="notice-icon">ℹ️</view>
              <text class="notice-title">拼场说明</text>
            </view>
            <view class="notice-content">
              <view class="notice-item">
                <view class="item-icon">•</view>
                <text class="item-text">拼场预约需要等待其他用户加入</text>
              </view>
              <view class="notice-item">
                <view class="item-icon">•</view>
                <text class="item-text">如果预约时间前2小时内无人加入，系统将自动退款</text>
              </view>
              <view class="notice-item">
                <view class="item-icon">•</view>
                <text class="item-text">请确保联系方式准确，便于其他用户联系</text>
              </view>
            </view>
          </view>
        </view>
        
        <!-- 预约描述 -->
        <view class="form-section">
          <text class="section-title">{{ bookingForm.bookingType === 'SHARED' ? '拼场说明' : '备注信息' }}</text>
          <textarea
            v-model="bookingForm.description"
            :placeholder="bookingForm.bookingType === 'SHARED' ? '球队实力中等，出汗局' : '请输入备注信息（可选）'"
            class="form-textarea"
            maxlength="200"
          ></textarea>
        </view>
      </view>
      
      <!-- 费用明细 -->
      <view class="cost-summary">
        <text class="summary-title">费用明细</text>
        
        <!-- 多个时间段的费用明细 -->
        <template v-if="selectedSlots && selectedSlots.length > 0">
          <view class="cost-item" v-for="(slot, index) in selectedSlots" :key="index">
            <text>{{ slot.startTime }}-{{ slot.endTime }}</text>
            <text>¥{{ getSlotPrice(slot) }}</text>
          </view>
        </template>
        
        <!-- 单个时间段的费用明细（兼容） -->
        <template v-else-if="selectedSlot">
          <view class="cost-item">
            <text>{{ selectedSlot.startTime }}-{{ selectedSlot.endTime }}</text>
            <text>¥{{ getSlotPrice(selectedSlot) }}</text>
          </view>
        </template>
        
        <!-- 默认场地费用 -->
        <template v-else>
          <view class="cost-item">
            <text>场地费用</text>
            <text>¥{{ venue?.price || 0 }}</text>
          </view>
        </template>
        
        <!-- 显示总价和拼场优惠信息 -->
        <template v-if="bookingForm.bookingType === 'SHARED'">
          <view class="cost-item" style="color: #ff6b00; background-color: #fff8f0; padding: 10rpx; border-radius: 8rpx; margin-top: 10rpx;">
            <text>拼场优惠</text>
            <text>¥{{ (totalCost / 2).toFixed(2) }} (5折)</text>
          </view>
          <view class="cost-total" style="border-top: 1px dashed #eee; padding-top: 20rpx; margin-top: 10rpx;">
            <text>总计（原价）</text>
            <text>¥{{ totalCost }}</text>
          </view>
          <view class="cost-total" style="margin-top: 5rpx;">
            <text>实付金额</text>
            <text class="total-amount" style="color: #ff6b00; font-size: 36rpx;">¥{{ (totalCost / 2).toFixed(2) }}</text>
          </view>
          <view class="info-tip" style="font-size: 24rpx; color: #999; margin-top: 10rpx; text-align: right;">
            <text>拼场订单，费用由两队均摊，每队支付总费用的50%</text>
          </view>
        </template>
        <template v-else>
          <view class="cost-total">
            <text>总计</text>
            <text class="total-amount">¥{{ totalCost }}</text>
          </view>
        </template>
      </view>
    </scroll-view>

  <!-- 底部操作 -->
  <view class="bottom-actions">
      <view class="bottom-cost">
        <text class="cost-label">{{ bookingForm.bookingType === 'SHARED' ? '实付金额：' : '总费用：' }}</text>
        <text class="cost-value" :class="{'shared-price': bookingForm.bookingType === 'SHARED'}">
          ¥{{ bookingForm.bookingType === 'SHARED' ? (totalCost / 2).toFixed(2) : totalCost.toFixed(2) }}
        </text>
      </view>
      <view class="action-buttons">
        <button class="cancel-btn" @click="goBack">取消</button>
        <button class="confirm-btn" :disabled="!canConfirm" @click="confirmBooking">确认预约</button>
      </view>
    </view>
  </view>
  </template>

<script>
import { useVenueStore } from '@/stores/venue.js'
import { useBookingStore } from '@/stores/booking.js'
import { useUserStore } from '@/stores/user.js'
import { forceRefreshTimeSlots, debugTimeSlotRefresh } from '@/utils/payment-debug.js'
import { validateCompleteBookingFlow, validateBookingData } from '@/utils/booking-price-validator.js'
import { startRealTimeDebugging, validatePriceTransmission, quickDiagnosis } from '@/utils/real-time-debugger.js'

export default {
  name: 'BookingCreate',
  
  data() {
    return {
      venueStore: null,
      bookingStore: null,
      userStore: null,
      venueId: null,
      selectedDate: '',
      selectedSlot: null,
      selectedSlots: [], // 存储多个选中的时间段


      bookingForm: {
        bookingType: 'EXCLUSIVE',
        teamName: '',
        contactInfo: '',
        description: ''
      },


    }
  },
  
  computed: {
    timeSlots() {
      return this.venueStore?.timeSlots || []
    },

    userInfo() {
      return this.userStore?.getUserInfo || {}
    },

    // 获取场馆信息，确保有默认值
    venue() {
      const venueData = this.venueStore?.venueDetailGetter || this.venueStore?.venueDetail
      console.log('venue计算属性 - 原始数据:', venueData)
      console.log('venue计算属性 - venueStore:', this.venueStore)

      if (!venueData) {
        console.log('venue计算属性 - 无数据')
        return null
      }

      const result = {
        ...venueData,
        supportSharing: venueData.supportSharing !== undefined ? venueData.supportSharing : true,
        price: venueData.price || 0
      }

      console.log('venue计算属性 - 处理后数据:', result)
      console.log('venue计算属性 - 价格:', result.price)
      return result
    },
    
    totalCost() {
      // 如果有多个时间段，计算所有时间段的总价格
      if (this.selectedSlots && this.selectedSlots.length > 0) {
        const total = this.selectedSlots.reduce((sum, slot) => {
          let slotPrice = 0
          
          // 优先使用时间段的价格
          if (slot.price) {
            slotPrice = parseFloat(slot.price)
          } else if (slot.pricePerHour) {
            slotPrice = parseFloat(slot.pricePerHour)
          } else {
            // 使用场馆价格
            const venuePrice = this.venue?.price || 0
            slotPrice = parseFloat(venuePrice) || 0
          }
          
          console.log('时间段价格:', slot, slotPrice)
          return sum + slotPrice
        }, 0)
        
        console.log('totalCost - 多时间段总价格:', total)
        return total
      }
      
      // 兼容单个时间段的情况
      if (this.selectedSlot && this.selectedSlot.price) {
        console.log('totalCost - 使用时间段价格:', this.selectedSlot.price)
        return parseFloat(this.selectedSlot.price)
      }
      
      // 如果时间段有每小时价格信息
      if (this.selectedSlot && this.selectedSlot.pricePerHour) {
        console.log('totalCost - 使用时间段每小时价格:', this.selectedSlot.pricePerHour)
        return parseFloat(this.selectedSlot.pricePerHour)
      }
      
      const venuePrice = this.venue?.pricePerHour || this.venue?.price || 0
      console.log('totalCost - 使用场馆价格:', venuePrice)
      return parseFloat(venuePrice) || 0
    },
    

    
    canConfirm() {
      const hasDate = !!this.selectedDate
      const hasSlot = !!(this.selectedSlots?.length > 0 || this.selectedSlot)
      const hasVenue = !!this.venue?.id
      const hasPrice = !!(this.venue?.price)
      
      const baseValid = hasDate && hasSlot && hasVenue && hasPrice
      
      // 添加详细调试信息
      console.log('canConfirm 详细检查:', {
        selectedDate: this.selectedDate,
        hasDate,
        selectedSlot: this.selectedSlot,
        hasSlot,
        venue: this.venue,
        hasVenue,
        hasPrice,
        venuePrice: this.venue?.price,
        venuePricePerHour: this.venue?.price,
        bookingType: this.bookingForm.bookingType,
        teamName: this.bookingForm.teamName,
        contactInfo: this.bookingForm.contactInfo,
        baseValid
      })
      
      if (this.bookingForm.bookingType === 'SHARED') {
        const hasTeamName = !!(this.bookingForm.teamName && this.bookingForm.teamName.trim())
        const hasContactInfo = !!(this.bookingForm.contactInfo && this.bookingForm.contactInfo.trim())
        const result = baseValid && hasTeamName && hasContactInfo
        
        console.log('拼场模式额外检查:', {
          hasTeamName,
          hasContactInfo,
          finalResult: result
        })
        return result
      }
      
      console.log('独占模式 canConfirm 结果:', baseValid)
      return baseValid
    }
  },
  
  onLoad(options) {
    // 初始化Pinia stores
    this.venueStore = useVenueStore()
    this.bookingStore = useBookingStore()
    this.userStore = useUserStore()

    console.log('页面加载参数:', options)
    this.venueId = options.venueId
    this.selectedDate = options.date
    
    // 确保venueId有值
    if (!this.venueId) {
      console.error('警告: venueId为空!')
      uni.showToast({
        title: '场馆ID缺失，请返回重试',
        icon: 'none'
      })
      setTimeout(() => {
        uni.navigateBack()
      }, 2000)
      return
    }
    
    // 从路由参数获取预约类型
    if (options.bookingType) {
      this.bookingForm.bookingType = options.bookingType
    }
    
    if (options.selectedSlots) {
      // 从场馆详情页传来的多个时间段数据
      try {
        this.selectedSlots = JSON.parse(decodeURIComponent(options.selectedSlots))
        this.selectedSlot = this.selectedSlots[0] // 保持兼容性，设置第一个时间段
        console.log('接收到的时间段数据:', this.selectedSlots)
      } catch (error) {
        console.error('解析时间段数据失败:', error)
      }
      this.loadVenueDetail()
    } else if (options.slotId) {
      // 兼容旧的单个时间段ID方式
      this.loadVenueAndSlot(options.slotId)
    } else {
      this.loadVenueDetail()
    }
  },
  
  methods: {
    
    // 加载场馆详情
    async loadVenueDetail() {
      try {
        console.log('开始加载场馆详情，venueId:', this.venueId)
        await this.venueStore.getVenueDetail(this.venueId)
        console.log('场馆详情加载完成，数据:', this.venueStore.venueDetailGetter)

        // 如果有日期，加载时间段
        if (this.selectedDate) {
          await this.loadTimeSlots()
        }
      } catch (error) {
        console.error('加载场馆详情失败:', error)
        
        // 如果后端不可用，设置模拟数据用于测试
        console.log('设置模拟场馆数据用于测试')
        this.venueStore.setVenueDetail({
          id: this.venueId || 1,
          name: '测试体育馆',
          price: 120,
          supportSharing: true,
          location: '测试地址',
          openingHours: '08:00 - 22:00'
        })

        // 设置模拟时间段数据
        if (this.selectedDate) {
          this.venueStore.setTimeSlots([
            {
              id: 1,
              startTime: '09:00',
              endTime: '10:00',
              status: 'AVAILABLE',
              price: 120
            },
            {
              id: 2,
              startTime: '10:00',
              endTime: '11:00',
              status: 'AVAILABLE',
              price: 120
            }
          ])
        }
        
        uni.showToast({
          title: '加载失败',
          icon: 'error'
        })
      }
    },
    
    // 加载场馆和指定时间段
    async loadVenueAndSlot(slotId) {
      try {
        console.log('loadVenueAndSlot 开始，slotId:', slotId)
        await this.venueStore.getVenueDetail(this.venueId)
        await this.loadTimeSlots()
        
        console.log('可用时间段:', this.timeSlots)
        
        // 查找并选择指定的时间段
        let slot = this.timeSlots.find(s => s.id == slotId)
        
        // 如果通过ID没找到，尝试通过时间段字符串查找（格式：'09:00-10:00'）
        if (!slot && slotId.includes('-')) {
          const [startTime, endTime] = slotId.split('-')
          slot = this.timeSlots.find(s => s.startTime === startTime && s.endTime === endTime)
        }
        
        console.log('找到的时间段:', slot)
        
        if (slot) {
          this.selectedSlot = slot
          console.log('已设置 selectedSlot:', this.selectedSlot)
        } else {
          console.warn('未找到指定的时间段:', slotId)
        }
      } catch (error) {
        console.error('加载失败:', error)
        
        // 如果后端不可用，设置模拟数据用于测试
        console.log('设置模拟数据用于测试')
        this.venueStore.setVenueDetail({
          id: this.venueId || 1,
          name: '测试体育馆',
          price: 120,
          supportSharing: true,
          location: '测试地址',
          openingHours: '08:00 - 22:00'
        })
        
        // 设置模拟时间段数据
        const mockSlots = [
          {
            id: 1,
            startTime: '09:00',
            endTime: '10:00',
            status: 'AVAILABLE',
            price: 120
          },
          {
            id: 2,
            startTime: '10:00',
            endTime: '11:00',
            status: 'AVAILABLE',
            price: 120
          },
          {
            id: 3,
            startTime: '14:00',
            endTime: '15:00',
            status: 'AVAILABLE',
            price: 120
          }
        ]
        
        this.venueStore.setTimeSlots(mockSlots)
        
        // 如果有指定的slotId，尝试选择对应的时间段
        if (slotId) {
          let slot = mockSlots.find(s => s.id == slotId)
          
          // 如果通过ID没找到，尝试通过时间段字符串查找
          if (!slot && slotId.includes('-')) {
            const [startTime, endTime] = slotId.split('-')
            slot = mockSlots.find(s => s.startTime === startTime && s.endTime === endTime)
          }
          
          if (slot) {
            this.selectedSlot = slot
            console.log('已设置模拟 selectedSlot:', this.selectedSlot)
          }
        }
        
        uni.showToast({
          title: '使用模拟数据',
          icon: 'none'
        })
      }
    },
    
    // 加载时间段
    async loadTimeSlots() {
      if (!this.selectedDate) return

      try {
        console.log('loadTimeSlots 调用参数:', { venueId: this.venueId, date: this.selectedDate })
        await this.venueStore.getTimeSlots(this.venueId, this.selectedDate)
      } catch (error) {
        console.error('加载时间段失败:', error)
      }
    },
    

    

    

    

    
    // 格式化日期时间
    formatDateTime(date, slot) {
      console.log('formatDateTime 调用:', { date, slot, selectedSlots: this.selectedSlots })
      
      if (!date) {
        console.log('formatDateTime 返回默认值: 请选择时间')
        return '请选择时间'
      }
      
      try {
        const dateObj = new Date(date)
        const year = dateObj.getFullYear()
        const month = dateObj.getMonth() + 1
        const day = dateObj.getDate()
        const weekDay = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'][dateObj.getDay()]
        
        const dateStr = `${year}年${month}月${day}日 ${weekDay}`
        
        // 如果有多个时间段，显示所有时间段
        if (this.selectedSlots && this.selectedSlots.length > 0) {
          const timeSlots = this.selectedSlots.map(slot => {
            let startTime = slot.startTime
            let endTime = slot.endTime
            
            // 如果时间包含秒，去掉秒部分
            if (startTime && startTime.length > 5) {
              startTime = startTime.substring(0, 5)
            }
            if (endTime && endTime.length > 5) {
              endTime = endTime.substring(0, 5)
            }
            
            return `${startTime}-${endTime}`
          })
          
          // 计算总时长
          const totalDuration = this.selectedSlots.reduce((total, slot) => {
            return total + this.calculateDuration(slot.startTime, slot.endTime)
          }, 0)
          
          // 格式化总时长显示
          const durationText = totalDuration % 1 === 0 ? totalDuration : totalDuration.toFixed(1)
          const result = `${dateStr} ${timeSlots.join('、')} (共${durationText}小时)`
          console.log('formatDateTime 多时间段结果:', result)
          return result
        }
        
        // 兼容单个时间段的情况
        if (!slot) {
          console.log('formatDateTime 返回默认值: 请选择时间')
          return '请选择时间'
        }
        
        // 处理时间格式，确保显示正确
        let startTime = slot.startTime
        let endTime = slot.endTime
        
        // 如果时间包含秒，去掉秒部分
        if (startTime && startTime.length > 5) {
          startTime = startTime.substring(0, 5)
        }
        if (endTime && endTime.length > 5) {
          endTime = endTime.substring(0, 5)
        }
        
        // 计算时长
        const duration = this.calculateDuration(startTime, endTime)
        
        // 格式化时长显示
        const durationText = duration % 1 === 0 ? duration : duration.toFixed(1)
        const timeStr = `${startTime}-${endTime}`
        const result = `${dateStr} ${timeStr} (${durationText}小时)`
        
        console.log('formatDateTime 单时间段结果:', result)
        return result
      } catch (error) {
        console.error('formatDateTime 错误:', error)
        return '时间格式错误'
      }
    },
    
    // 计算时长
    calculateDuration(startTime, endTime) {
      try {
        const [startHour, startMinute] = startTime.split(':').map(Number)
        const [endHour, endMinute] = endTime.split(':').map(Number)
        
        const startMinutes = startHour * 60 + startMinute
        const endMinutes = endHour * 60 + endMinute
        
        const durationMinutes = endMinutes - startMinutes
        const hours = durationMinutes / 60
        
        // 返回精确的小时数（保留一位小数）
        return Math.round(hours * 10) / 10
      } catch (error) {
        console.error('计算时长错误:', error)
        return 1
      }
    },
    
    // 获取单个时间段的价格
    getSlotPrice(slot) {
      console.log('🔍 getSlotPrice 调用，slot:', slot)
      console.log('🔍 slot.price:', slot.price)
      console.log('🔍 slot.pricePerHour:', slot.pricePerHour)
      console.log('🔍 venue.price:', this.venue?.price)

      let price = 0

      // 方法1: 使用时间段的价格
      if (slot.price && slot.price > 0) {
        price = parseFloat(slot.price)
        console.log('✅ 使用slot.price:', price)
        return price
      }

      // 方法2: 使用时间段的每小时价格
      if (slot.pricePerHour && slot.pricePerHour > 0) {
        price = parseFloat(slot.pricePerHour)
        console.log('✅ 使用slot.pricePerHour:', price)
        return price
      }

      // 方法3: 使用场馆价格（每小时）
      const venuePrice = this.venue?.price || 0
      if (venuePrice > 0) {
        // 场馆价格是每小时价格，时间段是30分钟，所以除以2
        price = parseFloat(venuePrice) / 2
        console.log('✅ 使用venue.price/2 (半小时):', price)
        return price
      }

      // 方法4: 使用默认价格
      price = 60 // 默认每个时间段60元（30分钟）
      console.log('⚠️ 使用默认价格:', price)
      return price
    },
    
    // 获取时间段状态文本
    getSlotStatusText(status) {
      const statusMap = {
        'AVAILABLE': '可预约',
        'RESERVED': '已预约',
        'OCCUPIED': '已占用',
        'MAINTENANCE': '维护中'
      }
      return statusMap[status] || status
    },
    
    // 确认预约
    async confirmBooking() {
      console.log('确认预约开始')
      console.log('canConfirm:', this.canConfirm)
      console.log('selectedSlots:', this.selectedSlots)
      console.log('selectedSlot:', this.selectedSlot)
      console.log('bookingType:', this.bookingType)

      if (!this.canConfirm) {
        console.log('无法确认预约，canConfirm为false')
        return
      }

      // 验证表单
      if (!this.validateForm()) {
        console.log('表单验证失败')
        return
      }

      try {
        uni.showLoading({ title: '创建中...' })
        
        let result

        console.log('预约创建 - 检查时间段选择:')
        console.log('- selectedSlots:', this.selectedSlots)
        console.log('- selectedSlots.length:', this.selectedSlots?.length)
        console.log('- selectedSlot:', this.selectedSlot)
        console.log('- 条件判断结果:', this.selectedSlots && this.selectedSlots.length > 0)

        // 处理多个时间段的情况 - 只创建一个订单
        if (this.selectedSlots && this.selectedSlots.length > 0) {
          // 获取第一个时间段作为主要时间段
          const firstSlot = this.selectedSlots[0];

          console.log('多时间段预约 - bookingType:', this.bookingForm.bookingType)
          console.log('多时间段预约 - 是否为拼场:', this.bookingForm.bookingType === 'SHARED')

          if (this.bookingForm.bookingType === 'SHARED') {
            // 拼场预约 - 只使用第一个时间段创建一个订单
            // 🔧 修复：添加价格字段到拼场预约数据中
            const calculatedPrice = this.selectedSlots.reduce((total, slot) => {
              const slotPrice = this.getSlotPrice(slot)
              console.log(`💰 拼场时间段 ${slot.startTime}-${slot.endTime} 价格:`, slotPrice)
              return total + slotPrice
            }, 0)

            console.log('💰 拼场预约计算的总价格:', calculatedPrice)

            const sharedBookingData = {
              venueId: parseInt(this.venueId),
              date: this.selectedDate,
              startTime: firstSlot.startTime,
              teamName: this.bookingForm.teamName || '',
              contactInfo: this.bookingForm.contactInfo || '',
              maxParticipants: 2, // 表示需要两个球队才能成功拼场
              description: this.bookingForm.description || '',
              slotIds: this.selectedSlots.map(slot => slot.id), // 传递所有选中的时间段ID
              price: calculatedPrice // 🔧 修复：添加价格字段
            }
            console.log('发送拼场预约数据:', sharedBookingData)
            console.log('firstSlot.startTime 类型和值:', typeof firstSlot.startTime, firstSlot.startTime)
            console.log('完整的 firstSlot 对象:', firstSlot)
            result = await this.bookingStore.createSharedBooking(sharedBookingData)
          } else {
            // 独享预约 - 只创建一个订单，包含所有选中的时间段
            console.log('进入独享预约分支')
            console.log('selectedSlots数量:', this.selectedSlots.length)
            console.log('firstSlot:', firstSlot)

            // 计算总价格
            const calculatedPrice = this.selectedSlots.reduce((total, slot) => {
              const slotPrice = this.getSlotPrice(slot)
              console.log(`💰 时间段 ${slot.startTime}-${slot.endTime} 价格:`, slotPrice)
              return total + slotPrice
            }, 0)

            console.log('💰 计算的总价格:', calculatedPrice)

            // 确定最终价格
            let finalPrice = calculatedPrice

            // 验证价格有效性
            if (calculatedPrice <= 0) {
              console.error('❌ 计算的价格为0或负数，使用备用价格计算')

              // 备用价格计算：根据时间段数量和默认价格
              const slotCount = this.selectedSlots.length
              const defaultPricePerSlot = 60 // 每个时间段60元
              finalPrice = slotCount * defaultPricePerSlot

              console.log(`💰 备用价格计算: ${slotCount}个时间段 × ${defaultPricePerSlot}元 = ${finalPrice}元`)
            }

            const bookingData = {
              venueId: this.venueId,
              date: this.selectedDate,
              startTime: firstSlot.startTime,
              endTime: this.selectedSlots[this.selectedSlots.length - 1].endTime,
              slotIds: this.selectedSlots.map(slot => slot.id),
              bookingType: this.bookingForm.bookingType,
              description: this.bookingForm.description,
              price: finalPrice
            }

            console.log('📤 发送给后端的预约数据:', bookingData)
            console.log('💰 最终价格:', finalPrice)

            // 🔍 实时价格传递验证
            const priceValidation = validatePriceTransmission(bookingData)
            if (!priceValidation.isValid) {
              console.error('❌ 实时价格验证失败:', priceValidation.issues)
            }

            // 验证预约数据
            const validation = validateBookingData(bookingData)
            if (!validation.valid) {
              console.error('❌ 预约数据验证失败:', validation.errors)
              throw new Error(`数据验证失败: ${validation.errors.join(', ')}`)
            }

            if (validation.warnings.length > 0) {
              console.warn('⚠️ 预约数据警告:', validation.warnings)
            }

            // 🔍 启动实时调试监控
            const debugSession = startRealTimeDebugging(this.venueStore)

            try {
              result = await this.bookingStore.createBooking(bookingData)

              // 🔍 快速诊断创建结果
              const diagnosis = await quickDiagnosis(bookingData, this.venueStore)
              console.log('📊 创建后快速诊断:', diagnosis)

            } finally {
              // 停止调试监控
              debugSession.stop()
            }
          }
          
          console.log('预约创建结果:', result)
        } else {
          // 单个时间段预约
          const bookingData = {
            venueId: this.venueId,
            date: this.selectedDate,
            startTime: this.selectedSlot.startTime,
            endTime: this.selectedSlot.endTime,
            slotId: this.selectedSlot.id,
            bookingType: this.bookingForm.bookingType,
            description: this.bookingForm.description,
            price: this.getSlotPrice(this.selectedSlot)
          }
          
          try {
             // 根据预约类型选择合适的API
             if (this.bookingForm.bookingType === 'SHARED') {
               // 拼场接口使用不同的数据格式
               // 🔧 修复：为单时间段拼场预约也添加价格字段
               const slotPrice = this.getSlotPrice(this.selectedSlot)
               console.log('💰 单时间段拼场预约价格:', slotPrice)

               const sharedBookingData = {
                 venueId: parseInt(this.venueId),
                 date: this.selectedDate,
                 startTime: this.selectedSlot.startTime,
                 teamName: this.bookingForm.teamName || '',
                 contactInfo: this.bookingForm.contactInfo || '',
                 maxParticipants: 2, // 修改为2，表示需要两个球队才能成功拼场
                 description: this.bookingForm.description || '',
                 price: slotPrice // 🔧 修复：添加价格字段
               }
               console.log('发送单时间段拼场预约数据:', sharedBookingData)
               console.log('selectedSlot.startTime 类型和值:', typeof this.selectedSlot.startTime, this.selectedSlot.startTime)
               console.log('完整的 selectedSlot 对象:', this.selectedSlot)
               result = await this.bookingStore.createSharedBooking(sharedBookingData)
               console.log('单时间段拼场预约创建结果:', result)
             } else {
               result = await this.bookingStore.createBooking(bookingData)
               console.log('单时间段预约创建结果:', result)
             }
           } catch (error) {
              console.error('预约创建失败:', error)
              // 不再使用模拟响应，而是抛出错误以便上层捕获
              throw error
            }
          }
        
        // 重新获取时间段数据以刷新状态
        try {
          console.log('🔄 开始刷新时间段状态')

          // 使用VenueStore的强力刷新方法
          const refreshResult = await this.venueStore.forceRefreshTimeSlots(this.venueId, this.selectedDate)
          console.log('🚀 VenueStore强力刷新结果:', refreshResult)

          if (refreshResult.success) {
            console.log('✅ 时间段刷新成功')

            // 清除选中状态
            this.selectedSlots = []
            this.selectedSlot = null
            console.log('🧹 已清除选中状态')

            // 通知页面更新
            this.$forceUpdate()
            console.log('🔄 强制更新页面')

            // 额外等待一下确保UI更新
            await new Promise(resolve => setTimeout(resolve, 200))
          } else {
            console.error('❌ VenueStore强力刷新失败:', refreshResult.error)

            // 如果VenueStore刷新失败，使用调试工具作为备用
            console.log('🔄 使用调试工具作为备用刷新方案')
            const debugRefreshResult = await forceRefreshTimeSlots(this.venueId, this.selectedDate, this.venueStore)
            console.log('🚀 调试工具刷新结果:', debugRefreshResult)

            if (debugRefreshResult.success) {
              console.log('✅ 备用刷新成功')
            } else {
              console.error('❌ 所有刷新方案都失败了')
            }

            // 清除选中状态
            this.selectedSlots = []
            this.selectedSlot = null
            this.$forceUpdate()
          }
        } catch (error) {
          console.error('❌ 刷新时间段状态失败:', error)
        }
        
        uni.hideLoading()
        
        uni.showToast({
          title: '预约成功',
          icon: 'success'
        })

        console.log('🎉 预约创建成功！准备跳转到支付页面')

        // 获取订单ID，支持多种返回格式
        console.log('📋 预约创建结果详情:', result)
        console.log('📋 result.id:', result.id)
        console.log('📋 result.orderId:', result.orderId)
        console.log('📋 result.data:', result.data)
        console.log('📋 result.data?.id:', result.data?.id)

        // 更全面的订单ID提取逻辑
        let orderId = null

        // 尝试多种可能的订单ID字段
        if (result.id) {
          orderId = result.id
        } else if (result.orderId) {
          orderId = result.orderId
        } else if (result.data && result.data.id) {
          orderId = result.data.id
        } else if (result.data && result.data.orderId) {
          orderId = result.data.orderId
        } else if (typeof result === 'number') {
          orderId = result
        }

        console.log('🆔 提取的订单ID:', orderId)
        console.log('🆔 订单ID类型:', typeof orderId)

        // 验证订单ID有效性
        if (orderId && (typeof orderId === 'number' || typeof orderId === 'string')) {
          console.log('✅ 订单ID有效，准备跳转到支付页面')

          // 立即跳转，不延迟
          uni.redirectTo({
            url: `/pages/payment/index?orderId=${orderId}&type=booking&from=create`,
            success: () => {
              console.log('✅ 成功跳转到支付页面')
            },
            fail: (error) => {
              console.error('❌ 跳转支付页面失败:', error)
              // 如果跳转失败，尝试导航到支付页面
              uni.navigateTo({
                url: `/pages/payment/index?orderId=${orderId}&type=booking&from=create`
              })
            }
          })
        } else {
          console.error('❌ 无法获取有效的订单ID，跳转到预约列表')
          console.error('❌ 原始结果:', result)

          uni.showModal({
            title: '提示',
            content: '预约创建成功，但无法获取订单信息。请到"我的预约"中查看。',
            success: () => {
              uni.redirectTo({
                url: '/pages/booking/list'
              })
            }
          })
        }
        
      } catch (error) {
        uni.hideLoading()
        console.error('创建预约失败:', error)
        uni.showToast({
          title: error.message || '创建失败',
          icon: 'error'
        })
      }
    },
    
    // 验证表单
    validateForm() {
      
      if (this.bookingForm.bookingType === 'SHARED') {
        if (!this.bookingForm.teamName.trim()) {
          uni.showToast({
            title: '请输入队伍名称',
            icon: 'error'
          })
          return false
        }
        
        if (!this.bookingForm.contactInfo.trim()) {
          uni.showToast({
            title: '请输入联系方式',
            icon: 'error'
          })
          return false
        }
      }
      
      return true
    },
    
    // 返回
    goBack() {
      uni.navigateBack()
    }
  },
  
  watch: {
    showTimeSelector(val) {
      if (val) {
        this.tempDate = this.selectedDate || new Date().toISOString().split('T')[0]
        this.tempSlot = this.selectedSlot
        this.$refs.timePopup.open()
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: 600rpx; // 增加底部空间，确保内容不被遮挡
}

.main-scroll-view {
  height: 100vh;
  box-sizing: border-box;
}

// 场馆信息
.venue-summary {
  display: flex;
  background-color: #ffffff;
  padding: 30rpx;
  margin-bottom: 20rpx;
  
  .venue-image {
    width: 120rpx;
    height: 120rpx;
    border-radius: 12rpx;
    margin-right: 24rpx;
  }
  
  .venue-info {
    flex: 1;
    
    .venue-name {
      display: block;
      font-size: 32rpx;
      font-weight: 600;
      color: #333333;
      margin-bottom: 8rpx;
    }
    
    .venue-location {
      display: block;
      font-size: 26rpx;
      color: #666666;
      margin-bottom: 8rpx;
    }
    
    .venue-price {
      display: block;
      font-size: 28rpx;
      color: #ff6b35;
      font-weight: 600;
    }
  }
}

// 预约表单
.booking-form {
  .form-section {
    background-color: #ffffff;
    margin-bottom: 20rpx;
    padding: 30rpx;
    
    .section-title {
      display: block;
      font-size: 28rpx;
      font-weight: 600;
      color: #333333;
      margin-bottom: 24rpx;
    }
    
    // 预约类型显示
    .booking-type-display {
      padding: 24rpx;
      background: #f8f9fa;
      border-radius: 12rpx;
      border: 2rpx solid #e9ecef;
      
      .booking-type-text {
        font-size: 30rpx;
        font-weight: 600;
        color: #ff6b35;
      }
    }
    
    // 时间信息
    .time-info {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 20rpx 0;
      
      .time-text {
        font-size: 28rpx;
        color: #333333;
      }
      
      .change-time-btn {
        padding: 8rpx 16rpx;
        background-color: #ff6b35;
        color: #ffffff;
        border: none;
        border-radius: 6rpx;
        font-size: 24rpx;
      }
    }
    
    // 表单项
    .form-item {
      margin-bottom: 30rpx;
      
      &:last-child {
        margin-bottom: 0;
      }
      
      .item-label {
        display: block;
        font-size: 26rpx;
        color: #333333;
        margin-bottom: 12rpx;
        
        .required {
          color: #ff4d4f;
        }
      }
      
      .form-input {
        width: 100%;
        padding: 24rpx;
        border: 2rpx solid #e8e8e8;
        border-radius: 12rpx;
        font-size: 28rpx;
        color: #333333;
        background-color: #ffffff;
        
        &:focus {
          border-color: #1890ff;
          background-color: #f6ffed;
        }
        
        &::placeholder {
          color: #cccccc;
        }
      }
      
      .form-picker {
        width: 100%;
        
        .picker-text {
          padding: 24rpx;
          border: 2rpx solid #e8e8e8;
          border-radius: 12rpx;
          font-size: 28rpx;
          color: #333333;
          background-color: #ffffff;
          text-align: left;
          
          &::after {
            content: '';
            position: absolute;
            right: 24rpx;
            top: 50%;
            transform: translateY(-50%);
            width: 0;
            height: 0;
            border-left: 8rpx solid #999999;
            border-top: 6rpx solid transparent;
            border-bottom: 6rpx solid transparent;
          }
        }
      }
      
      .form-textarea {
        width: 100%;
        min-height: 120rpx;
        padding: 20rpx;
        background-color: #f8f8f8;
        border: 1rpx solid #e8e8e8;
        border-radius: 8rpx;
        font-size: 28rpx;
        color: #333333;
        resize: none;
      }
      
      .picker-text {
        padding: 20rpx;
        background-color: #f8f8f8;
        border: 1rpx solid #e8e8e8;
        border-radius: 8rpx;
        font-size: 28rpx;
        color: #333333;
      }
    }
  }
}

// 费用明细
.cost-summary {
  background-color: #ffffff;
  padding: 30rpx;
  margin-bottom: 20rpx; // 恢复到合理值
  
  .summary-title {
    display: block;
    font-size: 28rpx;
    font-weight: 600;
    color: #333333;
    margin-bottom: 24rpx;
  }
  
  .cost-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16rpx 0;
    font-size: 26rpx;
    color: #666666;
  }
  
  .cost-total {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 20rpx;
    padding: 20rpx 0;
    border-top: 1px solid #f5f5f5;
    font-size: 28rpx;
    
    .total-amount {
      font-size: 36rpx;
      font-weight: bold;
      color: #333;
    }
  }
  
  .shared-price {
    color: #ff6b00 !important;
    font-weight: bold;
    font-size: 36rpx !important;
  }
  
  .info-tip {
    font-size: 24rpx;
    color: #999;
    margin-top: 10rpx;
    line-height: 1.4;
  }
}

// 底部操作
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #ffffff;
  padding: 20rpx 30rpx;
  border-top: 1rpx solid #f0f0f0;
  z-index: 999; // 确保底部操作栏在最上层
  
  .bottom-cost {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16rpx 0;
    border-bottom: 1rpx solid #f0f0f0;
    margin-bottom: 16rpx;
    
    .cost-label {
      font-size: 28rpx;
      color: #333333;
      font-weight: 600;
    }
    
    .cost-value {
      font-size: 32rpx;
      color: #ff6b35;
      font-weight: 700;
    }
  }
  
  .action-buttons {
    display: flex;
    
    .cancel-btn {
      flex: 1;
      height: 80rpx;
      background-color: #f5f5f5;
      color: #666666;
      border: none;
      border-radius: 8rpx;
      font-size: 28rpx;
      margin-right: 20rpx;
    }
    
    .confirm-btn {
      flex: 2;
      height: 80rpx;
      background-color: #ff6b35;
      color: #ffffff;
      border: none;
      border-radius: 8rpx;
      font-size: 28rpx;
      font-weight: 600;
      
      &[disabled] {
        background-color: #cccccc;
        color: #ffffff;
      }
    }
  }
}

// 时间选择弹窗
.time-selector {
  background-color: #ffffff;
  border-radius: 24rpx 24rpx 0 0;
  max-height: 80vh;
  
  .selector-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 30rpx;
    border-bottom: 1rpx solid #f0f0f0;
    
    .selector-title {
      font-size: 32rpx;
      font-weight: 600;
      color: #333333;
    }
    
    .close-btn {
      font-size: 32rpx;
      color: #999999;
      padding: 8rpx;
    }
  }
  
  .slots-container {
    padding: 30rpx;
    
    .slots-title {
      display: block;
      font-size: 28rpx;
      font-weight: 600;
      color: #333333;
      margin-bottom: 20rpx;
    }
    
    .slots-grid {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 20rpx;
      
      .slot-item {
        padding: 20rpx;
        border-radius: 8rpx;
        text-align: center;
        border: 2rpx solid #e8e8e8;
        transition: all 0.3s;
        position: relative;
        
        &.available {
          background: #f6ffed;
          border-color: #b7eb8f;
          
          &:active {
            background: #d9f7be;
          }
        }
        
        &.reserved {
          background: #fff2f0;
          border-color: #ffccc7;
          opacity: 0.6;
          cursor: not-allowed;
          
          .slot-time {
            color: #999999;
          }
          
          .slot-status {
            color: #ff4d4f;
            font-weight: 500;
          }
        }
        
        &.maintenance {
          background: #fff7e6;
          border-color: #ffd591;
          opacity: 0.6;
          cursor: not-allowed;
          
          .slot-time {
            color: #999999;
          }
          
          .slot-status {
            color: #ff9500;
            font-weight: 500;
          }
        }
        
        &.disabled {
          pointer-events: none;
          position: relative;
          
          &::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(255, 255, 255, 0.3);
            border-radius: 12rpx;
          }
        }
        
        &.time-restricted {
          background: #fff1f0;
          border-color: #ffccc7;
          opacity: 0.7;
          cursor: not-allowed;
          
          .slot-time {
            color: #999999;
          }
          
          .slot-status {
            color: #ff4d4f;
            font-weight: 500;
          }
        }
        
        &.selected {
          background: #e6f7ff;
          border-color: #91d5ff;
          box-shadow: 0 0 0 4rpx rgba(24, 144, 255, 0.2);
        }
        
        .slot-time {
          display: block;
          font-size: 26rpx;
          font-weight: 600;
          color: #333333;
          margin-bottom: 8rpx;
        }
        
        .slot-status {
          display: block;
          font-size: 22rpx;
          color: #999999;
        }
      }
    }
  }
  
  .selector-actions {
    display: flex;
    padding: 30rpx;
    border-top: 1rpx solid #f0f0f0;
    
    .selector-cancel {
      flex: 1;
      height: 80rpx;
      background-color: #f5f5f5;
      color: #666666;
      border: none;
      border-radius: 8rpx;
      font-size: 28rpx;
      margin-right: 20rpx;
    }
    
    .selector-confirm {
      flex: 2;
      height: 80rpx;
      background-color: #ff6b35;
      color: #ffffff;
      border: none;
      border-radius: 8rpx;
      font-size: 28rpx;
      font-weight: 600;
      
      &[disabled] {
        background-color: #cccccc;
        color: #ffffff;
      }
    }
  }
}

// 拼场说明
.shared-info {
  background-color: #e6f7ff;
  border: 1rpx solid #91d5ff;
  border-radius: 8rpx;
  padding: 24rpx;
  margin-bottom: 20rpx;
  
  .info-title {
    display: flex;
    align-items: center;
    font-size: 26rpx;
    font-weight: 600;
    color: #1890ff;
    margin-bottom: 16rpx;
    
    &::before {
      content: 'ℹ';
      margin-right: 8rpx;
      font-size: 28rpx;
    }
  }
  
  .info-list {
    .info-item {
      display: flex;
      align-items: flex-start;
      font-size: 24rpx;
      color: #666666;
      margin-bottom: 8rpx;
      
      &:last-child {
        margin-bottom: 0;
      }
      
      &::before {
        content: '•';
        margin-right: 8rpx;
        color: #1890ff;
        font-weight: bold;
      }
    }
  }
}

// 时间限制提示
.time-restriction-tip {
  background-color: #fff7e6;
  border: 1rpx solid #ffd591;
  border-radius: 8rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
  
  .tip-content {
    display: flex;
    align-items: center;
    font-size: 24rpx;
    color: #d46b08;
    
    &::before {
      content: '⚠';
      margin-right: 8rpx;
      font-size: 26rpx;
      color: #fa8c16;
    }
  }
}

// 拼场说明
.sharing-notice {
  background: linear-gradient(135deg, #e6f7ff 0%, #f0f9ff 100%);
  border: 2rpx solid #40a9ff;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-top: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(24, 144, 255, 0.1);
  
  .notice-header {
    display: flex;
    align-items: center;
    margin-bottom: 24rpx;
    
    .notice-icon {
      font-size: 32rpx;
      margin-right: 12rpx;
    }
    
    .notice-title {
      font-size: 30rpx;
      font-weight: 700;
      color: #1890ff;
      letter-spacing: 1rpx;
    }
  }
  
  .notice-content {
    .notice-item {
      display: flex;
      align-items: flex-start;
      margin-bottom: 16rpx;
      padding: 12rpx 0;
      
      &:last-child {
        margin-bottom: 0;
      }
      
      .item-icon {
        font-size: 24rpx;
        margin-right: 12rpx;
        margin-top: 2rpx;
        flex-shrink: 0;
      }
      
      .item-text {
        font-size: 26rpx;
        color: #333333;
        line-height: 1.6;
        flex: 1;
      }
    }
  }
}

// 时间限制提示
.time-notice {
  background-color: #fff7e6;
  border: 1rpx solid #ffd591;
  border-radius: 8rpx;
  padding: 20rpx;
  margin-top: 16rpx;
  
  .notice-text {
    display: block;
    font-size: 24rpx;
    color: #d46b08;
    line-height: 1.5;
  }
}
</style>