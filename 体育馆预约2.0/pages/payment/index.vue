<template>
  <view class="payment-container">
    <!-- 导航栏 -->
    <view class="nav-bar">
      <view class="nav-left" @click="goBack">
        <text class="nav-icon">←</text>
      </view>
      <view class="nav-title">订单支付</view>
      <view class="nav-right"></view>
    </view>

    <!-- 加载状态 -->
    <view v-if="loading" class="loading-container">
      <text class="loading-text">加载中...</text>
    </view>

    <!-- 订单信息 -->
    <view v-else-if="orderInfo" class="order-section">
      <view class="order-header">
        <text class="order-title">订单信息</text>
        <text class="order-no">{{ orderInfo.orderNo }}</text>
      </view>

      <view class="order-details">
        <view class="detail-item">
          <text class="detail-label">场馆名称</text>
          <text class="detail-value">{{ orderInfo.venueName }}</text>
        </view>
        <view class="detail-item">
          <text class="detail-label">预约时间</text>
          <text class="detail-value">{{ formatOrderDateTime() }}</text>
        </view>
        <view class="detail-item">
          <text class="detail-label">预约类型</text>
          <text class="detail-value">{{ getBookingTypeText() }}</text>
        </view>
        <view v-if="orderInfo.bookingType === 'SHARED' || orderInfo.isVirtualOrder" class="detail-item">
          <text class="detail-label">队伍名称</text>
          <text class="detail-value">{{ getTeamName() }}</text>
        </view>
        <view class="detail-item">
          <text class="detail-label">联系方式</text>
          <text class="detail-value">{{ getContactInfo() }}</text>
        </view>
      </view>

      <!-- 价格信息 -->
      <view class="price-section">
        <view class="price-item">
          <text class="price-label">订单金额</text>
          <text class="price-value">¥{{ getOrderAmount() }}</text>
        </view>
        <view v-if="orderInfo.bookingType === 'SHARED' || orderInfo.isVirtualOrder" class="price-note">
          <text class="note-text">* 拼场订单按队伍收费</text>
        </view>
      </view>
    </view>

    <!-- 支付方式选择 -->
    <view class="payment-methods">
      <view class="method-header">
        <text class="method-title">支付方式</text>
      </view>
      <view class="method-list">
        <view 
          class="method-item" 
          :class="{ active: selectedMethod === 'wechat' }"
          @click="selectMethod('wechat')"
        >
          <view class="method-info">
            <text class="method-icon">💳</text>
            <text class="method-name">微信支付</text>
          </view>
          <view class="method-radio">
            <text v-if="selectedMethod === 'wechat'" class="radio-checked">✓</text>
          </view>
        </view>
        <view 
          class="method-item" 
          :class="{ active: selectedMethod === 'alipay' }"
          @click="selectMethod('alipay')"
        >
          <view class="method-info">
            <text class="method-icon">💰</text>
            <text class="method-name">支付宝</text>
          </view>
          <view class="method-radio">
            <text v-if="selectedMethod === 'alipay'" class="radio-checked">✓</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 底部支付按钮 -->
    <view class="payment-footer">
      <view class="footer-info">
        <text class="footer-label">应付金额</text>
        <text class="footer-amount">¥{{ getOrderAmount() }}</text>
      </view>
      <button 
        class="pay-button" 
        :class="{ disabled: !canPay }"
        :disabled="!canPay"
        @click="handlePayment"
      >
        {{ payButtonText }}
      </button>
    </view>

    <!-- 支付结果弹窗 -->
    <uni-popup ref="resultPopup" type="center" :mask-click="false">
      <view class="result-popup">
        <view class="result-icon">
          <text v-if="paymentResult.success" class="success-icon">✓</text>
          <text v-else class="error-icon">✗</text>
        </view>
        <text class="result-title">{{ paymentResult.title }}</text>
        <text class="result-message">{{ paymentResult.message }}</text>
        <button class="result-button" @click="handleResultAction">
          {{ paymentResult.buttonText }}
        </button>
      </view>
    </uni-popup>
  </view>
</template>

<script>
import { payOrder, getPaymentStatus } from '@/api/payment.js'
import { getOrderDetail } from '@/api/order.js'
import { get } from '@/utils/request.js'
import { useBookingStore } from '@/stores/booking.js'

export default {
  name: 'PaymentPage',
  data() {
    return {
      bookingStore: null,
      orderId: null,
      orderType: 'booking', // booking 或 sharing
      orderInfo: null,
      loading: true,
      selectedMethod: 'wechat',
      paying: false,
      fromPage: '', // 记录来源页面
      paymentResult: {
        success: false,
        title: '',
        message: '',
        buttonText: '确定'
      }
    }
  },
  
  computed: {
    canPay() {
      if (!this.orderInfo || !this.selectedMethod || this.paying) return false

      // 虚拟订单的支付状态判断
      if (this.orderInfo.isVirtualOrder) {
        // 虚拟订单可支付的状态：PENDING（等待支付）
        return this.orderInfo.status === 'PENDING'
      } else {
        // 普通订单只有PENDING状态可以支付
        return this.orderInfo.status === 'PENDING'
      }
    },

    payButtonText() {
      if (this.paying) return '支付中...'
      if (!this.orderInfo) return '加载中...'

      // 虚拟订单的按钮文本逻辑
      if (this.orderInfo.isVirtualOrder) {
        if (this.orderInfo.status === 'PENDING') {
          const amount = this.orderInfo.paymentAmount || this.orderInfo.totalPrice
          return `立即支付 ¥${amount?.toFixed(2) || '0.00'}`
        } else {
          // 根据虚拟订单状态显示不同文本
          const statusMessages = {
            'SHARING_SUCCESS': '拼场已成功',
            'CANCELLED': '申请已取消',
            'EXPIRED': '申请已过期',
            'NOT_FOUND': '申请不存在',
            'ACCESS_DENIED': '无权访问'
          }
          return statusMessages[this.orderInfo.status] || '订单状态异常'
        }
      } else {
        // 普通订单的按钮文本逻辑
        if (this.orderInfo.status === 'PENDING') {
          return `立即支付 ¥${this.orderInfo.totalPrice?.toFixed(2) || '0.00'}`
        } else {
          return '订单状态异常'
        }
      }
    }
  },
  
  onLoad(options) {
    console.log('支付页面参数:', options)

    // 初始化Pinia store
    this.bookingStore = useBookingStore()

    if (options.orderId) {
      this.orderId = options.orderId
      this.orderType = options.type || 'booking'
      this.fromPage = options.from || ''  // 记录来源页面
      this.loadOrderInfo()
    } else {
      uni.showToast({
        title: '订单ID缺失',
        icon: 'error'
      })
      setTimeout(() => {
        uni.navigateBack()
      }, 1500)
    }
  },
  
  methods: {
    // 加载订单信息
    async loadOrderInfo() {
      try {
        this.loading = true

        // 检查是否是虚拟订单（负数ID）
        const isVirtualOrder = this.orderId < 0
        console.log('订单ID:', this.orderId, '是否为虚拟订单:', isVirtualOrder)

        let response
        if (isVirtualOrder) {
          // 虚拟订单：使用申请ID调用虚拟订单API
          const requestId = Math.abs(this.orderId) // 转换为正数
          console.log('获取虚拟订单详情，申请ID:', requestId)
          try {
            // 使用项目的请求工具
            response = await get(`/users/me/virtual-order/${requestId}`)
          } catch (error) {
            console.error('获取虚拟订单失败:', error)
            // 如果是404或403错误，创建一个错误状态的虚拟订单
            if (error.status === 404) {
              response = {
                data: {
                  id: this.orderId,
                  orderNo: `REQ_${requestId}`,
                  status: 'NOT_FOUND',
                  isVirtualOrder: true,
                  venueName: '未知场馆',
                  totalPrice: 0,
                  paymentAmount: 0
                }
              }
            } else if (error.status === 403) {
              response = {
                data: {
                  id: this.orderId,
                  orderNo: `REQ_${requestId}`,
                  status: 'ACCESS_DENIED',
                  isVirtualOrder: true,
                  venueName: '未知场馆',
                  totalPrice: 0,
                  paymentAmount: 0
                }
              }
            } else {
              throw error // 重新抛出其他错误
            }
          }
        } else {
          // 真实订单：使用Pinia Booking Store
          console.log('获取真实订单详情，订单ID:', this.orderId)
          console.log('bookingStore:', this.bookingStore)
          console.log('bookingStore.getBookingDetail:', this.bookingStore?.getBookingDetail)
          console.log('bookingStore类型:', typeof this.bookingStore)

          if (!this.bookingStore || typeof this.bookingStore.getBookingDetail !== 'function') {
            console.warn('bookingStore未正确初始化，使用原API作为备用')
            // 使用原来的API调用作为备用
            response = await getOrderDetail(this.orderId)
          } else {
            await this.bookingStore.getBookingDetail(this.orderId)
            console.log('Booking Store调用完成')

            // 从store的state中获取数据（修复：直接访问state而不是同名getter）
            const storeData = this.bookingStore.bookingDetail
            console.log('从Store state获取的数据:', storeData)
            console.log('Store state数据类型:', typeof storeData)
            console.log('Store state数据内容:', storeData)

            response = { data: storeData }
          }
        }

        // 处理不同的响应格式
        this.orderInfo = response.data || response
        console.log('最终订单信息:', this.orderInfo)

        // 修复订单金额为0的问题
        if (this.orderInfo && !isVirtualOrder && (this.orderInfo.totalPrice === 0 || !this.orderInfo.totalPrice)) {
          console.log('检测到订单金额为0，尝试修复...')

          // 如果订单有price字段，使用它
          if (this.orderInfo.price && this.orderInfo.price > 0) {
            console.log('使用订单的price字段:', this.orderInfo.price)
            this.orderInfo.totalPrice = this.orderInfo.price
          } else {
            // 尝试重新计算价格
            const calculatedPrice = this.calculateOrderPrice()
            if (calculatedPrice > 0) {
              console.log('使用计算的价格:', calculatedPrice)
              this.orderInfo.totalPrice = calculatedPrice
            }
          }
        }

        // 如果是虚拟订单，添加特殊标识和详细调试信息
        if (isVirtualOrder) {
          this.orderInfo.isVirtualOrder = true
          console.log('虚拟订单详细信息:')
          console.log('- 订单状态:', this.orderInfo.status)
          console.log('- 申请状态:', this.orderInfo.requestStatus)
          console.log('- 支付金额:', this.orderInfo.paymentAmount)
          console.log('- 总价:', this.orderInfo.totalPrice)
          console.log('- 队伍名称:', this.orderInfo.applicantTeamName)
          console.log('- 联系方式:', this.orderInfo.applicantContact)
          console.log('- 预约时间:', this.orderInfo.bookingTime)
          console.log('- 结束时间:', this.orderInfo.endTime)
          console.log('- 原始响应:', response)

          // 检查是否有错误状态
          if (!this.orderInfo.status) {
            console.error('虚拟订单状态为空！')
            this.orderInfo.status = 'PENDING' // 设置默认状态
          }

          // 检查支付金额
          if (!this.orderInfo.paymentAmount && !this.orderInfo.totalPrice) {
            console.error('虚拟订单金额为空！')
          }
        }

      } catch (error) {
        console.error('加载订单信息失败:', error)
        uni.showToast({
          title: '加载失败',
          icon: 'error'
        })
        setTimeout(() => {
          uni.navigateBack()
        }, 1500)
      } finally {
        this.loading = false
      }
    },
    
    // 选择支付方式
    selectMethod(method) {
      this.selectedMethod = method
    },
    
    // 处理支付
    async handlePayment() {
      if (!this.canPay) return

      try {
        this.paying = true

        // 显示支付加载
        uni.showLoading({ title: '支付中...' })

        // 模拟支付延迟
        await new Promise(resolve => setTimeout(resolve, 2000))

        // 模拟支付结果（80%成功率）
        const isSuccess = Math.random() > 0.2

        uni.hideLoading()

        if (isSuccess) {
          // 调用支付接口
          const response = await payOrder(this.orderId, this.selectedMethod)

          if (response.success) {
            // 支付成功，跳转到成功页面
            let successUrl = `/pages/payment/success?orderId=${this.orderId}`
            if (this.fromPage) {
              successUrl += `&from=${this.fromPage}`
            }
            uni.redirectTo({
              url: successUrl
            })
          } else {
            throw new Error(response.message || '支付失败')
          }
        } else {
          // 模拟支付失败
          throw new Error('支付失败，请检查账户余额或重试')
        }

      } catch (error) {
        uni.hideLoading()
        console.error('支付失败:', error)

        // 支付失败，跳转到失败页面
        uni.redirectTo({
          url: `/pages/payment/failed?orderId=${this.orderId}&reason=${encodeURIComponent(error.message)}`
        })
      } finally {
        this.paying = false
      }
    },

    // 处理结果操作（保留用于其他用途）
    handleResultAction() {
      this.$refs.resultPopup.close()

      if (this.paymentResult.success) {
        // 支付成功，跳转到订单列表
        uni.redirectTo({
          url: '/pages/booking/list'
        })
      }
      // 支付失败，留在当前页面重试
    },
    
    // 格式化日期时间
    formatDateTime(date, startTime, endTime) {
      if (!date || !startTime) return '未设置'

      // 处理日期
      let dateStr = ''
      if (typeof date === 'string' && date.includes('-')) {
        // 如果是 YYYY-MM-DD 格式
        const [year, month, day] = date.split('-')
        dateStr = `${month}-${day}`
      } else {
        // 其他格式
        const dateObj = new Date(date)
        dateStr = dateObj.toLocaleDateString('zh-CN', {
          month: '2-digit',
          day: '2-digit'
        })
      }

      // 处理时间
      const timeStr = endTime ? `${startTime}-${endTime}` : startTime

      return `${dateStr} ${timeStr}`
    },
    
    // 返回
    goBack() {
      uni.navigateBack()
    },

    // 格式化订单时间（处理虚拟订单和普通订单的差异）
    formatOrderDateTime() {
      if (!this.orderInfo) return '未设置'

      console.log('formatOrderDateTime - 订单数据:', this.orderInfo)
      console.log('formatOrderDateTime - 字段检查:')
      console.log('- bookingDate:', this.orderInfo.bookingDate)
      console.log('- bookingTime:', this.orderInfo.bookingTime)
      console.log('- startTime:', this.orderInfo.startTime)
      console.log('- endTime:', this.orderInfo.endTime)
      console.log('- isVirtualOrder:', this.orderInfo.isVirtualOrder)

      if (this.orderInfo.isVirtualOrder) {
        // 虚拟订单使用 bookingTime 和 endTime (LocalDateTime格式: "yyyy-MM-dd HH:mm:ss")
        const startTime = this.orderInfo.bookingTime
        const endTime = this.orderInfo.endTime

        console.log('虚拟订单时间格式化 - 原始数据:', { startTime, endTime })

        if (!startTime) {
          console.warn('虚拟订单开始时间为空')
          return '未设置'
        }

        try {
          // 处理后端返回的时间格式 "yyyy-MM-dd HH:mm:ss"，转换为iOS兼容格式
          let startDateTime, endDateTime

          if (typeof startTime === 'string') {
            // 后端格式: "2025-07-16 08:00:00"
            // iOS兼容格式: "2025-07-16T08:00:00"
            let isoTime = startTime
            if (startTime.includes(' ') && !startTime.includes('T')) {
              isoTime = startTime.replace(' ', 'T')
            }
            startDateTime = new Date(isoTime)
            console.log('支付页面时间转换 - 原始:', startTime, '转换后:', isoTime, '解析结果:', startDateTime)
          } else {
            startDateTime = new Date(startTime)
          }

          if (endTime) {
            if (typeof endTime === 'string') {
              let isoEndTime = endTime
              if (endTime.includes(' ') && !endTime.includes('T')) {
                isoEndTime = endTime.replace(' ', 'T')
              }
              endDateTime = new Date(isoEndTime)
              console.log('支付页面结束时间转换 - 原始:', endTime, '转换后:', isoEndTime, '解析结果:', endDateTime)
            } else {
              endDateTime = new Date(endTime)
            }
          }

          // 检查日期是否有效
          if (isNaN(startDateTime.getTime())) {
            console.error('无效的开始时间:', startTime)
            return '时间格式错误'
          }

          // 格式化日期 (MM-DD)
          const dateStr = startDateTime.toLocaleDateString('zh-CN', {
            month: '2-digit',
            day: '2-digit'
          })

          // 格式化开始时间 (HH:mm)
          const startTimeStr = startDateTime.toLocaleTimeString('zh-CN', {
            hour: '2-digit',
            minute: '2-digit',
            hour12: false
          })

          // 格式化结束时间 (HH:mm)
          let endTimeStr = ''
          if (endDateTime && !isNaN(endDateTime.getTime())) {
            endTimeStr = endDateTime.toLocaleTimeString('zh-CN', {
              hour: '2-digit',
              minute: '2-digit',
              hour12: false
            })
          }

          const result = `${dateStr} ${startTimeStr}${endTimeStr ? '-' + endTimeStr : ''}`
          console.log('虚拟订单时间格式化结果:', result)
          return result
        } catch (error) {
          console.error('虚拟订单时间格式化错误:', error, '原始数据:', { startTime, endTime })
          return '时间格式错误'
        }
      } else {
        // 普通订单：处理不同的字段名
        console.log('普通订单时间格式化')

        // 优先使用bookingTime字段（新格式），如果没有则使用bookingDate+startTime（旧格式）
        if (this.orderInfo.bookingTime) {
          // 新格式：bookingTime包含完整的日期时间
          const bookingTime = this.orderInfo.bookingTime
          let endTime = this.orderInfo.endTime

          // 如果没有endTime，尝试从其他字段计算
          if (!endTime) {
            // 检查是否有duration字段或其他时间相关字段
            console.log('没有endTime，检查其他字段:')
            console.log('- duration:', this.orderInfo.duration)
            console.log('- slotIds:', this.orderInfo.slotIds)
            console.log('- bookingType:', this.orderInfo.bookingType)

            // 如果有duration，计算结束时间
            if (this.orderInfo.duration) {
              try {
                const startDateTime = new Date(bookingTime.replace(' ', 'T'))
                const durationHours = parseFloat(this.orderInfo.duration)
                const endDateTime = new Date(startDateTime.getTime() + durationHours * 60 * 60 * 1000)
                endTime = endDateTime.toISOString().replace('T', ' ').substring(0, 19)
                console.log('根据duration计算的endTime:', endTime)
              } catch (error) {
                console.error('计算结束时间失败:', error)
              }
            } else {
              // 如果没有duration，根据预约类型和价格推算时长
              console.log('没有duration，尝试根据其他信息推算')

              // 从前端日志看，选择了4个时间段，每个30分钟，总共2小时
              // 可以根据这个模式推算结束时间
              try {
                const startDateTime = new Date(bookingTime.replace(' ', 'T'))
                console.log('开始时间解析:', startDateTime)

                // 默认假设2小时的预约（这是常见的预约时长）
                const endDateTime = new Date(startDateTime.getTime() + 2 * 60 * 60 * 1000)
                console.log('计算的结束时间:', endDateTime)

                // 格式化为 "yyyy-MM-dd HH:mm:ss" 格式
                const year = endDateTime.getFullYear()
                const month = String(endDateTime.getMonth() + 1).padStart(2, '0')
                const day = String(endDateTime.getDate()).padStart(2, '0')
                const hours = String(endDateTime.getHours()).padStart(2, '0')
                const minutes = String(endDateTime.getMinutes()).padStart(2, '0')
                const seconds = String(endDateTime.getSeconds()).padStart(2, '0')

                endTime = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
                console.log('根据默认时长推算的endTime:', endTime)
              } catch (error) {
                console.error('推算结束时间失败:', error)
              }
            }
          }

          console.log('使用bookingTime字段:', bookingTime, 'endTime:', endTime)

          try {
            // 处理bookingTime格式 "2025-07-19 16:00:00"
            let startDateTime
            if (typeof bookingTime === 'string') {
              let isoTime = bookingTime
              if (bookingTime.includes(' ') && !bookingTime.includes('T')) {
                isoTime = bookingTime.replace(' ', 'T')
              }
              startDateTime = new Date(isoTime)
            } else {
              startDateTime = new Date(bookingTime)
            }

            // 提取日期和时间
            const dateStr = startDateTime.toLocaleDateString('zh-CN', {
              month: '2-digit',
              day: '2-digit'
            })

            const startTimeStr = startDateTime.toLocaleTimeString('zh-CN', {
              hour: '2-digit',
              minute: '2-digit',
              hour12: false
            })

            // 如果有endTime，也格式化它
            let endTimeStr = ''
            if (endTime) {
              let endDateTime
              if (typeof endTime === 'string') {
                let isoEndTime = endTime
                if (endTime.includes(' ') && !endTime.includes('T')) {
                  isoEndTime = endTime.replace(' ', 'T')
                }
                endDateTime = new Date(isoEndTime)
              } else {
                endDateTime = new Date(endTime)
              }

              if (!isNaN(endDateTime.getTime())) {
                endTimeStr = endDateTime.toLocaleTimeString('zh-CN', {
                  hour: '2-digit',
                  minute: '2-digit',
                  hour12: false
                })
              }
            }

            const result = `${dateStr} ${startTimeStr}${endTimeStr ? '-' + endTimeStr : ''}`
            console.log('普通订单时间格式化结果:', result)
            return result
          } catch (error) {
            console.error('普通订单时间格式化错误:', error)
            return '时间格式错误'
          }
        } else {
          // 旧格式：使用bookingDate + startTime + endTime
          console.log('使用旧格式字段')
          return this.formatDateTime(this.orderInfo.bookingDate, this.orderInfo.startTime, this.orderInfo.endTime)
        }
      }
    },

    // 获取队伍名称
    getTeamName() {
      if (!this.orderInfo) return '未设置'

      if (this.orderInfo.isVirtualOrder) {
        return this.orderInfo.applicantTeamName || '未设置'
      } else {
        return this.orderInfo.teamName || '未设置'
      }
    },

    // 获取联系方式
    getContactInfo() {
      if (!this.orderInfo) return '未设置'

      if (this.orderInfo.isVirtualOrder) {
        return this.orderInfo.applicantContact || '未设置'
      } else {
        return this.orderInfo.contactInfo || '未设置'
      }
    },

    // 获取订单金额
    getOrderAmount() {
      if (!this.orderInfo) return '0.00'

      console.log('getOrderAmount - 订单信息:', this.orderInfo)
      console.log('getOrderAmount - 字段检查:')
      console.log('- totalPrice:', this.orderInfo.totalPrice)
      console.log('- paymentAmount:', this.orderInfo.paymentAmount)
      console.log('- price:', this.orderInfo.price)
      console.log('- isVirtualOrder:', this.orderInfo.isVirtualOrder)

      let amount
      if (this.orderInfo.isVirtualOrder) {
        amount = this.orderInfo.paymentAmount
      } else {
        // 普通订单：优先使用totalPrice，如果为0则尝试使用price字段
        amount = this.orderInfo.totalPrice
        if (!amount || amount === 0) {
          console.log('totalPrice为0，尝试使用price字段:', this.orderInfo.price)
          amount = this.orderInfo.price

          // 如果price字段也没有，尝试根据时间段计算价格
          if (!amount || amount === 0) {
            console.log('price字段也为0，尝试计算价格')
            amount = this.calculateOrderPrice()
          }
        }
      }

      const result = amount?.toFixed(2) || '0.00'
      console.log('getOrderAmount - 最终金额:', result)
      return result
    },

    // 计算订单价格（当后端价格为0时的备用方案）
    calculateOrderPrice() {
      if (!this.orderInfo) return 0

      console.log('calculateOrderPrice - 开始计算价格')

      // 尝试从订单信息中提取时间信息来计算价格
      const bookingTime = this.orderInfo.bookingTime
      const endTime = this.orderInfo.endTime
      const venueId = this.orderInfo.venueId

      console.log('价格计算参数:', { bookingTime, endTime, venueId })

      // 如果有时间段信息，计算时长
      if (bookingTime) {
        try {
          const startDateTime = new Date(bookingTime.replace(' ', 'T'))
          let duration = 1 // 默认1小时

          if (endTime) {
            const endDateTime = new Date(endTime.replace(' ', 'T'))
            duration = (endDateTime - startDateTime) / (1000 * 60 * 60) // 转换为小时
          } else {
            // 如果没有结束时间，根据预约类型估算
            // 从前端计算的价格推算：4个时间段×100元=400元，2小时，所以每小时200元
            // 但这里我们需要更准确的计算方式

            // 尝试从订单的其他信息推算时长
            // 如果是多时间段预约，通常是连续的半小时时间段
            // 从日志看是4个时间段，每个30分钟，总共2小时
            duration = 2 // 暂时使用2小时作为默认值

            console.log('没有结束时间，使用默认时长:', duration, '小时')
          }

          console.log('计算的时长:', duration, '小时')

          // 根据场馆ID或类型获取更准确的价格
          // 从最新日志看，场馆价格是120元/小时，每个时间段60元
          let hourlyRate = 120 // 使用实际的场馆价格

          // 如果能获取到场馆信息，使用场馆的实际价格
          if (this.orderInfo.venueName) {
            console.log('场馆名称:', this.orderInfo.venueName)
            // 根据前端日志中的价格信息调整
            // 场馆价格120元/小时，每个时间段60元，4个时间段=240元
            // 所以实际应该是 4个时间段 × 60元 = 240元
            // 但这里我们按时长计算：2小时 × 120元 = 240元
            hourlyRate = 120
          }

          const calculatedPrice = duration * hourlyRate

          console.log('计算的价格:', calculatedPrice)
          return calculatedPrice
        } catch (error) {
          console.error('价格计算失败:', error)
          return 0
        }
      }

      return 0
    },

    // 获取预约类型文本
    getBookingTypeText() {
      if (!this.orderInfo) return '未知'

      // 虚拟订单始终是拼场类型
      if (this.orderInfo.isVirtualOrder) {
        return '拼场'
      }

      return this.orderInfo.bookingType === 'SHARED' ? '拼场' : '独享'
    }
  }
}
</script>

<style lang="scss" scoped>
.payment-container {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 120rpx;
}

.nav-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 88rpx;
  padding: 0 32rpx;
  background-color: #ffffff;
  border-bottom: 1rpx solid #e5e5e5;
  
  .nav-left, .nav-right {
    width: 80rpx;
  }
  
  .nav-icon {
    font-size: 36rpx;
    color: #333333;
  }
  
  .nav-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #333333;
  }
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400rpx;
  
  .loading-text {
    font-size: 28rpx;
    color: #999999;
  }
}

.order-section {
  margin: 20rpx;
  background-color: #ffffff;
  border-radius: 16rpx;
  overflow: hidden;
}

.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx;
  border-bottom: 1rpx solid #f0f0f0;
  
  .order-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #333333;
  }
  
  .order-no {
    font-size: 24rpx;
    color: #999999;
  }
}

.order-details {
  padding: 0 32rpx;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 0;
  border-bottom: 1rpx solid #f8f8f8;
  
  &:last-child {
    border-bottom: none;
  }
  
  .detail-label {
    font-size: 28rpx;
    color: #666666;
  }
  
  .detail-value {
    font-size: 28rpx;
    color: #333333;
    text-align: right;
    flex: 1;
    margin-left: 32rpx;
  }
}

.price-section {
  padding: 32rpx;
  border-top: 1rpx solid #f0f0f0;
  background-color: #fafafa;
}

.price-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  
  .price-label {
    font-size: 32rpx;
    color: #333333;
    font-weight: 600;
  }
  
  .price-value {
    font-size: 36rpx;
    color: #ff6b35;
    font-weight: 700;
  }
}

.price-note {
  margin-top: 16rpx;
  
  .note-text {
    font-size: 24rpx;
    color: #999999;
  }
}

.payment-methods {
  margin: 20rpx;
  background-color: #ffffff;
  border-radius: 16rpx;
  overflow: hidden;
}

.method-header {
  padding: 32rpx;
  border-bottom: 1rpx solid #f0f0f0;
  
  .method-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #333333;
  }
}

.method-list {
  padding: 0 32rpx;
}

.method-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx 0;
  border-bottom: 1rpx solid #f8f8f8;
  
  &:last-child {
    border-bottom: none;
  }
  
  &.active {
    .method-name {
      color: #ff6b35;
    }
  }
}

.method-info {
  display: flex;
  align-items: center;
  
  .method-icon {
    font-size: 32rpx;
    margin-right: 16rpx;
  }
  
  .method-name {
    font-size: 28rpx;
    color: #333333;
  }
}

.method-radio {
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  border: 2rpx solid #ddd;
  display: flex;
  align-items: center;
  justify-content: center;
  
  .radio-checked {
    color: #ff6b35;
    font-size: 24rpx;
    font-weight: bold;
  }
}

.payment-footer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  align-items: center;
  padding: 24rpx 32rpx;
  background-color: #ffffff;
  border-top: 1rpx solid #e5e5e5;
  z-index: 100;
}

.footer-info {
  flex: 1;
  
  .footer-label {
    font-size: 24rpx;
    color: #666666;
    display: block;
  }
  
  .footer-amount {
    font-size: 32rpx;
    color: #ff6b35;
    font-weight: 700;
  }
}

.pay-button {
  width: 240rpx;
  height: 80rpx;
  background-color: #ff6b35;
  color: #ffffff;
  border: none;
  border-radius: 40rpx;
  font-size: 28rpx;
  font-weight: 600;
  
  &.disabled {
    background-color: #cccccc;
    color: #999999;
  }
}

.result-popup {
  width: 560rpx;
  padding: 60rpx 40rpx 40rpx;
  background-color: #ffffff;
  border-radius: 16rpx;
  text-align: center;
}

.result-icon {
  margin-bottom: 32rpx;
  
  .success-icon {
    display: inline-block;
    width: 80rpx;
    height: 80rpx;
    line-height: 80rpx;
    background-color: #52c41a;
    color: #ffffff;
    border-radius: 50%;
    font-size: 48rpx;
    font-weight: bold;
  }
  
  .error-icon {
    display: inline-block;
    width: 80rpx;
    height: 80rpx;
    line-height: 80rpx;
    background-color: #ff4d4f;
    color: #ffffff;
    border-radius: 50%;
    font-size: 48rpx;
    font-weight: bold;
  }
}

.result-title {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 16rpx;
}

.result-message {
  display: block;
  font-size: 28rpx;
  color: #666666;
  margin-bottom: 40rpx;
  line-height: 1.5;
}

.result-button {
  width: 200rpx;
  height: 72rpx;
  background-color: #ff6b35;
  color: #ffffff;
  border: none;
  border-radius: 36rpx;
  font-size: 28rpx;
}
</style>
