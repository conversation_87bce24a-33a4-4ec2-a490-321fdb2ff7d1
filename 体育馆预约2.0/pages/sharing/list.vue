<template>
  <view class="container">
    <!-- 显示模式切换 -->
    <view class="mode-switch">
      <view 
        class="mode-item"
        :class="{ active: showMode === 'joinable' }"
        @click="switchMode('joinable')"
      >
        可参与
      </view>
      <view 
        class="mode-item"
        :class="{ active: showMode === 'all' }"
        @click="switchMode('all')"
      >
        全部
      </view>
    </view>
    
    <!-- 筛选栏 -->
    <view class="filter-section">
      <scroll-view class="filter-scroll" scroll-x>
        <view class="filter-item" 
              :class="{ active: selectedStatus === '' }" 
              @click="selectStatus('')">
          全部
        </view>
        <view 
          v-for="status in statusOptions" 
          :key="status.value" 
          class="filter-item"
          :class="{ active: selectedStatus === status.value }"
          @click="selectStatus(status.value)"
        >
          {{ status.label }}
        </view>
      </scroll-view>
      <view class="filter-more" @click="showFilterModal">
        <text>筛选</text>
      </view>
    </view>
    
    <!-- 调试信息 -->
    <view v-if="true" class="debug-info" style="background: #f0f0f0; padding: 10px; margin: 10px; border-radius: 5px;">
      <view style="margin-bottom: 5px;"><text style="font-size: 12px; color: #666;">调试信息：</text></view>
      <view style="margin-bottom: 3px;"><text style="font-size: 12px; color: #666;">原始数据数量: {{ sharingOrders?.length || 0 }}</text></view>
      <view style="margin-bottom: 3px;"><text style="font-size: 12px; color: #666;">筛选后数量: {{ filteredSharingOrders?.length || 0 }}</text></view>
      <view style="margin-bottom: 3px;"><text style="font-size: 12px; color: #666;">加载状态: {{ loading ? '加载中' : '已完成' }}</text></view>
      <view style="margin-bottom: 3px;"><text style="font-size: 12px; color: #666;">选中状态: {{ selectedStatus || '全部' }}</text></view>
      <view style="margin-bottom: 3px;"><text style="font-size: 12px; color: #666;">Store状态: {{ sharingStore ? '已连接' : '未连接' }}</text></view>
      <view v-if="sharingOrders && sharingOrders.length > 0" style="margin-bottom: 3px;">
        <text style="font-size: 12px; color: #666;">第一条数据: {{ JSON.stringify(sharingOrders[0]) }}</text>
      </view>
    </view>
    
    <!-- 拼场列表 -->
    <view class="sharing-list">
      <view 
        v-for="sharing in filteredSharingOrders" 
        :key="sharing.id" 
        class="sharing-card"
        :class="{ 'full-card': sharing.status === 'FULL' || sharing.currentParticipants >= sharing.maxParticipants }"
        @click="navigateToDetail(sharing.id)"
      >
        <view class="card-header">
          <view class="venue-info">
            <text class="venue-name">{{ sharing.venueName || '未知场馆' }}</text>
            <text class="venue-location">📍 {{ sharing.venueLocation || '位置未知' }}</text>
          </view>
          <!-- 自己的拼场标识 -->
          <view v-if="isMySharing(sharing)" class="my-sharing-badge">
            <text class="badge-text">我的</text>
          </view>
          <!-- 已满标签 -->
          <view v-if="sharing.status === 'FULL' || sharing.currentParticipants >= sharing.maxParticipants" class="full-badge">
            <text class="badge-text">已满</text>
          </view>
          <view class="sharing-status" :class="getStatusClass(sharing.status)">
            {{ getStatusText(sharing.status) }}
          </view>
        </view>
        
        <view class="card-content">
          <view class="time-info">
            <text class="time-icon">🕐</text>
            <text class="time-text">{{ formatTimeRange(sharing) }}</text>
          </view>
          

          
          <view class="team-info">
            <text class="team-icon">👥</text>
            <text class="team-name">{{ sharing.teamName || '未命名队伍' }}</text>
          </view>
          
          <view class="participants-info">
            <text class="participants-text">参与球队：{{ sharing.currentParticipants || 0 }}/{{ sharing.maxParticipants || 2 }}支</text>
            <view class="progress-bar">
              <view
                class="progress-fill"
                :style="{ width: getProgressWidth(sharing) + '%' }"
              ></view>
            </view>
          </view>

          <!-- 倒计时显示 -->
          <CountdownTimer
            v-if="shouldShowCountdown(sharing)"
            :order="sharing"
            label="自动取消"
            :short="true"
            class="simple"
            @expired="onCountdownExpired"
          />
          
          <view class="price-info">
            <text class="price-label">费用：</text>
            <text class="price-value">¥{{ formatPrice(sharing.pricePerTeam || sharing.perTeamPrice || sharing.pricePerPerson || 0) }}</text>
            <text class="price-note">（每队费用）</text>
          </view>
          
          <view class="creator-info">
            <text class="creator-label">发起人：</text>
            <text class="creator-value">{{ sharing.creatorUsername || '未知' }}</text>
          </view>
          
          <view class="create-info">
            <text class="create-label">创建时间：</text>
            <text class="create-value">{{ formatCreateTime(sharing.createdAt) }}</text>
          </view>
          
          <view v-if="sharing.description" class="description">
            <text>{{ sharing.description }}</text>
          </view>
        </view>
        
        <view class="card-actions">
          <view class="organizer-info">
            <text class="organizer-name">{{ sharing.creatorUsername || '未知用户' }}</text>
          </view>
          
          <button 
            v-if="canJoinSharing(sharing)"
            class="join-btn"
            @click.stop="joinSharing(sharing.id)"
          >
            申请拼场
          </button>
          <view 
            v-else 
            class="join-disabled"
            :class="{ 'applied': hasAppliedToSharing(sharing) }"
          >
            {{ getJoinButtonText(sharing) }}
          </view>
        </view>
      </view>
    </view>
    
    <!-- 空状态 -->
    <view v-if="filteredSharingOrders.length === 0 && !loading" class="empty-state">
      <text class="empty-icon">🏀</text>
      <text class="empty-text">暂无拼场订单</text>
    </view>
    
    <!-- 加载状态 -->
    <view v-if="loading" class="loading-state">
      <text>加载中...</text>
    </view>
    
    <!-- 加载更多 -->
    <view v-if="hasMore && filteredSharingOrders.length > 0" class="load-more" @click="loadMore">
      <text>{{ loading ? '加载中...' : '加载更多' }}</text>
    </view>
    
    <!-- 筛选弹窗 -->
    <uni-popup ref="filterPopup" type="bottom">
      <view class="filter-modal">
        <view class="modal-header">
          <text class="modal-title">筛选条件</text>
          <text class="modal-close" @click="closeFilterModal">✕</text>
        </view>
        
        <view class="filter-content">
          <!-- 日期筛选 -->
          <view class="filter-group">
            <text class="group-title">活动日期</text>
            <view class="date-options">
              <view 
                v-for="date in dateOptions" 
                :key="date.value" 
                class="date-item"
                :class="{ active: filterOptions.date === date.value }"
                @click="selectDate(date.value)"
              >
                {{ date.label }}
              </view>
            </view>
          </view>
          
          <!-- 价格筛选 -->
          <view class="filter-group">
            <text class="group-title">价格范围</text>
            <view class="price-range">
              <input 
                v-model="filterOptions.minPrice" 
                type="number" 
                placeholder="最低价格" 
                class="price-input"
              />
              <text class="price-separator">-</text>
              <input 
                v-model="filterOptions.maxPrice" 
                type="number" 
                placeholder="最高价格" 
                class="price-input"
              />
            </view>
          </view>
          
          <!-- 人数筛选 -->
          <view class="filter-group">
            <text class="group-title">参与人数</text>
            <view class="participants-options">
              <view 
                v-for="participants in participantsOptions" 
                :key="participants.value" 
                class="participants-item"
                :class="{ active: filterOptions.participants === participants.value }"
                @click="selectParticipants(participants.value)"
              >
                {{ participants.label }}
              </view>
            </view>
          </view>
        </view>
        
        <view class="modal-footer">
          <button class="reset-btn" @click="resetFilter">重置</button>
          <button class="confirm-btn" @click="applyFilter">确定</button>
        </view>
      </view>
    </uni-popup>
    
    <!-- 申请拼场弹窗 -->
    <uni-popup ref="joinPopup" type="bottom">
      <view class="apply-modal">
        <view class="modal-header">
          <text class="modal-title">申请加入拼场</text>
          <text class="close-btn" @click="closeJoinModal">✕</text>
        </view>
        
        <view class="modal-content">
          <view class="form-item">
            <text class="form-label">队伍名称</text>
            <input 
              v-model="applyForm.teamName"
              class="form-input"
              placeholder="请输入队伍名称（可选）"
              maxlength="20"
            />
          </view>
          
          <view class="form-item">
            <text class="form-label">联系方式 <text class="required">*</text></text>
            <input 
              v-model="applyForm.contactInfo"
              class="form-input"
              placeholder="请输入手机号或微信号"
              maxlength="50"
            />
          </view>
          
          <view class="form-item">
            <text class="form-label">申请说明</text>
            <text class="form-hint">您将代表一支球队申请加入此拼场</text>
          </view>
          
          <view class="form-item">
            <text class="form-label">申请留言</text>
            <textarea 
              v-model="applyForm.message"
              class="form-textarea"
              placeholder="请输入申请留言（可选）"
              maxlength="200"
            ></textarea>
            <text class="char-count">{{ applyForm.message.length }}/200</text>
          </view>
        </view>
        
        <view class="modal-actions">
          <button class="modal-btn cancel-btn" @click="closeJoinModal">
            取消
          </button>
          <button 
            class="modal-btn confirm-btn" 
            :disabled="!canSubmitApplication"
            @click="submitApplication"
          >
            提交申请
          </button>
        </view>
      </view>
    </uni-popup>
    
    <!-- 悬浮按钮 -->
    <view class="floating-btn" @click="goToMyOrders">
      <text class="floating-btn-text">我的拼场</text>
    </view>
  </view>
</template>

<script>
import { useSharingStore } from '@/stores/sharing.js'
import { useUserStore } from '@/stores/user.js'
import CountdownTimer from '@/components/CountdownTimer.vue'
import { shouldShowCountdown } from '@/utils/countdown.js'
import { formatDate, formatDateTime } from '@/utils/helpers.js'

export default {
  name: 'SharingList',

  components: {
    CountdownTimer
  },
  
  data() {
    return {
      sharingStore: null,
      userStore: null,
      selectedStatus: '',
      currentSharing: null,
      showMode: 'joinable', // 'joinable' 可参与的, 'all' 全部
      userApplications: [], // 用户的申请记录
      
      // 申请表单数据
      applyForm: {
        teamName: '',
        contactInfo: '',
        message: ''
      },
      
      // 状态选项
      statusOptions: [
        { label: '开放中', value: 'OPEN' },
        { label: '等待对方支付', value: 'APPROVED_PENDING_PAYMENT' },
        { label: '拼场成功', value: 'SHARING_SUCCESS' },
        { label: '已确认', value: 'CONFIRMED' },
        { label: '已取消', value: 'CANCELLED' },
        { label: '已过期', value: 'EXPIRED' }
      ],
      
      // 筛选选项
      filterOptions: {
        date: '',
        minPrice: '',
        maxPrice: '',
        participants: ''
      },
      
      // 日期选项
      dateOptions: [
        { label: '今天', value: 'today' },
        { label: '明天', value: 'tomorrow' },
        { label: '本周', value: 'week' },
        { label: '本月', value: 'month' }
      ],
      
      // 人数选项
      participantsOptions: [
        { label: '2人', value: '2' },
        { label: '4人', value: '4' },
        { label: '6人', value: '6' },
        { label: '8人及以上', value: '8+' }
      ]
    }
  },
  
  computed: {
    sharingOrders() {
      return this.sharingStore?.sharingOrdersGetter || []
    },

    loading() {
      return this.sharingStore?.isLoading || false
    },

    pagination() {
      return this.sharingStore?.getPagination || { current: 0, totalPages: 0 }
    },

    userInfo() {
      return this.userStore?.getUserInfo || {}
    },

    filteredSharingOrders() {
      let orders = this.sharingOrders || []
      
      // 根据显示模式筛选
      if (this.showMode === 'joinable') {
        orders = orders.filter(order => {
          // 显示开放状态的拼场，包括：
          // 1. 还有空位且不是自己创建的拼场（可以申请）
          // 2. 自己已经申请过的拼场（显示状态，不能重复申请）
          const isOpenAndAvailable = order.status === 'OPEN' && 
                                   order.currentParticipants < order.maxParticipants &&
                                   !this.isMySharing(order)
          
          // 显示所有开放状态的拼场，让用户看到申请后的状态变化
          return order.status === 'OPEN' || order.status === 'FULL'
        })
      }
      // 'all' 模式显示所有状态的拼场订单，包括已满员、已确认等
      
      if (this.selectedStatus) {
        orders = orders.filter(order => order.status === this.selectedStatus)
      }
      
      // 应用筛选条件
      if (this.filterOptions.minPrice) {
        orders = orders.filter(order => (order.totalPrice || 0) >= parseFloat(this.filterOptions.minPrice))
      }
      
      if (this.filterOptions.maxPrice) {
        orders = orders.filter(order => (order.totalPrice || 0) <= parseFloat(this.filterOptions.maxPrice))
      }
      
      if (this.filterOptions.participants) {
        const targetParticipants = parseInt(this.filterOptions.participants)
        if (this.filterOptions.participants === '8+') {
          orders = orders.filter(order => (order.maxParticipants || 0) >= 8)
        } else {
          orders = orders.filter(order => (order.maxParticipants || 0) === targetParticipants)
        }
      }
      
      return orders
    },
    
    hasMore() {
      return this.pagination.current < Math.ceil(this.pagination.total / this.pagination.pageSize)
    },
    
    // 是否可以提交申请
    canSubmitApplication() {
      return this.applyForm.contactInfo.trim().length > 0
    }
  },
  
  onLoad() {
    // 初始化Pinia stores
    this.sharingStore = useSharingStore()
    this.userStore = useUserStore()

    // 监听拼场数据变化
    uni.$on('sharingDataChanged', this.onSharingDataChanged)
    // 监听订单取消事件
    uni.$on('orderCancelled', this.onOrderCancelled)
    this.initData()
  },

  onUnload() {
    // 移除监听器
    uni.$off('sharingDataChanged', this.onSharingDataChanged)
    uni.$off('orderCancelled', this.onOrderCancelled)
  },

  async onShow() {
    // 强制刷新数据，确保从其他页面返回时数据是最新的
    await this.refreshData()
    // 加载用户申请记录
    await this.loadUserApplications()
  },
  
  onPullDownRefresh() {
    this.refreshData()
  },
  
  onReachBottom() {
    if (this.hasMore && !this.loading) {
      this.loadMore()
    }
  },
  
  methods: {
    
    // 初始化数据
    async initData() {
      try {
        console.log('拼场列表页面：开始初始化数据')
        console.log('拼场列表页面：Store状态:', this.sharingStore)
        console.log('拼场列表页面：当前显示模式:', this.showMode)
        
        // 根据显示模式选择不同的API
        const apiMethod = this.showMode === 'all' ? this.sharingStore.getAllSharingOrders : this.sharingStore.getJoinableSharingOrders
        const result = await apiMethod({ page: 1, pageSize: 10 })
        console.log('拼场列表页面：API返回结果:', result)
        console.log('拼场列表页面：Store中的数据:', this.sharingOrders)
        console.log('拼场列表页面：初始化数据完成，订单数量:', this.sharingOrders?.length || 0)

        // 调试：检查第一个分享订单的数据结构
        if (this.sharingOrders && this.sharingOrders.length > 0) {
          console.log('第一个分享订单的完整数据结构:', this.sharingOrders[0])
          console.log('第一个分享订单的价格字段:', {
            pricePerTeam: this.sharingOrders[0].pricePerTeam,
            perTeamPrice: this.sharingOrders[0].perTeamPrice,
            pricePerPerson: this.sharingOrders[0].pricePerPerson,
            price: this.sharingOrders[0].price,
            totalPrice: this.sharingOrders[0].totalPrice,
            cost: this.sharingOrders[0].cost
          })
          console.log('第一个分享订单的参与者信息:', {
            currentParticipants: this.sharingOrders[0].currentParticipants,
            maxParticipants: this.sharingOrders[0].maxParticipants,
            participants: this.sharingOrders[0].participants,
            participantCount: this.sharingOrders[0].participantCount
          })
        }
        
        // 强制更新视图
        this.$forceUpdate()
      } catch (error) {
        console.error('拼场列表页面：初始化数据失败:', error)
        uni.showToast({
          title: '获取拼场数据失败',
          icon: 'none'
        })
      }
    },
    
    // 刷新数据
    async refreshData() {
      // 防止重复调用
      if (this.loading) {
        console.log('拼场列表页面：正在加载中，跳过重复调用')
        return
      }

      try {
        console.log('拼场列表页面：开始刷新数据，当前显示模式:', this.showMode)

        // 根据显示模式选择不同的API
        const apiMethod = this.showMode === 'all' ? this.sharingStore.getAllSharingOrders : this.sharingStore.getJoinableSharingOrders

        // 添加时间戳参数，确保不使用缓存
        const result = await apiMethod({
          page: 1,
          pageSize: 10,
          refresh: true,
          _t: Date.now() // 添加时间戳，防止缓存
        })

        console.log('拼场列表页面：API调用结果:', result)
        console.log('拼场列表页面：刷新数据完成，订单数量:', this.sharingOrders?.length || 0)
        console.log('拼场列表页面：Store中的数据:', this.sharingOrders)

        // 强制更新视图
        this.$forceUpdate()
        uni.stopPullDownRefresh()
      } catch (error) {
        uni.stopPullDownRefresh()
        console.error('拼场列表页面：刷新数据失败:', error)
        uni.showToast({
          title: '刷新数据失败',
          icon: 'none'
        })
      }
    },
    
    // 加载更多
    async loadMore() {
      if (this.loading || !this.hasMore) return
      
      try {
        console.log('拼场列表页面：开始加载更多，当前页码:', this.pagination.current, '显示模式:', this.showMode)
        const nextPage = this.pagination.current + 1
        // 根据显示模式选择不同的API
        const apiMethod = this.showMode === 'all' ? this.sharingStore.getAllSharingOrders : this.sharingStore.getJoinableSharingOrders
        await apiMethod({ 
          page: nextPage, 
          pageSize: 10,
          status: this.selectedStatus,
          ...this.filterOptions
        })
        console.log('拼场列表页面：加载更多完成，订单数量:', this.sharingOrders?.length || 0)
      } catch (error) {
        console.error('拼场列表页面：加载更多失败:', error)
        uni.showToast({
          title: '加载更多失败',
          icon: 'none'
        })
      }
    },
    
    // 选择状态
    async selectStatus(status) {
      this.selectedStatus = status
      try {
        // 根据显示模式选择不同的API
        const apiMethod = this.showMode === 'all' ? this.sharingStore.getAllSharingOrders : this.sharingStore.getJoinableSharingOrders
        await apiMethod({ 
          page: 1, 
          pageSize: 10, 
          status: status,
          refresh: true,
          ...this.filterOptions
        })
      } catch (error) {
        console.error('筛选失败:', error)
      }
    },
    
    // 切换显示模式
    async switchMode(mode) {
      if (this.showMode === mode) {
        console.log('拼场列表页面：模式未改变，跳过切换')
        return
      }

      console.log('拼场列表页面：切换显示模式从', this.showMode, '到', mode)
      this.showMode = mode
      this.selectedStatus = '' // 重置状态筛选

      try {
        // 切换模式时重新加载数据
        await this.refreshData()
      } catch (error) {
        console.error('切换模式失败:', error)
        uni.showToast({
          title: '切换模式失败，请重试',
          icon: 'error'
        })
        // 恢复之前的模式
        this.showMode = mode === 'all' ? 'joinable' : 'all'
      }
    },
    
    // 跳转到详情页
    navigateToDetail(sharingId) {
      uni.navigateTo({
        url: `/pages/sharing/detail?id=${sharingId}`
      })
    },
    
    // 判断是否可以加入拼场
    canJoinSharing(sharing) {
      // 如果是自己创建的拼场，不能申请
      if (this.userInfo && sharing.creatorUsername === this.userInfo.username) {
        return false
      }
      
      // 如果已经申请过，不能重复申请
      if (this.hasAppliedToSharing(sharing.id)) {
        return false
      }
      
      return sharing.status === 'OPEN' && 
             (sharing.currentParticipants || 0) < (sharing.maxParticipants || 0)
    },
    
    // 判断是否为自己的拼场
    isMySharing(sharing) {
      return this.userInfo && sharing.creatorUsername === this.userInfo.username
    },
    
    // 判断是否已申请过该拼场
    hasAppliedToSharing(sharingId) {
      return this.userApplications.some(app => 
        app.sharingOrder && app.sharingOrder.id === sharingId
      )
    },
    
    // 加载用户申请记录
    async loadUserApplications() {
      try {
        const applications = await this.sharingStore.getMySharingRequests()
        this.userApplications = applications || []
      } catch (error) {
        console.error('加载用户申请记录失败:', error)
        this.userApplications = []
      }
    },
    
    // 加入拼场
    joinSharing(sharingId) {
      this.currentSharing = this.sharingOrders.find(s => s.id === sharingId)
      // 重置表单
      this.resetApplyForm()
      this.$refs.joinPopup.open()
    },
    
    // 重置申请表单
    resetApplyForm() {
      this.applyForm = {
        teamName: '', // 队名默认为空，让用户自己填写
        contactInfo: this.userInfo?.phone || this.userInfo?.mobile || '', // 联系方式默认为手机号
        message: ''
      }
    },
    
    // 关闭加入弹窗
    closeJoinModal() {
      this.$refs.joinPopup.close()
      this.currentSharing = null
      this.resetApplyForm()
    },
    
    // 提交申请
    async submitApplication() {
      if (!this.canSubmitApplication) {
        uni.showToast({
          title: '请填写联系方式',
          icon: 'none'
        })
        return
      }
      
      try {
        uni.showLoading({ title: '提交中...' })
        
        const applicationData = {
          teamName: this.applyForm.teamName.trim(),
          contactInfo: this.applyForm.contactInfo.trim(),
          message: this.applyForm.message.trim()
        }
        
        const response = await this.sharingStore.applySharingOrder({
          orderId: this.currentSharing.id,
          data: applicationData
        })

        uni.hideLoading()
        this.closeJoinModal()

        // 检查申请是否被自动通过（需要支付）
        if (response && response.data && response.data.status === 'APPROVED_PENDING_PAYMENT') {
          // 自动通过，提示用户并引导支付
          uni.showModal({
            title: '申请已通过',
            content: '您的拼场申请已自动通过！请在30分钟内完成支付以确认参与。',
            showCancel: false,
            confirmText: '去支付',
            success: () => {
              // 跳转到支付页面，使用虚拟订单ID
              uni.navigateTo({
                url: `/pages/payment/index?orderId=${-response.data.id}&type=sharing&from=sharing-list`
              })
            }
          })
        } else if (response && response.data && response.data.status === 'APPROVED') {
          // 旧的自动通过逻辑（兼容性）
          uni.showModal({
            title: '申请已通过',
            content: '您的拼场申请已自动通过！请完成支付以确认参与。',
            showCancel: false,
            confirmText: '去支付',
            success: () => {
              // 跳转到支付页面，使用虚拟订单ID
              uni.navigateTo({
                url: `/pages/payment/index?orderId=${-response.data.id}&type=sharing&from=sharing-list`
              })
            }
          })
        } else {
          // 普通提交，显示等待审核提示
          uni.showToast({
            title: response?.message || '申请提交成功，等待审核',
            icon: 'success',
            duration: 2000
          })
        }

        // 刷新列表和用户申请记录
        await this.refreshData()
        await this.loadUserApplications()
        
      } catch (error) {
        uni.hideLoading()
        console.error('加入拼场失败:', error)
        uni.showToast({
          title: error.message || '加入失败',
          icon: 'error'
        })
      }
    },
    
    // 显示筛选弹窗
    showFilterModal() {
      this.$refs.filterPopup.open()
    },
    
    // 关闭筛选弹窗
    closeFilterModal() {
      this.$refs.filterPopup.close()
    },
    
    // 选择日期
    selectDate(date) {
      this.filterOptions.date = date
    },
    
    // 选择人数
    selectParticipants(participants) {
      this.filterOptions.participants = participants
    },
    
    // 重置筛选
    resetFilter() {
      this.filterOptions = {
        date: '',
        minPrice: '',
        maxPrice: '',
        participants: ''
      }
    },
    
    // 应用筛选
    async applyFilter() {
      this.closeFilterModal()
      try {
        await this.sharingStore.getJoinableSharingOrders({
          page: 1, 
          pageSize: 10,
          status: this.selectedStatus,
          refresh: true,
          ...this.filterOptions
        })
      } catch (error) {
        console.error('应用筛选失败:', error)
      }
    },
    
    // 格式化日期
    formatDate(date) {
      if (!date) return '--'
      return formatDate(date, 'MM-DD')
    },
    
    // 格式化时间
    formatDateTime(datetime) {
      if (!datetime) return '--'
      return formatDateTime(datetime)
    },
    
    // 格式化时间段
    formatTimeSlot(startTime, endTime) {
      if (!startTime && !endTime) {
        return '时间未指定'
      }
      if (startTime && !endTime) {
        return startTime
      }
      if (!startTime && endTime) {
        return endTime
      }
      return `${startTime}-${endTime}`
    },
    
    // 格式化时间范围显示（参考booking/list）
    formatTimeRange(sharing) {
      const startTime = sharing.startTime || sharing.bookingStartTime
      const endTime = sharing.endTime || sharing.bookingEndTime
      const timeSlotCount = sharing.timeSlotCount || 1
      
      if (!startTime || !endTime) {
        return '时间待定'
      }
      
      // 格式化时间显示（去掉秒数）
      const formatTime = (timeStr) => {
        if (!timeStr) return ''
        // 如果是完整的时间格式（HH:mm:ss），只取前5位
        if (timeStr.length > 5 && timeStr.includes(':')) {
          return timeStr.substring(0, 5)
        }
        return timeStr
      }
      
      const formattedStart = formatTime(startTime)
      const formattedEnd = formatTime(endTime)
      
      // 添加日期信息
      const dateStr = this.formatDate(sharing.bookingDate)
      
      // 如果有多个时间段，显示时间段数量
      if (timeSlotCount > 1) {
        return `${dateStr} ${formattedStart} - ${formattedEnd} (${timeSlotCount}个时段)`
      }
      
      return `${dateStr} ${formattedStart} - ${formattedEnd}`
    },
    
    // 格式化创建时间（参考booking/list）
    formatCreateTime(datetime) {
      if (!datetime) return '--'
      try {
        // 处理iOS兼容性问题：将空格分隔的日期时间格式转换为T分隔的ISO格式
        let dateStr = datetime
        if (typeof dateStr === 'string' && dateStr.includes(' ') && !dateStr.includes('T')) {
          dateStr = dateStr.replace(' ', 'T')
        }
        
        const date = new Date(dateStr)
        if (isNaN(date.getTime())) return '--'
        const year = date.getFullYear()
        const month = String(date.getMonth() + 1).padStart(2, '0')
        const day = String(date.getDate()).padStart(2, '0')
        const hour = String(date.getHours()).padStart(2, '0')
        const minute = String(date.getMinutes()).padStart(2, '0')
        return `${year}-${month}-${day} ${hour}:${minute}`
      } catch (error) {
        console.error('时间格式化错误:', error)
        return '--'
      }
    },
    
    // 格式化加入时间
    formatJoinTime() {
      if (!this.currentSharing) return ''
      return `${this.formatDate(this.currentSharing.bookingDate)} ${this.formatTimeSlot(this.currentSharing.startTime, this.currentSharing.endTime)}`
    },
    
    // 格式化价格显示
    formatPrice(price) {
      if (!price && price !== 0) return '0'
      const numPrice = Number(price)
      if (isNaN(numPrice)) return '0'
      return numPrice.toFixed(2)
    },
    
    // 获取进度条宽度
    getProgressWidth(sharing) {
      const current = sharing.currentParticipants || 0
      const max = sharing.maxParticipants || 2
      return Math.min((current / max) * 100, 100)
    },

    // 判断是否显示倒计时
    shouldShowCountdown(order) {
      return shouldShowCountdown(order)
    },

    // 倒计时过期处理
    onCountdownExpired(order) {
      console.log('拼场订单倒计时过期:', order.orderNo)
      // 刷新数据，更新订单状态
      this.refreshData()
    },
    
    // 获取状态样式类
    getStatusClass(status) {
      const statusMap = {
        'OPEN': 'status-open',
        'FULL': 'status-full',
        'CONFIRMED': 'status-confirmed',
        'CANCELLED': 'status-cancelled',
        'EXPIRED': 'status-expired'
      }
      return statusMap[status] || 'status-open'
    },
    
    // 获取状态文本
    getStatusText(status) {
      const statusMap = {
        'OPEN': '开放中(1/2)',
        'APPROVED_PENDING_PAYMENT': '等待对方支付',
        'SHARING_SUCCESS': '拼场成功(2人)',
        'CONFIRMED': '已确认',
        'CANCELLED': '已取消',
        'EXPIRED': '已过期'
      }
      return statusMap[status] || '开放中'
    },

    // 处理拼场数据变化
    onSharingDataChanged(data) {
      console.log('拼场列表页面：收到数据变化通知:', data)

      // 查找对应的订单并更新
      if (this.sharingOrders && data.orderId) {
        const order = this.sharingOrders.find(o => o.id == data.orderId)
        if (order) {
          // 更新参与人数
          if (data.currentParticipants !== undefined) {
            order.currentParticipants = data.currentParticipants
          }

          // 如果是批准申请，可能需要更新状态
          if (data.action === 'APPROVED' && order.currentParticipants >= 2) {
            order.status = 'SHARING_SUCCESS'
          }

          console.log('拼场列表页面：已更新订单数据:', order)
        }
      }

      // 强制刷新数据以确保一致性
      setTimeout(() => {
        this.refreshData()
      }, 1000)
    },

    // 处理订单取消事件
    onOrderCancelled(data) {
      console.log('拼场列表页面：收到订单取消通知:', data)

      if (data.type === 'booking' && data.orderId) {
        console.log('检测到预约订单取消，刷新拼场大厅数据')

        // 延迟刷新数据，确保后端状态已同步
        setTimeout(() => {
          console.log('开始刷新拼场大厅数据...')
          this.refreshData()
        }, 1500) // 稍微延长延迟时间，确保后端处理完成
      }
    },

    // 获取加入按钮文本
    getJoinButtonText(sharing) {
      // 如果是自己的拼场
      if (this.isMySharing(sharing)) {
        return '我的拼场'
      }
      // 如果已申请过该拼场
      if (this.hasAppliedToSharing(sharing)) {
        return '已申请'
      }
      if (sharing.status === 'FULL') {
        return '已满员'
      }
      if (sharing.status === 'CONFIRMED') {
        return '已确认'
      }
      if (sharing.status === 'CANCELLED') {
        return '已取消'
      }
      if (sharing.status === 'EXPIRED') {
        return '已过期'
      }
      return '申请拼场'
    },
    
    // 导航到我的拼场页面
    goToMyOrders() {
      uni.navigateTo({
        url: '/pages/sharing/my-orders'
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.container {
  background-color: #f5f5f5;
  min-height: 100vh;
}

// 显示模式切换
.mode-switch {
  display: flex;
  background-color: #ffffff;
  margin: 20rpx 30rpx;
  border-radius: 12rpx;
  padding: 8rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  
  .mode-item {
    flex: 1;
    text-align: center;
    padding: 16rpx 0;
    border-radius: 8rpx;
    font-size: 28rpx;
    color: #666666;
    transition: all 0.3s ease;
    
    &.active {
      background-color: #007aff;
      color: #ffffff;
      font-weight: bold;
    }
  }
 }

.container {
  padding-bottom: 120rpx;
}

// 悬浮按钮
.floating-btn {
  position: fixed;
  bottom: 120rpx;
  right: 30rpx;
  width: 120rpx;
  height: 120rpx;
  background-color: #ff6b35;
  border-radius: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 20rpx rgba(255, 107, 53, 0.3);
  z-index: 999;
  
  .floating-btn-text {
    font-size: 24rpx;
    color: #ffffff;
    font-weight: 500;
    text-align: center;
    line-height: 1.2;
  }
}

// 筛选栏
.filter-section {
  display: flex;
  background-color: #ffffff;
  padding: 20rpx 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
  
  .filter-scroll {
    flex: 1;
    white-space: nowrap;
    
    .filter-item {
      display: inline-block;
      padding: 12rpx 24rpx;
      margin-right: 20rpx;
      background-color: #f5f5f5;
      border-radius: 30rpx;
      font-size: 24rpx;
      color: #666666;
      
      &.active {
        background-color: #ff6b35;
        color: #ffffff;
      }
    }
  }
  
  .filter-more {
    padding: 12rpx 24rpx;
    background-color: #f5f5f5;
    border-radius: 30rpx;
    font-size: 24rpx;
    color: #666666;
  }
}

// 拼场列表
.sharing-list {
  padding: 20rpx 30rpx;
  
  .sharing-card {
    background-color: #ffffff;
    border-radius: 16rpx;
    padding: 30rpx;
    margin-bottom: 20rpx;
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
    
    &.full-card {
      background-color: #f8f8f8;
      opacity: 0.7;
      
      .venue-name {
        color: #999999 !important;
      }
      
      .time-text, .team-name {
        color: #999999 !important;
      }
    }
    
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: 20rpx;
      
      .venue-info {
        flex: 1;
        
        .venue-name {
          font-size: 32rpx;
          font-weight: bold;
          color: #333333;
          display: block;
          margin-bottom: 8rpx;
        }
        
        .venue-location {
          font-size: 24rpx;
          color: #999999;
        }
      }
      
      .my-sharing-badge {
        padding: 6rpx 12rpx;
        background-color: #ff6b35;
        border-radius: 16rpx;
        margin-right: 12rpx;
        
        .badge-text {
          font-size: 20rpx;
          color: #ffffff;
          font-weight: bold;
        }
      }
      
      .full-badge {
        padding: 6rpx 12rpx;
        background-color: #999999;
        border-radius: 16rpx;
        margin-right: 12rpx;
        
        .badge-text {
          font-size: 20rpx;
          color: #ffffff;
          font-weight: bold;
        }
      }
      
      .sharing-status {
        padding: 8rpx 16rpx;
        border-radius: 20rpx;
        font-size: 22rpx;
        
        &.status-open {
          background-color: #e8f5e8;
          color: #52c41a;
        }
        
        &.status-full {
          background-color: #fff2e8;
          color: #fa8c16;
        }
        
        &.status-confirmed {
          background-color: #e6f7ff;
          color: #1890ff;
        }
        
        &.status-cancelled {
          background-color: #fff1f0;
          color: #ff4d4f;
        }
        
        &.status-expired {
          background-color: #f6f6f6;
          color: #999999;
        }
      }
    }
    
    .card-content {
      .time-info, .team-info {
        display: flex;
        align-items: center;
        margin-bottom: 16rpx;
        
        .time-icon, .team-icon {
          font-size: 28rpx;
          margin-right: 12rpx;
        }
        
        .time-text, .team-name {
          font-size: 28rpx;
          color: #333333;
        }
      }
      
      .participants-info {
        margin-bottom: 16rpx;
        
        .participants-text {
          font-size: 26rpx;
          color: #666666;
          margin-bottom: 8rpx;
        }
        
        .progress-bar {
          height: 8rpx;
          background-color: #f0f0f0;
          border-radius: 4rpx;
          overflow: hidden;
          
          .progress-fill {
            height: 100%;
            background-color: #ff6b35;
            transition: width 0.3s ease;
          }
        }
      }
      
      .price-info, .creator-info, .create-info {
        display: flex;
        align-items: center;
        margin-bottom: 12rpx;
        
        .price-label, .creator-label, .create-label {
          font-size: 24rpx;
          color: #999999;
          margin-right: 8rpx;
        }
        
        .price-value {
          font-size: 28rpx;
          font-weight: bold;
          color: #ff6b35;
        }
        
        .price-note {
          font-size: 20rpx;
          color: #999999;
          margin-left: 8rpx;
        }
        
        .creator-value, .create-value {
          font-size: 24rpx;
          color: #666666;
        }
      }
      
      .description {
        margin-top: 16rpx;
        padding: 16rpx;
        background-color: #f8f8f8;
        border-radius: 8rpx;
        
        text {
          font-size: 24rpx;
          color: #666666;
          line-height: 1.5;
        }
      }
    }
    
    .card-actions {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-top: 20rpx;
      padding-top: 20rpx;
      border-top: 1rpx solid #f0f0f0;
      
      .organizer-info {
        display: flex;
        align-items: center;
        
        .organizer-name {
          font-size: 24rpx;
          color: #666666;
        }
      }
      
      .join-btn {
        padding: 12rpx 24rpx;
        background-color: #ff6b35;
        color: #ffffff;
        border-radius: 24rpx;
        font-size: 24rpx;
        border: none;
      }
      
      .join-disabled {
        padding: 12rpx 24rpx;
        background-color: #f0f0f0;
        color: #999999;
        border-radius: 24rpx;
        font-size: 24rpx;
        
        &.applied {
          background-color: #e8f4fd;
          color: #1890ff;
          border: 1rpx solid #91d5ff;
        }
      }
    }

    // 倒计时样式
    .countdown-container.simple {
      margin-top: 12rpx;
      padding: 6rpx 10rpx;
      font-size: 20rpx;

      .countdown-icon {
        font-size: 22rpx;
      }

      .countdown-content {
        .countdown-time {
          font-size: 20rpx;
        }
      }
    }
  }
}

// 空状态
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 60rpx;
  
  .empty-icon {
    font-size: 120rpx;
    margin-bottom: 30rpx;
  }
  
  .empty-text {
    font-size: 28rpx;
    color: #999999;
  }
}

// 加载状态
.loading-state {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 60rpx;
  
  text {
    font-size: 28rpx;
    color: #999999;
  }
}

// 加载更多
.load-more {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40rpx;
  
  text {
    font-size: 28rpx;
    color: #999999;
  }
}

// 筛选弹窗
.filter-modal {
  background-color: #ffffff;
  border-radius: 24rpx 24rpx 0 0;
  padding: 40rpx 30rpx;
  max-height: 80vh;
  
  .modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 40rpx;
    
    .modal-title {
      font-size: 32rpx;
      font-weight: bold;
      color: #333333;
    }
    
    .modal-close {
      font-size: 40rpx;
      color: #999999;
    }
  }
  
  .filter-content {
    .filter-group {
      margin-bottom: 40rpx;
      
      .group-title {
        font-size: 28rpx;
        font-weight: bold;
        color: #333333;
        margin-bottom: 20rpx;
      }
      
      .date-options, .participants-options {
        display: flex;
        flex-wrap: wrap;
        gap: 16rpx;
        
        .date-item, .participants-item {
          padding: 16rpx 24rpx;
          background-color: #f5f5f5;
          border-radius: 24rpx;
          font-size: 24rpx;
          color: #666666;
          
          &.active {
            background-color: #ff6b35;
            color: #ffffff;
          }
        }
      }
      
      .price-range {
        display: flex;
        align-items: center;
        gap: 16rpx;
        
        .price-input {
          flex: 1;
          padding: 16rpx;
          border: 1rpx solid #e0e0e0;
          border-radius: 8rpx;
          font-size: 24rpx;
        }
        
        .price-separator {
          font-size: 24rpx;
          color: #999999;
        }
      }
    }
  }
  
  .modal-footer {
    display: flex;
    gap: 20rpx;
    margin-top: 40rpx;
    
    .reset-btn, .confirm-btn {
      flex: 1;
      padding: 24rpx;
      border-radius: 12rpx;
      font-size: 28rpx;
      border: none;
    }
    
    .reset-btn {
      background-color: #f5f5f5;
      color: #666666;
    }
    
    .confirm-btn {
      background-color: #ff6b35;
      color: #ffffff;
    }
  }
}

// 申请拼场弹窗
.apply-modal {
  background-color: #ffffff;
  border-radius: 24rpx 24rpx 0 0;
  padding: 0;
  max-height: 80vh;
  
  .modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 32rpx 32rpx 0;
    
    .modal-title {
      font-size: 32rpx;
      font-weight: 600;
      color: #333333;
    }
    
    .close-btn {
      font-size: 32rpx;
      color: #999999;
      padding: 8rpx;
    }
  }
  
  .modal-content {
    padding: 32rpx;
    max-height: 60vh;
    overflow-y: auto;
    
    .form-item {
      margin-bottom: 32rpx;
      
      .form-label {
        display: block;
        font-size: 28rpx;
        color: #333333;
        margin-bottom: 16rpx;
        
        .required {
          color: #ff4d4f;
        }
      }
      
      .form-hint {
        font-size: 24rpx;
        color: #999999;
        line-height: 1.5;
      }
      
      .form-input {
        width: 100%;
        padding: 24rpx;
        border: 1rpx solid #e0e0e0;
        border-radius: 12rpx;
        font-size: 28rpx;
        background-color: #fafafa;
        
        &:focus {
          border-color: #ff6b35;
          background-color: #ffffff;
        }
      }
      
      .form-textarea {
        width: 100%;
        min-height: 120rpx;
        padding: 24rpx;
        border: 1rpx solid #e0e0e0;
        border-radius: 12rpx;
        font-size: 28rpx;
        background-color: #fafafa;
        resize: none;
        
        &:focus {
          border-color: #ff6b35;
          background-color: #ffffff;
        }
      }
      
      .char-count {
        display: block;
        text-align: right;
        font-size: 24rpx;
        color: #999999;
        margin-top: 8rpx;
      }
    }
  }
  
  .modal-actions {
    display: flex;
    padding: 24rpx 32rpx 32rpx;
    gap: 24rpx;
    
    .modal-btn {
      flex: 1;
      padding: 28rpx;
      border-radius: 12rpx;
      font-size: 28rpx;
      border: none;
      
      &.cancel-btn {
        background-color: #f5f5f5;
        color: #666666;
      }
      
      &.confirm-btn {
        background-color: #ff6b35;
        color: #ffffff;
        
        &:disabled {
          background-color: #cccccc;
          color: #999999;
        }
      }
    }
  }
}
</style>