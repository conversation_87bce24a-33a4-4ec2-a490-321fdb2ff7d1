<template>
  <view class="container">
    <view class="header">
      <text class="title">拼场功能测试</text>
    </view>
    
    <!-- 测试参数 -->
    <view class="test-section">
      <view class="section-title">测试参数</view>
      
      <view class="test-item">
        <text class="test-label">场馆ID:</text>
        <input v-model="testVenueId" placeholder="输入场馆ID" class="test-input" />
      </view>
      
      <view class="test-item">
        <text class="test-label">测试日期:</text>
        <input v-model="testDate" placeholder="YYYY-MM-DD" class="test-input" />
      </view>
      
      <view class="test-item">
        <text class="test-label">队伍名称:</text>
        <input v-model="testTeamName" placeholder="输入队伍名称" class="test-input" />
      </view>
      
      <view class="test-item">
        <text class="test-label">联系方式:</text>
        <input v-model="testContactInfo" placeholder="输入联系方式" class="test-input" />
      </view>
    </view>
    
    <!-- 测试1: 拼场价格计算诊断 -->
    <view class="test-section">
      <view class="section-title">测试1: 拼场价格计算诊断</view>
      
      <button @click="testPriceCalculation" class="test-button">测试价格计算</button>
      
      <view v-if="priceTestResult" class="test-result">
        <text class="result-title">价格计算结果:</text>
        <view class="result-item">
          <text>总原价: ¥{{ priceTestResult.priceCalculation.totalOriginalPrice }}</text>
        </view>
        <view class="result-item">
          <text>每队价格: ¥{{ priceTestResult.priceCalculation.pricePerTeam }}</text>
        </view>
        <view class="result-item">
          <text>优惠金额: ¥{{ priceTestResult.priceCalculation.discountAmount }}</text>
        </view>
        <view v-if="priceTestResult.issues.length > 0" class="result-item">
          <text>问题: {{ priceTestResult.issues.join(', ') }}</text>
        </view>
      </view>
    </view>
    
    <!-- 测试2: 拼场数据结构诊断 -->
    <view class="test-section">
      <view class="section-title">测试2: 拼场数据结构诊断</view>
      
      <button @click="testDataStructure" class="test-button">测试数据结构</button>
      
      <view v-if="dataTestResult" class="test-result">
        <text class="result-title">数据结构检查:</text>
        <view v-for="(field, key) in dataTestResult.requiredFields" :key="key" class="result-item">
          <text>{{ key }}: {{ field ? '✅' : '❌' }}</text>
        </view>
        <view v-if="dataTestResult.issues.length > 0" class="result-item">
          <text>问题: {{ dataTestResult.issues.join(', ') }}</text>
        </view>
      </view>
    </view>
    
    <!-- 测试3: 综合拼场诊断 -->
    <view class="test-section">
      <view class="section-title">测试3: 综合拼场诊断</view>
      
      <button @click="testComprehensiveDiagnosis" class="test-button">综合诊断</button>
      
      <view v-if="comprehensiveResult" class="test-result">
        <text class="result-title">综合诊断结果:</text>
        <view class="result-item">
          <text>总体状态: {{ comprehensiveResult.overallStatus }}</text>
        </view>
        <view class="result-item">
          <text>总结: {{ comprehensiveResult.summary }}</text>
        </view>
        <view class="result-item">
          <text>发现问题: {{ comprehensiveResult.allIssues.length }}个</text>
        </view>
        <view class="result-item">
          <text>修复建议: {{ comprehensiveResult.allRecommendations.length }}个</text>
        </view>
      </view>
    </view>
    
    <!-- 测试4: 拼场快速修复 -->
    <view class="test-section">
      <view class="section-title">测试4: 拼场快速修复</view>
      
      <button @click="testQuickFix" class="test-button">快速修复</button>
      
      <view v-if="quickFixResult" class="test-result">
        <text class="result-title">快速修复结果:</text>
        <view class="result-item">
          <text>修复成功: {{ quickFixResult.success ? '✅' : '❌' }}</text>
        </view>
        <view class="result-item">
          <text>应用修复: {{ quickFixResult.appliedFixes.length }}个</text>
        </view>
        <view v-if="quickFixResult.fixedData" class="result-item">
          <text>修复后价格: ¥{{ quickFixResult.fixedData.price }}</text>
        </view>
      </view>
    </view>
    
    <!-- 测试5: 模拟拼场创建 -->
    <view class="test-section">
      <view class="section-title">测试5: 模拟拼场创建</view>
      
      <button @click="testSharingCreation" class="test-button">模拟创建拼场</button>
      
      <view v-if="creationResult" class="test-result">
        <text class="result-title">创建结果:</text>
        <view class="result-item">
          <text>成功: {{ creationResult.success ? '✅' : '❌' }}</text>
        </view>
        <view v-if="creationResult.orderId" class="result-item">
          <text>订单ID: {{ creationResult.orderId }}</text>
        </view>
        <view v-if="creationResult.error" class="result-item">
          <text>错误: {{ creationResult.error }}</text>
        </view>
      </view>
    </view>
    
    <!-- 测试日志 -->
    <view class="test-section">
      <view class="section-title">测试日志</view>
      
      <button @click="clearLogs" class="test-button secondary">清除日志</button>
      
      <view class="test-logs">
        <view v-for="(log, index) in testLogs" :key="index" class="log-item">
          <text>{{ log }}</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { useVenueStore } from '@/stores/venue.js'
import { useBookingStore } from '@/stores/booking.js'

export default {
  name: 'SharingTest',
  
  data() {
    return {
      testVenueId: '29',
      testDate: '2025-07-20',
      testTeamName: '测试队伍',
      testContactInfo: '13800138000',
      priceTestResult: null,
      dataTestResult: null,
      comprehensiveResult: null,
      quickFixResult: null,
      creationResult: null,
      testLogs: []
    }
  },
  
  setup() {
    const venueStore = useVenueStore()
    const bookingStore = useBookingStore()
    return {
      venueStore,
      bookingStore
    }
  },
  
  methods: {
    addLog(message) {
      const timestamp = new Date().toLocaleTimeString()
      this.testLogs.push(`[${timestamp}] ${message}`)
      console.log(`[拼场测试] ${message}`)
    },
    
    clearLogs() {
      this.testLogs = []
    },
    
    // 生成测试时间段
    generateTestSlots() {
      return [
        {
          id: `default_${this.testVenueId}_${this.testDate}_12_0`,
          startTime: '12:00',
          endTime: '12:30',
          price: 100
        },
        {
          id: `default_${this.testVenueId}_${this.testDate}_12_30`,
          startTime: '12:30',
          endTime: '13:00',
          price: 100
        }
      ]
    },
    
    // 生成测试场馆
    generateTestVenue() {
      return {
        id: parseInt(this.testVenueId),
        name: '测试场馆',
        price: 200
      }
    },
    
    // 生成测试表单
    generateTestForm() {
      return {
        teamName: this.testTeamName,
        contactInfo: this.testContactInfo,
        description: '测试拼场',
        bookingType: 'SHARED'
      }
    },
    
    async testPriceCalculation() {
      try {
        this.addLog('开始测试价格计算...')
        
        const sharingModule = await import('@/utils/sharing-fix-diagnosis.js')
        this.priceTestResult = sharingModule.diagnoseSharingPriceCalculation(
          this.generateTestSlots(),
          this.generateTestVenue(),
          this.generateTestForm()
        )
        
        this.addLog(`价格计算测试完成: 每队¥${this.priceTestResult.priceCalculation.pricePerTeam}`)
      } catch (error) {
        this.addLog(`价格计算测试失败: ${error.message}`)
        console.error('价格计算测试失败:', error)
      }
    },
    
    async testDataStructure() {
      try {
        this.addLog('开始测试数据结构...')
        
        const testData = {
          venueId: parseInt(this.testVenueId),
          date: this.testDate,
          startTime: '12:00',
          teamName: this.testTeamName,
          contactInfo: this.testContactInfo,
          maxParticipants: 2,
          price: 100
        }
        
        const sharingModule = await import('@/utils/sharing-fix-diagnosis.js')
        this.dataTestResult = sharingModule.diagnoseSharingDataStructure(testData)
        
        this.addLog(`数据结构测试完成: ${this.dataTestResult.issues.length}个问题`)
      } catch (error) {
        this.addLog(`数据结构测试失败: ${error.message}`)
        console.error('数据结构测试失败:', error)
      }
    },
    
    async testComprehensiveDiagnosis() {
      try {
        this.addLog('开始综合诊断...')
        
        const sharingModule = await import('@/utils/sharing-fix-diagnosis.js')
        this.comprehensiveResult = await sharingModule.comprehensiveSharingDiagnosis(
          this.generateTestSlots(),
          this.generateTestVenue(),
          this.generateTestForm(),
          this.testVenueId,
          this.testDate
        )
        
        this.addLog(`综合诊断完成: ${this.comprehensiveResult.overallStatus}`)
      } catch (error) {
        this.addLog(`综合诊断失败: ${error.message}`)
        console.error('综合诊断失败:', error)
      }
    },
    
    async testQuickFix() {
      try {
        this.addLog('开始快速修复测试...')
        
        const sharingModule = await import('@/utils/sharing-fix-diagnosis.js')
        this.quickFixResult = sharingModule.quickSharingFix(
          this.generateTestSlots(),
          this.generateTestVenue(),
          this.generateTestForm(),
          this.testVenueId,
          this.testDate
        )
        
        this.addLog(`快速修复测试完成: ${this.quickFixResult.success ? '成功' : '失败'}`)
      } catch (error) {
        this.addLog(`快速修复测试失败: ${error.message}`)
        console.error('快速修复测试失败:', error)
      }
    },
    
    async testSharingCreation() {
      try {
        this.addLog('开始模拟拼场创建...')
        
        const testData = {
          venueId: parseInt(this.testVenueId),
          date: this.testDate,
          startTime: '12:00',
          endTime: '13:00',
          teamName: this.testTeamName,
          contactInfo: this.testContactInfo,
          maxParticipants: 2,
          description: '测试拼场',
          price: 100,
          slotIds: [`default_${this.testVenueId}_${this.testDate}_12_0`]
        }
        
        const response = await this.bookingStore.createSharedBooking(testData)
        
        this.creationResult = {
          success: true,
          orderId: response.id || response.orderId || response.data?.id,
          response: response
        }
        
        this.addLog(`拼场创建成功: 订单ID ${this.creationResult.orderId}`)
      } catch (error) {
        this.creationResult = {
          success: false,
          error: error.message
        }
        this.addLog(`拼场创建失败: ${error.message}`)
        console.error('拼场创建失败:', error)
      }
    }
  }
}
</script>

<style scoped>
.container {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 30px;
}

.title {
  font-size: 24px;
  font-weight: bold;
  color: #333;
}

.test-section {
  background-color: white;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.section-title {
  font-size: 18px;
  font-weight: bold;
  color: #333;
  margin-bottom: 15px;
  border-bottom: 2px solid #007AFF;
  padding-bottom: 5px;
}

.test-item {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.test-label {
  width: 80px;
  font-size: 14px;
  color: #666;
}

.test-input {
  flex: 1;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.test-button {
  background-color: #007AFF;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 12px 24px;
  font-size: 16px;
  margin-bottom: 15px;
}

.test-button.secondary {
  background-color: #666;
}

.test-result {
  background-color: #f8f9fa;
  border-radius: 6px;
  padding: 15px;
  border-left: 4px solid #007AFF;
}

.result-title {
  font-weight: bold;
  color: #333;
  margin-bottom: 10px;
  display: block;
}

.result-item {
  margin-bottom: 8px;
}

.result-item text {
  font-size: 14px;
  color: #555;
}

.test-logs {
  max-height: 300px;
  overflow-y: auto;
  background-color: #f8f9fa;
  border-radius: 6px;
  padding: 15px;
}

.log-item {
  margin-bottom: 5px;
  font-size: 12px;
  color: #666;
  font-family: monospace;
}
</style>
